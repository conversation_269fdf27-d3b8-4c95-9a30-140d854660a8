# =============================================
# \u6570\u636E\u5E93\u914D\u7F6E
# =============================================
# \u6570\u636E\u5E93\u8FDE\u63A5\u4FE1\u606F
spring.datasource.druid.username=admin
spring.datasource.druid.password=r1RGAwWt8dJJj+wt
spring.datasource.druid.url=***************************************************************************************************************************************************************************************************************************************************
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver

# =============================================
# Redis\u914D\u7F6E
# =============================================
# Redis\u8FDE\u63A5\u4FE1\u606F
spring.redis.host=redis-shzlcvjf8142r1sez.redis.ivolces.com
spring.redis.port=6379
spring.redis.password=SmKJxOpFka3O7V

# =============================================
# \u9489\u9489\u914D\u7F6E
# =============================================
# \u9489\u9489\u5E94\u7528\u914D\u7F6E
ding.talk.app.key=dingxplq9fb6xjvmhi8y
ding.talk.app.secret=feeySXbugwG67GXX07J2CdEm3gjlsXaAYkW0a-QOzx6XLfkqiEgu5zy-v-6ElodZ
ding.talk.agent.id=3584984634

# \u9489\u9489\u5BA1\u6279\u6D41\u914D\u7F6E
ding.talk.flow.enable=false
ding.talk.flow.vehicle.disposal.process.code=PROC-F80BE5C1-694D-4C77-A073-ED3CD48205D9
ding.talk.flow.vehicle.scrap.process.code=PROC-7225F406-8B8C-4C71-B796-DF478669A18E
ding.talk.flow.vehicle.purchase.process.code=PROC-A4E3BCF3-D9F0-45DF-B0B2-C9389F92E0D7
ding.talk.flow.vehicle.transfer.process.code=PROC-CA023E7E-957F-4E97-9567-31E01DBE77E7
ding.talk.flow.vehicle.allocate.process.code=PROC-660D040B-F270-4CA1-B52B-9A13984BFFBC
ding.talk.flow.vehicle.modify.business.process.code=PROC-8AB855DE-5E24-4D1C-8C1F-52C3F08AE3EB
ding.talk.flow.vehicle.transfer.fixed.process.code=PR***********-AB37-4A05-BCB8-28E5BC02CD5C
# \u8F66\u8F86\u9006\u5904\u7F6E\u6D41\u7A0B
ding.talk.flow.vehicle.reverse.disposal.process.code = PROC-FBAEB742-1C56-436A-98A0-A2E2A4591A84
# 商务业务出售流程
ding.talk.flow.vehicle.business.sell.process.code=PROC-29783BC7-6B3D-42DB-967B-CC961BFDFA7A

# =============================================
# SSO\u914D\u7F6E 
# =============================================
# SSO\u5BA2\u6237\u7AEF\u914D\u7F6E
sso.clientId=6312403491993600
rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2ICl/4OwITSXdLSthCPujCNizoaii+YaPx8sQ1X2SxGTpA/FgsFq3B4QeugBAR4+gCcO93zFlBftAIgVzbUBPikYOvsmp7vokfxqeEoEejf1ewbKIqXQGNnUQ+bxmOfRw7Yz8LbzK/u84RfwUwCg/Yh7VDignUVuPsbxgNaNlbsU1/TuFnwiiZdCptdiks7JCWuzy998yc4PdIkyRUcWdeFKUAzb3o4YBZuUsCnh/Ye2u13E5RBmSuNka+wvfq26yXNS0UeRaD6t7qV7IOUa7ZoTm0tK3fB5nPcE+b9YERAK6wq8GgRszu6lqLL3eiTDjBu8aj06o3qbqJUqgc8YgQIDAQAB
login.url=https://cheguan.96822.net/dazhong-transportation-management/index
sso.token.url=https://dzsso.96822.net/sso/auth/api/v1/login/token

# =============================================
# \u6587\u4EF6\u7CFB\u7EDF\u914D\u7F6E
# =============================================
# \u6587\u4EF6\u7BA1\u7406\u7CFB\u7EDF\u914D\u7F6E
file.mfs.url=https://cheguan.96822.net/dazhong
file.mfs.root.path=/opt/data/dazhong

# =============================================
# \u5916\u90E8\u63A5\u53E3\u914D\u7F6E
# =============================================
# \u6C7D\u8F66\u4E4B\u5BB6\u63A5\u53E3\u914D\u7F6E
model.queryVehicleBaseList.url=https://md.evcard.vip/mdpartner/model/queryVehicleBaseList
model.getVehicleBaseInfo.url=https://md.evcard.vip/mdpartner/model/getVehicleBaseInfo
# VIN\u7801\u67E5\u8BE2\u63A5\u53E3\u914D\u7F6E
vin.getVehicleModelInfo.url=https://api.tanshuapi.com/api/vin/v2/index
# \u5927\u4F17\u4EA4\u901A\u63A5\u53E3\u914D\u7F6E
# dzjt.getCaeInfo.url=https://taxi.96822.net/api/public/query/getCarInfo
dzjt.getCaeInfo.url=http://***********:8080/api/public/query/getCarInfo

# =============================================
# \u5B89\u5168\u914D\u7F6E
# =============================================
# \u7B7E\u540D\u5BC6\u94A5
sign.secretKey=DKJW2NIGyhlLtTnHJcKWk3jtq88n3qCY
#\u6307\u5B9A\u65E5\u5FD7\u914D\u7F6E\u6587\u4EF6
logging.config=classpath:logback-spring.xml

# 维修saas同步配置
saas.sync.key=A7kL9pQ2xV4eZ8mN1bR3tY6cF5wU0jIq
saas.sync.vehicle.url=http://saas.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-info/batch
saas.sync.model.url=http://saas.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-model/batch
saas.sync.org.url=http://saas.gcsrental.com/auto-care-saas/api/v1/data-sync/org-info/batch