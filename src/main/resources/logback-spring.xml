<configuration debug="false">
	<springProperty scop="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>
	<springProperty scop="context" name="spring.profiles.active" source="spring.profiles.active" defaultValue="dev"/>
	<springProperty scop="context" name="mybatis-plus.log" source="mybatis-plus.log" defaultValue="com"/>
	<springProperty scop="context" name="log.path" source="log.path" defaultValue="/opt/logs"/>

	<property name="APP_NAME" value="${spring.application.name}"/>
	<property name="LOG_HOME" value="${log.path}" />
	<property name="CONSOLE_LOG_PATTERN_FILE" value="%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} %C:%M:%L [%thread] %-5level %msg%n"/>
	<!-- 彩色日志 -->
	<!-- 彩色日志依赖的渲染类 -->
	<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
	<conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
	<conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
	<!-- 彩色日志格式 -->
	<property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<!--这里替换成AspectLogbackEncoderclass="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder"-->
		<encoder>
			<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
			<pattern>${CONSOLE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>

	<appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/${APP_NAME}/${APP_NAME}.error.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/${APP_NAME}/%d{yyyy-MM,aux,Asia/Shanghai}/${APP_NAME}.error.log.%d{yyyy-MM-dd,Asia/Shanghai}.%i.log.gz</fileNamePattern>
			<maxHistory>180</maxHistory>
			<maxFileSize>50MB</maxFileSize>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} [%thread] %-5level %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
	</appender>

	<appender name="FILE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_HOME}/${APP_NAME}/${APP_NAME}.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<FileNamePattern>${LOG_HOME}/${APP_NAME}/%d{yyyy-MM,aux,Asia/Shanghai}/${APP_NAME}.log.%d{yyyy-MM-dd,Asia/Shanghai}.%i.log.gz</FileNamePattern>
			<MaxHistory>180</MaxHistory>
			<maxFileSize>50MB</maxFileSize>
		</rollingPolicy>
		<!--这里替换成AspectLogbackEncoderclass="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder"-->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} [%thread] %-5level %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!-- 打印sql-->
	<logger name="com.dzjt" level="DEBUG" />
	<logger name="com.dazhong" level="DEBUG" />
	<logger name="com.fhs" level="DEBUG" />

	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="ERROR" />
		<appender-ref ref="FILE" />
	</root>

</configuration>