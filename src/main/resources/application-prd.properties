# =============================================
# 数据库配置
# =============================================
# 数据库连接信息
spring.datasource.druid.username=admin
spring.datasource.druid.password=r1RGAwWt8dJJj+wt
spring.datasource.druid.url=********************************************************************************************************************************************************************************************************************************
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver

# =============================================
# Redis配置
# =============================================
# Redis连接信息
spring.redis.host=**************
spring.redis.port=6379
spring.redis.password=SmKJxOpFka3O7V/b

# =============================================
# 钉钉配置
# =============================================
# 钉钉应用配置
ding.talk.app.key=dingxplq9fb6xjvmhi8y
ding.talk.app.secret=feeySXbugwG67GXX07J2CdEm3gjlsXaAYkW0a-QOzx6XLfkqiEgu5zy-v-6ElodZ
ding.talk.agent.id=3584984634

# 钉钉审批流配置
ding.talk.flow.vehicle.disposal.process.code=PROC-F80BE5C1-694D-4C77-A073-ED3CD48205D9
ding.talk.flow.vehicle.scrap.process.code=PROC-7225F406-8B8C-4C71-B796-DF478669A18E
ding.talk.flow.vehicle.purchase.process.code=PROC-A4E3BCF3-D9F0-45DF-B0B2-C9389F92E0D7
ding.talk.flow.vehicle.transfer.process.code=PROC-CA023E7E-957F-4E97-9567-31E01DBE77E7
ding.talk.flow.vehicle.allocate.process.code=PROC-660D040B-F270-4CA1-B52B-9A13984BFFBC
ding.talk.flow.vehicle.modify.business.process.code=PROC-8AB855DE-5E24-4D1C-8C1F-52C3F08AE3EB
ding.talk.flow.vehicle.transfer.fixed.process.code=PR***********-AB37-4A05-BCB8-28E5BC02CD5C
# 车辆逆处置流程
ding.talk.flow.vehicle.reverse.disposal.process.code = PROC-FBAEB742-1C56-436A-98A0-A2E2A4591A84
# 商务业务出售流程
ding.talk.flow.vehicle.business.sell.process.code=PROC-F4FEA6FA-2620-4554-8051-E11561A4FCBF

# =============================================
# SSO配置 
# =============================================
# SSO客户端配置
sso.clientId=6312403491993600
rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2ICl/4OwITSXdLSthCPujCNizoaii+YaPx8sQ1X2SxGTpA/FgsFq3B4QeugBAR4+gCcO93zFlBftAIgVzbUBPikYOvsmp7vokfxqeEoEejf1ewbKIqXQGNnUQ+bxmOfRw7Yz8LbzK/u84RfwUwCg/Yh7VDignUVuPsbxgNaNlbsU1/TuFnwiiZdCptdiks7JCWuzy998yc4PdIkyRUcWdeFKUAzb3o4YBZuUsCnh/Ye2u13E5RBmSuNka+wvfq26yXNS0UeRaD6t7qV7IOUa7ZoTm0tK3fB5nPcE+b9YERAK6wq8GgRszu6lqLL3eiTDjBu8aj06o3qbqJUqgc8YgQIDAQAB
login.url=http://cheguan.96822.net/dazhong-transportation-management/index
sso.token.url=https://dzsso.96822.net/sso/auth/api/v1/login/token

# =============================================
# 文件系统配置
# =============================================
# 文件管理系统配置
file.mfs.url=http://cheguan.96822.net/dazhong
file.mfs.root.path=/opt/data/dazhong

# =============================================
# 外部接口配置
# =============================================
# 汽车之家接口配置
model.queryVehicleBaseList.url=https://md.evcard.vip/mdpartner/model/queryVehicleBaseList
model.getVehicleBaseInfo.url=https://md.evcard.vip/mdpartner/model/getVehicleBaseInfo
# VIN码查询接口配置
vin.getVehicleModelInfo.url=https://api.tanshuapi.com/api/vin/v2/index
# 大众交通接口配置
dzjt.getCaeInfo.url=https://dztaxi.96822.net/api/public/query/getCarInfo

# =============================================
# 安全配置
# =============================================
# 签名密钥
sign.secretKey=DKJW2NIGyhlLtTnHJcKWk3jtq88n3qCY

# 维修saas同步配置
saas.sync.key=A7kL9pQ2xV4eZ8mN1bR3tY6cF5wU0jIq
saas.sync.vehicle.url=http://saas.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-info/batch
saas.sync.model.url=http://saas.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-model/batch
saas.sync.org.url=http://saas.gcsrental.com/auto-care-saas/api/v1/data-sync/org-info/batch