# =============================================
# 应用基础配置
# =============================================
# 应用名称
spring.application.name=dazhong-transportation-vlms
# 服务端口
server.port=20929
# 设置应用的上下文路径
server.servlet.context-path=/dazhong-transportation-vlms
# 激活的Spring配置文件
spring.profiles.active=sit

# =============================================
# 数据库连接池配置
# =============================================
# 数据库连接池类型
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# 连接池配置
spring.datasource.druid.max-active=100
spring.datasource.druid.initial-size=1
spring.datasource.druid.max-wait=10000
spring.datasource.druid.min-idle=1
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=select 'x'
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-open-prepared-statements=50
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20

# =============================================
# Redis配置
# =============================================
# Redis连接池配置
spring.redis.database=0
spring.redis.timeout=10000
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.max-active=32

# =============================================
# 日志配置
# =============================================
# 日志文件配置
logging.config=classpath:logback.xml
logging.file.max-history=7
logging.file.max-size=100MB

# =============================================
# 文件上传配置
# =============================================
# 设置单个文件的最大大小为50MB
spring.servlet.multipart.max-file-size=50MB
# 设置整个请求的最大大小为100MB
spring.servlet.multipart.max-request-size=100MB

# =============================================
# HTTP编码配置
# =============================================
# 字符编码配置
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

# =============================================
# Jackson配置
# =============================================
# JSON序列化配置
spring.jackson.default-property-inclusion=non_null

# 维修saas同步配置
saas.sync.key=dzjt
saas.sync.vehicle.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-info/batch
saas.sync.model.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-model/batch
saas.sync.org.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/org-info/batch

# 大众交通简道云配置-不区分环境
jdy.app.key=ZQgEvmd6yhnufrpfEZlKUxwg8gNPRS2C
jdy.app.id=67f5c251c0f5a69d12a1ed5a
jdy.entry.id=67453603fe5ffa50aaca0c27
jdy.entry.widget.url=https://api.jiandaoyun.com/api/v5/app/entry/widget/list
jdy.entry.data.url=https://api.jiandaoyun.com/api/v5/app/entry/data/list
