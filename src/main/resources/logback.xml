<?xml version="1.0" encoding="UTF-8"?>
<!-- Logback配置文件，参考文档：http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="10 seconds">
    <!-- 引入Spring Boot默认的日志配置 -->
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    
    <!-- 定义日志存储路径和应用名称变量 -->
    <property name="LOG_PATH" value="./log"/>
    <property name="APP_Name" value="dazhong"/>
    <!-- 设置日志上下文名称 -->
    <contextName>${APP_Name}</contextName>

    <!-- 设置时区为Asia/Shanghai -->
    <property name="TIME_ZONE" value="Asia/Shanghai"/>

    <!-- ============================================= -->
    <!-- INFO级别日志配置 -->
    <!-- ============================================= -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 设置日志级别过滤器，只记录INFO及以上级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <!-- 设置日志文件路径 -->
        <File>${LOG_PATH}/${APP_Name}-info.log</File>
        <!-- 配置日志滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天滚动，日志文件名格式 -->
            <FileNamePattern>${LOG_PATH}/${APP_Name}-info.log.%d{yyyy-MM-dd}</FileNamePattern>
        </rollingPolicy>
        <!-- 配置日志输出格式 -->
        <encoder>
            <!-- 日志格式：时间 | 线程 | 级别 | 类名 | 消息 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | %thread | %-5level | %logger | %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- ============================================= -->
    <!-- ERROR级别日志配置 -->
    <!-- ============================================= -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 设置日志级别过滤器，只记录ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <!-- 设置错误日志文件路径 -->
        <File>${LOG_PATH}/${APP_Name}-error.log</File>
        <!-- 配置错误日志滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天滚动，错误日志文件名格式 -->
            <FileNamePattern>${LOG_PATH}/${APP_Name}-error.log.%d{yyyy-MM-dd}</FileNamePattern>
        </rollingPolicy>
        <!-- 配置错误日志输出格式 -->
        <encoder>
            <!-- 日志格式：时间 | 线程 | 级别 | 类名 | 消息 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | %thread | %-5level | %logger | %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- ============================================= -->
    <!-- 控制台输出配置 -->
    <!-- ============================================= -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!-- 控制台日志格式：时间 [线程] 级别 类名 - 消息 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- ============================================= -->
    <!-- MyBatis SQL日志配置 -->
    <!-- ============================================= -->
    <!-- 配置MyBatis的SQL语句和参数日志级别为DEBUG -->
    <logger name="com.dazhong.transportation.vlms.mapper" level="DEBUG" additivity="false">
        <appender-ref ref="STDOUT" />
    </logger>

    <!-- ============================================= -->
    <!-- 根日志配置 -->
    <!-- ============================================= -->
    <!-- 设置全局日志级别为INFO，并配置所有日志输出目标 -->
    <root level="INFO">
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="STDOUT" />
    </root>

</configuration>
