<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <classPathEntry location="src/main/resources/mybatis/jdbc/mysql-connector-java-8.0.25.jar"/>

    <!-- MyBatis3DynamicSql -->
    <context id="dsql" targetRuntime="MyBatis3DynamicSql">
        <property name="javaFileEncoding" value="UTF-8"/>

        <!-- 配置生成pojo的序列化的插件，mybatis支持很多插件，这些插件都在 org.mybatis.generator.plugins包下  -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <commentGenerator>
            <!--  If suppressAllComments option is true, this option will be ignored. -->
            <property name="suppressAllComments" value="false"/>
            <property name="suppressDate" value="true"/>
            <!-- 添加数据库注释 -->
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*********************************************************"
                        userId="dzjt" password="Evcard@1204">
            <property name="useInformationSchema" value="true"/>
            <!-- 若不配置，则可能错误使用其他数据库的同名数据表 -->
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <!-- The <javaModelGenerator> element is used to define properties of the Java model generator. -->
        <javaModelGenerator targetPackage="com.dazhong.transportation.vlms.model"
                            targetProject="src/main/java" >
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- The <javaClientGenerator> element is used to define properties of the Java client generator.  -->
        <!-- type : If the <context> targetRuntime is MyBatis3DynamicSql this attribute is optional and ignored.-->
        <javaClientGenerator targetPackage="com.dazhong.transportation.vlms.mapper"
                             targetProject="src/main/java" type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="t_vehicle_device_info" schema="">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <domainObjectRenamingRule searchString="^T" replaceString="" />
        </table>

    </context>
</generatorConfiguration>
