# =============================================
# \u6570\u636E\u5E93\u914D\u7F6E
# =============================================
# \u6570\u636E\u5E93\u8FDE\u63A5\u4FE1\u606F
spring.datasource.druid.username=vehicle_test
spring.datasource.druid.password=NBY3Wsz%n&
spring.datasource.druid.url=************************************************************************************************************************************************************************************************************************************
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver

# =============================================
# Redis\u914D\u7F6E
# =============================================
# Redis\u8FDE\u63A5\u4FE1\u606F
spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=dzjt
spring.redis.database=10
spring.redis.timeout=10000
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.max-active=32

# =============================================
# \u9489\u9489\u914D\u7F6E
# =============================================
# \u9489\u9489\u5E94\u7528\u914D\u7F6E
ding.talk.app.key=dingxplq9fb6xjvmhi8y
ding.talk.app.secret=feeySXbugwG67GXX07J2CdEm3gjlsXaAYkW0a-QOzx6XLfkqiEgu5zy-v-6ElodZ
ding.talk.agent.id=3584984634

# \u9489\u9489\u5BA1\u6279\u6D41\u914D\u7F6E
# \u8F66\u8F86\u5904\u7F6E\u6D41\u7A0B
ding.talk.flow.vehicle.disposal.process.code=PROC-F80BE5C1-694D-4C77-A073-ED3CD48205D9
# \u8F66\u8F86\u62A5\u5E9F\u6D41\u7A0B
ding.talk.flow.vehicle.scrap.process.code=PROC-7225F406-8B8C-4C71-B796-DF478669A18E
# \u8F66\u8F86\u7533\u8D2D\u6D41\u7A0B
ding.talk.flow.vehicle.purchase.process.code=PROC-A4E3BCF3-D9F0-45DF-B0B2-C9389F92E0D7
# \u8F66\u8F86\u8F6C\u7C4D\u6D41\u7A0B
ding.talk.flow.vehicle.transfer.process.code=PROC-CA023E7E-957F-4E97-9567-31E01DBE77E7
# \u8F66\u8F86\u8C03\u62E8\u6D41\u7A0B
ding.talk.flow.vehicle.allocate.process.code=PROC-660D040B-F270-4CA1-B52B-9A13984BFFBC
# \u8F66\u8F86\u4E1A\u52A1\u7C7B\u578B\u4FEE\u6539\u6D41\u7A0B
ding.talk.flow.vehicle.modify.business.process.code=PROC-8AB855DE-5E24-4D1C-8C1F-52C3F08AE3EB
# \u8F66\u8F86\u8F6C\u56FA\u6D41\u7A0B
ding.talk.flow.vehicle.transfer.fixed.process.code=PROC-55110771-AB37-4A05-BCB8-28E5BC02CD5C
# \u8F66\u8F86\u9006\u5904\u7F6E\u6D41\u7A0B
ding.talk.flow.vehicle.reverse.disposal.process.code = PROC-FBAEB742-1C56-436A-98A0-A2E2A4591A84
# \u5546\u52A1\u4E1A\u52A1\u51FA\u552E\u6D41\u7A0B
ding.talk.flow.vehicle.business.sell.process.code=PROC-F4FEA6FA-2620-4554-8051-E11561A4FCBF


# =============================================
# SSO\u914D\u7F6E
# =============================================
# SSO\u5BA2\u6237\u7AEF\u914D\u7F6E
sso.clientId=6226261277652993
rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2sPFl1ThilzByX8ENi/nGrms0YjGzFeo8OBvfBBt/yxPB57SjqWlcmDcEXtCotye8KxOjaXvCnTKJuBUscBkGWAe3AAgIeYdsf1b34gBaB4c5j70nUl6s+X27MPkj2gY5pM7ABa7Kb7qHlNHyNSvWWjqBjn7LiV3DTG+NW0pkC7Q1HzvszfbV280cQuyC5o02N1DgzRJ3CT7VMpo3A7RtqBFiDbNLLDipKeKaIm9iaTR6hRP9qjJHN+5cOvJmeck8FUnvhjyu5e850kSe0dTp2w+/fgu/FBabl8xX9OzcoxwBPJ9aJVips53mm85eOdwWufgcOsANuV8GstFlh6U1QIDAQAB/nc/E0AAvbFCOVEbBNgC/uZES9rQ2n4PGF7uvYiJBmDTskBh2Lv0jZXPFbhlHNCgzARTb8sXfsvYctpWfoaFeB8X8/Toin4cN6nWiplR/G0gEyQCNTHNjUXgHdhIgbv+/dThBMohnDuttw1erlfdfH3dTqHxrbkx2FkSC54sZHcHSbxO8E6iFdcXpduKfusC4EN8rQPBWEPOBeFdyKfU1IKhJM4WL9TLZq4ftFjc9PJpVt84GBmlhRyN81/AuRy7Ciq4dAs8G426FfLjgRs0QIDAQAB
# \u767B\u5F55\u9875\u9762URL
login.url=https://test.96822.net/dazhong-transportation-management/index
# SSO Token\u63A5\u53E3URL
sso.token.url=https://test.96822.net/sso/auth/api/v1/login/token

# =============================================
# \u6587\u4EF6\u7CFB\u7EDF\u914D\u7F6E
# =============================================
# \u6587\u4EF6\u7BA1\u7406\u7CFB\u7EDF\u914D\u7F6E
file.mfs.url=https://test.96822.net/dazhong
file.mfs.root.path=/oss/file

# =============================================
# \u5916\u90E8\u63A5\u53E3\u914D\u7F6E
# =============================================
# \u6C7D\u8F66\u4E4B\u5BB6\u63A5\u53E3\u914D\u7F6E
model.queryVehicleBaseList.url=https://md-st.evcard.vip/mdpartner/model/queryVehicleBaseList
model.getVehicleBaseInfo.url=https://md-st.evcard.vip/mdpartner/model/getVehicleBaseInfo
# VIN\u7801\u67E5\u8BE2\u63A5\u53E3\u914D\u7F6E
vin.getVehicleModelInfo.url=https://api.tanshuapi.com/api/vin/v2/index
# \u5927\u4F17\u4EA4\u901A\u63A5\u53E3\u914D\u7F6E
dzjt.getCaeInfo.url=http://**********:8080/api/public/query/getCarInfo

# =============================================
# \u5B89\u5168\u914D\u7F6E
# =============================================
# \u7B7E\u540D\u5BC6\u94A5
sign.secretKey=CKKW4MIGyhlLtTnHNcKWk1jtq66n3qGY

# \u7EF4\u4FEEsaas\u540C\u6B65\u914D\u7F6E
saas.sync.key=dzjt
saas.sync.vehicle.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-info/batch
saas.sync.model.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/vehicle-model/batch
saas.sync.org.url=http://saas-sit.gcsrental.com/auto-care-saas/api/v1/data-sync/org-info/batch