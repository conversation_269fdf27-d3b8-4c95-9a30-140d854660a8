package com.dazhong.transportation.vlms.constant;

import java.util.HashMap;

public class BizConstant {

    public static final String DEFAULT_CHARSET_TYPE = "UTF-8";


    public static final String LOGIN_TOKEN_KEY = "loginToken_";

    public static final String LOGIN_USER_KEY = "user_";

    // 用户登录有效期 2小时
    public static final Integer LOGIN_EXPIRES_IN = 7200;

    public static final String system_code = "dazhong-transportation-vlms";

    public static final String SYSTEM_NAME = "system";

    // 最大上传文件限制 100MB
    public static final long MAX_UPLOAD_LENGTH = 100000000;
    /**
     * 日志类型 操作类型 1=新增角色 2=修改角色 3=启用角色 4=禁用角色 5=修改用户角色 6=新增资源 7-修改资源 8-删除资源  9-新增用户 10-修改用户 11-删除用户
     */
    public static class OperateType {
        /**
         * 1=新增角色
         */
        public final static Integer OperateType_1 = 1;
        /**
         * 2=修改角色
         */
        public final static Integer OperateType_2 = 2;
        /**
         * 3=启用角色
         */
        public final static Integer OperateType_3 = 3;
        /**
         * 4=禁用角色
         */
        public final static Integer OperateType_4 = 4;
        /**
         * 5=修改用户角色
         */
        public final static Integer OperateType_5 = 5;
        /**
         * 6=新增资源
         */
        public final static Integer OperateType_6 = 6;
        /**
         * 7-修改资源
         */
        public final static Integer OperateType_7 = 7;
        /**
         * 8-删除资源
         */
        public final static Integer OperateType_8 = 8;

        /**
         * 13-新增用户
         */
        public final static Integer OperateType_13 = 9;
        /**
         * 14-修改用户
         */
        public final static Integer OperateType_14 = 10;
        /**
         * 15-删除用户
         */
        public final static Integer OperateType_15 = 11;
    }

    /***
     * 公用状态
     */
    public static class CommonStatusStatus {
        /**
         * 启用
         */
        public final static Integer enable = 1;
        /**
         * 禁用
         */
        public final static Integer disabled = 2;
    }

    /***
     * 是否超管 1-是 2-否
     */
    public static class SystemAdminType {

        public final static Integer isAdministrator = 1;

        public final static Integer nonAdministrator = 2;
    }

    public static class BusinessType {
        /**
         * 系统管理
         */
        public final static Integer businessType_1 = 1;
        /**
         * 组织架构
         */
        public final static Integer businessType_2 = 2;

        /**
         * 车辆采购
         */
        public final static Integer businessType_3 = 3;

        /**
         * 额度流水单
         */
        public final static Integer businessType_4 = 4;

        /**
         * 车辆主数据日志
         */
        public final static Integer businessType_5 = 5;


    }

    /**
     * 申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
     */
    public static class ApplyStatus {
        /**
         * 1=未提交
         */
        public final static Integer applyStatus_1 = 1;
        /**
         * 2=审批中
         */
        public final static Integer applyStatus_2 = 2;
        /**
         * 3=审批通过
         */
        public final static Integer applyStatus_3 = 3;
        /**
         * 4=审批拒绝
         */
        public final static Integer applyStatus_4 = 4;
        /**
         * 5=已作废
         */
        public final static Integer applyStatus_5 = 5;
        /**
         * 6=已完成(已关闭)
         */
        public final static Integer applyStatus_6 = 6;
    }

    /**
     * 业务类型 insurance-保险、illegal-违章、accident-事故
     */
    public static class SyncExternalBusinessType {
        public final static String insurance = "insurance";
        public final static String illegal = "illegal";
        public final static String accident = "accident";
    }

    public static HashMap<Integer, String> insuranceTypeMap = new HashMap<Integer, String>() {{
        put(1, "商业险");
        put(2, "交强险");
    }};


    public static HashMap<Integer, String> illegalDealStatusMap = new HashMap<Integer, String>() {{
        put(1, "已处理");
        put(2, "未处理");
    }};

}
