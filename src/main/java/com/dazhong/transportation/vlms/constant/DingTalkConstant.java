package com.dazhong.transportation.vlms.constant;

import java.util.HashMap;

public class DingTalkConstant {

    public static final int DING_TALK_HTTP_STATUS = 200;

    /**
     * 钉钉根据手机号获取用户唯一ID 接口地址
     */
    public static final String USER_GET_BY_MOBILE_SERVER_URL = "https://oapi.dingtalk.com/topapi/v2/user/getbymobile";

    /**
     * 钉钉根据用户唯一ID获取用户详情接口
     */
    public static final String GET_USER_INFO_SERVER_URL = "https://oapi.dingtalk.com/topapi/v2/user/get";

    /**
     * 获取钉钉组织架构
     */
    public static final String GET_DEPARTMENT_SERVER_URL = "https://oapi.dingtalk.com/department/list";


    public static final String FLOW_SYSTEM_USER = "bpms_system";


    public static final String DEFAULT_SYSTEM_USER_TO_VIEW = "系统操作";

    /**
     * 钉钉审批状态枚举
     */
    public static HashMap<String, String> dingTalkStatusMap = new HashMap<String, String>() {{
        put("RUNNING", "审批中");
        put("TERMINATED", "已撤销");
        put("COMPLETED", "审批完成");
    }};

    /**
     * 钉钉审批结果枚举
     */
    public static HashMap<String, String> dingTalkResultMap = new HashMap<String, String>() {{
        put("agree", "同意");
        put("refuse", "拒绝");
        put("terminate", "撤销");
    }};

    /**
     * 钉钉审批操作节点结果枚举
     */
    public static HashMap<String, String> dingTalkOptResultMap = new HashMap<String, String>() {{
        put("AGREE", "同意");
        put("REFUSE", "拒绝");
        put("NONE", "未处理");
    }};

    //钉钉审批回调事件类型
    public static String NOTIFY_TYPE_START = "start";//审批实例开始

    public static String NOTIFY_TYPE_FINISH = "finish";//审批正常结束（同意或拒绝）

    public static String NOTIFY_TYPE_TERMINATE = "terminate";//审批终止（发起人撤销审批单）

}
