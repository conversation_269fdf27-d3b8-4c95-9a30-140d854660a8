package com.dazhong.transportation.vlms.utils;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.dingtalkstorage_1_0.models.*;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.tea.TeaModel;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.aliyun.dingtalkoauth2_1_0.*;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.constant.DingTalkConstant;
import com.dazhong.transportation.vlms.dto.CommitDingFlowAttachmentDto;
import com.dazhong.transportation.vlms.dto.DingFlowAttachmentDto;
import com.dazhong.transportation.vlms.dto.DingFlowAttachmentFileInfoDto;
import com.dazhong.transportation.vlms.dto.DingTalkFlowNotifyDto;
import com.dazhong.transportation.vlms.service.IFileService;
import lombok.extern.slf4j.Slf4j;
import com.aliyun.teautil.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.pqc.crypto.newhope.NHOtherInfoGenerator;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 钉钉相关公共接口
 */
@Slf4j
public class DingTalkCommentUtils {

    private static final String ACCESS_TOKEN_KEY = "accessTokenKey";

    //失效时间单位 S
    private static final int ACCESS_TOKEN_EXPIRE_TIME = 7100;

    private static Long agentId = 3283332634L;
    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dingtalkworkflow_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkworkflow_1_0.Client(config);
    }

    public static com.aliyun.dingtalkstorage_1_0.Client createStorageClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkstorage_1_0.Client(config);
    }

    /**
     * 获取钉钉AccessToken
     *  accessToken的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的accessToken。
     * 开发者需要缓存accessToken，用于后续接口的调用。因为每个应用的accessToken是彼此独立的，所以进行缓存时需要区分应用来进行存储。
     * @return
     * @throws Exception
     */
    public static String getAccessToken(String appKey,String appSecret){

        try {
            String accessToken = Global.instance.redisUtil.getStr(ACCESS_TOKEN_KEY);
            if (StringUtils.isNotBlank(accessToken)){
                return accessToken;
            }
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
            config.protocol = "https";
            config.regionId = "central";
            Client client = new Client(config);
            GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest()
                    .setAppKey(appKey)
                    .setAppSecret(appSecret);
            String newAccessToken = client.getAccessToken(getAccessTokenRequest).getBody().getAccessToken();
            Global.instance.redisUtil.set(ACCESS_TOKEN_KEY,newAccessToken,ACCESS_TOKEN_EXPIRE_TIME);
            return newAccessToken;
        } catch (TeaException err) {
            if (!Common.empty(err.code) && !Common.empty(err.message)) {
                log.error("dingTalk getAccessToken exception {}", JSONObject.toJSONString(err));
            }
        } catch (Exception e) {
            log.error("dingTalk getAccessToken exception {}",e);
        }
        return null;
    }

    /**
     * 构建钉钉审批内容
     * @param approves
     * @param processCode
     * @param originatorUserId
     * @param formComponentValueMap
     * @return
     */
    public static StartProcessInstanceRequest buildStartProcessInstanceRequest(List<StartProcessInstanceRequest.StartProcessInstanceRequestApprovers> approves,
                                                                               String processCode,
                                                                               String originatorUserId,
                                                                               Map<String,String> formComponentValueMap,
                                                                               Long dingTalkAgentId,
                                                                               CommitDingFlowAttachmentDto attachmentFile,
                                                                               String accessToken,
                                                                               String unionId,
                                                                               List<String> targetSelectUserIds,
                                                                               Long originatorDeptId) throws Exception {
        List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues> formComponentValues = new ArrayList<>();
        formComponentValueMap.forEach((key,value) ->{
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                    .setName(key)
                    .setValue(value);
            formComponentValues.add(formComponentValue);
        });
        if (attachmentFile != null && !CollectionUtils.isEmpty(attachmentFile.getAttachmentFileList())){
            List<DingFlowAttachmentDto> attachmentDtoList = getDingFlowAttachmentDtoList(attachmentFile.getAttachmentFileList()
                    ,accessToken,originatorUserId,unionId);
            if (!CollectionUtils.isEmpty(attachmentDtoList)){
                StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValue = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                        .setName(attachmentFile.getName())
                        .setValue(JSONObject.toJSONString(attachmentDtoList));
                formComponentValues.add(formComponentValue);
            }
        }
        //构建自定义审批人
        List<StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners> targetSelectActioners =
                getActorKeyList(formComponentValueMap,targetSelectUserIds,accessToken,processCode,originatorUserId);

        StartProcessInstanceRequest startProcessInstanceRequest = new StartProcessInstanceRequest()
                .setApprovers(approves)
                .setDeptId(originatorDeptId)
                .setMicroappAgentId(dingTalkAgentId)
                .setOriginatorUserId(originatorUserId)
                .setProcessCode(processCode)
                .setFormComponentValues(formComponentValues);
        if (!CollectionUtils.isEmpty(targetSelectActioners)){
            startProcessInstanceRequest.setTargetSelectActioners(targetSelectActioners);
        }
        return startProcessInstanceRequest;
    }

    public static List<DingFlowAttachmentDto> getDingFlowAttachmentDtoList(List<DingFlowAttachmentFileInfoDto> files
            , String accessToken, String originatorUserId, String unionId){
        if (CollectionUtils.isEmpty(files)){
            return new ArrayList<>();
        }
        try {
            List<DingFlowAttachmentDto> result = Lists.newArrayList();
            for (DingFlowAttachmentFileInfoDto file : files) {
                String spaceId = getAttachmentSpace(accessToken,originatorUserId).toString();
                String uploadKey = getFileUploadInfoByFile(accessToken,spaceId,file,unionId);
                CommitFileResponse response = getCommitInfoByFile(accessToken,spaceId,uploadKey,file,unionId);
                CommitFileResponseBody.CommitFileResponseBodyDentry  bodyInfo = response.getBody().getDentry();
                result.add(new DingFlowAttachmentDto(bodyInfo.getSpaceId(),
                        bodyInfo.getName(),
                        bodyInfo.getSize(),
                        bodyInfo.getExtension(),
                        bodyInfo.getId()));
            }
            return result;
        }catch (Exception e){
            log.error("getDingFlowAttachmentDtoList exception",e);
        }
        return Lists.newArrayList();
    }


    /**
     * 获取自定义审批人actorKey
     * 如果没有则不定义
     * @param formComponentValueMap
     * @param targetSelectUserIds
     * @param accessToken
     * @param processCode
     * @param originatorUserId
     * @return
     * @throws TeaException
     * @throws Exception
     */
    public static List<StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners> getActorKeyList(Map<String,String> formComponentValueMap
            ,List<String> targetSelectUserIds,String accessToken,String processCode,String originatorUserId) throws TeaException,Exception{
        List<ProcessForecastRequest.ProcessForecastRequestFormComponentValues> formComponentValues = Lists.newArrayList();
        formComponentValueMap.forEach((key,value) ->{
            ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValue = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                    .setName(key)
                    .setValue(value);
            formComponentValues.add(formComponentValue);
        });
        ProcessForecastRequest processForecastRequest = new ProcessForecastRequest()
                .setDeptId(-1)
                .setUserId(originatorUserId)
                .setProcessCode(processCode)
                .setFormComponentValues(formComponentValues);
        com.aliyun.dingtalkworkflow_1_0.Client client = createClient();
        ProcessForecastHeaders processForecastHeaders = new ProcessForecastHeaders();
        processForecastHeaders.xAcsDingtalkAccessToken = accessToken;
        ProcessForecastResponse ProcessForecastResponse = client.processForecastWithOptions(processForecastRequest, processForecastHeaders, new RuntimeOptions());
        List<ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules> resultWorkflowActivityRules
                = ProcessForecastResponse.getBody().getResult().getWorkflowActivityRules();
        List<StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(resultWorkflowActivityRules)){
            return result;
        }
        for (ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules detail : resultWorkflowActivityRules) {
            if ("target_select".equals(detail.getActivityType())){
                StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners resultDetail =
                        new StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners();
                resultDetail.setActionerKey(detail.getWorkflowActor().getActorKey());
                resultDetail.setActionerUserIds(targetSelectUserIds);
                result.add(resultDetail);
            }
        }
        return result;
    }


    public static Long getAttachmentSpace(String accessToken,String userId) throws TeaException,Exception {
        com.aliyun.dingtalkworkflow_1_0.Client client = createClient();
        com.aliyun.dingtalkworkflow_1_0.models.GetAttachmentSpaceHeaders getAttachmentSpaceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetAttachmentSpaceHeaders();
        getAttachmentSpaceHeaders.xAcsDingtalkAccessToken = accessToken;
        com.aliyun.dingtalkworkflow_1_0.models.GetAttachmentSpaceRequest getAttachmentSpaceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetAttachmentSpaceRequest()
                .setUserId(userId)
                .setAgentId(agentId);
        GetAttachmentSpaceResponse attachmentSpaceWithOptions = client.getAttachmentSpaceWithOptions(getAttachmentSpaceRequest, getAttachmentSpaceHeaders, new RuntimeOptions());
        return attachmentSpaceWithOptions.getBody().getResult().getSpaceId();
    }


    public static String getFileUploadInfoByFile(String accessToken, String spaceId,DingFlowAttachmentFileInfoDto file,String unionId)
            throws TeaException,Exception {
        com.aliyun.dingtalkstorage_1_0.Client client = createStorageClient();
        GetFileUploadInfoHeaders getFileUploadInfoHeaders = new GetFileUploadInfoHeaders();
        getFileUploadInfoHeaders.xAcsDingtalkAccessToken = accessToken;
        GetFileUploadInfoRequest.GetFileUploadInfoRequestOptionPreCheckParam optionPreCheckParam = new GetFileUploadInfoRequest.GetFileUploadInfoRequestOptionPreCheckParam()
                .setSize(file.getFileSize())
                .setParentId("0")
                .setName(file.getFileName());
        GetFileUploadInfoRequest.GetFileUploadInfoRequestOption option = new GetFileUploadInfoRequest.GetFileUploadInfoRequestOption()
                .setStorageDriver("DINGTALK")
                .setPreCheckParam(optionPreCheckParam)
                .setPreferRegion("SHANGHAI");
        GetFileUploadInfoRequest getFileUploadInfoRequest = new GetFileUploadInfoRequest()
                .setUnionId(unionId)
                .setProtocol("HEADER_SIGNATURE")
                .setMultipart(false)
                .setOption(option);
        GetFileUploadInfoResponse fileUploadInfoWithOptions = client.getFileUploadInfoWithOptions(spaceId, getFileUploadInfoRequest, getFileUploadInfoHeaders, new RuntimeOptions());
        //执行上传操作
        GetFileUploadInfoResponseBody body = fileUploadInfoWithOptions.getBody();
        URL url = new URL(body.headerSignatureInfo.getResourceUrls().get(0));
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        OutputStream out = null;
        InputStream is = null;
        int responseCode = 0;
        try {
            Map<String, String> headers = body.headerSignatureInfo.headers;
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            connection.setDoOutput(true);
            connection.setRequestMethod("PUT");
            connection.setUseCaches(false);
            connection.setRequestProperty("Accept-Charset", "UTF-8");
            connection.setRequestProperty("contentType", "UTF-8");
            connection.setRequestProperty("Authorization", body.headerSignatureInfo.headers.get("Authorization"));
            connection.setRequestProperty("x-oss-date", body.headerSignatureInfo.headers.get("x-oss-date"));
            connection.setReadTimeout(10000);
            connection.setConnectTimeout(10000);
            connection.connect();
            out = connection.getOutputStream();
            is = new FileInputStream(file.getFile());
            byte[] b = new byte[1024];
            int temp;
            while ((temp = is.read(b)) != -1) {
                out.write(b, 0, temp);
            }
            responseCode = connection.getResponseCode();
            out.flush();
        }catch (Exception e){
            log.error("UploadInfo exception responseCode {}",responseCode,e);
        }finally {
            if (out != null){
                out.close();
            }
            if (is != null){
                is.close();
            }
            if (connection != null){
                connection.disconnect();
            }
        }
        return body.getUploadKey();
    }

    public static CommitFileResponse getCommitInfoByFile(String accessToken, String apaceId
            , String uploadKey, DingFlowAttachmentFileInfoDto fileInfoDto,String unionId) throws Exception {
        com.aliyun.dingtalkstorage_1_0.Client commitFileClient = createStorageClient();
        com.aliyun.dingtalkstorage_1_0.models.CommitFileHeaders commitFileHeaders = new com.aliyun.dingtalkstorage_1_0.models.CommitFileHeaders();
        commitFileHeaders.xAcsDingtalkAccessToken = accessToken;
        com.aliyun.dingtalkstorage_1_0.models.CommitFileRequest.CommitFileRequestOption commitFileOption = new com.aliyun.dingtalkstorage_1_0.models.CommitFileRequest.CommitFileRequestOption()
                .setSize(fileInfoDto.getFileSize())
                .setConflictStrategy("AUTO_RENAME")
                .setAppProperties(java.util.Arrays.asList());
        com.aliyun.dingtalkstorage_1_0.models.CommitFileRequest commitFileRequest = new com.aliyun.dingtalkstorage_1_0.models.CommitFileRequest()
                .setUnionId(unionId)
                .setUploadKey(uploadKey)
                .setName(fileInfoDto.getFileName())
                .setParentId("0")
                .setOption(commitFileOption);
        CommitFileResponse commitFileResponse = commitFileClient.commitFileWithOptions(apaceId, commitFileRequest, commitFileHeaders, new RuntimeOptions());
        return commitFileResponse;
    }


    public static DingTalkFlowNotifyDto buildNotifyDto(JSONObject bizData,String eventId){
        DingTalkFlowNotifyDto notifyDto = new DingTalkFlowNotifyDto();
        notifyDto.setProcessCode(bizData.getString("processCode"));
        notifyDto.setProcessInstanceId(bizData.getString("processInstanceId"));
        notifyDto.setResult(bizData.getString("result"));
        if (DingTalkConstant.NOTIFY_TYPE_TERMINATE.equals(bizData.getString("type"))){
            notifyDto.setResult("refuse");
        }
        if (StringUtils.isNotBlank(bizData.getString("finishTime"))){
            notifyDto.setFinishTime(Long.parseLong(bizData.getString("finishTime")));
        }
        notifyDto.setEventId(eventId);
        return notifyDto;
    }

}
