package com.dazhong.transportation.vlms.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 * URL编码解码工具类
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
public class UrlUtils {

    private static final String DEFAULT_CHARSET = "UTF-8";

    /**
     * URL解码
     * 
     * @param str 需要解码的字符串
     * @return 解码后的字符串，如果解码失败则返回原字符串
     */
    public static String decode(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        
        try {
            return URLDecoder.decode(str, DEFAULT_CHARSET);
        } catch (UnsupportedEncodingException e) {
            log.warn("URL解码失败，使用原字符串，str: {}", str, e);
            return str;
        } catch (Exception e) {
            log.warn("URL解码异常，使用原字符串，str: {}", str, e);
            return str;
        }
    }

    /**
     * URL编码
     * 
     * @param str 需要编码的字符串
     * @return 编码后的字符串，如果编码失败则返回原字符串
     */
    public static String encode(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        
        try {
            return URLEncoder.encode(str, DEFAULT_CHARSET);
        } catch (UnsupportedEncodingException e) {
            log.warn("URL编码失败，使用原字符串，str: {}", str, e);
            return str;
        } catch (Exception e) {
            log.warn("URL编码异常，使用原字符串，str: {}", str, e);
            return str;
        }
    }

    /**
     * 安全的URL解码，支持多次解码
     * 有些情况下可能会出现多次编码的情况，此方法会尝试多次解码直到无法再解码为止
     * 
     * @param str 需要解码的字符串
     * @return 解码后的字符串
     */
    public static String safeDecodeMultiple(String str) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        
        String decoded = str;
        String previous;
        int maxAttempts = 5; // 最多尝试5次解码，防止无限循环
        int attempts = 0;
        
        do {
            previous = decoded;
            decoded = decode(decoded);
            attempts++;
        } while (!decoded.equals(previous) && attempts < maxAttempts);
        
        return decoded;
    }
} 