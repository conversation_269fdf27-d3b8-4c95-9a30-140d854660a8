package com.dazhong.transportation.vlms.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.dto.response.DingTalkDepartmentTreeResponse;
import com.dazhong.transportation.vlms.dto.response.OrgTreeResponse;
import com.dazhong.transportation.vlms.dto.response.ResourceTreeResponse;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.OrgInfo;
import com.dazhong.transportation.vlms.model.ResourceInfo;
import com.dingtalk.api.response.OapiDepartmentListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipFile;

/**
 * 工具类
 * <AUTHOR>
 * @date 2024-12-26 10:38
 */
@Slf4j
public class CommonUtils {


    /**
     * 获取资源树
     * @param list
     * @return
     */
    public static ResourceTreeResponse getResourceTree(List<ResourceInfo> list){
        // list按照资源ID去重
        list = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getId()))), ArrayList::new));

        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        list.forEach(info -> {
            Map<String, Object> map = BeanUtil.beanToMap(info);
            TreeNode<String> node = new TreeNode<>(map.get("id").toString(), map.get("parentResourceId").toString(), map.get("resourceName").toString(), 0L);
            node.setExtra(map);
            nodeList.add(node);
        });
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setWeightKey("weight");
        treeNodeConfig.setIdKey("resId");
        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(nodeList, "-1", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    tree.putExtra("id", treeNode.getExtra().get("id"));
                    tree.putExtra("resourceCode", treeNode.getExtra().get("resourceCode"));
                    tree.putExtra("resourceName", treeNode.getExtra().get("resourceName"));
                    tree.putExtra("resourceKey", treeNode.getExtra().get("resourceKey"));
                    tree.putExtra("resourceUrl", treeNode.getExtra().get("resourceUrl"));
                    tree.putExtra("resourceIconUrl", treeNode.getExtra().get("resourceIconUrl"));
                    tree.putExtra("resourceType", treeNode.getExtra().get("resourceType"));
                    tree.putExtra("hide", treeNode.getExtra().get("hide"));
                    tree.putExtra("hideTitle", treeNode.getExtra().get("hideTitle"));
                    tree.putExtra("parentResourceId", treeNode.getExtra().get("parentResourceId"));
                    Long sort = Convert.toLong(treeNode.getExtra().get("sort"), 0L);
                    Date createTime = (Date) treeNode.getExtra().get("createTime");
                    if (sort == 0 && treeNode.getExtra().get("createTime") != null) {
                        sort = createTime.getTime();
                    }
                    tree.putExtra("sort", treeNode.getExtra().get("sort"));
                    tree.putExtra("weight", sort);
                });
        if (ObjectUtil.length(treeNodes) != 1) {
            throw new ServiceException("资源树结构错误");
        }
        Tree<String> tree = treeNodes.get(0);
        ResourceTreeResponse treeResponse = BeanUtil.toBean(tree, ResourceTreeResponse.class);
        return treeResponse;
    }


    /**
     * 获取资源树
     * @param list
     * @return
     */
    public static List<OrgTreeResponse> getOrgTree(List<OrgInfo> list){
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        list.forEach(info -> {
            Map<String, Object> map = BeanUtil.beanToMap(info);
            TreeNode<String> node = new TreeNode<>(map.get("id").toString(), map.get("parentId").toString(), map.get("companyName").toString(), 0L);
            node.setExtra(map);
            nodeList.add(node);
        });
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setWeightKey("weight");
        treeNodeConfig.setIdKey("id");
        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(nodeList, "-1", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    tree.putExtra("companyName", treeNode.getExtra().get("companyName"));
                    tree.putExtra("checkedState", treeNode.getExtra().get("checkedState"));
                    tree.putExtra("disabledState", treeNode.getExtra().get("isDeleted").equals(1) ? 1 : 2);
                    Long sort = Convert.toLong(treeNode.getExtra().get("position"), 0L);
                    Date createTime = (Date) treeNode.getExtra().get("createTime");
                    if (sort == 0 && treeNode.getExtra().get("createTime") != null) {
                        sort = createTime.getTime();
                    }
                    tree.putExtra("sort", treeNode.getExtra().get("position"));
                    tree.putExtra("weight", sort);
                });
        if (ObjectUtil.length(treeNodes) == 0) {
            throw new ServiceException("组织机构结构错误");
        }
        List<OrgTreeResponse> treeListResponse = BeanUtil.copyToList(treeNodes, OrgTreeResponse.class);// JSONUtil.toBean(JSONUtil.toJsonStr(tree), OrgTreeResponse.class);
        return treeListResponse;
    }


    /**
     * 获取资源树
     * @param list
     * @return
     */
    public static List<DingTalkDepartmentTreeResponse> getDepartmentTree(List<OapiDepartmentListResponse.Department> list){
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        list.forEach(info -> {
            Map<String, Object> map = BeanUtil.beanToMap(info);
            TreeNode<String> node = new TreeNode<>(map.get("id").toString(), map.get("parentid")==null?"-1":map.get("parentid").toString(), map.get("name").toString(), 0L);
            node.setExtra(map);
            nodeList.add(node);
        });
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setWeightKey("weight");
        treeNodeConfig.setIdKey("id");
        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(nodeList, "-1", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    tree.putExtra("name", treeNode.getExtra().get("name"));
                    Long sort = Convert.toLong(treeNode.getExtra().get("id"), 0L);
                    tree.putExtra("sort", treeNode.getExtra().get("id"));
                    tree.putExtra("weight", sort);
                });
        if (ObjectUtil.length(treeNodes) == 0) {
            throw new ServiceException("钉钉机构结构错误");
        }
        List<DingTalkDepartmentTreeResponse> treeListResponse = BeanUtil.copyToList(treeNodes, DingTalkDepartmentTreeResponse.class);// JSONUtil.toBean(JSONUtil.toJsonStr(tree), OrgTreeResponse.class);
        return treeListResponse;
    }

    /**
     * 递归获取组织机构树
     * @param orgInfos
     * @param code
     * @return
     */
    public static List<OrgTreeResponse> buildOrgTree(List<OrgInfo> orgInfos, String code) {
        List<OrgTreeResponse> orgTreeList = new ArrayList<>();
        for (OrgInfo orgInfo : orgInfos) {
            if (orgInfo.getCompanyCode().length() == code.length() + 2
                    && StringUtils.startsWith(orgInfo.getCompanyCode(), code)) {
                OrgTreeResponse treeResponse = new OrgTreeResponse();
                treeResponse.setId(orgInfo.getId());
                treeResponse.setCompanyName(orgInfo.getCompanyName());
                orgTreeList.add(treeResponse);
                treeResponse.setChildren(buildOrgTree(orgInfos, orgInfo.getCompanyCode()));
            }
        }
        // 当不再有子节点时，为递归的结束点
        if (orgTreeList.size() == 0) {
            return null;
        }
        return orgTreeList;
    }

    /**
     * 创建ZipFile
     *
     * @param filePath
     * @return
     */
    public static ZipFile createZipFile(String filePath) {
        ZipFile zf = null;
        try {
            zf = new ZipFile(filePath, Charset.forName("GBK"));
        } catch (IOException e) {
            e.printStackTrace();
            log.info("转换ZipFile异常，GBK转换：{}", e.getMessage());
            try {
                zf = new ZipFile(filePath, StandardCharsets.UTF_8);
                log.info("UTF-8转换ZipFile成功");
            } catch (IOException ex) {
                ex.printStackTrace();
                log.info("转换ZipFile异常，UTF-8转换：{}", e.getMessage());
            }
        }
        return zf;
    }

    /**
     * 删除文件
     * @param filePath
     */
    public static void deleteIfExists(String filePath){
        try {
            String path = Global.instance.mfsRootPath + "/" + filePath;
            Files.deleteIfExists(Paths.get(path));
        } catch (IOException e) {
            log.error("删除文件失败：{}", e.getMessage());
        }
    }
}
