package com.dazhong.transportation.vlms.utils;

import com.dazhong.transportation.vlms.exception.ServiceException;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;


/**
 * 参数验证工具类
 *
 * <AUTHOR>
 * @date 2024-12-23 13:22
 */
public class ValidationUtils {
    /**
     * 使用hibernate的注解来进行验证
     */
    private static Validator validator = Validation.byProvider(HibernateValidator.class).configure().failFast(true).buildValidatorFactory().getValidator();

    /**
     * 判断参数是否为空
     *
     * @param obj
     * @param <T>
     */
    public static <T> void validate(T obj) {
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(obj);
        // 抛出检验异常
        if (constraintViolations.size() > 0) {
            throw new ServiceException(String.format("参数校验失败:%s", constraintViolations.iterator().next().getMessage()));
        }
    }
}
