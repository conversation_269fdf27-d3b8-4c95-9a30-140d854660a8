package com.dazhong.transportation.vlms.utils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


public abstract class RSAUtils {
    public static final String KEY_ALGORITHM = "RSA";
    public static final String SIGNATURE_ALGORITHM = "MD5withRSA";
    private static final int BLOCK_SIZE = 245;
    private static final int OUTPUT_BLOCK_SIZE = 256;
    private static final String PUBLIC_KEY = "RSAPublicKey";
    private static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * decode <br>
     * decode by private key
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPrivateKey(byte[] data, String key)
            throws Exception {
        // decrypt   
        byte[] keyBytes = decryptBASE64(key);

        // get private key
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);


        Cipher rsaCipher = Cipher.getInstance(keyFactory.getAlgorithm());
        rsaCipher.init(Cipher.DECRYPT_MODE, privateKey);
        int blocks = data.length / OUTPUT_BLOCK_SIZE;
        ByteArrayOutputStream decodedStream = new ByteArrayOutputStream(data.length);
        for (int i = 0; i < blocks; i++) {
            decodedStream.write(rsaCipher.doFinal(data, i * OUTPUT_BLOCK_SIZE, OUTPUT_BLOCK_SIZE));
        }
        return decodedStream.toByteArray();
    }

    /**
     * decode <br>
     * decode by public key
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] decryptByPublicKey(byte[] data, String key)
            throws Exception {
        // decode 
        byte[] keyBytes = decryptBASE64(key);

        // get public key
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);

        Cipher rsaCipher = Cipher.getInstance(keyFactory.getAlgorithm());
        rsaCipher.init(Cipher.DECRYPT_MODE, publicKey);
        int blocks = data.length / OUTPUT_BLOCK_SIZE;
        ByteArrayOutputStream decodedStream = new ByteArrayOutputStream(data.length);
        for (int i = 0; i < blocks; i++) {
            decodedStream.write(rsaCipher.doFinal(data, i * OUTPUT_BLOCK_SIZE, OUTPUT_BLOCK_SIZE));
        }
        return decodedStream.toByteArray();
    }

    /**
     * encode
     * encode data<br>
     * encode data by public key
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPublicKey(byte[] data, String key)
            throws Exception {
        // encode by public key
        byte[] keyBytes = decryptBASE64(key);

        // get public key
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicKey = keyFactory.generatePublic(x509KeySpec);


        Cipher rsaCipher = Cipher.getInstance(keyFactory.getAlgorithm());
        rsaCipher.init(Cipher.ENCRYPT_MODE, publicKey);
        int blocks = data.length / BLOCK_SIZE;
        int lastBlockSize = data.length % BLOCK_SIZE;
        byte[] encryptedData = new byte[(lastBlockSize == 0 ? blocks : blocks + 1) * OUTPUT_BLOCK_SIZE];
        for (int i = 0; i < blocks; i++) {
            rsaCipher.doFinal(data, i * BLOCK_SIZE, BLOCK_SIZE, encryptedData, i * OUTPUT_BLOCK_SIZE);
        }
        if (lastBlockSize != 0) {
            rsaCipher.doFinal(data, blocks * BLOCK_SIZE, lastBlockSize, encryptedData, blocks * OUTPUT_BLOCK_SIZE);
        }
        return encryptedData;
    }

    /**
     * encode <br>
     * encode by private key
     *
     * @param data
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPrivateKey(byte[] data, String key)
            throws Exception {
        // encode data  
        byte[] keyBytes = decryptBASE64(key);

        // get private key
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);

        Cipher rsaCipher = Cipher.getInstance(keyFactory.getAlgorithm());
        rsaCipher.init(Cipher.ENCRYPT_MODE, privateKey);
        int blocks = data.length / BLOCK_SIZE;
        int lastBlockSize = data.length % BLOCK_SIZE;
        byte[] encryptedData = new byte[(lastBlockSize == 0 ? blocks : blocks + 1) * OUTPUT_BLOCK_SIZE];
        for (int i = 0; i < blocks; i++) {
            rsaCipher.doFinal(data, i * BLOCK_SIZE, BLOCK_SIZE, encryptedData, i * OUTPUT_BLOCK_SIZE);
        }
        if (lastBlockSize != 0) {
            rsaCipher.doFinal(data, blocks * BLOCK_SIZE, lastBlockSize, encryptedData, blocks * OUTPUT_BLOCK_SIZE);
        }
        return encryptedData;
        // encode data by private key
//        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
//        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
//
//        return cipher.doFinal(data);
    }


    /**
     * initial private key
     *
     * @return
     * @throws Exception
     */
    public static Map<String, Object> initKey() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator
                .getInstance(KEY_ALGORITHM);
        keyPairGen.initialize(2048);

        KeyPair keyPair = keyPairGen.generateKeyPair();

        // public key
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();

        // private key
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

        Map<String, Object> keyMap = new HashMap<String, Object>(2);

        keyMap.put(PUBLIC_KEY, publicKey);
        keyMap.put(PRIVATE_KEY, privateKey);
        return keyMap;
    }

    /**
     * transfer binary system to hexadecimal
     *
     * @param buf
     * @return
     */
    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * transfer hexadecimal to binary
     *
     * @param hexStr
     * @return
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1)
            return null;
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }


    public static byte[] decryptBASE64(String encodedString) {
        // 使用 Base64 解码器解码字符串
        byte[] decodedBytes = Base64.getDecoder().decode(encodedString);
        return decodedBytes;
    }
}  