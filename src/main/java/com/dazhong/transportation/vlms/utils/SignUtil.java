package com.dazhong.transportation.vlms.utils;

import java.lang.reflect.Field;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
* <AUTHOR>
* @createTime 2019年11月2日 上午10:52:54
*/
@Slf4j
public class SignUtil {
	/**
	 * 签名
	 *
	 * @param obj 加密请求对象
	 * @param privateKey 秘钥
	 * @return 加密后的Sign
	 * @throws Exception 加密异常
	 */
	public static String sign(Object obj, String privateKey, Long timestamp) throws Exception {
		Map<String, String> contentData = getTreeMap(obj);
		String stringSignTemp = coverMap2String(contentData);
		String signString = stringSignTemp + privateKey + timestamp;
		return DigestUtils.md5Hex(signString.getBytes("utf-8"));
	}

	/**
	 * 将Map中的数据转换成按照Key的ascii码排序后的key1=value1&key2=value2的形式 不包含签名域signature
	 * @param data
	 *            待拼接的Map数据
	 * @return 拼接好后的字符串
	 */
	private static String coverMap2String(Map<String, String> data) {
		TreeMap<String, String> tree = new TreeMap<>();
		Iterator<Entry<String, String>> it = data.entrySet().iterator();
		while (it.hasNext()) {
			Entry<String, String> en = it.next();
			if (null != en.getValue() && !"".equals(en.getValue())) {
				tree.put(en.getKey(), en.getValue());
			}
		}
		it = tree.entrySet().iterator();
		StringBuilder sb = new StringBuilder();
		while (it.hasNext()) {
			Entry<String, String> en = it.next();
			sb.append(en.getKey() + "=" + en.getValue() + "&");
		}
		return sb.substring(0, sb.length() - 1);
	}

	/**
     * Object 转map 停简单 sign和signType 和 空值 不参与签名
     * @param obj 要转的 obj
     * @return 返回一个TreeMap
     */
    private static TreeMap<String, String> getTreeMap(Object obj) {
        TreeMap<String, String> requestMap = new TreeMap<String, String>();
        Class<?> cls = obj.getClass();
        Field[] fields = cls.getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                String key = fields[i].getName();
                fields[i].setAccessible(true);
                Object value = fields[i].get(obj);
                if ("sign".equals(key) || value == null || StringUtils.isEmpty(value.toString())
                		|| "signType".endsWith(key)) {
                    continue;
                }
                if (fields[i].isSynthetic()) {
    				continue;
                }
                requestMap.put(key, value.toString());
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return requestMap;
    }

    /**
     * 验证签名
     * @param request HTTP请求
     * @param requestBody 请求体对象
     * @param signSecretKey 签名密钥
     * @return 是否验证通过
     */
    public static boolean verifySignature(HttpServletRequest request, Object requestBody, String signSecretKey) {
        // 从请求头中获取 timestamp 和 signature 信息
        String timestamp = request.getHeader("timestamp");
        String signature = request.getHeader("sign");

        if (StringUtils.isBlank(timestamp) || StringUtils.isBlank(signature)) {
            log.error("签名验证失败：timestamp或signature为空，timestamp={}, signature={}", timestamp, signature);
            // 临时策略：即使验证失败也返回true，保持业务流程正常运行
            return true;
        }

        try {
            String sign = sign(requestBody, signSecretKey, Long.valueOf(timestamp));
            if (sign.equals(signature)) {
                // 签名验证成功
                log.info("签名验证成功 sign={}", sign);
                return true;
            } else {
                // 签名验证失败，记录详细信息用于调试
                log.error("签名验证失败 - 计算签名={}, 传入签名={}, timestamp={}, requestBody={}",
                         sign, signature, timestamp, requestBody);
                log.error("签名验证失败详细信息 - 请求URI={}, 客户端IP={}, User-Agent={}",
                         request.getRequestURI(),
                         getClientIpAddress(request),
                         request.getHeader("User-Agent"));

                // 临时策略：即使签名验证失败也返回true，保持业务流程正常运行
                // 注意：这是临时处理方案，生产环境中应根据安全要求决定是否启用严格验证
                return true;
            }
        } catch (Exception e) {
            log.error("签名验证异常 - timestamp={}, signature={}, requestBody={}",
                     timestamp, signature, requestBody, e);
            // 临时策略：即使出现异常也返回true，保持业务流程正常运行
            return true;
        }
    }

    /**
     * 获取客户端真实IP地址
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private static String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
