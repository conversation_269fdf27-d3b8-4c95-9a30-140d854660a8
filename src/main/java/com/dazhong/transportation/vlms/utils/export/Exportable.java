package com.dazhong.transportation.vlms.utils.export;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;

@FunctionalInterface
public interface Exportable {

    /**
     * 导出数据
     * @param writeSheet 导出Sheet对象
     * @param excelWriter 导出Excel对象
     * @return true:有结果  false:无满足条件数据
     */
    public abstract boolean export(ExcelWriter excelWriter, WriteSheet writeSheet);

}
