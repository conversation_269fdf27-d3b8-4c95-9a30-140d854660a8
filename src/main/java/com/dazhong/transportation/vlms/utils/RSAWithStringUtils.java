package com.dazhong.transportation.vlms.utils;


import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.constant.BizConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RSAWithStringUtils {


    /**
	 * 私钥加密
     * @param result
     * @param privateKey
     * @return source string
     */
    public static String encodeDataByPrivateKey(String result, String privateKey) {
        String source = "";
        try {
            byte[] encodedData = RSAUtils.encryptByPrivateKey(result.getBytes(BizConstant.DEFAULT_CHARSET_TYPE), privateKey);
            source = RSAUtils.parseByte2HexStr(encodedData);
        } catch (Exception e) {
			log.error("私钥加密失败 {}",source,e);
        }
        return source;
    }

    /**
	 * 公钥加密
     * @param result
     * @param publicKey
     * @return source string
     */
    public static String encodeDataByPublicKey(String result, String publicKey) {
        String source = "";
        try {
            byte[] encodedData = RSAUtils.encryptByPublicKey(result.getBytes(BizConstant.DEFAULT_CHARSET_TYPE), publicKey);
            source = RSAUtils.parseByte2HexStr(encodedData);
        } catch (Exception e) {
			log.error("公钥加密失败 {}",source,e);
        }
        return source;
    }

	/**
	 * 公钥解密
	 *
	 * @param source
	 * @param publicKey
	 * @return String with Hex
	 */
	public static String decodeDataByPublicKey(String source, String publicKey) {
		byte[] byteCode = RSAUtils.parseHexStr2Byte(source);
		byte[] decodedData = null;
		String result = "";
		try {
			decodedData = RSAUtils.decryptByPublicKey(byteCode, publicKey);
			result = new String(decodedData, BizConstant.DEFAULT_CHARSET_TYPE);
		} catch (Exception e) {
			log.error("公钥解密失败 {}",source,e);
		}
		return result;
	}

    /**
	 * 私钥解密
     * @param source
     * @param privateKey
     * @return String with Hex
     */
    public static String decodeDataByPrivateKey(String source, String privateKey) {
        byte[] byteCode = RSAUtils.parseHexStr2Byte(source);
        byte[] decodedData = null;
        String result = "";
        try {
            decodedData = RSAUtils.decryptByPrivateKey(byteCode, privateKey);
            result = new String(decodedData, BizConstant.DEFAULT_CHARSET_TYPE);
        } catch (Exception e) {
			log.error("私钥解密失败 {}",source,e);
        }
        return result;
    }

    public static void main(String[] a) {
//	    String s = RSAWithStringUtils.encodeDataByPublicKey("{\"accessToken\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xkN9cvkm4zRA4-TLzoOaUF4Fr1g3pODZxMayS7dcxgk\",\"refreshToken\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.s1qFkLjYfguapQlf3x1bVyQ2fWNnbSB0ZpubOVD1RDo\",\"refreshTokenExpiration\":14400,\"state\":\"34567\",\"tokenType\":\"bearer\",\"accountId\":\"****************\",\"roles\":\"\",\"expiration\":*************}");
//       System.out.println(s);
//       String e = RSAWithStringUtils.decodeDataByPrivateKey(s);
//       System.out.println(e);
//		String pkey = "";
//		String prket="";
//		try {
//			Map<String,Object> map = RSAUtils.initKey();
//			pkey = RSAUtils.getPublicKey(map);
//			prket =  RSAUtils.getPrivateKey(map);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		System.out.println(pkey);
//		System.out.println();
//		System.out.println(prket);
//		System.out.println();
//		String s2 = RSAWithStringUtils.encodeDataByPublicKey("{\"accessToken\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xkN9cvkm4zRA4-TLzoOaUF4Fr1g3pODZxMayS7dcxgk\",\"refreshToken\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.s1qFkLjYfguapQlf3x1bVyQ2fWNnbSB0ZpubOVD1RDo\",\"refreshTokenExpiration\":14400,\"state\":\"34567\",\"tokenType\":\"bearer\",\"accountId\":\"****************\",\"roles\":\"\",\"expiration\":*************}");
//		System.out.println(s2);
//
//
//		String e2 = RSAWithStringUtils.decodeDataByPrivateKey(s2);
//		System.out.println(e2);
//
//
//        String s1 = RSAWithStringUtils.encodeDataByPrivateKey("{\"accessToken\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xkN9cvkm4zRA4-TLzoOaUF4Fr1g3pODZxMayS7dcxgk\",\"refreshToken\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.s1qFkLjYfguapQlf3x1bVyQ2fWNnbSB0ZpubOVD1RDo\",\"refreshTokenExpiration\":14400,\"state\":\"34567\",\"tokenType\":\"bearer\",\"accountId\":\"****************\",\"roles\":\"\",\"expiration\":*************}");
//        System.out.println(s1);
//        String e1 = RSAWithStringUtils.decodeDataByPublicKey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fMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCn0LGJLxyh+panmiGklQO0RjjWrMGf5sBH8WSCbI6RsQ3hQ3HAuyGx4BcTnh9I15nymoYKfCZQHBqMGjef92u3wbU7RVwFrex3lubzOI2fMXJ0us4hmrv5GVdEL5qnu90DAdb/a9LKO8MCZUw2QqfGDl/YDSLtk4Wo21Kp66x1awIDAQAB");
//        System.out.println(e1);
        String k = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDaw8WXVOGKXMHJfwQ2L+cauazRiMbMV6jw4G98EG3/LE8HntKOpaVyYNwRe0Ki3J7wrE6Npe8KdMom4FSxwGQZYB7cACAh5h2x/VvfiAFoHhzmPvSdSXqz5fbsw+SPaBjmkzsAFrspvuoeU0fI1K9ZaOoGOfsuJXcNMb41bSmQLtDUfO+zN9tXbzRxC7ILmjTY3UODNEncJPtUymjcDtG2oEWINs0ssOKkp4poib2JpNHqFE/2qMkc37lw68mZ5yTwVSe+GPK7l7znSRJ7R1OnbD79+C78UFpuXzFf07NyjHAE8n1olWKmzneabzl453Ba5+Bw6wA25Xway0WWHpTVAgMBAAECggEARVuQR7xnjWB/KA0XHr7tPHHlssD0llCshUIC1oi29xHrNHVMOGGYJL1EyF4V8GCWG6Ple89CDESe2tiAwaJHmiv5XLLFfTnh7E5xVFtgZsw1SWJoV322Y1bjhIlO65CLZH8FU6/hQwj+XQL2XVvhBC3ZFIRJeIX0SsCcl3+2j7lR8vxsmDCJQFrPl+ZwrnhqkciV+BVy/Dg5nBEBB0k6bdgUTs0qa98lVpxvzozfKR6tTdBKnQar87/x2iJEbrDGwwq7gFt/SztBF6SOsZn9lefZVCzvvtUAPK0/Zmf1ngo+AChspMhVVglg0Qaks/w/pL5dKiS6rlo7W3rHQ3MeeQKBgQDw41YlvmFtL5AOwDZZWUFiEn16Iow5znQgnvM5AAkS0lD1+TVbXTQzEAt6KYfdG7fOmKFYifvHj31wZwKbXrXhisna+JuUPboYuEbUY8dDEvfcS6nDovzgcmTKaeG8uSvEGQQbxb8GdbBGeKXhs87BFJPvT/W498lhsQy44Q0PUwKBgQDofSKORGpialqAwbysV9mcgxBraVZjRZAPBUCdPUEik/Kh5kR6mD8K7em6aGWHvrPW7tB5vKVnibz+0VUnBix9VLhjFUqDceIfQbD4jTqJX6FdmG32G5cOCLlfWczafCuE+7JLsLkb+MEPtpQFqCfX/qukVvk19aRyqFtI5CdONwKBgQDPRz+UwMpC0r+KAdmCbqw1QKXaF6pD/6OrgjrWHHKyUGk8DxHbqUrGeZL2ro/rMfOrAWksr6cIpC8TLoBuw0OMbQmes3fkSm084iyIi8clr0e6txzAbBpe4/BYwF1vMIM5fTEs2K60V0a0jRJoMXnCaPuW3F4bqJQPzPKej4DWYwKBgQDb1cehIww9dzeQ3KEdjgxVNJGIH5jyUcrAiUTp6gv3OFKLyIMkMWYbXZBPFXw7+ZVwPW2gT0NQPiwZ/7M3LV1QPZYFCxLkbAFXcPR4coLRdz3MlOAVgU06lgFDKlTMm/FC1AVqEPdYfcWAqDIbjz/tvABta1hHL7mKOavnpuWLzQKBgEz6yn9lEMHSoS8Q77xv+JPf++CgVQw8+hFlx+/HR1d6JcNeNa7mnbvrFHl7xZgJQUnJ9Acww62Mnu5XMgFktLQjMo7BXgjH6UfuzfMI3KIysFd37YcQdSccZN94imSbQ6ctm85rmN7VpXoYt0IVTK7mcGzOc04vQHH0iVgO+QAW";
        String p = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2sPFl1ThilzByX8ENi/nGrms0YjGzFeo8OBvfBBt/yxPB57SjqWlcmDcEXtCotye8KxOjaXvCnTKJuBUscBkGWAe3AAgIeYdsf1b34gBaB4c5j70nUl6s+X27MPkj2gY5pM7ABa7Kb7qHlNHyNSvWWjqBjn7LiV3DTG+NW0pkC7Q1HzvszfbV280cQuyC5o02N1DgzRJ3CT7VMpo3A7RtqBFiDbNLLDipKeKaIm9iaTR6hRP9qjJHN+5cOvJmeck8FUnvhjyu5e850kSe0dTp2w+/fgu/FBabl8xX9OzcoxwBPJ9aJVips53mm85eOdwWufgcOsANuV8GstFlh6U1QIDAQAB/nc/E0AAvbFCOVEbBNgC/uZES9rQ2n4PGF7uvYiJBmDTskBh2Lv0jZXPFbhlHNCgzARTb8sXfsvYctpWfoaFeB8X8/Toin4cN6nWiplR/G0gEyQCNTHNjUXgHdhIgbv+/dThBMohnDuttw1erlfdfH3dTqHxrbkx2FkSC54sZHcHSbxO8E6iFdcXpduKfusC4EN8rQPBWEPOBeFdyKfU1IKhJM4WL9TLZq4ftFjc9PJpVt84GBmlhRyN81/AuRy7Ciq4dAs8G426FfLjgRs0QIDAQAB";

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("status", true);
        resultMap.put("message", "同步成功");

        String value = encodeDataByPublicKey(
                JSONUtil.toJsonStr(resultMap), p);
//        System.out.println(value);
//        System.out.println("解密0" + decodeDataByPrivateKey(value, k));
        System.out.println("解密1" + decodeDataByPublicKey("38AAB9897026634F82539BA17A621CF807BB9AF1F2CCC74F73D1EA74AD94F754C810E1AAFA36A521FC7717CA95B92FC54D92B8BE2083B840FDF962A9A4F8F4BCD5AB250DE4F26A756360D8F45A8476AB847FD1AF82B059963F1D35A72F1A0D6567105D9B8F02DB44620429312576552996AC92DF21A90D67B8B7FC076A674578491F8A032095D1C304FCE27C235BB35475F7C696ACDED8B1B793B24705FD35938C8E6031ACD91519DCF1AFCC4B1B47526E63D0F8F884DB105DBA5046301B280CCE5825027D3540AB2CA0C6504DF964EF705A98ED55FEDFF8F50F0ED3B7A3D099F17C9EF6DB382C795440B3437C8ED65D0450A5FE30B4F4EAC036357A662FE8E8", p));
//        System.out.println("解密2" + decodeDataByPrivateKey("871A5655D7001F0290F22C30CC7AC62F2B8E0510FFD3955CF3830C235DFDC476B73A35C7C918759CF0F47A5D2BE7A212F4F4F068905FB632A4A40F33783A4DCEB0F8D637AC9F8C3A9D24E83EFC7DB4D0006B3D32085C98BC98F9D1937DDFA6C2BB70308E99EC7A99E753F373EB936EFCA916109CC66023FF0B4C787CCDBAE08EEE773E9AD7BBE61872615E3A1748ECEF109A7BFEFB0962C30EFF56AA049F543DE753576E28E3DD3A068641AD4CF45935A9FE73320128F215A577BD9E5716652D97EAB05C2A718AA394C394C403CB901CD605D9032FF13BEA67B9BF7CDC5889CCBBA3653CFEFE470D95E7727183E9317DC3E37720B910D8E33199CD67D024E7AE", k));
    }
}
