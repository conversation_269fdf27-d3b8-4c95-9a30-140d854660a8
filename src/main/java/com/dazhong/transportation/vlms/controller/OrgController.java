package com.dazhong.transportation.vlms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveManagerRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateOrgInfoRequest;
import com.dazhong.transportation.vlms.dto.response.DataDictResponse;
import com.dazhong.transportation.vlms.dto.response.OrgDetailsResponse;
import com.dazhong.transportation.vlms.dto.response.OrgListResponse;
import com.dazhong.transportation.vlms.dto.response.OrgTreeResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 组织架构接口
 *
 * <AUTHOR>
 * @date 2024-12-23 09:41
 */
@Slf4j
@Api(value = "组织架构接口", tags = "组织架构")
@RestController
@RequestMapping(value = "api")
public class OrgController {

    @Autowired
    private IOrgService orgService;

    /**
     * 查询全部组织机构树
     * @return
     */
    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询全部组织机构树", httpMethod = "POST")
    @RequestMapping(value = "queryAllOrgTree", method = RequestMethod.POST)
    public ResultResponse<OrgTreeResponse> queryAllOrgTree() {
        return orgService.queryAllOrgTree();
    }

    /**
     * 查询启用状态组织机构树
     * @return
     */
    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询启用状态组织机构树", httpMethod = "GET")
    @RequestMapping(value = "queryEnableStatusOrgTree", method = RequestMethod.GET)
    public ResultResponse<OrgTreeResponse> queryEnableStatusOrgTree() {
        return orgService.queryEnableStatusOrgTree();
    }

    /**
     * 查询当前登录用户组织架构列表
     * @param tokenUserInfo
     * @return
     */
    @ApiIgnore
    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询当前登录用户组织架构列表", httpMethod = "GET")
    @RequestMapping(value = "queryCurrentUserOrgList", method = RequestMethod.GET)
    public ResultResponse<OrgListResponse> queryCurrentUserOrgList(@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        List<OrgListResponse> orgList = orgService.queryUserOrgList(tokenUserInfo.getUserId());
        return ResultResponse.success(orgList);
    }

    /**
     * 查询用户组织机构列表
     * @return
     */
    @ApiOperation(value = "查询用户组织机构列表", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryUserOrgList/{userId}", method = RequestMethod.GET)
    public ResultResponse<OrgListResponse> queryUserOrgList(@ApiParam(value = "用户id 必传", required = true) @PathVariable Long userId) {
        List<OrgListResponse> orgList = orgService.queryUserOrgList(userId);
        return ResultResponse.success(orgList);
    }

    /**
     * 查询当前登录用户组织架构树
     * @param tokenUserInfo
     * @return
     */
    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询当前登录用户组织架构树", httpMethod = "GET")
    @RequestMapping(value = "queryCurrentUserOrgTree", method = RequestMethod.GET)
    public ResultResponse<OrgTreeResponse> queryCurrentUserOrgTree(@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        return orgService.queryUserOrgTree(tokenUserInfo.getUserId());
    }

    /**
     * 查询用户组织架构树
     * @return
     */
    @ApiOperation(value = "查询用户组织架构树", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryUserOrgTree/{userId}", method = RequestMethod.GET)
    public ResultResponse<OrgListResponse> queryUserOrgTree(@ApiParam(value = "用户id 必传", required = true) @PathVariable Long userId) {
        return orgService.queryUserOrgTree(userId);
    }


    /**
     * 查询用户关联公司列表
     * @return
     */
    @ApiOperation(value = "查询用户关联公司列表", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryUserOwnerList/{userId}", method = RequestMethod.GET)
    public ResultResponse<DataDictResponse<Integer>> queryUserOwnerList(@ApiParam(value = "用户id 必传", required = true) @PathVariable Long userId) {
        DataDictResponse<Integer> list = orgService.queryUserOwnerList(userId);
        return ResultResponse.success(list);
    }

    /**
     * 查询当前登录用户关联公司列表
     * @param tokenUserInfo
     * @return
     */
    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询当前登录用户关联公司列表", httpMethod = "GET")
    @RequestMapping(value = "queryCurrentUserOwnerList", method = RequestMethod.GET)
    public ResultResponse<DataDictResponse<Integer>> queryCurrentUserOwnerList(@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        DataDictResponse<Integer> list = orgService.queryUserOwnerList(tokenUserInfo.getUserId());
        return ResultResponse.success(list);
    }

    /**
     * 查询组织机构详情
     * @return
     */
    @ApiOperation(value = "查询组织机构详情", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryOrgDetails/{id}", method = RequestMethod.GET)
    public ResultResponse<OrgDetailsResponse> queryOrgDetails(@ApiParam(value = "id 必传", required = true) @PathVariable Long id) {
        return orgService.queryOrgDetails(id);
    }

    /**
     * 更新组织架构
     * @return
     */
    @ApiOperation(value = "更新组织架构", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "updateOrgInfo", method = RequestMethod.POST)
    public ResultResponse updateOrgInfo(@RequestBody UpdateOrgInfoRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return orgService.updateOrgInfo(request,tokenUserInfo);
    }

    /**
     * 保存管理者信息
     * @return
     */
    @ApiIgnore
    @ApiOperation(value = "保存管理者信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "saveOrgGeneralManager", method = RequestMethod.POST)
    public ResultResponse saveManagerInfo(@RequestBody SaveManagerRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return orgService.saveManagerInfo(request, tokenUserInfo);
    }

    /**
     * 导入组织架构
     * @return
     */
    @LoginRequiredAnnotation(required = false)
    @RequestMapping(value = "importOrgInfo", method = RequestMethod.POST)
    public ResultResponse importOrgInfo() {
        return orgService.importOrgInfo();
    }

    /**
     * 同步组织架构信息
     * @return
     */
    @RequestMapping(value = "syncOrganization", method = RequestMethod.GET)
    public ResultResponse syncOrganization() {
        orgService.syncOrganization();
        return ResultResponse.success();
    }
}
