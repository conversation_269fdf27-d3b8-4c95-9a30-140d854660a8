package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.request.SaveResourceRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateResourceRequest;
import com.dazhong.transportation.vlms.dto.response.ResourceTreeResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.IResourceService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 系统资源接口
 *
 * <AUTHOR>
 * @date 2024-12-23 11:03
 */
@Slf4j
@Api(value = "系统资源接口", tags = "系统资源")
@RestController
@RequestMapping(value = "api")
public class ResourceController {

    @Resource
    private IResourceService resourceService;

    @ApiOperation(value = "新增资源信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "addResourceInfo", method = RequestMethod.POST)
    public ResultResponse addResourceInfo(@RequestBody SaveResourceRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return resourceService.addResource(request,tokenUserInfo);
    }

    /**
     * 删除权限资源
     * @param request
     * @return
     */
    @ApiOperation(value = "删除资源信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "deleteAuthResource", method = RequestMethod.POST)
    public ResultResponse deleteAuthResource(@RequestBody BaseIdRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return resourceService.deleteResource(request.getId(),tokenUserInfo);
    }

    /**
     * 修改资源信息
     * @param request
     * @return
     */
    @ApiOperation(value = "修改资源信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "updateResourceInfo", method = RequestMethod.POST)
    public ResultResponse updateResourceInfo(@RequestBody UpdateResourceRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return resourceService.updateResource(request,tokenUserInfo);
    }

    /**
     * 查询用户权限资源树
     * @return
     */
    @ApiOperation(value = "查询用户权限资源树", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryUserResourceTree", method = RequestMethod.GET)
    public ResultResponse<ResourceTreeResponse> queryUserResourceTree(@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        return resourceService.queryUserResourceTree(tokenUserInfo);
    }
}
