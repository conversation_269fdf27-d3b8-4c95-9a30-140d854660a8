package com.dazhong.transportation.vlms.controller;

import cn.hutool.core.util.IdUtil;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.dto.response.DownloadFileInfoResponse;
import com.dazhong.transportation.vlms.dto.response.UploadFileResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.VehicleAttachmentTypeEnum;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.IFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Api(value = "文件相关接口", tags = "文件相关接口")
@Slf4j
@RestController
@LoginRequiredAnnotation(required = true)
@RequestMapping(value = "api/file")
public class FileController {

    @Autowired
    private IFileService fileService;

    @ApiOperation(value = "查询下载文件列表")
    @PostMapping("/searchDownloadFileList")
    public ResultResponse<PageResponse<DownloadFileInfoResponse>> searchDownloadFileList(@RequestBody @Validated PageRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        return ResultResponse.success(fileService.searchDownloadFileList(request, tokenUserInfo));
    }

    @ApiOperation(value = "车辆附件上传", httpMethod = "POST")
    @RequestMapping(value = "/uploadVehicleAttachment", method = RequestMethod.POST)
    public ResultResponse<UploadFileResponse> uploadFile(@RequestParam("files") List<MultipartFile> files,@RequestParam Integer fileType) {
        // 3-行驶证文件 4-产证文件 5-合格证文件 6-发票文件 7-购置税文件 8-营运证
        VehicleAttachmentTypeEnum vehicleAttachmentTypeEnum = VehicleAttachmentTypeEnum.getVehicleAttachmentType(fileType);
        if (vehicleAttachmentTypeEnum == null) {
            throw new ServiceException("上传文件枚举类型错误");
        }
        if (files.isEmpty()) {
            return ResultResponse.businessFailed("文件不能为空");
        }
        try {
            List<UploadFileResponse> responseList = new ArrayList<>();
            // 确保文件存储目录存在
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    continue;
                }
                // 只能上传的文件类型
                String fileName = file.getOriginalFilename();
                if ((!StringUtils.endsWithIgnoreCase(fileName, ".pdf")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".jpeg")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".png")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".jpg"))) {
                    return ResultResponse.businessFailed("车辆附件只能上传PDF或者图片文件");
                }
                String midPath = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now()) + "/" + vehicleAttachmentTypeEnum.getFilePath();
                String fileDirPath = Global.instance.mfsRootPath + "/" + midPath;
                File fileDir = new File(fileDirPath);
                if (!fileDir.exists()) {
                    fileDir.mkdirs();
                }

                int lastIndex = fileName.lastIndexOf(".");
                String fileExt = "";
                if (lastIndex > 0) {
                    fileExt = fileName.substring(lastIndex);
                }
                String newFileName = IdUtil.objectId() + fileExt;
                // 构建文件存储的完整路径
                Path filePath = Paths.get(fileDirPath, newFileName);
                // 将文件保存到指定路径
                Files.copy(file.getInputStream(), filePath);
                UploadFileResponse response = new UploadFileResponse();
                response.setFilePath(midPath + "/" + newFileName);
                response.setFileName(fileName);
                responseList.add(response);
            }
            return ResultResponse.success(responseList);
        } catch (Exception e) {
            log.error("车辆附件上传失败", e);
            return ResultResponse.businessFailed("车辆附件上传失败");
        }
    }

}
