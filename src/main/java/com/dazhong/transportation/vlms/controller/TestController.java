package com.dazhong.transportation.vlms.controller;


import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 测试接口
 * <AUTHOR>
 * @date 2024-12-20 13:41
 */
@ApiIgnore
@Api(value = "健康检查", tags = "健康检查")
@RestController
@RequestMapping(value = "api")
public class TestController {

    @ApiOperation(value = "健康检查", httpMethod = "GET")
    @GetMapping(value = "testConnection")
    public ResultResponse testConnection(){
        return ResultResponse.success();
    }
}
