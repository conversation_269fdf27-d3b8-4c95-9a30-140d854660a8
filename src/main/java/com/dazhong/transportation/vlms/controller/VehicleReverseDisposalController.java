package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.QueryVehicleRequest;
import com.dazhong.transportation.vlms.dto.request.SaveReverseDisposalApplicationRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.ReverseDisposalApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleBasicResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetail;
import com.dazhong.transportation.vlms.service.IVehicleReverseDisposalService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(value = "车辆逆处置相关接口", tags = "车辆逆处置")
@Slf4j
@RestController
@RequestMapping(value = "api/vehicleReverseDisposal")
public class VehicleReverseDisposalController {

    @Autowired
    private IVehicleReverseDisposalService vehicleReverseDisposalService;

    /**
     * 查询逆处置车辆信息
     *
     * @return
     */
    @ApiOperation(value = "查询逆处置车辆信息（车架号/车牌号）", httpMethod = "POST")
    @RequestMapping(value = "queryReverseDisposalVehicleInfo", method = RequestMethod.POST)
    public ResultResponse<VehicleBasicResponse> queryReverseDisposalVehicleInfo(@RequestBody QueryVehicleRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleReverseDisposalService.queryAndCheckVehicleBasicInfo(request));
    }

    /**
     * 查询车辆逆处置任务列表
     *
     * @return 返回列表
     */
    @ApiOperation(value = "查询车辆逆处置申请单列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryVehicleReverseDisposalList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleReverseDisposalListDto>> queryList(@RequestBody SearchVehicleDisposalListRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleReverseDisposalService.queryPageResponse(request, tokenUserInfo));
    }

    /**
     * 保存逆处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "保存逆处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "saveApplication", method = RequestMethod.POST)
    public ResultResponse<Long> saveApplication(@RequestBody SaveReverseDisposalApplicationRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleReverseDisposalService.saveApplication(request, tokenUserInfo);
    }

    /**
     * 提交逆处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "提交逆处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "submitApplication", method = RequestMethod.POST)
    public ResultResponse<Long> submitApplication(@RequestBody SaveReverseDisposalApplicationRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleReverseDisposalService.submitApplication(request, tokenUserInfo);
    }

    /**
     * 作废逆处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "作废逆处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "cancelApplication", method = RequestMethod.POST)
    public ResultResponse<Void> cancelApplication(@RequestBody BaseIdRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleReverseDisposalService.cancelApplication(request.getId(), tokenUserInfo);
    }

    /**
     * 撤回逆处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "撤回逆处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "withdrawApplication", method = RequestMethod.POST)
    public ResultResponse<Void> withdrawApplication(@RequestBody BaseIdRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleReverseDisposalService.withdrawApplication(request.getId(), tokenUserInfo);
    }

    /**
     * 获取逆处置申请单详情
     *
     * @return 返回列表
     */
    @ApiOperation(value = "获取逆处置申请单详情", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "getApplicationDetail", method = RequestMethod.POST)
    public ResultResponse<ReverseDisposalApplicationDetailResponse> getReverseDisposalDetail(@RequestBody BaseIdRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleReverseDisposalService.queryReverseDisposalDetail(request.getId()));
    }

    /**
     * 批量上传处置车辆明细
     *
     * @return 处置车辆明细list
     */
    @ApiOperation(value = "批量上传处置车辆明细", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importReverseDisposalVehicleDetail", method = RequestMethod.POST)
    public ResultResponse<List<ImportDisposalVehicleDetail>> importDisposalVehicleDetail(@RequestBody BaseImportFileUrlRequest request) {
        return ResultResponse.success(vehicleReverseDisposalService.getReverseDisposalVehicleDetailList(request));
    }
}
