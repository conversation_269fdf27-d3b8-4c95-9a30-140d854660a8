package com.dazhong.transportation.vlms.controller;

import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.response.*;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.ImportExcelTypeEnum;
import com.dazhong.transportation.vlms.excel.*;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.service.sync.IHandleSyncDataService;
import com.dazhong.transportation.vlms.service.sync.VehicleSyncExternalAccidentServiceImpl;
import com.dazhong.transportation.vlms.service.sync.VehicleSyncExternalIllegalServiceImpl;
import com.dazhong.transportation.vlms.service.sync.VehicleSyncExternalInsuranceServiceImpl;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import com.dazhong.transportation.vlms.utils.UrlUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.page.PageMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(value = "车辆相关接口", tags = "车辆相关接口")
@Slf4j
@LoginRequiredAnnotation(required = true)
@RestController
@RequestMapping(value = "api")
public class VehicleController {

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private VehicleSyncExternalAccidentServiceImpl vehicleSyncExternalAccidentService;


    @Autowired
    private VehicleSyncExternalIllegalServiceImpl vehicleSyncExternalIllegalService;


    @Autowired
    private VehicleSyncExternalInsuranceServiceImpl vehicleSyncExternalInsuranceService;
    /**
     * 查询入库车辆列表
     *
     * @return
     */
    @ApiOperation(value = "入库车辆列表", httpMethod = "POST")
    @RequestMapping(value = "searchInboundVehicleList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<InboundVehicleResponse>> searchInboundVehicleList(@RequestBody SearchInboundVehicleRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        PageResponse pageResponse = vehicleService.searchInboundVehicleList(request, tokenUserInfo);
        return ResultResponse.success(pageResponse);
    }

    /**
     * 查询入库车辆详情
     *
     * @return
     */
    @ApiOperation(value = "入库车辆详情", httpMethod = "GET")
    @RequestMapping(value = "getInboundVehicleDetails/{vin}", method = RequestMethod.GET)
    public ResultResponse<InboundVehicleDetailsResponse> getInboundVehicleDetails(@ApiParam(value = "车架号 必传", required = true) @PathVariable String vin) {
        // 对车架号进行URL解码，解决中文乱码问题
        String decodedVin = UrlUtils.safeDecodeMultiple(vin);
        return vehicleService.getInboundVehicleDetails(decodedVin);
    }

    /**
     * 查询车辆列表
     *
     * @return
     */
    @ApiOperation(value = "查询车辆列表", httpMethod = "POST")
    @RequestMapping(value = "searchAssetVehicleList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleListResponse>> searchAssetVehicleList(@RequestBody SearchAssetVehicleRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        PageResponse pageResponse = vehicleService.searchAssetVehicleList(request, tokenUserInfo);
        return ResultResponse.success(pageResponse);
    }

    /**
     * 查询车辆详情
     *
     * @return
     */
    @ApiOperation(value = "查询车辆详情", httpMethod = "GET")
    @RequestMapping(value = "getVehicleDetails/{vin}", method = RequestMethod.GET)
    public ResultResponse<VehicleDetailsResponse> getVehicleDetails(@ApiParam(value = "车架号 必传", required = true) @PathVariable String vin) {
        // 对车架号进行URL解码，解决中文乱码问题
        String decodedVin = UrlUtils.safeDecodeMultiple(vin);
        return vehicleService.getVehicleDetails(decodedVin);
    }

    /**
     * 查询车辆基础信息
     *
     * @return
     */
    @ApiOperation(value = "查询车辆基础信息", httpMethod = "GET")
    @RequestMapping(value = "getVehicleBasicInfo/{vin}", method = RequestMethod.GET)
    public ResultResponse<VehicleBasicResponse> getVehicleBasicInfo(@ApiParam(value = "车架号 必传", required = true) @PathVariable String vin) {
        // 对车架号进行URL解码，解决中文乱码问题
        String decodedVin = UrlUtils.safeDecodeMultiple(vin);
        return vehicleService.getVehicleBasicInfo(decodedVin);
    }

    /**
     * 查询车辆基础信息
     *
     * @return
     */
    @ApiOperation(value = "查询车辆基础信息（车架号/车牌号）", httpMethod = "POST")
    @RequestMapping(value = "queryVehicleBasicInfo", method = RequestMethod.POST)
    public ResultResponse<VehicleBasicResponse> queryVehicleBasicInfo(@RequestBody QueryVehicleRequest request) {
        ValidationUtils.validate(request);
        if (StringUtils.isNotBlank(request.getVin())) {
            return vehicleService.getVehicleBasicInfo(request.getVin());
        }
        if (StringUtils.isNotBlank(request.getLicensePlate())) {
            return vehicleService.getVehicleBasicInfoByLicensePlate(request.getLicensePlate());
        }
        return ResultResponse.businessFailed("请正确输入车架号或车牌号！");
    }

    /**
     * 导入车辆数据信息
     *
     * @return
     */
    @ApiOperation(value = "导入车辆数据信息", httpMethod = "POST")
    @RequestMapping(value = "importVehicleData", method = RequestMethod.POST)
    public ResultResponse importVehicleData(@RequestBody ImportVehicleInfoRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ResultResponse resultResponse = ResultResponse.success();
        ImportExcelTypeEnum importExcelTypeEnum = null;
        try {
            ValidationUtils.validate(request);
            // 文件类型 1-装潢信息 2-其他信息 9-车辆折旧信息 10-年检到期日 11-车辆主数据 12-管理员批量修改数据
            int fileType = request.getFileType();
            importExcelTypeEnum = ImportExcelTypeEnum.getImportExcel(fileType);
            if (importExcelTypeEnum == null) {
                throw new ServiceException("上传文件枚举类型错误");
            }
            if (ImportExcelTypeEnum.vehicleDecoration.getCode() == fileType) {
                // 批量导入装潢信息
                List<ImportVehicleDecoration> readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleDecoration.class);
                resultResponse = vehicleService.importVehicleDecoration(readList, tokenUserInfo);
            } else if (ImportExcelTypeEnum.vehicleOtherInfo.getCode() == fileType) {
                // 批量导入其他信息
                List<ImportVehicleOtherInfo> readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleOtherInfo.class);
                resultResponse = vehicleService.importVehicleOtherInfo(readList, tokenUserInfo);
            } else if (ImportExcelTypeEnum.vehicleDepreciationInfo.getCode() == fileType) {
                // 批量导入折旧信息
                List<ImportVehicleDepreciationInfo> readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleDepreciationInfo.class);
                resultResponse = vehicleService.importVehicleDepreciationInfo(readList, tokenUserInfo);
            } else if (ImportExcelTypeEnum.vehicleInspectionExpiryDate.getCode() == fileType) {
                // 批量登记年检到期日
                List<ImportAnnualInspectionExpiryDate> readList = ExcelUtil.read(request.getFilePath(), 0, ImportAnnualInspectionExpiryDate.class);
                resultResponse = vehicleService.importAnnualInspectionExpiryDate(readList, tokenUserInfo);
            } else if (ImportExcelTypeEnum.vehicleMasterDate.getCode() == fileType) {
                // 批量导入车辆主数据信息
                List<ImportVehicleMasterData> readList = ExcelUtil.read(request.getFilePath(), 0, ImportVehicleMasterData.class);
                resultResponse = vehicleService.importVehicleMasterDate(readList, tokenUserInfo);
            } else if (ImportExcelTypeEnum.VehicleDataInfo.getCode() == fileType) {
                // 上传管理员批量修改数据
                List<ImportAdminVehicleOtherInfo> readList = ExcelUtil.read(request.getFilePath(), 0, ImportAdminVehicleOtherInfo.class);
                resultResponse = vehicleService.importVehicleDataInfo(readList, tokenUserInfo);
            }
            CommonUtils.deleteIfExists(request.getFilePath());
        } catch (Exception exception) {
            CommonUtils.deleteIfExists(request.getFilePath());
            log.error("导入车辆数据信息异常", exception);
            if (exception instanceof ServiceException) {
                throw new ServiceException(StrUtil.format("{},请重新上传", exception.getMessage()));
            }
            throw new ServiceException(StrUtil.format("{}失败,请重新上传", importExcelTypeEnum.getDesc()));
        }
        return resultResponse;
    }

    /**
     * 上传车辆附件信息
     *
     * @return
     */
    @ApiOperation(value = "上传车辆附件信息", httpMethod = "POST")
    @RequestMapping(value = "importVehicleFill", method = RequestMethod.POST)
    public ResultResponse importVehicleFill(@RequestBody ImportVehicleInfoRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        try {
            ValidationUtils.validate(request);
            ResultResponse response = vehicleService.importVehicleFill(request, tokenUserInfo);
            CommonUtils.deleteIfExists(request.getFilePath());
            return response;
        } catch (Exception exception) {
            CommonUtils.deleteIfExists(request.getFilePath());
            log.error("文件上传失败", exception);
            if (exception instanceof ServiceException) {
                throw new ServiceException(StrUtil.format("{},请重新上传",exception.getMessage()));
            }
            throw new ServiceException("文件上传失败,请重新上传");
        }
    }

    /**
     * 证照流水记录查询
     *
     * @return
     */
    @ApiOperation(value = "证照流水记录查询", httpMethod = "POST")
    @RequestMapping(value = "queryVehicleFileRecord", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleFileRecordResponse>> queryVehicleFileRecord(@RequestBody VinQueryRequest request) {
        ValidationUtils.validate(request);
        return vehicleService.queryVehicleFileRecord(request);
    }


    /**
     * 装潢流水记录查询
     *
     * @return
     */
    @ApiOperation(value = "装潢流水记录查询", httpMethod = "POST")
    @RequestMapping(value = "queryVehicleDecorationRecord", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleDecorationResponse>> queryVehicleDecorationRecord(@RequestBody VinQueryRequest request) {
        ValidationUtils.validate(request);
        return vehicleService.queryVehicleDecorationRecord(request);
    }

    /**
     * 折旧记录查询
     *
     * @return
     */
    @ApiOperation(value = "折旧记录查询", httpMethod = "POST")
    @RequestMapping(value = "queryVehicleDepreciationRecord", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleDepreciationDataResponse>> queryVehicleDepreciationRecord(@RequestBody VinQueryRequest request) {
        ValidationUtils.validate(request);
        return vehicleService.queryVehicleDepreciationRecord(request);
    }

    /**
     * 合同记录查询
     *
     * @return
     */
    @ApiOperation(value = "合同记录查询", httpMethod = "POST")
    @RequestMapping(value = "queryVehicleContractRecord", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleContractDataResponse>> queryVehicleContractRecord(@RequestBody VinQueryRequest request) {
        ValidationUtils.validate(request);
        return vehicleService.queryVehicleContractRecord(request);
    }


    /**
     * 根据车架号模糊搜索
     *
     * @return
     */
    @ApiOperation(value = "根据车架号模糊搜索", httpMethod = "POST")
    @RequestMapping(value = "getComBoxVehicleInfo", method = RequestMethod.POST)
    public ResultResponse<VehicleBaseListResponse> getComBoxVehicleInfo(@RequestBody ComBoxVehicleRequest request) {
        return vehicleService.getComBoxVehicleInfo(request.getVin(), request.getLicensePlate());
    }


    /**
     * 保存车辆装潢信息
     *
     * @return
     */
    @ApiOperation(value = "保存车辆装潢信息", httpMethod = "POST")
    @RequestMapping(value = "saveVehicleDecoration", method = RequestMethod.POST)
    public ResultResponse saveVehicleDecoration(@RequestBody VehicleDecorationRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleService.saveVehicleDecoration(request, tokenUserInfo);
    }

    /**
     * 保存车辆其他信息
     *
     * @return
     */
    @ApiOperation(value = "保存车辆其他信息", httpMethod = "POST")
    @RequestMapping(value = "saveVehicleOtherInfo", method = RequestMethod.POST)
    public ResultResponse saveVehicleOtherInfo(@RequestBody VehicleOtherInfoRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleService.saveVehicleOtherInfo(request, tokenUserInfo);
    }

    /**
     * 保存车辆附件信息
     *
     * @return
     */
    @ApiOperation(value = "保存车辆附件信息", httpMethod = "POST")
    @RequestMapping(value = "saveVehicleAttachment", method = RequestMethod.POST)
    public ResultResponse saveVehicleAttachment(@RequestBody VehicleAttachmentRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleService.saveVehicleAttachment(request, tokenUserInfo);
    }

    /**
     * 批量保存车辆附件信息
     *
     * @return
     */
    @ApiOperation(value = "批量保存车辆附件信息", httpMethod = "POST")
    @RequestMapping(value = "batchSaveVehicleAttachment", method = RequestMethod.POST)
    public ResultResponse batchSaveVehicleAttachment(@RequestBody BatchSaveVehicleAttachmentRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleService.batchSaveVehicleAttachment(request, tokenUserInfo);
    }


    @ApiOperation(value = "导出车辆列表", httpMethod = "POST")
    @PostMapping(value = "exportAssetVehicleInfo")
    public ResultResponse exportAssetVehicleInfo(@RequestBody @Validated SearchAssetVehicleRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        //异步导出
        taskExecutor.execute(() -> vehicleService.exportAssetVehicleInfo(request, tokenUserInfo));
        return ResultResponse.success();
    }

    @ApiOperation(value = "获取车辆保险信息", httpMethod = "POST")
    @PostMapping(value = "getVehicleInsurance")
    public ResultResponse getVehicleInsurance(@RequestBody @Validated GetVehicleSyncExternalRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        //校验参数
        ValidationUtils.validate(request);
        return vehicleSyncExternalInsuranceService.getSyncExternalInfoByLicensePlate(request);
    }

    @ApiOperation(value = "获取车辆违章信息", httpMethod = "POST")
    @PostMapping(value = "getVehicleIllegal")
    public ResultResponse getVehicleIllegal(@RequestBody @Validated GetVehicleSyncExternalRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        //校验参数
        ValidationUtils.validate(request);
        return vehicleSyncExternalIllegalService.getSyncExternalInfoByLicensePlate(request);
    }


    @ApiOperation(value = "获取车辆事故信息", httpMethod = "POST")
    @PostMapping(value = "getVehicleAccident")
    public ResultResponse getVehicleAccident(@RequestBody @Validated GetVehicleSyncExternalRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        //校验参数
        ValidationUtils.validate(request);
        return vehicleSyncExternalAccidentService.getSyncExternalInfoByLicensePlate(request);
    }

    /**
     * 手动触发从大众交通核心系统同步车辆信息
     *
     * @return 同步结果
     */
    @ApiOperation(value = "手动触发从大众交通核心系统同步车辆信息", httpMethod = "POST")
    @PostMapping(value = "syncAllVehicleInfoFromDaZhong")
    public ResultResponse<Void> syncAllVehicleInfoFromDaZhong() {
        log.info("手动触发从大众交通核心系统同步车辆信息");
        try {
            return vehicleService.syncAllVehicleInfoFromDaZhong();
        } catch (Exception e) {
            log.error("手动触发从大众交通核心系统同步车辆信息失败", e);
            return ResultResponse.businessFailed("同步车辆信息失败：" + e.getMessage());
        }
    }

    /**
     * 手动触发同步车辆信息到SAAS系统
     *
     * @param isFullSync 是否全量同步（true:全量同步，false:增量同步）
     * @return 同步结果
     */
    @ApiOperation(value = "手动触发同步车辆信息到SAAS系统", httpMethod = "POST")
    @PostMapping(value = "syncVehicleToSaas")
    public ResultResponse<Void> syncVehicleToSaas(@RequestParam(value = "isFullSync", defaultValue = "false") boolean isFullSync) {
        log.info("手动触发同步车辆信息到SAAS系统，同步模式：{}", isFullSync ? "全量同步" : "增量同步");
        try {
            return vehicleService.syncVehicleToSaasManually(isFullSync);
        } catch (Exception e) {
            log.error("手动触发同步车辆信息到SAAS系统失败", e);
            return ResultResponse.businessFailed("同步车辆信息失败：" + e.getMessage());
        }
    }

    /**
     * 手动触发同步车型信息到SAAS系统
     *
     * @param isFullSync 是否全量同步（true:全量同步，false:增量同步）
     * @return 同步结果
     */
    @ApiOperation(value = "手动触发同步车型信息到SAAS系统", httpMethod = "POST")
    @PostMapping(value = "syncVehicleModelToSaas")
    public ResultResponse<Void> syncVehicleModelToSaas(@RequestParam(value = "isFullSync", defaultValue = "false") boolean isFullSync) {
        log.info("手动触发同步车型信息到SAAS系统，同步模式：{}", isFullSync ? "全量同步" : "增量同步");
        try {
            return vehicleService.syncVehicleModelToSaasManually(isFullSync);
        } catch (Exception e) {
            log.error("手动触发同步车型信息到SAAS系统失败", e);
            return ResultResponse.businessFailed("同步车型信息失败：" + e.getMessage());
        }
    }
}
