package com.dazhong.transportation.vlms.controller;

import cn.hutool.core.bean.BeanUtil;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.database.TableOperateLogService;
import com.dazhong.transportation.vlms.dto.request.SearchLogRequest;
import com.dazhong.transportation.vlms.dto.response.OperateLogResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.OperateLog;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 操作日志接口
 * <AUTHOR>
 * @date 2024-12-24 09:55
 */
@Slf4j
@RestController
@RequestMapping(value = "api")
@Api(value = "操作日志接口", tags = "系统日志")
public class OperateLogController {

    @Resource
    private TableOperateLogService operateLogService;

    /**
     * 查询日志列表
     * @return
     */
    @ApiOperation(value = "查询日志列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "searchOperateLog", method = RequestMethod.POST)
    public ResultResponse<PageResponse> searchOperateLog(@RequestBody SearchLogRequest request) {
        ValidationUtils.validate(request);
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<OperateLog> list = operateLogService.searchOperateLog(request);
        PageInfo<OperateLog> pageInfo = new PageInfo<>(list);
        List<OperateLogResponse> responseList = new ArrayList<>();
        for (OperateLog operateLog : list) {
            OperateLogResponse response = BeanUtil.copyProperties(operateLog,OperateLogResponse.class);
            responseList.add(response);
        }
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return ResultResponse.success(pageResponse);
    }
}
