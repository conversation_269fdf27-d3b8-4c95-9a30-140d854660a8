package com.dazhong.transportation.vlms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.dto.DaZhongUserInfo;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.response.LoginUserResponse;
import com.dazhong.transportation.vlms.dto.response.UserInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.SsoUserInfo;
import com.dazhong.transportation.vlms.model.UserInfo;
import com.dazhong.transportation.vlms.model.UserOrgInfo;
import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.service.IUserService;
import com.dazhong.transportation.vlms.utils.RSAWithStringUtils;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 系统用户接口
 *
 * <AUTHOR>
 * @date 2024-12-20 16:41
 */
@Api(value = "系统用户接口", tags = "系统用户")
@Slf4j
@RestController
@RequestMapping(value = "api")
public class UserController {

    @Autowired
    private IUserService userService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    public RedisUtils redisUtils;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${rsa.public.key}")
    private String publicKey;

    @Value("${sso.clientId}")
    private String ssoClientId;

    @Value("${login.url}")
    private String loginUrl;

    @Value("${sso.token.url}")
    private String ssoTokenUrl;

    /**
     * 用户登录
     *
     * @return
     */
    @ApiOperation(value = "用户登录", httpMethod = "POST")
    @RequestMapping(value = "login", method = RequestMethod.POST)
    public ResultResponse<LoginUserResponse> login(@RequestBody LoginRequest request) {
        ValidationUtils.validate(request);
        // 登录参数信息解密
        String param = request.getParam();
        String accountId = null;
        //TODO 本地调用上线后注释掉
        boolean isCheckLogin = true;
        if (param.equals("local") && isCheckLogin) {
            accountId = "lichengxiang";
        } else {
            String result = RSAWithStringUtils.decodeDataByPublicKey(param, publicKey);
            if (StringUtils.isBlank(result)) {
                return ResultResponse.businessFailed("大众交通登录账号解密失败");
            }
            JSONObject jsonObject = JSONUtil.parseObj(result);
            String authCode = jsonObject.getStr("authCode");
            String state = jsonObject.getStr("state");
            // 请求第三方接口获取用户信息
            Map<String, String> params = new HashMap<>();
            params.put("code", authCode);
            params.put("redirect_uri", loginUrl);
            params.put("state", state);
            JSONObject paramJson = new JSONObject();
            paramJson.set("clientId", ssoClientId);
            paramJson.set("param", RSAWithStringUtils.encodeDataByPublicKey(JSONUtil.toJsonStr(params), publicKey));
            log.info("获取登录用户信息入参-{}", JSONUtil.toJsonStr(paramJson));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Content-Type", "application/json");
            HttpEntity<String> httpEntity = new HttpEntity(paramJson.toString(), headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(ssoTokenUrl, httpEntity, JSONObject.class);
            log.info("验证登录用户信息结果-{}", JSONUtil.toJsonStr(responseEntity));
            if (responseEntity.getStatusCode().value() != 200) {
                return ResultResponse.businessFailed("大众交通验证登录账号调用失败");
            }
            JSONObject jsonobject = responseEntity.getBody();
            String requestStatus = jsonobject.getStr("requestStatus");
            if (StringUtils.isBlank(requestStatus) || !"CURRENT".equals(requestStatus)) {
                return ResultResponse.businessFailed("大众交通登录账号验证失败,请重新登录");
            }
            String value = jsonobject.getStr("value");
            result = RSAWithStringUtils.decodeDataByPublicKey(value, publicKey);
            log.info("大众交通登录用户信息-{}", result);
            if (StringUtils.isBlank(result)) {
                return ResultResponse.businessFailed("大众交通登录用户信息解密失败");
            }
            JSONObject loginUserJson = JSONUtil.parseObj(result);
            accountId = loginUserJson.getStr("accountId");
            log.info("大众交通登录账号-{}", accountId);
            if (StringUtils.isBlank(accountId)) {
                return ResultResponse.businessFailed("大众交通登录用户信息不存在");
            }
        }
        UserInfo userInfo = userService.queryUserByAccount(accountId);
        if (userInfo == null) {
            return ResultResponse.businessFailed("系统用户信息不存在");
        }
        // 保存用户登录信息 有效期2小时
        int random = ThreadLocalRandom.current().nextInt(1000, 99999);
        String userToken = StrUtil.format("{}{}", UUID.randomUUID().toString().replaceAll("-", ""), random);
        LoginUserResponse response = new LoginUserResponse();
        response.setIsSystemAdmin(userInfo.getIsSystemAdmin());
        response.setUserName(StrUtil.format("{}-{}", userInfo.getName(), userInfo.getMobilePhone()));
        response.setToken(userToken);
        TokenUserInfo tokenUserInfo = BeanUtil.copyProperties(userInfo, TokenUserInfo.class);
        //是否超管 1-是 2-否
        int isSystemAdmin = userInfo.getIsSystemAdmin();
        if (isSystemAdmin == BizConstant.SystemAdminType.nonAdministrator) {
            // 查询用户关联的组织架构和owner信息
            List<UserOrgInfo> userOrgList = orgService.queryUserOrgInfo(userInfo.getId());
            List<Long> orgIdList = userOrgList.stream().filter(userOrgInfo -> userOrgInfo.getOrgType() == 1).map(UserOrgInfo::getOrgId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(orgIdList)) {
                tokenUserInfo.setOrgIdList(orgIdList);
            }
            // 用户关联的owner
            List<Long> ownerList = userOrgList.stream().filter(userOrgInfo -> userOrgInfo.getOrgType() == 2).map(UserOrgInfo::getOrgId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ownerList)) {
                List<Integer> intList = ownerList.stream()
                        .map(Long::intValue)
                        .collect(Collectors.toList());
                tokenUserInfo.setOwnerIdList(intList);
            }
            if (CollectionUtil.isEmpty(ownerList) && CollectionUtil.isEmpty(orgIdList)) {
                // 设置默认值
                tokenUserInfo.setOrgIdList(Collections.singletonList(-999999L));
                tokenUserInfo.setOwnerIdList(Collections.singletonList(-999999));
            }
        }
        tokenUserInfo.setUserId(userInfo.getId());
        // 登录用户信息保存到Redis
        redisUtils.set(BizConstant.LOGIN_TOKEN_KEY + response.getToken(), String.valueOf(tokenUserInfo.getUserId()), BizConstant.LOGIN_EXPIRES_IN);
        redisUtils.set(BizConstant.LOGIN_USER_KEY + tokenUserInfo.getUserId(), JSONUtil.toJsonStr(tokenUserInfo), BizConstant.LOGIN_EXPIRES_IN);
        return ResultResponse.success(response);
    }

    /**
     * 用户登出
     *
     * @return
     */
    @ApiOperation(value = "用户登出", httpMethod = "GET")
    @RequestMapping(value = "loginOut", method = RequestMethod.GET)
    public ResultResponse loginOut(HttpServletRequest request) {
        String token = request.getHeader("token");
        if (StringUtils.isBlank(token)) {
            return ResultResponse.success();
        }
        String userId = redisUtils.getStr(BizConstant.LOGIN_TOKEN_KEY + token);
        if (StringUtils.isNotBlank(userId)) {
            redisUtils.del(BizConstant.LOGIN_TOKEN_KEY + token);
            redisUtils.del(BizConstant.LOGIN_USER_KEY + userId);
        }
        return ResultResponse.success();
    }

    /**
     * 查询用户列表
     *
     * @return
     */
    @ApiOperation(value = "分页查询用户列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "searchUserList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<UserInfoResponse>> searchUserList(@RequestBody SearchUserRequest request) {
        ValidationUtils.validate(request);
        PageResponse pageResponse = userService.queryUserList(request);
        return ResultResponse.success(pageResponse);
    }

    /**
     * 查询大众账号列表
     *
     * @return
     */
    @ApiOperation(value = "查询大众账号列表", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryUserAccount", method = RequestMethod.GET)
    public ResultResponse<DaZhongUserInfo> queryUserAccount() {
        List<DaZhongUserInfo> responseList = new ArrayList<>();
        DaZhongUserInfo response = null;
        List<SsoUserInfo> list = userService.querySsoUserList();
        for (SsoUserInfo ssoUserInfo : list) {
            response = new DaZhongUserInfo();
            response.setSsoUserId(String.valueOf(ssoUserInfo.getUserId()));
            response.setMobilePhone(ssoUserInfo.getMobilePhone());
            response.setName(ssoUserInfo.getName());
            responseList.add(response);
        }
        return ResultResponse.success(responseList);
    }

    /**
     * 查询用户信息
     *
     * @return
     */
    @ApiOperation(value = "查询用户信息", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryUserInfo/{userId}", method = RequestMethod.GET)
    public ResultResponse<UserInfoResponse> queryUserInfo(@ApiParam(value = "用户ID 必传", required = true) @PathVariable Long userId) {
        return userService.queryUserInfo(userId);
    }

    /**
     * 新增用户信息
     *
     * @param request
     * @param userInfo
     * @return
     */
    @ApiOperation(value = "新增用户信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "addUser", method = RequestMethod.POST)
    public ResultResponse addUser(@RequestBody SaveUserRequest request, @TokenUserAnnotation TokenUserInfo userInfo) {
        return userService.addUser(request, userInfo);
    }

    /**
     * 修改用户信息
     *
     * @param request
     * @param userInfo
     * @return
     */
    @ApiOperation(value = "修改用户信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "updateUser", method = RequestMethod.POST)
    public ResultResponse updateUser(@RequestBody UpdateUserRequest request, @TokenUserAnnotation TokenUserInfo userInfo) {
        return userService.updateUser(request, userInfo);
    }

    /**
     * 绑定用户钉钉信息
     *
     * @return
     */
    @ApiOperation(value = "绑定用户钉钉信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "bindDingTalkUser", method = RequestMethod.POST)
    public ResultResponse bindDingTalkUser(@RequestBody BindDingTalkRequest request, @TokenUserAnnotation TokenUserInfo userInfo) {
        ValidationUtils.validate(request);
        return userService.bindDingTalkUser(request, userInfo);
    }

    /**
     * 删除用户信息
     *
     * @return
     */
    @ApiOperation(value = "删除用户信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "deleteUser", method = RequestMethod.POST)
    public ResultResponse deleteUser(@RequestBody BaseIdRequest request, @TokenUserAnnotation TokenUserInfo userInfo) {
        ValidationUtils.validate(request);
        return userService.deleteUser(request, userInfo);
    }

    /**
     * 查询用户钉钉信息
     *
     * @return
     */
    @ApiOperation(value = "查询用户钉钉信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryUserDingTalk", method = RequestMethod.POST)
    public ResultResponse queryUserDingTalk(@RequestBody QueryDingTalkRequest request, @TokenUserAnnotation TokenUserInfo userInfo) {
        ValidationUtils.validate(request);
        return userService.queryUserDingTalk(request, userInfo);
    }

}
