package com.dazhong.transportation.vlms.controller;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.dazhong.transportation.vlms.config.ApiSignatureAnnotation;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.config.HandleSyncDataContext;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.dto.DatabaseDictSyncDto;
import com.dazhong.transportation.vlms.dto.SyncSsoUserDto;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseDictSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.SearchAssetVehicleSyncDataResponse;
import com.dazhong.transportation.vlms.dto.response.SearchDatabaseDictSyncDataResponse;
import com.dazhong.transportation.vlms.dto.response.SearchDatabaseTableSyncDataResponse;
import com.dazhong.transportation.vlms.dto.response.UploadFileResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.DataDictEnum;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.service.IUserService;
import com.dazhong.transportation.vlms.service.IVehicleModelService;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.service.sync.IHandleSyncDataService;
import com.dazhong.transportation.vlms.utils.RSAWithStringUtils;
import com.dazhong.transportation.vlms.utils.SignUtil;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 公用接口
 *
 * <AUTHOR>
 * @date 2024-12-23 09:41
 */
@Slf4j
@Api(value = "公用接口", tags = "公用接口")
@RestController
public class BasicController {

    @Autowired
    private HandleSyncDataContext handleSyncDataContext;

    @Autowired
    private IUserService userService;

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private IVehicleModelService vehicleModelService;

    @Autowired
    private IDataDictService dataDictService;

    @Value("${rsa.public.key}")
    private String publicKey;

    @Value("${sso.clientId}")
    private String ssoClientId;

    @Value("${sign.secretKey}")
    private String signSecretKey;

    /**
     * 同步数据库表数据
     */
    @ApiOperation(value = "查询数据库表同步数据", httpMethod = "POST")
    @ApiSignatureAnnotation
    @PostMapping(value = "sync/searchDatabaseTableSyncData")
    public ResultResponse<SearchDatabaseTableSyncDataResponse> searchDatabaseTableSyncData(
            @RequestBody @Validated SearchDatabaseTableSyncDataRequest request, HttpServletRequest httpRequest) {
        // 验证签名
        if (!SignUtil.verifySignature(httpRequest, request, signSecretKey)) {
            throw new ServiceException("签名验证失败");
        }

        // 设置默认值
        if (request.getIndex() == null) {
            request.setIndex(-1L);
        }
        if (request.getPageSize() == null) {
            request.setPageSize(1000L);
        }

        SearchDatabaseTableSyncDataResponse response;
        switch (request.getTableName()) {
            //车辆表
            case "Vehicle":
                response = vehicleService.searchVehicleDatabaseSyncData(request);
                break;
            //车辆资产表
            case "VehicleAsset":
                response = vehicleService.searchVehicleAssetDatabaseSyncData(request);
                break;
            //车辆型号表
            case "VehicleModel":
                response = vehicleModelService.searchVehicleModelDatabaseSyncData(request);
                break;
            default:
                throw new ServiceException("暂不支持对该表的同步数据查询");
        }
        return ResultResponse.success(response);
    }

    @ApiOperation(value = "查询数据库字典同步数据", httpMethod = "POST")
    @ApiSignatureAnnotation
    @PostMapping(value = "sync/searchDatabaseDictSyncData")
    public ResultResponse<SearchDatabaseDictSyncDataResponse> searchDatabaseDictSyncData(
            @RequestBody @Validated SearchDatabaseDictSyncDataRequest request, HttpServletRequest httpRequest) {
        // 验证签名
        if (!SignUtil.verifySignature(httpRequest, request, signSecretKey)) {
            throw new ServiceException("签名验证失败");
        }

        // 校验是否是合法的字典表
        DataDictEnum dataDictEnum = DataDictEnum.getByTableName(request.getDictTableName());
        if (dataDictEnum == null) {
            throw new ServiceException("暂不支持对该表的同步数据查询");
        }
        List<DatabaseDictSyncDto> result;
        switch (dataDictEnum) {
            case OWNER:
                result = dataDictService.queryOwnerSyncData();
                break;
            case ORGANIZATION:
                result = dataDictService.queryOrgSyncData();
                break;
            case SUPPLIER:
                result = dataDictService.querySupplierSyncData();
                break;
            case AREA:
                result = dataDictService.queryAreaSyncData();
                break;
            default:
                result = dataDictService.queryMaintainDictSyncData(dataDictEnum.getValue());
                break;
        }
        return ResultResponse.success(new SearchDatabaseDictSyncDataResponse(dataDictEnum.getTableName(), result));
    }

    /**
     * 同步车辆中间表数据
     */
    @ApiOperation(value = "查询车辆中间表同步数据", httpMethod = "POST")
    @ApiSignatureAnnotation
    @PostMapping(value = "sync/searchAssetVehicleSyncData")
    public ResultResponse<SearchAssetVehicleSyncDataResponse> searchAssetVehicleSyncData(
            @RequestBody SearchAssetVehicleSyncDataRequest request, HttpServletRequest httpRequest) {
        // 验证签名
        if (!SignUtil.verifySignature(httpRequest, request, signSecretKey)) {
            throw new ServiceException("签名验证失败");
        }
        return ResultResponse.success(vehicleService.searchAssetVehicleSyncData(request));
    }

    @ApiOperation(value = "同步数据", httpMethod = "POST")
    @ApiSignatureAnnotation
    @RequestMapping(value = "open", method = RequestMethod.POST)
    public ResultResponse syncData(@RequestBody @Validated SyncDataRequest request, HttpServletRequest httpRequest) {
        // 验证签名
        if (!SignUtil.verifySignature(httpRequest, request, signSecretKey)) {
            throw new ServiceException("签名验证失败");
        }

        IHandleSyncDataService service = handleSyncDataContext.getSyncDataService(request.getMethod());
        if (service == null) {
            return ResultResponse.businessFailed("未找到对应的同步方法");
        }
        return service.handleSyncData(request);
    }

    /**
     * 同步单点账号数据
     *
     * @return
     */
    @ApiOperation(value = "同步单点账号数据", httpMethod = "POST")
    @RequestMapping(value = "syncSsoData", method = RequestMethod.POST)
    public String syncSsoData(@RequestParam String clientId, @RequestParam String param) {
        log.info("同步单点账号数据 clientId = {} param={}", clientId, param);
        Map<String, Object> resultMap = new HashMap<>();
        boolean status = true;
        String message = StringUtils.EMPTY;
        resultMap.put("status", true);
        if (StringUtils.isBlank(clientId) || StringUtils.isBlank(param)) {
            status = false;
            message = "参数不能为空";
        }
        if (!clientId.equals(ssoClientId)) {
            status = false;
            message = "clientId不匹配";
        }

        String result = RSAWithStringUtils.decodeDataByPublicKey(param, publicKey);
        if (StringUtils.isBlank(result)) {
            status = false;
            message = "参数解密失败";
        }

        if (status) {
            // 参数解密
            SyncSsoUserDto ssoUserDto = JSONUtil.toBean(result, SyncSsoUserDto.class);
            try {
                userService.syncSsoUser(ssoUserDto);
            } catch (Exception e) {
                log.error("用户同步失败-{}", e.getMessage());
                status = false;
                message = "用户同步失败";
            }
        }
        resultMap.put("status", status);
        resultMap.put("message", message);
        log.info("同步单点账号数据 resultMap={}", JSONUtil.toJsonStr(resultMap));
        return RSAWithStringUtils.encodeDataByPublicKey(JSONUtil.toJsonStr(resultMap), publicKey);
    }

    @ApiOperation(value = "文件上传", httpMethod = "POST")
    @RequestMapping(value = "/api/uploadFile", method = RequestMethod.POST)
    public ResultResponse<UploadFileResponse> uploadFile(@RequestParam("files") List<MultipartFile> files) {
        if (files.isEmpty()) {
            return ResultResponse.businessFailed("文件不能为空");
        }
        try {
            List<UploadFileResponse> responseList = new ArrayList<>();
            // 确保文件存储目录存在
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    continue;
                }
                // 只能上传的文件类型
                String fileName = file.getOriginalFilename();
                if ((!StringUtils.endsWithIgnoreCase(fileName, ".xls")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".xlsx")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".pdf")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".zip")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".jpeg")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".png")
                        && !StringUtils.endsWithIgnoreCase(fileName, ".jpg"))) {
                    return ResultResponse.businessFailed("文件格式错误");
                }
                long size = file.getSize();
                if (size > BizConstant.MAX_UPLOAD_LENGTH) {
                    return ResultResponse.businessFailed("最大上传文件支持100MB");
                }
                String midPath = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now());
                String fileDirPath = Global.instance.mfsRootPath + "/" + midPath;
                File fileDir = new File(fileDirPath);
                if (!fileDir.exists()) {
                    fileDir.mkdirs();
                }

                int lastIndex = fileName.lastIndexOf(".");
                String fileExt = "";
                if (lastIndex > 0) {
                    fileExt = fileName.substring(lastIndex);
                }
                String newFileName = IdUtil.objectId() + fileExt;
                // 构建文件存储的完整路径
                Path filePath = Paths.get(fileDirPath, newFileName);
                // 将文件保存到指定路径
                Files.copy(file.getInputStream(), filePath);
                UploadFileResponse response = new UploadFileResponse();
                response.setFilePath(midPath + "/" + newFileName);
                response.setFileName(fileName);
                responseList.add(response);
            }
            return ResultResponse.success(responseList);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return ResultResponse.businessFailed("文件上传失败");
        }
    }

    /**
     * 获取保司信息详情
     */
    @ApiOperation(value = "获取简道云信息", notes = "获取简道云信息")
    @GetMapping("/syncJianDaoYunData")
    public ResultResponse<Void> syncJianDaoYunData() {
        IHandleSyncDataService service = handleSyncDataContext.getSyncDataService("jian.dao.yun.Data.sync");
        if (service != null){
            service.handleSyncData(null);
        }
        return ResultResponse.success();
    }

}
