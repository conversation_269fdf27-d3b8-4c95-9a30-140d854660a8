package com.dazhong.transportation.vlms.controller;

import cn.hutool.core.bean.BeanUtil;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.ResourceTreeResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.dto.response.RoleResponse;
import com.dazhong.transportation.vlms.model.RoleInfo;
import com.dazhong.transportation.vlms.service.IRoleService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统角色接口
 * <AUTHOR>
 * @date 2024-12-20 16:41
 */
@Slf4j
@Api(value = "系统角色接口", tags = "系统角色")
@RestController
@RequestMapping(value = "api")
public class RoleController {

    @Autowired
    private IRoleService roleService;

    /**
     * 查询角色列表
     * @return
     */
    @ApiOperation(value = "查询角色列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "searchRoleList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<RoleResponse>> searchRoleList(@RequestBody PageRequest request , @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<RoleInfo> list = roleService.queryRoleList(tokenUserInfo);
        PageInfo<RoleInfo> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<RoleResponse> responseList = new ArrayList<>();
        list.forEach(userInfo -> {
            RoleResponse response = BeanUtil.copyProperties(userInfo,RoleResponse.class);
            responseList.add(response);
        });
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return ResultResponse.success(pageResponse);
    }


    /**
     * 删除角色信息
     * @return
     */
    @ApiIgnore
    @ApiOperation(value = "删除角色信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "deleteRole", method = RequestMethod.POST)
    public ResultResponse<PageResponse> deleteRole(@RequestBody BaseIdRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return roleService.deleteRole(request,tokenUserInfo);
    }

    /**
     * 更新角色状态
     * @return
     */
    @ApiOperation(value = "更新角色状态", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "updateRoleStatus", method = RequestMethod.POST)
    public ResultResponse<PageResponse> updateRoleStatus(@RequestBody UpdateRoleStatusRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return roleService.updateRoleStatus(request,tokenUserInfo);
    }

    /**
     * 新增角色
     * @return
     */
    @ApiOperation(value = "新增角色", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "addRole", method = RequestMethod.POST)
    public ResultResponse<PageResponse> saveRole(@RequestBody SaveRoleRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return roleService.addRole(request,tokenUserInfo);
    }

    /**
     * 修改角色
     * @return
     */
    @ApiOperation(value = "修改角色", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "updateRole", method = RequestMethod.POST)
    public ResultResponse<PageResponse> updateRole(@RequestBody UpdateRoleRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return roleService.updateRole(request,tokenUserInfo);
    }


    /**
     * 查看角色资源树
     * @return
     */
    @ApiOperation(value = "查看角色资源树", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryRoleResourceTree/{roleId}", method = RequestMethod.GET)
    public ResultResponse<ResourceTreeResponse> queryRoleResourceTree(@ApiParam(value = "角色id 必传", required = true) @PathVariable Long roleId) {
        return roleService.queryRoleResourceTree(roleId);
    }

    /**
     * 查询角色明细
     * @return
     */
    @ApiOperation(value = "查询角色明细", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryRoleInfo/{roleId}", method = RequestMethod.GET)
    public ResultResponse<RoleResponse> queryRoleInfo(@ApiParam(value = "角色id 必传", required = true) @PathVariable Long roleId) {
        return roleService.queryRoleInfo(roleId);
    }
}
