package com.dazhong.transportation.vlms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleApplicationListDto;
import com.dazhong.transportation.vlms.dto.request.ImportVehicleApplicationFileUrlRequest;
import com.dazhong.transportation.vlms.dto.request.SaveVehicleApplicationRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleApplicationListRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetail;
import com.dazhong.transportation.vlms.service.IVehicleApplicationService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(value = "车辆主数据申请相关接口", tags = "车辆主数据申请")
@Slf4j
@RestController
@RequestMapping(value = "api/vehicleApplication")
public class VehicleApplicationController {

    @Autowired
    private IVehicleApplicationService vehicleApplicationService;

    /**
     * 查询车辆申请单列表
     *
     * @return 返回列表
     */
    @ApiOperation(value = "查询车辆申请单列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryVehicleApplicationList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleApplicationListDto>> queryList(@RequestBody SearchVehicleApplicationListRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleApplicationService.queryVehicleApplicationPageResponse(request, tokenUserInfo));
    }

    /**
     * 保存车辆申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "保存车辆申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "saveApplication", method = RequestMethod.POST)
    public ResultResponse<Long> saveSaleApplication(@RequestBody SaveVehicleApplicationRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleApplicationService.saveApplication(request, tokenUserInfo);
    }

    /**
     * 提交车辆申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "提交车辆申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "submitApplication", method = RequestMethod.POST)
    public ResultResponse<Void> submitApplication(@RequestBody SaveVehicleApplicationRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleApplicationService.submitApplication(request, tokenUserInfo);
    }

    /**
     * 作废车辆申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "作废车辆申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "cancelApplication", method = RequestMethod.POST)
    public ResultResponse<Void> cancelApplication(@RequestBody BaseIdRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleApplicationService.cancelApplication(request.getId(), tokenUserInfo);
    }

    /**
     * 撤回车辆申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "撤回车辆申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "withdrawApplication", method = RequestMethod.POST)
    public ResultResponse<Void> withdrawApplication(@RequestBody BaseIdRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleApplicationService.withdrawApplication(request.getId(), tokenUserInfo);
    }

    /**
     * 获取车辆申请单详情
     *
     * @return 返回列表
     */
    @ApiOperation(value = "获取车辆申请单详情", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "getApplicationDetail", method = RequestMethod.POST)
    public ResultResponse<VehicleApplicationDetailResponse> getApplicationDetail(@RequestBody BaseIdRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleApplicationService.queryApplicationDetail(request.getId()));
    }

    /**
     * 批量上传车辆主数据申请明细
     *
     * @return 处置车辆明细list
     */
    @ApiOperation(value = "批量上传车辆主数据申请明细", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importVehicleApplicationDetail", method = RequestMethod.POST)
    public ResultResponse<List<ImportDisposalVehicleDetail>> importVehicleApplicationDetail(@RequestBody ImportVehicleApplicationFileUrlRequest request) {
        return ResultResponse.success(vehicleApplicationService.getImportVehicleDetailList(request));
    }
}
