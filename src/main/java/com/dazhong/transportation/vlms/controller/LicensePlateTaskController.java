package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskQuotaDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateTaskDetailRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.ImportRevocationVehicleResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateTaskDetailResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateTaskInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportLicensePlateQuota;
import com.dazhong.transportation.vlms.excel.ImportLicensePlateTaskVehicle;
import com.dazhong.transportation.vlms.service.ILicensePlateTaskInfoService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "车牌任务", tags = "车牌任务")
@Slf4j
@RestController
@RequestMapping(value = "api/licensePlateTask")
public class LicensePlateTaskController {

    @Autowired
    private ILicensePlateTaskInfoService licensePlateTaskInfoService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 查询车牌任务分页列表
     *
     * @param request 请求参数
     * @return 返回列表
     */
    @ApiOperation(value = "查询车牌任务分页列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<LicensePlateTaskInfoResponse>> queryList(@RequestBody SearchLicensePlateTaskInfoRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateTaskInfoService.queryPageResponse(request));
    }

    /**
     * 上牌额度登记
     *
     * @return 返回结果
     */
    @ApiOperation(value = "上牌任务登记", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "saveAssignmentTask", method = RequestMethod.POST)
    public ResultResponse<Void> saveAssignmentTask(@RequestBody SaveLicensePlateTaskInfoRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return licensePlateTaskInfoService.saveAssignmentTask(request, tokenUserInfo);
    }

    /**
     * 退牌任务登记
     *
     * @return 返回结果
     */
    @ApiOperation(value = "退牌任务登记", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "saveRevocationTask", method = RequestMethod.POST)
    public ResultResponse<Void> saveRevocationTask(@RequestBody SaveLicensePlateTaskInfoRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return licensePlateTaskInfoService.saveRevocationTask(request, tokenUserInfo);
    }

    /**
     * 根据任务编号获取任务详情
     *
     * @return 返回结果
     */
    @ApiOperation(value = "根据任务编号获取任务详情", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "getDetailByTaskNumber", method = RequestMethod.POST)
    public ResultResponse<LicensePlateTaskDetailResponse> getDetailByTaskNumber(@RequestBody SearchLicensePlateTaskDetailRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateTaskInfoService.getDetailByTaskNumber(request.getTaskNumber()));
    }

    /**
     * 根据任务编号获取车牌列表
     *
     * @return 返回结果
     */
    @ApiOperation(value = "根据任务编号获取车牌列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "getLicensePlateListByTaskNumber", method = RequestMethod.POST)
    public ResultResponse<List<String>> getLicensePlateListByTaskNumber(@RequestBody SearchLicensePlateTaskDetailRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateTaskInfoService.getLicensePlateListByTaskNumber(request.getTaskNumber()));
    }

    /**
     * 根据任务编号获取额度信息列表
     *
     * @return 返回结果
     */
    @ApiOperation(value = "根据任务编号获取额度信息列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "getQuotaDetailListByTaskNumber", method = RequestMethod.POST)
    public ResultResponse<List<LicensePlateTaskQuotaDetailDto>> getQuotaDetailListByTaskNumber(@RequestBody SearchLicensePlateTaskDetailRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateTaskInfoService.getLicensePlateTaskQuotaDetailsByTaskNumber(request.getTaskNumber()));
    }

    /**
     * 批量上传车辆信息
     *
     * @return
     */
    @ApiOperation(value = "批量上传车辆信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importVehicleDetail", method = RequestMethod.POST)
    public ResultResponse<List<ImportLicensePlateTaskVehicle>> importVehicleDetail(@RequestBody BaseImportFileUrlRequest request) {
        return ResultResponse.success(licensePlateTaskInfoService.getImportLicensePlateTaskVehicleList(request));
    }

    /**
     * 批量上传额度单
     *
     * @return
     */
    @ApiOperation(value = "批量上传额度单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importLicensePlateQuota", method = RequestMethod.POST)
    public ResultResponse<List<ImportLicensePlateQuota>> importLicensePlateQuotaList(@RequestBody BaseImportFileUrlRequest request) {
        return ResultResponse.success(licensePlateTaskInfoService.getImportLicensePlateQuotaList(request));
    }

    /**
     * 批量上传退牌车辆信息
     *
     * @return
     */
    @ApiOperation(value = "批量上传退牌车辆信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importRevocationVehicleDetail", method = RequestMethod.POST)
    public ResultResponse<ImportRevocationVehicleResponse> importRevocationVehicleDetail(@RequestBody BaseImportFileUrlRequest request) {
        return ResultResponse.success(licensePlateTaskInfoService.getImportRevocationVehicleDetail(request));
    }

    @ApiOperation(value = "导出车牌任务列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @PostMapping(value = "exportTaskList")
    public ResultResponse<Void> exportTaskList(@RequestBody @Validated SearchLicensePlateTaskInfoRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        //异步导出
        taskExecutor.execute(() -> licensePlateTaskInfoService.exportTaskList(request, tokenUserInfo));
        return ResultResponse.success();
    }
}
