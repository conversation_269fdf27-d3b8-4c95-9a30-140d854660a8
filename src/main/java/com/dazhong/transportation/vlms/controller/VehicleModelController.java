package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.ComboAutohomeVehicleModelRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleModelListRequest;
import com.dazhong.transportation.vlms.dto.request.UpsertVehicleModelRequest;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleModelInfoResponse;
import com.dazhong.transportation.vlms.service.IVehicleModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(value = "车型相关接口", tags = "车型相关接口")
@Slf4j
@RestController
@RequestMapping(value = "api/vehicleModel")
@LoginRequiredAnnotation(required = true)
public class VehicleModelController {

    @Autowired
    private IVehicleModelService vehicleModelService;

    @ApiOperation(value = "查询车型列表", notes = "获取所有车型的列表信息")
    @PostMapping("/searchVehicleModelList")
    public ResultResponse<PageResponse<VehicleModelInfoResponse>> searchVehicleModelList(@RequestBody @Valid SearchVehicleModelListRequest request) {
        return ResultResponse.success(vehicleModelService.searchVehicleModelList(request));
    }

    @ApiOperation(value = "查看车型详情", notes = "根据车型ID获取车型的详细信息")
    @GetMapping("/getVehicleModelDetail/{modelId}")
    public ResultResponse<VehicleModelInfoResponse> getVehicleModelDetail(@ApiParam(value = "车型ID", required = true) @PathVariable("modelId") Long modelId) {
        return ResultResponse.success(vehicleModelService.getVehicleModelDetail(modelId));
    }

    @ApiOperation(value = "新增车型", notes = "新增一款新的车型")
    @PostMapping("/createVehicleModel")
    public ResultResponse<String> createVehicleModel(@RequestBody @Valid UpsertVehicleModelRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        vehicleModelService.createVehicleModel(request, tokenUserInfo);
        return ResultResponse.success();
    }

    @ApiOperation(value = "修改车型", notes = "根据车型ID修改车型信息")
    @PostMapping("/updateVehicleModel")
    public ResultResponse<String> updateVehicleModel(@RequestBody @Valid UpsertVehicleModelRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        //校验参数
        if(request.getId() == null){
            throw new IllegalArgumentException("车型主键缺失");
        }
        vehicleModelService.updateVehicleModel(request, tokenUserInfo);
        return ResultResponse.success();
    }

    @ApiOperation(value = "获取车型下拉框", notes = "获取车型下拉框")
    @GetMapping("/comboVehicleModel")
    public ResultResponse<ComboResponse<Long, String>> comboVehicleModel(){
        ComboResponse<Long, String> response = vehicleModelService.comboVehicleModel();
        return ResultResponse.success(response);
    }

    /**
     * 获取车型编号下拉列表
     *
     * 功能说明：
     * 1. 从车型信息表中查询所有有效的车型记录
     * 2. 提取车型ID和对应的车型编号(vehicleModelNo)字段
     * 3. 过滤掉车型编号为空的记录，确保返回的数据都是有效的
     * 4. 对查询结果按车型编号进行去重处理，避免重复的车型编号出现在下拉列表中
     * 5. 去重时保留第一个匹配的记录（按当前排序规则：车型名称降序）
     * 6. 返回格式化的下拉列表数据，供前端下拉选择组件使用
     *
     * @return 车型编号下拉列表，key为车型ID(Long类型)，value为车型编号(String类型)
     */
    @ApiOperation(value = "获取车型编号下拉列表", notes = "返回车型ID和对应的车型编号，已去重处理，用于前端下拉选择组件")
    @GetMapping("/comboVehicleModelNo")
    public ResultResponse<ComboResponse<Long, String>> comboVehicleModelNo(){
        try {
            // 调用服务层方法获取车型编号下拉列表数据（已包含去重逻辑）
            ComboResponse<Long, String> result = vehicleModelService.comboVehicleModelNo();
            return ResultResponse.success(result);
        } catch (Exception e) {
            // 异常处理：记录错误日志并返回失败响应
            log.error("获取车型编号下拉列表失败：{}", e.getMessage(), e);
            return ResultResponse.businessFailed("获取车型编号下拉列表失败，请稍后重试");
        }
    }

    @ApiOperation(value = "获取汽车之家下拉框", notes = "获取汽车之家下拉框")
    @PostMapping("/comboAutohomeVehicleModel")
    public ResultResponse<ComboResponse<Long, String>> comboAutohomeVehicleModel(@RequestBody ComboAutohomeVehicleModelRequest request){
        ComboResponse<Long, String> response = vehicleModelService.comboAutohomeVehicleModel(request.getAutohomeModelName());
        return ResultResponse.success(response);
    }

    

    @ApiOperation(value = "获取汽车之家车型详情", notes = "根据汽车之家车型ID获取车型的详细信息")
    @GetMapping("/getVehicleModelDetailByAutohome/{autohomeModelId}")
    public ResultResponse<VehicleModelInfoResponse> getVehicleModelDetailByAutohome(@ApiParam(value = "汽车之家车型ID", required = true) @PathVariable("autohomeModelId") Long autohomeModelId){
        return ResultResponse.success(vehicleModelService.getVehicleModelDetailByAutohome(autohomeModelId));
    }


}
