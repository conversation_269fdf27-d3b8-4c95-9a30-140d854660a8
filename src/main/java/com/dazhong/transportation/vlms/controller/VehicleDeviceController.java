package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDeviceListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.IVehicleDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "车辆设备相关接口", tags = "车辆设备相关接口")
@Slf4j
@RestController
@RequestMapping(value = "api/vehicleDevice")
@LoginRequiredAnnotation(required = true)
public class VehicleDeviceController {

    @Autowired
    private IVehicleDeviceService vehicleDeviceService;


    @ApiOperation(value = "查询设备列表", notes = "获取所有设备的列表信息")
    @PostMapping("/searchVehicleDeviceList")
    public ResultResponse<PageResponse<VehicleDeviceInfoResponse>> searchVehicleDeviceList(@RequestBody SearchVehicleDeviceListRequest request){
        PageResponse<VehicleDeviceInfoResponse> response = vehicleDeviceService.searchVehicleDeviceList(request);
        return ResultResponse.success(response);
    }
}
