package com.dazhong.transportation.vlms.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.PurchaseApplyDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.PurchaseApplyDetailResponse;
import com.dazhong.transportation.vlms.dto.response.PurchaseApplyResponse;
import com.dazhong.transportation.vlms.dto.response.PurchaseIntentionResponse;
import com.dazhong.transportation.vlms.dto.response.PurchaseOccupiedResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.service.IVehiclePurchaseService;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 车辆采购接口
 * <AUTHOR>
 * @date 2024-12-30 13:12
 */
@Slf4j
@Api(value = "车辆采购接口", tags = "车辆采购接口")
@RestController
@RequestMapping(value = "api")
public class PurchaseController {

    @Autowired
    private IVehiclePurchaseService vehiclePurchaseService;

    @Autowired
    private IOrgService orgService;

    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "分页查询采购意向列表", httpMethod = "POST")
    @RequestMapping(value = "searchPurchaseIntention", method = RequestMethod.POST)
    public ResultResponse<PageResponse<PurchaseIntentionResponse>> searchPurchaseIntention(@RequestBody SearchPurchaseIntentionRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        PageResponse pageResponse = vehiclePurchaseService.searchPurchaseIntention(request,tokenUserInfo);
        return ResultResponse.success(pageResponse);
    }

    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询采购意向详情", httpMethod = "GET")
    @RequestMapping(value = "queryPurchaseIntention/{intentionNo}", method = RequestMethod.GET)
    public ResultResponse<PurchaseIntentionResponse> queryPurchaseIntention(@ApiParam(value = "采购意向单号 必传", required = true) @PathVariable String intentionNo){
        return vehiclePurchaseService.queryPurchaseIntention(intentionNo);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "导入采购意向", httpMethod = "POST")
    @RequestMapping(value = "importPurchaseIntention", method = RequestMethod.POST)
    public ResultResponse importPurchaseIntention(@RequestBody BaseImportFileUrlRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        try {
            ValidationUtils.validate(request);
            ResultResponse response = vehiclePurchaseService.importPurchaseIntention(request,tokenUserInfo);
            CommonUtils.deleteIfExists(request.getFilePath());
            return response;
        } catch (Exception exception) {
            CommonUtils.deleteIfExists(request.getFilePath());
            log.error("导入采购意向异常", exception);
            if (exception instanceof ServiceException) {
                throw new ServiceException(StrUtil.format("{},请重新上传", exception.getMessage()));
            }
            throw new ServiceException("导入采购意向失败,请重新上传");
        }
    }

    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "分页查询采购申请列表", httpMethod = "POST")
    @RequestMapping(value = "searchPurchaseApply", method = RequestMethod.POST)
    public ResultResponse<PageResponse<PurchaseApplyResponse>> searchPurchaseApply(@RequestBody SearchPurchaseApplyRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        PageResponse pageResponse = vehiclePurchaseService.searchPurchaseApply(request,tokenUserInfo);
        return ResultResponse.success(pageResponse);
    }

    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询采购申请详情", httpMethod = "GET")
    @RequestMapping(value = "queryPurchaseApplyDetail/{applyNo}", method = RequestMethod.GET)
    public ResultResponse<PurchaseApplyDetailResponse> queryPurchaseApplyDetail(@ApiParam(value = "采购申请编号 必传", required = true) @PathVariable String applyNo){
        PurchaseApplyDetailResponse response = vehiclePurchaseService.queryPurchaseApplyDetail(applyNo);
        return ResultResponse.success(response);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "查询采购详情额度占用列表", httpMethod = "GET")
    @RequestMapping(value = "queryPurchaseOccupiedList/{applyNo}", method = RequestMethod.GET)
    public ResultResponse<PurchaseOccupiedResponse> queryPurchaseOccupiedList(@ApiParam(value = "采购申请编号 必传", required = true) @PathVariable String applyNo){
        return vehiclePurchaseService.queryPurchaseOccupiedList(applyNo);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "释放采购详情额度占用", httpMethod = "POST")
    @RequestMapping(value = "releasePurchaseOccupied", method = RequestMethod.POST)
    public ResultResponse<PurchaseOccupiedResponse> releasePurchaseOccupied(@RequestBody ReleasePurchaseOccupiedRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return vehiclePurchaseService.releasePurchaseOccupied(request, tokenUserInfo);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "创建采购申请", httpMethod = "POST")
    @RequestMapping(value = "addPurchaseApply", method = RequestMethod.POST)
    public ResultResponse addPurchaseApply(@RequestBody AddPurchaseApplyRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        if (StringUtils.isBlank(tokenUserInfo.getDingTalkNum()) && request.getOperateType() == 2){
            return ResultResponse.businessFailed("当前用户未绑定钉钉账号，请先绑定钉钉账号");
        }
        List<PurchaseApplyDetailDto> applyDetailList = request.getApplyDetailList();
        if (CollectionUtil.isNotEmpty(applyDetailList)){
            for (PurchaseApplyDetailDto dto : applyDetailList) {
                if (dto.getQuantity() != null && dto.getQuantity() <= 0) {
                    return ResultResponse.businessFailed("采购详情数量不能小于1");
                }
                if (dto.getUnitPrice() != null && dto.getUnitPrice().compareTo(BigDecimal.ZERO) < 0){
                    return ResultResponse.businessFailed("单价不能小于0");
                }
                if (dto.getOtherCosts() != null && dto.getOtherCosts().compareTo(BigDecimal.ZERO) < 0){
                    return ResultResponse.businessFailed("其他费用不能小于0");
                }
                if (dto.getTotalPrice() != null && dto.getTotalPrice().compareTo(BigDecimal.ZERO) < 0){
                    return ResultResponse.businessFailed("总价不能小于0");
                }
            }
        }
        return vehiclePurchaseService.addPurchaseApply(request,tokenUserInfo);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "编辑采购申请", httpMethod = "POST")
    @RequestMapping(value = "updatePurchaseApply", method = RequestMethod.POST)
    public ResultResponse updatePurchaseApply(@RequestBody UpdatePurchaseApplyRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        if (StringUtils.isBlank(tokenUserInfo.getDingTalkNum()) && request.getOperateType() == 2){
            return ResultResponse.businessFailed("当前用户未绑定钉钉账号，请先绑定钉钉账号");
        }
        List<PurchaseApplyDetailDto> applyDetailList = request.getApplyDetailList();
        if (CollectionUtil.isNotEmpty(applyDetailList)){
            for (PurchaseApplyDetailDto dto : applyDetailList) {
                if (dto.getUnitPrice() != null && dto.getUnitPrice().compareTo(BigDecimal.ZERO) < 0){
                    return ResultResponse.businessFailed("单价不能小于0");
                }
                if (dto.getOtherCosts() != null && dto.getOtherCosts().compareTo(BigDecimal.ZERO) < 0){
                    return ResultResponse.businessFailed("其他费用不能小于0");
                }
                if (dto.getTotalPrice() != null && dto.getTotalPrice().compareTo(BigDecimal.ZERO) < 0){
                    return ResultResponse.businessFailed("总价不能小于0");
                }
                if (dto.getQuantity() != null && dto.getQuantity() <= 0) {
                    return ResultResponse.businessFailed("采购详情数量不能小于1");
                }
            }
        }
        return vehiclePurchaseService.updatePurchaseApply(request,tokenUserInfo);
    }

    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "修改采购申请状态(撤回和作废)", httpMethod = "POST")
    @RequestMapping(value = "editPurchaseApplyStatus", method = RequestMethod.POST)
    public ResultResponse editPurchaseApplyStatus(@RequestBody EditPurchaseApplyStatusRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return vehiclePurchaseService.editPurchaseApplyStatus(request,tokenUserInfo);
    }

    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "关闭采购申请", httpMethod = "POST")
    @RequestMapping(value = "closePurchaseApply", method = RequestMethod.POST)
    public ResultResponse closePurchaseApply(@RequestBody ClosePurchaseApplyRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return vehiclePurchaseService.closePurchaseApply(request,tokenUserInfo);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "获取采购申请明细行号列表", httpMethod = "GET")
    @RequestMapping(value = "queryApplyDetailsNoList/{applyNo}", method = RequestMethod.GET)
    public ResultResponse queryApplyDetailsNoList(@ApiParam(value = "采购申请编号 必传", required = true) @PathVariable String applyNo){
        return vehiclePurchaseService.queryApplyDetailsNoList(applyNo);
    }

    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "单笔采购收货", httpMethod = "POST")
    @RequestMapping(value = "purchasingReceiving", method = RequestMethod.POST)
    public ResultResponse purchasingReceiving(@RequestBody PurchaseReceivingRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return vehiclePurchaseService.purchasingReceiving(request,tokenUserInfo);
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "批量采购收货", httpMethod = "POST")
    @RequestMapping(value = "batchPurchasingReceiving", method = RequestMethod.POST)
    public ResultResponse batchPurchasingReceiving(@RequestBody BaseImportFileUrlRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        try {
            ValidationUtils.validate(request);
            ResultResponse response = vehiclePurchaseService.batchPurchasingReceiving(request,tokenUserInfo);
            CommonUtils.deleteIfExists(request.getFilePath());
            return response;
        } catch (Exception exception) {
            CommonUtils.deleteIfExists(request.getFilePath());
            log.error("批量采购收货信息异常", exception);
            if (exception instanceof ServiceException) {
                throw new ServiceException(StrUtil.format("{},请重新上传", exception.getMessage()));
            }
            throw new ServiceException("批量收货失败,请重新上传");
        }
    }


    @LoginRequiredAnnotation(required = true)
    @ApiOperation(value = "刷新采购审批结果", httpMethod = "POST")
    @RequestMapping(value = "refreshPurchasing", method = RequestMethod.POST)
    public ResultResponse refreshPurchasing(@RequestBody RefreshDingTalkApprovalRequest request,@TokenUserAnnotation TokenUserInfo tokenUserInfo){
        ValidationUtils.validate(request);
        return vehiclePurchaseService.refreshPurchasing(request,tokenUserInfo);
    }
}
