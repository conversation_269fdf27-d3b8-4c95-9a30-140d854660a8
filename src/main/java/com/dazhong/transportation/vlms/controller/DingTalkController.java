package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.DealDingTalkWorkFlowResultRequest;
import com.dazhong.transportation.vlms.dto.request.GetDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDingTalkOrderDetailRequest;
import com.dazhong.transportation.vlms.dto.response.DingTalkDepartmentTreeResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkWorkFlowResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.IVehicleApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(value = "钉钉审批流接口", tags = "审批流")
@RestController
@RequestMapping(value = "api/dingTalk")
public class DingTalkController {

    @Resource
    private IDingTalkService dingTalkService;

    @Autowired
    private IVehicleApplicationService iVehicleApplicationService;


    @ApiOperation(value = "查询钉钉审批流程", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/getDingTalkWorkFlowDetail", method = RequestMethod.POST)
    public ResultResponse<GetDingTalkWorkFlowResponse> getDingTalkWorkFlowDetail(@RequestBody GetDingTalkWorkFlowRequest request) {
        GetDingTalkWorkFlowResponse response = dingTalkService.getDingTalkWorkFlow(request);
        return ResultResponse.success(response);
    }

    @ApiOperation(value = "查询钉钉组织架构树", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/getDepartmentTree", method = RequestMethod.GET)
    public ResultResponse<DingTalkDepartmentTreeResponse> getDepartmentTree() {
        List<DingTalkDepartmentTreeResponse>  departmentTree = dingTalkService.queryDepartmentTree();
        return ResultResponse.success(departmentTree);
    }

    @ApiOperation(value = "查询登录人钉钉组织下拉列表", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/getDepartmentCombo", method = RequestMethod.GET)
    public ResultResponse<ComboResponse<Long, String>> getDepartmentCombo(@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ComboResponse<Long, String>  departmentTree = dingTalkService.queryDepartmentCombo(tokenUserInfo);
        return ResultResponse.success(departmentTree);
    }


    @ApiOperation(value = "手动处理钉钉回调结果", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/dealDingFlowResult", method = RequestMethod.POST)
    public ResultResponse dealDingFlowResult(@RequestBody DealDingTalkWorkFlowResultRequest request) {
        dingTalkService.dealDingFlowResult(request);
        return ResultResponse.success();
    }

    @ApiOperation(value = "同步钉钉单据详情", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/syncOrderDetail", method = RequestMethod.POST)
    public ResultResponse<String> syncOrderDetail(@RequestBody SyncDingTalkOrderDetailRequest request,
                                                 @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        try {
            log.info("开始同步钉钉单据详情，钉钉单据号：{}，操作人：{}", request.getDingTalkNo(), tokenUserInfo.getName());
            dingTalkService.syncDingTalkOrderDetail(request.getDingTalkNo());
            log.info("同步钉钉单据详情成功，钉钉单据号：{}", request.getDingTalkNo());
            return ResultResponse.success("同步钉钉单据详情成功");
        } catch (ServiceException e) {
            log.error("同步钉钉单据详情失败，钉钉单据号：{}，错误信息：{}", request.getDingTalkNo(), e.getMessage(), e);
            return ResultResponse.businessFailed("同步钉钉单据详情失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("同步钉钉单据详情异常，钉钉单据号：{}", request.getDingTalkNo(), e);
            return ResultResponse.businessFailed("同步钉钉单据详情异常，请联系管理员");
        }
    }
}
