package com.dazhong.transportation.vlms.controller;

import cn.hutool.core.bean.BeanUtil;
import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.DataOwnerDto;
import com.dazhong.transportation.vlms.dto.DataSupplierDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.DeleteDataDictRequest;
import com.dazhong.transportation.vlms.dto.request.EditDataDictRequest;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.dto.response.DataDictResponse;
import com.dazhong.transportation.vlms.dto.response.DataMaintainDictResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.DataDictInfo;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典接口
 * <AUTHOR>
 * @date 2024-12-23 09:41
 */
@Slf4j
@Api(value = "数据字典接口", tags = "数据字典")
@RestController
@RequestMapping(value = "api")
@LoginRequiredAnnotation(required = true)
public class DataDictController {

    @Resource
    private IDataDictService dataDictService;

    @ApiOperation(value = "根据系统编码查询字典配置信息", notes = "数据字典配置信息")
    @GetMapping(value = "queryDataMaintainDict/{systemCode}")
    public ResultResponse<DataDictResponse<Integer>> queryDataMaintainDict(@ApiParam(value = "系统编码", required = true) @PathVariable("systemCode") String systemCode){
        DataDictResponse<Integer> response = dataDictService.queryDataMaintainDict(systemCode);
        return ResultResponse.success(response);
    }

    @ApiOperation(value = "新增可维护字典配置信息")
    @PostMapping(value = "addDataMaintainDict")
    public ResultResponse addDataMaintainDict(@RequestBody @Validated EditDataDictRequest<Integer> request,  @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        dataDictService.addDataMaintainDict(request, tokenUserInfo);
        return ResultResponse.success();
    }

    @ApiOperation(value = "修改可维护字典配置信息")
    @PostMapping(value = "updateDataMaintainDict")
    public ResultResponse updateDataMaintainDict(@RequestBody @Validated EditDataDictRequest<Integer> request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        dataDictService.updateDataMaintainDict(request, tokenUserInfo);
        return ResultResponse.success();
    }


    @ApiOperation(value = "查询供应商列表", notes = "供应商列表")
    @GetMapping(value = "querySupplierList")
    public ResultResponse<DataMaintainDictResponse<DataSupplierDto>> querySupplierList(){
        DataMaintainDictResponse<DataSupplierDto> response = dataDictService.querySupplierList();
        return ResultResponse.success(response);
    }

    @ApiOperation(value = "新增供应商信息")
    @PostMapping(value = "createSupplierInfo")
    public ResultResponse createSupplierInfo(@RequestBody @Validated DataMaintainDictResponse<DataSupplierDto> request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        dataDictService.createSupplierInfo(request, tokenUserInfo);
        return ResultResponse.success();
    }

    @ApiOperation(value = "修改供应商信息")
    @PostMapping(value = "updateSupplierInfo")
    public ResultResponse updateSupplierInfo(@RequestBody @Validated DataMaintainDictResponse<DataSupplierDto> request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        dataDictService.updateSupplierInfo(request, tokenUserInfo);
        return ResultResponse.success();
    }

    @ApiOperation(value = "查询资产车辆拥有公司列表", notes = "资产车辆拥有公司列表")
    @GetMapping(value = "queryOwnerList")
    public ResultResponse<DataMaintainDictResponse<DataOwnerDto>> queryOwnerList(){
        DataMaintainDictResponse<DataOwnerDto> response = dataDictService.queryOwnerList();
        return ResultResponse.success(response);
    }


    /**
     * 新增资产车辆拥有公司信息
     *
     * 功能说明：
     * 1. 支持用户指定自定义ID值，如果不指定则系统自动生成
     * 2. 在保存数据前会检查指定的ID是否已被占用
     * 3. 如果ID已被占用，会抛出业务异常并返回明确错误信息
     * 4. 同时会检查公司名称是否重复，确保数据唯一性
     * 5. 所有验证通过后才会保存数据到数据库
     *
     * @param request 车辆拥有公司信息请求对象，包含待新增的公司信息列表
     * @param tokenUserInfo 当前登录用户信息，用于记录操作人
     * @return 操作结果，成功时返回success状态
     * @throws ServiceException 当ID已被占用或公司名称重复时抛出业务异常
     */
    @ApiOperation(value = "新增资产车辆拥有公司信息")
    @PostMapping(value = "createOwnerInfo")
    public ResultResponse createOwnerInfo(@RequestBody @Validated DataMaintainDictResponse<DataOwnerDto> request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        // 调用服务层方法处理新增逻辑，包含ID冲突检查和数据验证
        dataDictService.createOwnerInfo(request, tokenUserInfo);
        return ResultResponse.success();
    }

    //修改资产车辆拥有公司信息
    @ApiOperation(value = "修改资产车辆拥有公司信息")
    @PostMapping(value = "updateOwnerInfo")
    public ResultResponse updateOwnerInfo(@RequestBody @Validated DataMaintainDictResponse<DataOwnerDto> request, @TokenUserAnnotation TokenUserInfo tokenUserInfo){
        dataDictService.updateOwnerInfo(request, tokenUserInfo);
        return ResultResponse.success();
    }



    /**
     * 查询数据字典
     * @return
     */
    @ApiOperation(value = "分页查询数据字典", httpMethod = "POST")
    @RequestMapping(value = "searchDataDictList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<DataDictResponse>> searchDataDictList(@RequestBody PageRequest request) {
        ValidationUtils.validate(request);
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<DataDictInfo> list = dataDictService.queryDataDictList(request);
        PageInfo<DataDictInfo> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<DataDictResponse> responseList = new ArrayList<>();
        list.forEach(info -> {
            DataDictResponse response = BeanUtil.copyProperties(info,DataDictResponse.class);
            responseList.add(response);
        });
        PageResponse pageResponse = new PageResponse<>();
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return ResultResponse.success(pageResponse);
    }

    /**
     * 新增数据字典
     * @return
     */
    @ApiOperation(value = "新增数据字典", httpMethod = "POST")
    @RequestMapping(value = "addDataDict", method = RequestMethod.POST)
    public ResultResponse addDataDict(@RequestBody EditDataDictRequest<String> request) {
        ValidationUtils.validate(request);
        return dataDictService.addDataDict(request);
    }

    /**
     * 修改数据字典
     * @return
     */
    @ApiOperation(value = "修改数据字典", httpMethod = "POST")
    @RequestMapping(value = "updateDataDict", method = RequestMethod.POST)
    public ResultResponse updateDataDict(@RequestBody EditDataDictRequest<String> request) {
        ValidationUtils.validate(request);
        return dataDictService.updateDataDict(request);
    }

    /**
     * 查询数据字典详情
     *
     * @return
     */
    @ApiOperation(value = "查询数据字典详情", httpMethod = "GET")
    @RequestMapping(value = "queryDataDict/{dataCode}", method = RequestMethod.GET)
    public ResultResponse<DataDictResponse> queryDataDict(@ApiParam(value = "字典code", required = true) @PathVariable String dataCode) {
        DataDictResponse response = dataDictService.queryDataDictByCode(dataCode);
        return ResultResponse.success(response);
    }


    /**
     * 删除数据字段
     * @param request
     * @return
     */
    @ApiOperation(value = "删除数据字段", httpMethod = "POST")
    @RequestMapping(value = "deleteDataDict", method = RequestMethod.POST)
    public ResultResponse deleteDataDict(@RequestBody DeleteDataDictRequest request) {
        ValidationUtils.validate(request);
        return dataDictService.deleteDataDict(request);
    }
}
