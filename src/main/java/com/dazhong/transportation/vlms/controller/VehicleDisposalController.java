package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.DisposalDingTalkDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.DisposalApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetail;
import com.dazhong.transportation.vlms.service.IVehicleDisposalService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(value = "车辆处置相关接口", tags = "车辆处置")
@Slf4j
@RestController
@RequestMapping(value = "api/vehicleDisposal")
public class VehicleDisposalController {

    @Autowired
    private IVehicleDisposalService vehicleDisposalService;

    /**
     * 查询车辆处置任务列表
     *
     * @return 返回列表
     */
    @ApiOperation(value = "查询车辆处置申请单列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryVehicleDisposalList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<VehicleDisposalListDto>> queryList(
            @RequestBody SearchVehicleDisposalListRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleDisposalService.queryVehicleDisposalPageResponse(request, tokenUserInfo));
    }

    /**
     * 关联退牌任务
     *
     * @return 返回列表
     */
    @ApiOperation(value = "关联退牌任务", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "relateReturnLicenseTask", method = RequestMethod.POST)
    public ResultResponse<Void> relateReturnLicenseTask(@RequestBody UpdateDisposalApplicationRequest request,
            @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleDisposalService.relateReturnLicenseTask(request.getDisposalApplicationId(),
                request.getTaskNumber(), tokenUserInfo, true);
    }

    /**
     * 保存处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "保存处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "saveApplication", method = RequestMethod.POST)
    public ResultResponse<Long> saveApplication(@RequestBody SaveDisposalApplicationRequest request,
            @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleDisposalService.saveApplication(request, tokenUserInfo);
    }

    /**
     * 提交处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "提交处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "submitApplication", method = RequestMethod.POST)
    public ResultResponse<Long> submitApplication(@RequestBody SaveDisposalApplicationRequest request,
            @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleDisposalService.submitApplication(request, tokenUserInfo);
    }

    /**
     * 作废处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "作废处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "cancelApplication", method = RequestMethod.POST)
    public ResultResponse<Void> cancelApplication(@RequestBody BaseIdRequest request,
            @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleDisposalService.cancelApplication(request.getId(), tokenUserInfo);
    }

    /**
     * 撤回处置申请单
     *
     * @return 返回列表
     */
    @ApiOperation(value = "撤回处置申请单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "withdrawApplication", method = RequestMethod.POST)
    public ResultResponse<Void> withdrawApplication(@RequestBody BaseIdRequest request,
            @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return vehicleDisposalService.withdrawApplication(request.getId(), tokenUserInfo);
    }

    /**
     * 获取处置申请单详情
     *
     * @return 返回列表
     */
    @ApiOperation(value = "获取处置申请单详情", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "getApplicationDetail", method = RequestMethod.POST)
    public ResultResponse<DisposalApplicationDetailResponse> getDisposalDetail(@RequestBody BaseIdRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleDisposalService.queryDisposalDetail(request.getId()));
    }

    /**
     * 获取处置申请单钉钉回调详情
     *
     * @return 返回列表
     */
    @ApiOperation(value = "获取处置申请单钉钉回调详情", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryDisposalDingTalkDetailList", method = RequestMethod.POST)
    public ResultResponse<List<DisposalDingTalkDetailDto>> queryDisposalDingTalkDetailList(
            @RequestBody BaseIdRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(vehicleDisposalService.queryDisposalDingTalkDetailList(request.getId()));
    }

    /**
     * 批量上传处置车辆明细
     *
     * @return 处置车辆明细list
     */
    @ApiOperation(value = "批量上传处置车辆明细", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importDisposalVehicleDetail", method = RequestMethod.POST)
    public ResultResponse<List<ImportDisposalVehicleDetail>> importDisposalVehicleDetail(
            @RequestBody ImportDisposalVehicleInfoRequest request) {
        return ResultResponse.success(vehicleDisposalService.getDisposalVehicleDetailList(request));
    }

    /**
     * 保存二手车交易发票URL
     *
     * @param request
     * @param tokenUserInfo
     */
    @ApiOperation(value = "保存二手车交易发票URL", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "saveSecondhandCarInvoiceUrls", method = RequestMethod.POST)
    public ResultResponse<Void> saveSecondhandCarInvoiceUrls(@RequestBody SaveSecondhandCarInvoiceUrlsRequest request,
            @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        return vehicleDisposalService.saveSecondhandCarInvoiceUrls(request.getUrlList(), request.getDisposalId(),
                tokenUserInfo);
    }

    /**
     * 信息登记（导入）
     *
     * @param request
     * @param tokenUserInfo
     */
    @ApiOperation(value = "信息登记（导入）", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "importDisposalVehicleInfo", method = RequestMethod.POST)
    public ResultResponse<Void> importDisposalVehicleInfo(@RequestBody ImportDisposalVehicleInfoRequest request,
            @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        return vehicleDisposalService.importDisposalVehicleInfo(request, tokenUserInfo);
    }

}
