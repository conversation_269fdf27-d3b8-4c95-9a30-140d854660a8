package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.LicensePlateQuotaDto;
import com.dazhong.transportation.vlms.dto.LicensePlateQuotaOperateLogDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaTransactionOperateLogResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaTransactionRecordResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.ILicensePlateQuotaService;
import com.dazhong.transportation.vlms.service.ILicensePlateQuotaTransactionRecordService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Api(value = "车牌额度", tags = "车牌额度")
@Slf4j
@RestController
@RequestMapping(value = "api/licensePlateQuota")
public class LicensePlateQuotaController {

    @Autowired
    private ILicensePlateQuotaService licensePlateQuotaService;

    @Autowired
    private ILicensePlateQuotaTransactionRecordService licensePlateQuotaTransactionRecordService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 查询额度一览列表
     *
     * @param request 查询参数
     * @return 返回额度一览列表
     */
    @ApiOperation(value = "查询额度一览列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<LicensePlateQuotaDto>> queryList(@RequestBody SearchLicensePlateQuotaRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateQuotaService.queryPageResponse(request));
    }

    /**
     * 查询额度总数
     *
     * @param request 查询参数
     * @return 返回额度一览列表
     */
    @ApiOperation(value = "查询额度总数", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryTotalQuota", method = RequestMethod.POST)
    public ResultResponse<LicensePlateQuotaResponse> queryTotalQuota(@RequestBody SearchLicensePlateQuotaRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateQuotaService.queryTotalQuota(request));
    }

    /**
     * 调整额度总数
     *
     * @param request       调整参数
     * @param tokenUserInfo 登录用户信息
     * @return 返回结果
     */
    @ApiOperation(value = "调整额度总数", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "adjustTotalQuota", method = RequestMethod.POST)
    public ResultResponse<Void> adjustTotalQuota(@RequestBody AdjustTotalQuotaRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return licensePlateQuotaService.adjustTotalQuota(request, tokenUserInfo);
    }

    /**
     * 查询车牌额度日志接口
     *
     * @param request 查询参数
     * @return 返回车牌额度日志列表
     */
    @ApiOperation(value = "查询车牌额度日志接口", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryOperateLogList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<LicensePlateQuotaOperateLogDto>> queryOperateLogList(@RequestBody SearchLicensePlateQuotaOperateLogRequest request) {
        ValidationUtils.validate(request);
        if (null != request.getCreateTimeEnd()) {
            LocalDateTime localDateTime = request.getCreateTimeEnd().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            // 加一天
            localDateTime = localDateTime.plusDays(1);
            request.setCreateTimeEnd(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        return ResultResponse.success(licensePlateQuotaService.queryOperateLogPageResponse(request));
    }

    /**
     * 根据额度类型查询公司信息
     *
     * @param request 查询参数
     * @return 返回公司信息列表
     */
    @ApiOperation(value = "根据额度类型查询公司信息", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryOwnerInfoListByQuotaType", method = RequestMethod.POST)
    public ResultResponse<PageInfo<LicensePlateQuotaTransactionRecordResponse>> queryOwnerInfoListByQuotaType(@RequestBody SearchLicensePlateQuotaRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateQuotaService.queryOwnerInfoListByQuotaType(request.getQuotaType()));
    }

    /**
     * 查询额度流水单分页列表
     *
     * @param request 查询参数
     * @return 返回车牌额度日志列表
     */
    @ApiOperation(value = "查询额度流水单分页列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryTransactionRecordList", method = RequestMethod.POST)
    public ResultResponse<PageResponse<LicensePlateQuotaTransactionRecordResponse>> queryTransactionRecordList(@RequestBody SearchLicensePlateQuotaTransactionRecordRequest request) {
        ValidationUtils.validate(request);
        return ResultResponse.success(licensePlateQuotaTransactionRecordService.queryPageResponse(request));
    }

    @ApiOperation(value = "导出额度流水单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "exportTransactionRecord", method = RequestMethod.POST)
    public ResultResponse exportTransactionRecord(@RequestBody SearchLicensePlateQuotaTransactionRecordRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        //异步导出
        taskExecutor.execute(() -> licensePlateQuotaTransactionRecordService.exportTransactionRecord(request, tokenUserInfo));
        return ResultResponse.success();
    }

    /**
     * 编辑额度单
     *
     * @param request       查询参数
     * @param tokenUserInfo 登录用户信息
     * @return 返回车牌额度日志列表
     */
    @ApiOperation(value = "编辑额度单", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "updateQuotaTransactionRecord", method = RequestMethod.POST)
    public ResultResponse<Void> updateQuotaTransactionRecord(@RequestBody UpdateQuotaTransactionRecordRequest request, @TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ValidationUtils.validate(request);
        return licensePlateQuotaTransactionRecordService.updateQuotaTransactionRecord(request, tokenUserInfo);
    }

    /**
     * 查询额度单操作日志
     *
     * @param searchLogRequest 查询参数
     * @return 返回车牌额度日志列表
     */
    @ApiOperation(value = "查询额度单操作日志", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryTransactionRecordOperateLog", method = RequestMethod.POST)
    public ResultResponse<PageResponse<LicensePlateQuotaTransactionOperateLogResponse>> queryTransactionRecordOperateLog(@RequestBody SearchLogRequest searchLogRequest) {
        ValidationUtils.validate(searchLogRequest);
        return ResultResponse.success(licensePlateQuotaTransactionRecordService.queryOperateLogPageResponse(searchLogRequest));
    }

    /**
     * 查询额度单下拉列表
     *
     * @return 返回车牌额度日志列表
     */
    @ApiOperation(value = "查询额度单下拉列表", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "queryQuotaNumberCombo", method = RequestMethod.POST)
    public ResultResponse<ComboResponse<String, String>> queryQuotaNumberCombo(@RequestBody ComboQuotaNumberRequest comboQuotaNumberRequest) {
        return ResultResponse.success(licensePlateQuotaTransactionRecordService.queryQuotaNumberCombo(comboQuotaNumberRequest));
    }
}
