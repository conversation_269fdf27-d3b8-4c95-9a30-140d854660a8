package com.dazhong.transportation.vlms.thread;

import com.alibaba.fastjson.JSON;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.dto.DingTalkFlowNotifyDto;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DealDingTalkFlowResponseRunnable implements Runnable {

	private DingTalkFlowNotifyDto notifyDto;


	public DealDingTalkFlowResponseRunnable(DingTalkFlowNotifyDto notifyDto) {
		this.notifyDto  = notifyDto;
	}

	@Override
	public void run() {
		log.info("回调事件信息:{}", JSON.toJSONString(notifyDto));
		try {
			//车辆调拨、车辆转籍、车辆切换业务线
			if (Global.instance.dingTalkConfig.getVehicleAllocateProcessCode().equals(notifyDto.getProcessCode())
					|| Global.instance.dingTalkConfig.getVehicleModifyBusinessProcessCode().equals(notifyDto.getProcessCode())
					|| Global.instance.dingTalkConfig.getVehicleTransferProcessCode().equals(notifyDto.getProcessCode())) {
				Global.instance.vehicleApplicationService.dingTalkResultProcess(notifyDto.getProcessInstanceId(), notifyDto.getResult());
			}
			//车辆转固钉钉审批流CODE
			else if (Global.instance.dingTalkConfig.getVehicleTransferFixedProcessCode().equals(notifyDto.getProcessCode())) {
				Global.instance.transferFixedService.dingTalkResultProcess(notifyDto.getProcessInstanceId(), notifyDto.getResult());
			}
			//车辆采购钉钉审批流CODE
			else if (Global.instance.dingTalkConfig.getVehiclePurchaseProcessCode().equals(notifyDto.getProcessCode())) {
				Global.instance.vehiclePurchaseService.dingTalkResultProcess(notifyDto.getProcessInstanceId(), notifyDto.getResult());
			}
			//车辆处置钉钉审批流CODE
			else if (Global.instance.dingTalkConfig.getVehicleDisposalProcessCode().equals(notifyDto.getProcessCode()) ||
					Global.instance.dingTalkConfig.getVehicleScrapProcessCode().equals(notifyDto.getProcessCode()) ||
					Global.instance.dingTalkConfig.getVehicleBusinessSellProcessCode().equals(notifyDto.getProcessCode())) {
				Global.instance.vehicleDisposalService.dingTalkResultProcess(notifyDto.getProcessInstanceId(), notifyDto.getResult());
			}
			//车辆逆处置钉钉审批流CODE
			else if (Global.instance.dingTalkConfig.getVehicleReverseDisposalProcessCode().equals(notifyDto.getProcessCode())) {
				Global.instance.vehicleReverseDisposalService.dingTalkResultProcess(notifyDto.getProcessInstanceId(), notifyDto.getResult());
			}
		} catch (Exception e) {
			log.error("处理钉钉审批流回调信息异常", e);
		}
	}

}
