package com.dazhong.transportation.vlms.exception;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 错误码
 */
@AllArgsConstructor
@Getter
public enum ExceptionEnum {

    PARAM_ERR(-1001, "参数错误"),
    BUSINESS_FAIL(-1002, "业务异常"),
    EXCEPTION(-1003, "系统异常"),
    LOGIN_FAIL(-1004, "用户未登录"),
    SIGN_FAIL(-1005, "验签校验失败"),

    DING_TALK_FAIL(-1006,"钉钉审批流接口异常"),
    ;

    final int code;

    final String message;

}
