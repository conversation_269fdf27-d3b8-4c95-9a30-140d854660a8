package com.dazhong.transportation.vlms.exception;

import lombok.Getter;


public class ServiceException extends RuntimeException {

    @Getter
    private final ExceptionEnum exceptionEnum;

    public ServiceException(String message) {
        super(message);
        this.exceptionEnum = ExceptionEnum.BUSINESS_FAIL;
    }

    public ServiceException(ExceptionEnum resultCode) {
        super(resultCode.message);
        this.exceptionEnum = resultCode;
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }

    public Throwable doFillInStackTrace() {
        return super.fillInStackTrace();
    }
}
