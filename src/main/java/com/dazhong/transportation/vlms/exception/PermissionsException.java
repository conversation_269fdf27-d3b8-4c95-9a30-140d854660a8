package com.dazhong.transportation.vlms.exception;

import lombok.Getter;


public class PermissionsException extends RuntimeException {

    @Getter
    private final ExceptionEnum exceptionEnum;

    public PermissionsException(ExceptionEnum resultCode) {
        super(resultCode.message);
        this.exceptionEnum = resultCode;
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }

    public Throwable doFillInStackTrace() {
        return super.fillInStackTrace();
    }
}
