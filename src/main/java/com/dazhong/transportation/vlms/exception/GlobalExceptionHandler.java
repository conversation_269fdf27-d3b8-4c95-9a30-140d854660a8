package com.dazhong.transportation.vlms.exception;

import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * ServiceException异常
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    public ResultResponse handServiceException(ServiceException e) {
        return ResultResponse.businessFailed(e.getMessage());
    }

    /**
     * LoginException异常
     */
    @ExceptionHandler(PermissionsException.class)
    @ResponseBody
    public ResultResponse handLoginException(PermissionsException e) {
        return ResultResponse.exceptionFailed(e.getExceptionEnum());
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResultResponse handleArgumentException(MethodArgumentNotValidException e){
        // 只获取默认消息
        List<String> defaultMessages = e.getBindingResult().getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        String errorMessage = String.join("，", defaultMessages);
        return ResultResponse.businessFailed(errorMessage);
    }

    /**
     * 其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResultResponse handException(Exception e) {
        log.error(e.getMessage(), e);
        return ResultResponse.exceptionFailed(ExceptionEnum.EXCEPTION);
    }
}
