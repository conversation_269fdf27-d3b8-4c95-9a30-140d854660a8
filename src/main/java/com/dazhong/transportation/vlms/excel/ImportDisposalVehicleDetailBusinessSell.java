package com.dazhong.transportation.vlms.excel;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
@ApiModel(description = "导入处置车辆信息-商售申请单")
public class ImportDisposalVehicleDetailBusinessSell extends ImportDisposalVehicleDetail {

    @ExcelProperty(value = "牌照性质（手工）", index = 2)
    @ApiModelProperty(value = "牌照性质（手工）")
    private String licenseTypeManual;

    @ExcelProperty(value = "公里数", index = 3)
    @ApiModelProperty(value = "公里数")
    private BigDecimal mileage;

    @ExcelProperty(value = "车况", index = 4)
    @ApiModelProperty(value = "车况") 
    private String vehicleCondition;

    @ExcelProperty(value = "原值", index = 5)
    @ApiModelProperty(value = "原值")
    private BigDecimal originalValue;

    @ExcelProperty(value = "累计折旧", index = 6)
    @ApiModelProperty(value = "累计折旧")
    private BigDecimal accumulatedDepreciation;

    @ExcelProperty(value = "当月资产净值", index = 7)
    @ApiModelProperty(value = "当月资产净值")
    private BigDecimal currentMonthNetValue;

    @ExcelProperty(value = "最低出售价（元）", index = 8)
    @ApiModelProperty(value = "最低出售价（元）")
    private BigDecimal minimumSellingPrice;

    @ExcelProperty(value = "拍卖保留价（元）", index = 9)
    @ApiModelProperty(value = "拍卖保留价（元）")
    private BigDecimal auctionReservePrice;

    @ExcelProperty(value = "预计盈亏1", index = 10)
    @ApiModelProperty(value = "预计盈亏1")
    private BigDecimal expectedProfitLossOne;

    @ExcelProperty(value = "预计盈亏2", index = 11)
    @ApiModelProperty(value = "预计盈亏2")
    private BigDecimal expectedProfitLossTwo;
}
