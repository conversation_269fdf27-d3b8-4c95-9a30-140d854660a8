package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportVehicleDepreciationInfo {

    @ExcelProperty(value = "车架号", index = 0)
    @NotBlank(message = "车架号不能为空")
    private String vin;

    @ExcelProperty(value = "原值", index = 1)
    @NotNull(message = "原值不能为空")
    @Digits(integer = 8, fraction = 2, message = "原值精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "原值不能为负数")
    private BigDecimal originalAmount;

    @ExcelProperty(value = "折旧月", index = 2)
    @NotBlank(message = "折旧月不能为空")
    private String depreciationMonths;

    @ExcelProperty(value = "折旧开始日期", index = 3)
    @NotNull(message = "折旧开始日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "折旧开始日期格式不正确")
    private String depreciationStartDate;

    @ExcelProperty(value = "当前年月", index = 4)
    @NotBlank(message = "当前年月不能为空")
    private String currentMonth ;

    @ExcelProperty(value = "已折旧月", index = 5)
    @NotBlank(message = "已折旧月不能为空")
    private String accumulatedDepreciationMonths;

    @ExcelProperty(value = "已折旧金额", index = 6)
    @NotNull(message = "已折旧金额不能为空")
    @Digits(integer = 8, fraction = 2, message = "已折旧金额不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "已折旧金额不能为负数")
    private BigDecimal depreciationAmount;

    @ExcelProperty(value = "剩余残值金额", index = 7)
    @NotNull(message = "剩余残值金额不能为空")
    @Digits(integer = 8, fraction = 2, message = "剩余残值金额精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "剩余残值金额不能为负数")
    private BigDecimal remainingResidualValue;

}
