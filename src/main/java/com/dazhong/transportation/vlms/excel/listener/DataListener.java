package com.dazhong.transportation.vlms.excel.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.dazhong.transportation.vlms.exception.ServiceException;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Excel监听器
 *
 * <AUTHOR>
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class DataListener<T> extends AnalysisEventListener<T> {

	/**
	 * 使用hibernate的注解来进行验证
	 */
	private static Validator validator = Validation.byProvider(HibernateValidator.class).configure().failFast(true).buildValidatorFactory().getValidator();

	/**
	 * 缓存的数据列表
	 */
	private final List<T> dataList = new ArrayList<>();

	@Override
	public void invoke(T data, AnalysisContext analysisContext) {
		Set<ConstraintViolation<T>> constraintViolations = validator.validate(data);
		if (!constraintViolations.isEmpty()) {
			throw new ServiceException(StrUtil.format("第{}行数据校验失败:{}",analysisContext.readRowHolder().getRowIndex(), constraintViolations.iterator().next().getMessage()));
		}
		dataList.add(data);
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext analysisContext) {
//		log.info("读取excel成功");
	}

}
