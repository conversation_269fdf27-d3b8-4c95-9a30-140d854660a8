package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportAnnualInspectionExpiryDate {

    @ExcelProperty(value = "车架号", index = 0)
    @NotBlank(message = "车架号不能为空")
    private String vin;

    @ExcelProperty(value = "年检到日期", index = 1)
    @NotBlank(message = "年检到日期")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "年检到日期格式不正确")
    private String annualInspectionExpiryDate;

}
