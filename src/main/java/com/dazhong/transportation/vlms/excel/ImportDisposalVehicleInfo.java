package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ApiModel(description = "导入处置车辆信息")
public class ImportDisposalVehicleInfo {

    @ExcelProperty(value = "车架号", index = 0)
    @NotBlank(message = "车架号不能为空")
    @ApiModelProperty(value = "车架号", required = true)
    private String vin;

    @ExcelProperty(value = "实际售价(元)", index = 1)
    @ApiModelProperty(value = "实际售价(元)")
    private BigDecimal actualSellingPrice;

    @ExcelProperty(value = "实际净售价(元)", index = 2)
    @ApiModelProperty(value = "实际净售价(元)")
    private BigDecimal actualNetPrice;

    @ExcelProperty(value = "实际净售价差值(元)", index = 3)
    @ApiModelProperty(value = "实际净售价差值(元)")
    private BigDecimal netPriceDiff;

    @ExcelProperty(value = "实际出售损益", index = 4)
    @ApiModelProperty(value = "实际出售损益")
    private BigDecimal saleGainLoss;

    @ExcelProperty(value = "退役日期", index = 5)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "退役日期格式不正确")
    @ApiModelProperty(value = "退役日期")
    private String realRetirementDate;
}