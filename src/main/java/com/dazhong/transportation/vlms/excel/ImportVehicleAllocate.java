package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
@ApiModel(description = "导入车辆（调拨）信息")
public class ImportVehicleAllocate extends ImportVehicleApplication {

    @ExcelProperty(value = "实际运营公司（所属）修改为", index = 1)
    @ApiModelProperty(value = "实际运营公司（所属）修改为")
    private String ownOrganizationNameUpdate;

    @ExcelProperty(value = "实际运营公司（使用）修改为", index = 2)
    @ApiModelProperty(value = "实际运营公司（使用）修改为")
    private String usageOrganizationNameUpdate;
}
