package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
@ApiModel(description = "导入逆处置车辆信息")
public class ImportReverseDisposalVehicleDetail {

    @ExcelProperty(value = "车架号", index = 0)
    @NotBlank(message = "车架号不能为空")
    @ApiModelProperty(value = "车架号")
    private String vin;

    @ExcelProperty(value = "车牌号", index = 1)
    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "资产编号")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车型id", example = "3")
    private String vehicleModelId;

    @ApiModelProperty(value = "车型", example = "宝马320i", notes = "车辆的具体型号")
    private String vehicleModelName;

    @ApiModelProperty(value = "关联审批中处置单号", example = "CZ123456")
    private String disposalDocumentNo;

    @ApiModelProperty(value = "资产所属公司id", example = "10001")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产所属公司名", example = "公司A")
    private String assetCompanyName;

    @ApiModelProperty(value = "实际运营公司（所属）id", example = "1")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "实际运营公司（所属）名", example = "XYZ")
    private String ownOrganizationName;

    @ApiModelProperty(value = "实际运营公司（使用）id", example = "1")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "实际运营公司（使用）名", example = "XYZ")
    private String usageOrganizationName;

}
