package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportLicensePlateTaskVehicle {

    @ExcelProperty(value = "车架号", index = 0)
    @NotBlank(message = "车架号不能为空")
    private String vin;

    @ExcelProperty(value = "车牌号", index = 1)
    @NotBlank(message = "车牌号不能为空")
    private String licensePlate;

    @ExcelProperty(value = "车辆类型 (行驶证)", index = 2)
    @NotBlank(message = "使用性质不能为空")
    private String vehicleTypeRegistrationCardString;

    private Integer vehicleTypeRegistrationCard;

    @ExcelProperty(value = "使用性质 (行驶证)", index = 3)
    @NotBlank(message = "使用性质不能为空")
    private String usageIdRegistrationCardString;

    private Integer usageIdRegistrationCard;

    @ExcelProperty(value = "注册日期(行驶证)", index = 4)
    @NotBlank(message = "注册日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "注册日期格式不正确")
    private String registrationDateRegistrationCard;

    @ExcelProperty(value = "发证日期 (行驶证)", index = 5)
    @NotBlank(message = "发证日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "发证日期格式不正确")
    private String issuanceDateRegistrationCard;

    @ExcelProperty(value = "档案编号", index = 6)
    @NotBlank(message = "档案编号不能为空")
    private String fileNumber;

    @ExcelProperty(value = "强制报废日期 (行驶证)", index = 7)
    @NotBlank(message = "强制报废日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "强制报废日期格式不正确")
    private String retirementDateRegistrationCard;

    @ExcelProperty(value = "年检到期日 (行驶证)", index = 8)
    @NotBlank(message = "年检到期日不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "年检到期日格式不正确")
    private String annualInspectionDueDateRegistrationCard;

    @ExcelProperty(value = "额度单打印时间", index = 9)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "额度单打印时间格式不正确")
    private String quotaPrintDate;

    @ExcelProperty(value = "额度编号", index = 10)
    private String quotaNumber;
}
