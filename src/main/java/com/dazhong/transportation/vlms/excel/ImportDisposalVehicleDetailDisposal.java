package com.dazhong.transportation.vlms.excel;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容样式
@ApiModel(description = "导入处置车辆信息-处置申请单")
public class ImportDisposalVehicleDetailDisposal extends ImportDisposalVehicleDetail {

    @ExcelProperty(value = "市场预估价（元）", index = 2)
    @ApiModelProperty(value = "市场预估价（元）")
    private BigDecimal estimatedMarketPrice;

    @ExcelProperty(value = "起拍价（元）", index = 3)
    @ApiModelProperty(value = "起拍价（元）")
    private BigDecimal startingPrice;

    @ExcelProperty(value = "保留价(元)", index = 4)
    @ApiModelProperty(value = "保留价(元)")
    private BigDecimal minimumAcceptablePrice;

    @ExcelProperty(value = "出售原因", index = 5)
    @ApiModelProperty(value = "出售原因")
    private String saleReason;

    @ExcelProperty(value = "实际出售损益", index = 6)
    @ApiModelProperty(value = "实际出售损益")
    private BigDecimal saleGainLoss;
}
