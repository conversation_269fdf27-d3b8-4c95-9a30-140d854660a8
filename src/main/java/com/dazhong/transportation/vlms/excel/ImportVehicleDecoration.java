package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportVehicleDecoration {

    @ExcelProperty(value = "车架号", index = 0)
    @NotBlank(message = "车架号 不能为空")
    String vin;

    @ExcelProperty(value = "装潢类型", index = 1)
    @NotBlank(message = "装潢类型 不能为空")
    String decorationTypeDesc;

    @ExcelProperty(value = "装潢内容", index = 2)
    @NotBlank(message = "装潢内容 不能为空")
    String decorationContent;

    @NotNull(message = "基本价格不能为空")
    @Digits(integer = 8, fraction = 2, message = "价格精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "价格不能为负数")
    private BigDecimal basePrice;

}
