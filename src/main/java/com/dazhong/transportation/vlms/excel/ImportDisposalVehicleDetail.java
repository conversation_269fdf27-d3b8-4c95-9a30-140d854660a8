package com.dazhong.transportation.vlms.excel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
@ApiModel(description = "导入处置车辆信息")
public class ImportDisposalVehicleDetail {

    @ExcelProperty(value = "车架号", index = 0)
    @ApiModelProperty(value = "车架号")
    private String vin;

    @ExcelProperty(value = "车牌号", index = 1)
    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "资产编号")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车型id", example = "3")
    private String vehicleModelId;

    @ApiModelProperty(value = "车型", example = "宝马320i", notes = "车辆的具体型号")
    private String vehicleModelName;

    @ApiModelProperty(value = "资产所属公司id", example = "10001")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产所属公司名", example = "公司A")
    private String assetCompanyName;

    @ApiModelProperty(value = "实际运营公司（所属）id", example = "1")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "实际运营公司（所属）名", example = "XYZ")
    private String ownOrganizationName;

    @ApiModelProperty(value = "实际运营公司（使用）id", example = "1")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "实际运营公司（使用）名", example = "XYZ")
    private String usageOrganizationName;

    @ApiModelProperty(value = "所属车队")
    private String belongingTeam;

    @ApiModelProperty(value = "投产日期（旧系统字段）")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "投产日期格式不正确")
    private String startDate;

    @ApiModelProperty(value = "使用年限")
    private Integer usageAgeLimit;

    @ApiModelProperty(value = "使用期限（月）", example = "5")
    private Integer usageMonthLimit = 1;

    @ApiModelProperty(value = "上牌日期（旧系统字段）", example = "2025-02-17")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "上牌日期格式不正确")
    private String licenseDate;

    @ApiModelProperty(value = "到填表日已用月数", example = "36")
    private Integer usedMonths;

    @ApiModelProperty(value = "使用性质 (行驶证)")
    private Integer usageIdRegistrationCard;

    /**
     * 商售单字段
     */
    @ApiModelProperty(value = "额度类型", example = "10")
    private Integer quotaType;

    @ApiModelProperty(value = "商品车型id", example = "10")
    private Integer vehicleAbbreviationId;

    @ApiModelProperty(value = "颜色id", example = "10")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "msrp", example = "10")
    private String msrp;
}
