package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
@ApiModel(description = "导入处置车辆（切换业务线）信息")
public class ImportVehicleModifyBusiness extends ImportVehicleApplication {

    @ExcelProperty(value = "条线修改为", index = 1)
    @ApiModelProperty(value = "条线修改为")
    private String productLineUpdateString;

    @ExcelProperty(value = "业务类型修改为", index = 2)
    @ApiModelProperty(value = "业务类型修改为")
    private String businessLineUpdateString;
}
