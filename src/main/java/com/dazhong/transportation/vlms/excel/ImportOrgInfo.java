package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportOrgInfo {

    @ExcelProperty(value = "id", index = 0)
    Long id;

    @ExcelProperty(value = "代码", index = 1)
    String orgCode;

    @ExcelProperty(value = "名称", index = 2)
    String orgName;

    @ExcelProperty(value = "父节点标识", index = 3)
    String pgUid;

    @ExcelProperty(value = "类型 0 公司 1 部门", index = 4)
    int orgType;

    @ExcelProperty(value = "状态 2是启用1是停用'", index = 5)
    int vStatus;

    @ExcelProperty(value = "唯一标识", index = 6)
    String vgUid;

}
