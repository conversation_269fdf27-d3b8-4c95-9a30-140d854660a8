package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportAdminVehicleOtherInfo {

    @ExcelProperty(value = "车架号", index = 0)
    @NotBlank(message = "车架号不能为空")
    @Size(max = 30, message = "车架号长度不能大于30")
    private String vin;

    @ExcelProperty(value = "营运类别", index = 1)
    private String operationCategory;

    @ExcelProperty(value = "营运证号", index = 2)
    @Size(max = 50, message = "营运证号长度不能大于50")
    private String operationCertificateNumber;

    @ExcelProperty(value = "是否营运", index = 3)
    private String isOperating;

    @ExcelProperty(value = "号牌种类", index = 4)
    private String licensePlateType;

    @ExcelProperty(value = "管辖区域", index = 5)
    private String jurisdictionalDistrict;

    @ExcelProperty(value = "车辆出厂日期 (产证)", index = 6)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "车辆出厂日期格式不正确")
    private String vehicleManufacturingDate;

    @ExcelProperty(value = "发证日期 (产证)", index = 7)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "发证日期格式不正确")
    private String issuanceDate;

    @ExcelProperty(value = "产证编号", index = 8)
    private String certificateNumber;

    @ExcelProperty(value = "资产所属公司", index = 9)
    private String assetCompanyName;

    @ExcelProperty(value = "实际运营公司（所属）", index = 10)
    private String ownOrganizationName;

    @ExcelProperty(value = "实际运营公司（使用）", index = 11)
    private String usageOrganizationName;

    @ExcelProperty(value = "使用年限", index = 12)
    @Pattern(regexp = "^[0-9]+$", message = "使用年限只能输入整数")
    private String usageYears;

    @ExcelProperty(value = "折旧年限", index = 13)
    @Pattern(regexp = "^[0-9]+$", message = "折旧年限只能输入整数")
    private String depreciationYears;

    @ExcelProperty(value = "裸车价", index = 14)
    @Digits(integer = 8, fraction = 2, message = "裸车价精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "裸车价不能为负数")
    private BigDecimal nakedCarPrice;

    @ExcelProperty(value = "购置税", index = 15)
    @Digits(integer = 8, fraction = 2, message = "购置税精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "购置税不能为负数")
    private BigDecimal purchaseTax;

    @ExcelProperty(value = "牌照费", index = 16)
    @Digits(integer = 8, fraction = 2, message = "牌照费精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "牌照费不能为负数")
    private BigDecimal licensePlateFee;

    @ExcelProperty(value = "上牌杂费", index = 17)
    @Digits(integer = 8, fraction = 2, message = "上牌杂费精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "上牌杂费不能为负数")
    private BigDecimal registrationMiscellaneousFee;

    @ExcelProperty(value = "装潢费", index = 18)
    @Digits(integer = 8, fraction = 2, message = "装潢费精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "装潢费不能为负数")
    private BigDecimal decorationFee;

    @ExcelProperty(value = "购置总价", index = 19)
    @Digits(integer = 10, fraction = 2, message = "购置总价精度不能超过10位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "购置总价不能为负数")
    private BigDecimal purchaseTotalPrice;

    @ExcelProperty(value = "资产状态", index = 20)
    private String propertyStatus;

    @ExcelProperty(value = "条线", index = 21)
    private String productLine;

    @ExcelProperty(value = "业务线", index = 22)
    private String businessLine;

    @ExcelProperty(value = "强制报废日期 (行驶证)", index = 23)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "强制报废日期格式不正确")
    private String retirementDateRegistrationCard;

    @ExcelProperty(value = "注册日期(行驶证)", index = 24)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "注册日期格式不正确")
    private String registrationDateRegistrationCard;

    @ExcelProperty(value = "发动机号", index = 25)
    private String engineNo;

    @ExcelProperty(value = "车身颜色", index = 26)
    private String vehicleBodyColor;

    @ExcelProperty(value = "车型ID", index = 27)
    private Long vehicleModelId;

    @ExcelProperty(value = "实际报废日期", index = 28)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "实际报废日期格式不正确")
    private String realRetirementDate;

    @ExcelProperty(value = "发动机型号（车辆）", index = 29)
    private String engineModel;

}
