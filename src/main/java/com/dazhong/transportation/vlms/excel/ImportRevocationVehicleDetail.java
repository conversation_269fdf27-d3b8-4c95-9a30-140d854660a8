package com.dazhong.transportation.vlms.excel;

import java.util.Date;

import javax.validation.constraints.NotBlank;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
@ApiModel(description = "导入退牌车辆信息")
public class ImportRevocationVehicleDetail {

    @ExcelProperty(value = "车牌号", index = 0)
    @NotBlank(message = "车牌号不能为空")
    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ExcelProperty(value = "额度是否退还", index = 1)
    @ApiModelProperty(value = "退还额度类型 1-退还 2-不退还 3-不涉及")
    private String returnQuotaTypeString;

    @ExcelProperty(value = "退牌时间", index = 2)
    @ApiModelProperty(value = "退牌时间", example = "2023-10-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licensePlateWithdrawalDate;

    @ExcelProperty(value = "额度单打印时间", index = 3)
    @ApiModelProperty(value = "额度单打印时间(yyyy-mm-dd)", example = "2023-01-02")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date quotaPrintDate;

    @ExcelProperty(value = "额度编号", index = 4)
    @ApiModelProperty(value = "额度编号", example = "Q001")
    private String quotaNumber;
}
