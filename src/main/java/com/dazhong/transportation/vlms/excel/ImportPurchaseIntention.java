package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-01-15 14:22
 */
@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportPurchaseIntention {

    @ExcelProperty(value = "合同号", index = 0)
    @NotBlank(message = "合同号不能为空")
    @Size(max = 64, message = "合同号长度不能大于64")
    private String contractNo;

    @ExcelProperty(value = "合同所属公司", index = 1)
    @NotBlank(message = "合同所属公司不能为空")
    private String assetOwner;

    @ExcelProperty(value = "合同开始日期", index = 2)
    @NotBlank(message = "合同开始日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "合同开始日期格式不正确")
    private String contractStartDate;

    @ExcelProperty(value = "合同结束日期", index = 3)
    @NotBlank(message = "合同结束日期日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "合同结束日期格式不正确")
    private String contractEndDate;

    @ExcelProperty(value = "客户信息", index = 4)
    @Size(max = 30, message = "客户信息长度不能大于30")
    private String customerUser;

    @ExcelProperty(value = "车型", index = 5)
    @NotBlank(message = "车型名称不能为空")
    private String vehicleModelName;

    @ExcelProperty(value = "数量(台)", index = 6)
    @NotNull(message = "数量(台)不能为空")
    private Integer quantity;

    @ExcelProperty(value = "车身颜色", index = 7)
    @NotBlank(message = "车身颜色不能为空")
    private String vehicleBodyColor;

    @ExcelProperty(value = "内饰颜色", index = 8)
    private String vehicleInteriorColor;

    @ExcelProperty(value = "指导价(元)", index = 9)
    @NotNull(message = "指导价不能为空")
    @Digits(integer = 8, fraction = 2, message = "指导价精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "指导价不能为负数")
    private BigDecimal guidePrice;

    @ExcelProperty(value = "客户期望到车日期", index = 10)
    @NotBlank(message = "客户期望到车日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "客户期望到车日期格式不正确")
    private String expectedArrivalDate;

    @ExcelProperty(value = "约定最晚到车日期", index = 11)
    @NotBlank(message = "约定最晚到车日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "约定最晚到车日期格式不正确")
    private String expectedArrivalLastDate;

    @ExcelProperty(value = "合同约定牌照属性", index = 12)
    private String licensePlateAttribute;

    @ExcelProperty(value = "合同约定牌照所属地", index = 13)
    private String licensePlateLocation;

    @ExcelProperty(value = "装潢需求", index = 14)
    private String decorationDemand;

    @ExcelProperty(value = "销售姓名", index = 15)
    @NotBlank(message = "销售姓名不能为空")
    private String saleName;

    @ExcelProperty(value = "采购意向备注", index = 16)
    private String purchaseRemark;

}
