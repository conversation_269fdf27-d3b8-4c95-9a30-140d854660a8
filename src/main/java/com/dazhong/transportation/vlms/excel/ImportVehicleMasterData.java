package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportVehicleMasterData {

    @ExcelProperty(value = "车牌号", index = 0)
    @NotBlank(message = "车牌号不能为空")
    @Size(max = 20, message = "车牌号长度不能大于20")
    private String licensePlate;

    @ExcelProperty(value = "发动机号", index = 1)
    @NotBlank(message = "发动机号不能为空")
    @Size(max = 30, message = "发动机号长度不能大于30")
    private String engineNo;

    @ExcelProperty(value = "车架号", index = 2)
    @NotBlank(message = "车架号不能为空")
    @Size(max = 30, message = "车架号长度不能大于30")
    private String vin;

    @ExcelProperty(value = "车身颜色", index = 3)
    @NotBlank(message = "颜色不能为空")
    private String vehicleBodyColor;

    @ExcelProperty(value = "合同形式", index = 4)
    @NotBlank(message = "合同形式不能为空")
    private String contractType;

    @ExcelProperty(value = "营运类别", index = 5)
    @NotBlank(message = "营运类别不能为空")
    private String operationCategory;

    @ExcelProperty(value = "车型", index = 6)
    @NotBlank(message = "车型不能为空")
    private String vehicleModelName;

    @ExcelProperty(value = "营运证号", index = 7)
    @NotBlank(message = "营运证号不能为空")
    private String operationCertificateNumber;

    @ExcelProperty(value = "营运/非营运 ", index = 8)
    @NotBlank(message = "营运/非营运不能为空")
    private String isOperating;

    @ExcelProperty(value = "号牌种类", index = 9)
    @NotBlank(message = "号牌种类不能为空")
    private String licensePlateType;

    @ExcelProperty(value = "管辖区县", index = 10)
    @NotBlank(message = "管辖区县不能为空")
    private String jurisdictionalDistrict;

    @ExcelProperty(value = "投产日期（旧系统字段）", index = 11)
    @NotBlank(message = "投产日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "投产日期格式不正确")
    private String startDate;

    @ExcelProperty(value = "上牌日期（旧系统字段）", index = 12)
    @NotBlank(message = "上牌日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "上牌日期格式不正确")
    private String licenseDate;

    @ExcelProperty(value = "车辆出厂日期 (产证)", index = 13)
    @NotBlank(message = "车辆出厂日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "车辆出厂日期格式不正确")
    private String productDate;

    @ExcelProperty(value = "购买日期（旧系统字段）", index = 14)
    @NotBlank(message = "购买日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "购买日期格式不正确")
    private String purchaseDate;

    @ExcelProperty(value = "机动车所有者", index = 15)
    @NotBlank(message = "机动车所有者不能为空")
    private String owner;

    @ExcelProperty(value = "所有人", index = 16)
    @NotBlank(message = "所有人不能为空")
    private String ownerName;

    @ExcelProperty(value = "档案编号", index = 17)
    private String fileNumber;

    @ExcelProperty(value = "注册登记证编号", index = 18)
    @NotBlank(message = "注册登记证编号不能为空")
    private String vehicleRegisterNo;

    @ExcelProperty(value = "强制报废日期 (产证)", index = 19)
    @NotBlank(message = "强制报废日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "强制报废日期格式不正确")
    private String retirementDate;

    @ExcelProperty(value = "投运日期（旧系统字段）", index = 20)
    @NotBlank(message = "投运日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "投运日期格式不正确")
    private String operationStartDate;

    @ExcelProperty(value = "企业名称", index = 21)
    @NotBlank(message = "企业名称不能为空")
    private String companyName;

    /**
     * 是否有产权证 1-是 2-否
     */
    @ExcelProperty(value = "是否有产权证", index = 22)
    @NotBlank(message = "是否有产权证不能为空")
    private String hasRight;

    @ExcelProperty(value = "资产机构-实际运营公司（所属）", index = 23)
    @NotBlank(message = "资产机构-实际运营公司（所属）不能为空")
    private String ownOrganizationName;

    @ExcelProperty(value = "使用机构-实际运营公司（使用）", index = 24)
    @NotBlank(message = "使用机构-实际运营公司（使用）不能为空")
    private String usageOrganizationName;

    @ExcelProperty(value = "获得方式", index = 25)
    @NotBlank(message = "获得方式不能为空")
    private String obtainWay;

    @ExcelProperty(value = "过入单位", index = 26)
    private String fromCompany;

    @ExcelProperty(value = "使用性质 (行驶证)", index = 27)
    @NotBlank(message = "使用性质不能为空")
    private String usageIdRegistrationCard;

    @ExcelProperty(value = "供应商", index = 28)
    @NotBlank(message = "供应商不能为空")
    private String supplier;

    @ExcelProperty(value = "使用年限", index = 29)
    @NotBlank(message = "使用年限不能为空")
    @Pattern(regexp = "^[0-9]+$", message = "使用年限只能输入整数")
    private String usageYears;

    @ExcelProperty(value = "裸车价", index = 30)
    @NotNull(message = "裸车价不能为空")
    @Digits(integer = 8, fraction = 2, message = "裸车价精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "裸车价不能为负数")
    private BigDecimal purchasePrice;

    @ExcelProperty(value = "购置税", index = 31)
    @NotNull(message = "购置税不能为空")
    @Digits(integer = 8, fraction = 2, message = "购置税精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "购置税不能为负数")
    private BigDecimal purchaseTax;

    @ExcelProperty(value = "牌照费", index = 32)
    @NotNull(message = "牌照费不能为空")
    @Digits(integer = 8, fraction = 2, message = "牌照费精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "牌照费不能为负数")
    private BigDecimal licensePlateFee;

    @ExcelProperty(value = "上牌杂费", index = 33)
    @NotNull(message = "上牌杂费不能为空")
    @Digits(integer = 8, fraction = 2, message = "上牌杂费精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "上牌杂费不能为负数")
    private BigDecimal registrationMiscellaneousFee;

    @ExcelProperty(value = "装潢费", index = 34)
    @NotNull(message = "装潢费不能为空")
    @Digits(integer = 8, fraction = 2, message = "装潢费精度不能超过8位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "装潢费不能为负数")
    private BigDecimal decorationFee;

    @ExcelProperty(value = "购置总价", index = 35)
    @NotNull(message = "购置总价不能为空")
    @Digits(integer = 10, fraction = 2, message = "购置总价精度不能超过10位，小数点后最多2位")
    @DecimalMin(value = "0.00", message = "购置总价不能为负数")
    private BigDecimal purchaseTotalPrice;

    @ExcelProperty(value = "折旧年限", index = 36)
    @NotBlank(message = "折旧年限不能为空")
    @Pattern(regexp = "^[0-9]+$", message = "折旧年限只能输入整数")
    private String depreciationYears;

    @ExcelProperty(value = "条线", index = 37)
    @NotBlank(message = "条线不能为空")
    private String productLine;

    @ExcelProperty(value = "业务类型", index = 38)
    @NotBlank(message = "业务类型不能为空")
    private String businessLine;
}
