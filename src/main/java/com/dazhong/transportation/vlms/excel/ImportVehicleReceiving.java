package com.dazhong.transportation.vlms.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2025-01-15 14:22
 */
@Getter
@Setter
@ToString
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容样式
public class ImportVehicleReceiving {

    @ExcelProperty(value = "采购申请钉钉号", index = 0)
    @NotBlank(message = "采购申请钉钉号不能为空")
    private String approvalNumber;

    @ExcelProperty(value = "采购申请明细行号", index = 1)
    @NotBlank(message = "采购申请明细行号不能为空")
    private String applyDetailsNo;

    @ExcelProperty(value = "车辆资产所属公司", index = 2)
    @NotBlank(message = "资产所属公司不能为空")
    private String assetOwner;

    @ExcelProperty(value = "车架号", index = 3)
    @NotBlank(message = "车架号不能为空")
    @Size(max = 17,min = 17, message = "车架号长度只能17位")
    private String vin;

    @ExcelProperty(value = "发动机号", index = 4)
    @NotBlank(message = "发动机号不能为空")
    @Size(max = 30, message = "发动机号长度不能大于30")
    private String engineNo;

    @ExcelProperty(value = "车型", index = 5)
    @NotBlank(message = "车型不能为空")
    private String vehicleModelName;

    @ExcelProperty(value = "车身颜色", index = 6)
    @NotBlank(message = "车身颜色不能为空")
    private String bodyColor;

    @ExcelProperty(value = "内饰颜色", index = 7)
    @NotBlank(message = "内饰颜色不能为空")
    @Size(max = 20, message = "内饰颜色长度不能大于20")
    private String interiorColor;

    @ExcelProperty(value = "下单日期", index = 8)
    @NotBlank(message = "下单日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "下单日期格式不正确")
    private String orderDate;

    @ExcelProperty(value = "收货日期", index = 9)
    @NotBlank(message = "收货日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "收货日期格式不正确")
    private String receiptDate;

    @ExcelProperty(value = "供应商", index = 10)
    @NotBlank(message = "供应商不能为空")
    private String supplier;

    @ExcelProperty(value = "是否回购", index = 11)
    @NotBlank(message = "是否回购不能为空")
    private String isRepurchase;

    @ExcelProperty(value = "回购时间", index = 12)
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}", message = "回购时间格式不正确")
    private String repurchaseDate;

    @ExcelProperty(value = "回购要求", index = 13)
    @Size(max = 255, message = "回购要求长度不能大于255")
    private String repurchaseRequirements;
}
