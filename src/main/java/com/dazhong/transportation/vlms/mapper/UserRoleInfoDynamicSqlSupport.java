package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class UserRoleInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_role_info")
    public static final UserRoleInfo userRoleInfo = new UserRoleInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.id")
    public static final SqlColumn<Long> id = userRoleInfo.id;

    /**
     * Database Column Remarks:
     *   用户id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.user_id")
    public static final SqlColumn<Long> userId = userRoleInfo.userId;

    /**
     * Database Column Remarks:
     *   角色id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.role_id")
    public static final SqlColumn<Long> roleId = userRoleInfo.roleId;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = userRoleInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.create_time")
    public static final SqlColumn<Date> createTime = userRoleInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = userRoleInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.create_oper_name")
    public static final SqlColumn<String> createOperName = userRoleInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.update_time")
    public static final SqlColumn<Date> updateTime = userRoleInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = userRoleInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_role_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = userRoleInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_role_info")
    public static final class UserRoleInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> userId = column("user_id", JDBCType.BIGINT);

        public final SqlColumn<Long> roleId = column("role_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public UserRoleInfo() {
            super("t_user_role_info");
        }
    }
}