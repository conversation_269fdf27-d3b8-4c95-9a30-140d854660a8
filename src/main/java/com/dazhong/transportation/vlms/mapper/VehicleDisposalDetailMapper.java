package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleDisposalDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.dazhong.transportation.vlms.model.VehicleDisposalDetail;

@Mapper
public interface VehicleDisposalDetailMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<VehicleDisposalDetail> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, disposalId, vehicleAssetId, vin, licensePlate, vehicleModelId, assetCompanyId, assetCompanyName, ownOrganizationId, ownOrganizationName, usageOrganizationId, usageOrganizationName, belongingTeam, startDate, realRetirementDate, licenseDate, usageMonthLimit, usageIdRegistrationCard, usedMonths, estimatedMarketPrice, startingPrice, estimatedNetSaleValue, minimumAcceptablePrice, handlingFee, saleReason, actualSaleReason, usageNature, disposalType, estimatedVehicleLoss, governmentSubsidyAmount, disposalReason, originalValue, accumulatedDepreciation, netValue, financialEvaluation, actualSellingPrice, actualNetPrice, netPriceDiff, saleGainLoss, actualSoldQuantity, quantityDiff, actualSaleDesc, estimatedScrapLossAmount, isInsuranceAuction, actualAuctionAmount, actualScrapProfitLoss, licenseTypeManual, quotaType, vehicleAbbreviationId, vehicleColorId, msrp, mileage, vehicleCondition, minimumSellingPrice, auctionReservePrice, expectedProfitLossOne, expectedProfitLossTwo, currentMonthNetValue, secondhandCarInvoiceUrl, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName, fileUrl, actualSaleFileUrl);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleDisposalDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleDisposalDetailResult")
    Optional<VehicleDisposalDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleDisposalDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="disposal_id", property="disposalId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_name", property="assetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="own_organization_name", property="ownOrganizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="usage_organization_name", property="usageOrganizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="belonging_team", property="belongingTeam", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="real_retirement_date", property="realRetirementDate", jdbcType=JdbcType.DATE),
        @Result(column="license_date", property="licenseDate", jdbcType=JdbcType.DATE),
        @Result(column="usage_month_limit", property="usageMonthLimit", jdbcType=JdbcType.INTEGER),
        @Result(column="usage_id_registration_card", property="usageIdRegistrationCard", jdbcType=JdbcType.INTEGER),
        @Result(column="used_months", property="usedMonths", jdbcType=JdbcType.INTEGER),
        @Result(column="estimated_market_price", property="estimatedMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="starting_price", property="startingPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="estimated_net_sale_value", property="estimatedNetSaleValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="minimum_acceptable_price", property="minimumAcceptablePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="handling_fee", property="handlingFee", jdbcType=JdbcType.DECIMAL),
        @Result(column="sale_reason", property="saleReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="actual_sale_reason", property="actualSaleReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_nature", property="usageNature", jdbcType=JdbcType.VARCHAR),
        @Result(column="disposal_type", property="disposalType", jdbcType=JdbcType.INTEGER),
        @Result(column="estimated_vehicle_loss", property="estimatedVehicleLoss", jdbcType=JdbcType.DECIMAL),
        @Result(column="government_subsidy_amount", property="governmentSubsidyAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="disposal_reason", property="disposalReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="original_value", property="originalValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="accumulated_depreciation", property="accumulatedDepreciation", jdbcType=JdbcType.VARCHAR),
        @Result(column="net_value", property="netValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="financial_evaluation", property="financialEvaluation", jdbcType=JdbcType.VARCHAR),
        @Result(column="actual_selling_price", property="actualSellingPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="actual_net_price", property="actualNetPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="net_price_diff", property="netPriceDiff", jdbcType=JdbcType.DECIMAL),
        @Result(column="sale_gain_loss", property="saleGainLoss", jdbcType=JdbcType.DECIMAL),
        @Result(column="actual_sold_quantity", property="actualSoldQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="quantity_diff", property="quantityDiff", jdbcType=JdbcType.INTEGER),
        @Result(column="actual_sale_desc", property="actualSaleDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="estimated_scrap_loss_amount", property="estimatedScrapLossAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_insurance_auction", property="isInsuranceAuction", jdbcType=JdbcType.INTEGER),
        @Result(column="actual_auction_amount", property="actualAuctionAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="actual_scrap_profit_loss", property="actualScrapProfitLoss", jdbcType=JdbcType.DECIMAL),
        @Result(column="license_type_manual", property="licenseTypeManual", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_abbreviation_id", property="vehicleAbbreviationId", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
        @Result(column="msrp", property="msrp", jdbcType=JdbcType.VARCHAR),
        @Result(column="mileage", property="mileage", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_condition", property="vehicleCondition", jdbcType=JdbcType.VARCHAR),
        @Result(column="minimum_selling_price", property="minimumSellingPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="auction_reserve_price", property="auctionReservePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="expected_profit_loss_one", property="expectedProfitLossOne", jdbcType=JdbcType.DECIMAL),
        @Result(column="expected_profit_loss_two", property="expectedProfitLossTwo", jdbcType=JdbcType.DECIMAL),
        @Result(column="current_month_net_value", property="currentMonthNetValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="secondhand_car_invoice_url", property="secondhandCarInvoiceUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_url", property="fileUrl", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="actual_sale_file_url", property="actualSaleFileUrl", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<VehicleDisposalDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default int insert(VehicleDisposalDetail record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDisposalDetail, c ->
            c.map(disposalId).toProperty("disposalId")
            .map(vehicleAssetId).toProperty("vehicleAssetId")
            .map(vin).toProperty("vin")
            .map(licensePlate).toProperty("licensePlate")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(assetCompanyName).toProperty("assetCompanyName")
            .map(ownOrganizationId).toProperty("ownOrganizationId")
            .map(ownOrganizationName).toProperty("ownOrganizationName")
            .map(usageOrganizationId).toProperty("usageOrganizationId")
            .map(usageOrganizationName).toProperty("usageOrganizationName")
            .map(belongingTeam).toProperty("belongingTeam")
            .map(startDate).toProperty("startDate")
            .map(realRetirementDate).toProperty("realRetirementDate")
            .map(licenseDate).toProperty("licenseDate")
            .map(usageMonthLimit).toProperty("usageMonthLimit")
            .map(usageIdRegistrationCard).toProperty("usageIdRegistrationCard")
            .map(usedMonths).toProperty("usedMonths")
            .map(estimatedMarketPrice).toProperty("estimatedMarketPrice")
            .map(startingPrice).toProperty("startingPrice")
            .map(estimatedNetSaleValue).toProperty("estimatedNetSaleValue")
            .map(minimumAcceptablePrice).toProperty("minimumAcceptablePrice")
            .map(handlingFee).toProperty("handlingFee")
            .map(saleReason).toProperty("saleReason")
            .map(actualSaleReason).toProperty("actualSaleReason")
            .map(usageNature).toProperty("usageNature")
            .map(disposalType).toProperty("disposalType")
            .map(estimatedVehicleLoss).toProperty("estimatedVehicleLoss")
            .map(governmentSubsidyAmount).toProperty("governmentSubsidyAmount")
            .map(disposalReason).toProperty("disposalReason")
            .map(originalValue).toProperty("originalValue")
            .map(accumulatedDepreciation).toProperty("accumulatedDepreciation")
            .map(netValue).toProperty("netValue")
            .map(financialEvaluation).toProperty("financialEvaluation")
            .map(actualSellingPrice).toProperty("actualSellingPrice")
            .map(actualNetPrice).toProperty("actualNetPrice")
            .map(netPriceDiff).toProperty("netPriceDiff")
            .map(saleGainLoss).toProperty("saleGainLoss")
            .map(actualSoldQuantity).toProperty("actualSoldQuantity")
            .map(quantityDiff).toProperty("quantityDiff")
            .map(actualSaleDesc).toProperty("actualSaleDesc")
            .map(estimatedScrapLossAmount).toProperty("estimatedScrapLossAmount")
            .map(isInsuranceAuction).toProperty("isInsuranceAuction")
            .map(actualAuctionAmount).toProperty("actualAuctionAmount")
            .map(actualScrapProfitLoss).toProperty("actualScrapProfitLoss")
            .map(licenseTypeManual).toProperty("licenseTypeManual")
            .map(quotaType).toProperty("quotaType")
            .map(vehicleAbbreviationId).toProperty("vehicleAbbreviationId")
            .map(vehicleColorId).toProperty("vehicleColorId")
            .map(msrp).toProperty("msrp")
            .map(mileage).toProperty("mileage")
            .map(vehicleCondition).toProperty("vehicleCondition")
            .map(minimumSellingPrice).toProperty("minimumSellingPrice")
            .map(auctionReservePrice).toProperty("auctionReservePrice")
            .map(expectedProfitLossOne).toProperty("expectedProfitLossOne")
            .map(expectedProfitLossTwo).toProperty("expectedProfitLossTwo")
            .map(currentMonthNetValue).toProperty("currentMonthNetValue")
            .map(secondhandCarInvoiceUrl).toProperty("secondhandCarInvoiceUrl")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
            .map(fileUrl).toProperty("fileUrl")
            .map(actualSaleFileUrl).toProperty("actualSaleFileUrl")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default int insertSelective(VehicleDisposalDetail record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDisposalDetail, c ->
            c.map(disposalId).toPropertyWhenPresent("disposalId", record::getDisposalId)
            .map(vehicleAssetId).toPropertyWhenPresent("vehicleAssetId", record::getVehicleAssetId)
            .map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(assetCompanyName).toPropertyWhenPresent("assetCompanyName", record::getAssetCompanyName)
            .map(ownOrganizationId).toPropertyWhenPresent("ownOrganizationId", record::getOwnOrganizationId)
            .map(ownOrganizationName).toPropertyWhenPresent("ownOrganizationName", record::getOwnOrganizationName)
            .map(usageOrganizationId).toPropertyWhenPresent("usageOrganizationId", record::getUsageOrganizationId)
            .map(usageOrganizationName).toPropertyWhenPresent("usageOrganizationName", record::getUsageOrganizationName)
            .map(belongingTeam).toPropertyWhenPresent("belongingTeam", record::getBelongingTeam)
            .map(startDate).toPropertyWhenPresent("startDate", record::getStartDate)
            .map(realRetirementDate).toPropertyWhenPresent("realRetirementDate", record::getRealRetirementDate)
            .map(licenseDate).toPropertyWhenPresent("licenseDate", record::getLicenseDate)
            .map(usageMonthLimit).toPropertyWhenPresent("usageMonthLimit", record::getUsageMonthLimit)
            .map(usageIdRegistrationCard).toPropertyWhenPresent("usageIdRegistrationCard", record::getUsageIdRegistrationCard)
            .map(usedMonths).toPropertyWhenPresent("usedMonths", record::getUsedMonths)
            .map(estimatedMarketPrice).toPropertyWhenPresent("estimatedMarketPrice", record::getEstimatedMarketPrice)
            .map(startingPrice).toPropertyWhenPresent("startingPrice", record::getStartingPrice)
            .map(estimatedNetSaleValue).toPropertyWhenPresent("estimatedNetSaleValue", record::getEstimatedNetSaleValue)
            .map(minimumAcceptablePrice).toPropertyWhenPresent("minimumAcceptablePrice", record::getMinimumAcceptablePrice)
            .map(handlingFee).toPropertyWhenPresent("handlingFee", record::getHandlingFee)
            .map(saleReason).toPropertyWhenPresent("saleReason", record::getSaleReason)
            .map(actualSaleReason).toPropertyWhenPresent("actualSaleReason", record::getActualSaleReason)
            .map(usageNature).toPropertyWhenPresent("usageNature", record::getUsageNature)
            .map(disposalType).toPropertyWhenPresent("disposalType", record::getDisposalType)
            .map(estimatedVehicleLoss).toPropertyWhenPresent("estimatedVehicleLoss", record::getEstimatedVehicleLoss)
            .map(governmentSubsidyAmount).toPropertyWhenPresent("governmentSubsidyAmount", record::getGovernmentSubsidyAmount)
            .map(disposalReason).toPropertyWhenPresent("disposalReason", record::getDisposalReason)
            .map(originalValue).toPropertyWhenPresent("originalValue", record::getOriginalValue)
            .map(accumulatedDepreciation).toPropertyWhenPresent("accumulatedDepreciation", record::getAccumulatedDepreciation)
            .map(netValue).toPropertyWhenPresent("netValue", record::getNetValue)
            .map(financialEvaluation).toPropertyWhenPresent("financialEvaluation", record::getFinancialEvaluation)
            .map(actualSellingPrice).toPropertyWhenPresent("actualSellingPrice", record::getActualSellingPrice)
            .map(actualNetPrice).toPropertyWhenPresent("actualNetPrice", record::getActualNetPrice)
            .map(netPriceDiff).toPropertyWhenPresent("netPriceDiff", record::getNetPriceDiff)
            .map(saleGainLoss).toPropertyWhenPresent("saleGainLoss", record::getSaleGainLoss)
            .map(actualSoldQuantity).toPropertyWhenPresent("actualSoldQuantity", record::getActualSoldQuantity)
            .map(quantityDiff).toPropertyWhenPresent("quantityDiff", record::getQuantityDiff)
            .map(actualSaleDesc).toPropertyWhenPresent("actualSaleDesc", record::getActualSaleDesc)
            .map(estimatedScrapLossAmount).toPropertyWhenPresent("estimatedScrapLossAmount", record::getEstimatedScrapLossAmount)
            .map(isInsuranceAuction).toPropertyWhenPresent("isInsuranceAuction", record::getIsInsuranceAuction)
            .map(actualAuctionAmount).toPropertyWhenPresent("actualAuctionAmount", record::getActualAuctionAmount)
            .map(actualScrapProfitLoss).toPropertyWhenPresent("actualScrapProfitLoss", record::getActualScrapProfitLoss)
            .map(licenseTypeManual).toPropertyWhenPresent("licenseTypeManual", record::getLicenseTypeManual)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(vehicleAbbreviationId).toPropertyWhenPresent("vehicleAbbreviationId", record::getVehicleAbbreviationId)
            .map(vehicleColorId).toPropertyWhenPresent("vehicleColorId", record::getVehicleColorId)
            .map(msrp).toPropertyWhenPresent("msrp", record::getMsrp)
            .map(mileage).toPropertyWhenPresent("mileage", record::getMileage)
            .map(vehicleCondition).toPropertyWhenPresent("vehicleCondition", record::getVehicleCondition)
            .map(minimumSellingPrice).toPropertyWhenPresent("minimumSellingPrice", record::getMinimumSellingPrice)
            .map(auctionReservePrice).toPropertyWhenPresent("auctionReservePrice", record::getAuctionReservePrice)
            .map(expectedProfitLossOne).toPropertyWhenPresent("expectedProfitLossOne", record::getExpectedProfitLossOne)
            .map(expectedProfitLossTwo).toPropertyWhenPresent("expectedProfitLossTwo", record::getExpectedProfitLossTwo)
            .map(currentMonthNetValue).toPropertyWhenPresent("currentMonthNetValue", record::getCurrentMonthNetValue)
            .map(secondhandCarInvoiceUrl).toPropertyWhenPresent("secondhandCarInvoiceUrl", record::getSecondhandCarInvoiceUrl)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
            .map(fileUrl).toPropertyWhenPresent("fileUrl", record::getFileUrl)
            .map(actualSaleFileUrl).toPropertyWhenPresent("actualSaleFileUrl", record::getActualSaleFileUrl)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default Optional<VehicleDisposalDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default List<VehicleDisposalDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default List<VehicleDisposalDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default Optional<VehicleDisposalDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleDisposalDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(disposalId).equalTo(record::getDisposalId)
                .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
                .set(vin).equalTo(record::getVin)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(vehicleModelId).equalTo(record::getVehicleModelId)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(assetCompanyName).equalTo(record::getAssetCompanyName)
                .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
                .set(ownOrganizationName).equalTo(record::getOwnOrganizationName)
                .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
                .set(usageOrganizationName).equalTo(record::getUsageOrganizationName)
                .set(belongingTeam).equalTo(record::getBelongingTeam)
                .set(startDate).equalTo(record::getStartDate)
                .set(realRetirementDate).equalTo(record::getRealRetirementDate)
                .set(licenseDate).equalTo(record::getLicenseDate)
                .set(usageMonthLimit).equalTo(record::getUsageMonthLimit)
                .set(usageIdRegistrationCard).equalTo(record::getUsageIdRegistrationCard)
                .set(usedMonths).equalTo(record::getUsedMonths)
                .set(estimatedMarketPrice).equalTo(record::getEstimatedMarketPrice)
                .set(startingPrice).equalTo(record::getStartingPrice)
                .set(estimatedNetSaleValue).equalTo(record::getEstimatedNetSaleValue)
                .set(minimumAcceptablePrice).equalTo(record::getMinimumAcceptablePrice)
                .set(handlingFee).equalTo(record::getHandlingFee)
                .set(saleReason).equalTo(record::getSaleReason)
                .set(actualSaleReason).equalTo(record::getActualSaleReason)
                .set(usageNature).equalTo(record::getUsageNature)
                .set(disposalType).equalTo(record::getDisposalType)
                .set(estimatedVehicleLoss).equalTo(record::getEstimatedVehicleLoss)
                .set(governmentSubsidyAmount).equalTo(record::getGovernmentSubsidyAmount)
                .set(disposalReason).equalTo(record::getDisposalReason)
                .set(originalValue).equalTo(record::getOriginalValue)
                .set(accumulatedDepreciation).equalTo(record::getAccumulatedDepreciation)
                .set(netValue).equalTo(record::getNetValue)
                .set(financialEvaluation).equalTo(record::getFinancialEvaluation)
                .set(actualSellingPrice).equalTo(record::getActualSellingPrice)
                .set(actualNetPrice).equalTo(record::getActualNetPrice)
                .set(netPriceDiff).equalTo(record::getNetPriceDiff)
                .set(saleGainLoss).equalTo(record::getSaleGainLoss)
                .set(actualSoldQuantity).equalTo(record::getActualSoldQuantity)
                .set(quantityDiff).equalTo(record::getQuantityDiff)
                .set(actualSaleDesc).equalTo(record::getActualSaleDesc)
                .set(estimatedScrapLossAmount).equalTo(record::getEstimatedScrapLossAmount)
                .set(isInsuranceAuction).equalTo(record::getIsInsuranceAuction)
                .set(actualAuctionAmount).equalTo(record::getActualAuctionAmount)
                .set(actualScrapProfitLoss).equalTo(record::getActualScrapProfitLoss)
                .set(licenseTypeManual).equalTo(record::getLicenseTypeManual)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(vehicleAbbreviationId).equalTo(record::getVehicleAbbreviationId)
                .set(vehicleColorId).equalTo(record::getVehicleColorId)
                .set(msrp).equalTo(record::getMsrp)
                .set(mileage).equalTo(record::getMileage)
                .set(vehicleCondition).equalTo(record::getVehicleCondition)
                .set(minimumSellingPrice).equalTo(record::getMinimumSellingPrice)
                .set(auctionReservePrice).equalTo(record::getAuctionReservePrice)
                .set(expectedProfitLossOne).equalTo(record::getExpectedProfitLossOne)
                .set(expectedProfitLossTwo).equalTo(record::getExpectedProfitLossTwo)
                .set(currentMonthNetValue).equalTo(record::getCurrentMonthNetValue)
                .set(secondhandCarInvoiceUrl).equalTo(record::getSecondhandCarInvoiceUrl)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName)
                .set(fileUrl).equalTo(record::getFileUrl)
                .set(actualSaleFileUrl).equalTo(record::getActualSaleFileUrl);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleDisposalDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(disposalId).equalToWhenPresent(record::getDisposalId)
                .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
                .set(vin).equalToWhenPresent(record::getVin)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
                .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
                .set(ownOrganizationName).equalToWhenPresent(record::getOwnOrganizationName)
                .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
                .set(usageOrganizationName).equalToWhenPresent(record::getUsageOrganizationName)
                .set(belongingTeam).equalToWhenPresent(record::getBelongingTeam)
                .set(startDate).equalToWhenPresent(record::getStartDate)
                .set(realRetirementDate).equalToWhenPresent(record::getRealRetirementDate)
                .set(licenseDate).equalToWhenPresent(record::getLicenseDate)
                .set(usageMonthLimit).equalToWhenPresent(record::getUsageMonthLimit)
                .set(usageIdRegistrationCard).equalToWhenPresent(record::getUsageIdRegistrationCard)
                .set(usedMonths).equalToWhenPresent(record::getUsedMonths)
                .set(estimatedMarketPrice).equalToWhenPresent(record::getEstimatedMarketPrice)
                .set(startingPrice).equalToWhenPresent(record::getStartingPrice)
                .set(estimatedNetSaleValue).equalToWhenPresent(record::getEstimatedNetSaleValue)
                .set(minimumAcceptablePrice).equalToWhenPresent(record::getMinimumAcceptablePrice)
                .set(handlingFee).equalToWhenPresent(record::getHandlingFee)
                .set(saleReason).equalToWhenPresent(record::getSaleReason)
                .set(actualSaleReason).equalToWhenPresent(record::getActualSaleReason)
                .set(usageNature).equalToWhenPresent(record::getUsageNature)
                .set(disposalType).equalToWhenPresent(record::getDisposalType)
                .set(estimatedVehicleLoss).equalToWhenPresent(record::getEstimatedVehicleLoss)
                .set(governmentSubsidyAmount).equalToWhenPresent(record::getGovernmentSubsidyAmount)
                .set(disposalReason).equalToWhenPresent(record::getDisposalReason)
                .set(originalValue).equalToWhenPresent(record::getOriginalValue)
                .set(accumulatedDepreciation).equalToWhenPresent(record::getAccumulatedDepreciation)
                .set(netValue).equalToWhenPresent(record::getNetValue)
                .set(financialEvaluation).equalToWhenPresent(record::getFinancialEvaluation)
                .set(actualSellingPrice).equalToWhenPresent(record::getActualSellingPrice)
                .set(actualNetPrice).equalToWhenPresent(record::getActualNetPrice)
                .set(netPriceDiff).equalToWhenPresent(record::getNetPriceDiff)
                .set(saleGainLoss).equalToWhenPresent(record::getSaleGainLoss)
                .set(actualSoldQuantity).equalToWhenPresent(record::getActualSoldQuantity)
                .set(quantityDiff).equalToWhenPresent(record::getQuantityDiff)
                .set(actualSaleDesc).equalToWhenPresent(record::getActualSaleDesc)
                .set(estimatedScrapLossAmount).equalToWhenPresent(record::getEstimatedScrapLossAmount)
                .set(isInsuranceAuction).equalToWhenPresent(record::getIsInsuranceAuction)
                .set(actualAuctionAmount).equalToWhenPresent(record::getActualAuctionAmount)
                .set(actualScrapProfitLoss).equalToWhenPresent(record::getActualScrapProfitLoss)
                .set(licenseTypeManual).equalToWhenPresent(record::getLicenseTypeManual)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(vehicleAbbreviationId).equalToWhenPresent(record::getVehicleAbbreviationId)
                .set(vehicleColorId).equalToWhenPresent(record::getVehicleColorId)
                .set(msrp).equalToWhenPresent(record::getMsrp)
                .set(mileage).equalToWhenPresent(record::getMileage)
                .set(vehicleCondition).equalToWhenPresent(record::getVehicleCondition)
                .set(minimumSellingPrice).equalToWhenPresent(record::getMinimumSellingPrice)
                .set(auctionReservePrice).equalToWhenPresent(record::getAuctionReservePrice)
                .set(expectedProfitLossOne).equalToWhenPresent(record::getExpectedProfitLossOne)
                .set(expectedProfitLossTwo).equalToWhenPresent(record::getExpectedProfitLossTwo)
                .set(currentMonthNetValue).equalToWhenPresent(record::getCurrentMonthNetValue)
                .set(secondhandCarInvoiceUrl).equalToWhenPresent(record::getSecondhandCarInvoiceUrl)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
                .set(fileUrl).equalToWhenPresent(record::getFileUrl)
                .set(actualSaleFileUrl).equalToWhenPresent(record::getActualSaleFileUrl);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default int updateByPrimaryKey(VehicleDisposalDetail record) {
        return update(c ->
            c.set(disposalId).equalTo(record::getDisposalId)
            .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
            .set(vin).equalTo(record::getVin)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(vehicleModelId).equalTo(record::getVehicleModelId)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(assetCompanyName).equalTo(record::getAssetCompanyName)
            .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
            .set(ownOrganizationName).equalTo(record::getOwnOrganizationName)
            .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
            .set(usageOrganizationName).equalTo(record::getUsageOrganizationName)
            .set(belongingTeam).equalTo(record::getBelongingTeam)
            .set(startDate).equalTo(record::getStartDate)
            .set(realRetirementDate).equalTo(record::getRealRetirementDate)
            .set(licenseDate).equalTo(record::getLicenseDate)
            .set(usageMonthLimit).equalTo(record::getUsageMonthLimit)
            .set(usageIdRegistrationCard).equalTo(record::getUsageIdRegistrationCard)
            .set(usedMonths).equalTo(record::getUsedMonths)
            .set(estimatedMarketPrice).equalTo(record::getEstimatedMarketPrice)
            .set(startingPrice).equalTo(record::getStartingPrice)
            .set(estimatedNetSaleValue).equalTo(record::getEstimatedNetSaleValue)
            .set(minimumAcceptablePrice).equalTo(record::getMinimumAcceptablePrice)
            .set(handlingFee).equalTo(record::getHandlingFee)
            .set(saleReason).equalTo(record::getSaleReason)
            .set(actualSaleReason).equalTo(record::getActualSaleReason)
            .set(usageNature).equalTo(record::getUsageNature)
            .set(disposalType).equalTo(record::getDisposalType)
            .set(estimatedVehicleLoss).equalTo(record::getEstimatedVehicleLoss)
            .set(governmentSubsidyAmount).equalTo(record::getGovernmentSubsidyAmount)
            .set(disposalReason).equalTo(record::getDisposalReason)
            .set(originalValue).equalTo(record::getOriginalValue)
            .set(accumulatedDepreciation).equalTo(record::getAccumulatedDepreciation)
            .set(netValue).equalTo(record::getNetValue)
            .set(financialEvaluation).equalTo(record::getFinancialEvaluation)
            .set(actualSellingPrice).equalTo(record::getActualSellingPrice)
            .set(actualNetPrice).equalTo(record::getActualNetPrice)
            .set(netPriceDiff).equalTo(record::getNetPriceDiff)
            .set(saleGainLoss).equalTo(record::getSaleGainLoss)
            .set(actualSoldQuantity).equalTo(record::getActualSoldQuantity)
            .set(quantityDiff).equalTo(record::getQuantityDiff)
            .set(actualSaleDesc).equalTo(record::getActualSaleDesc)
            .set(estimatedScrapLossAmount).equalTo(record::getEstimatedScrapLossAmount)
            .set(isInsuranceAuction).equalTo(record::getIsInsuranceAuction)
            .set(actualAuctionAmount).equalTo(record::getActualAuctionAmount)
            .set(actualScrapProfitLoss).equalTo(record::getActualScrapProfitLoss)
            .set(licenseTypeManual).equalTo(record::getLicenseTypeManual)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(vehicleAbbreviationId).equalTo(record::getVehicleAbbreviationId)
            .set(vehicleColorId).equalTo(record::getVehicleColorId)
            .set(msrp).equalTo(record::getMsrp)
            .set(mileage).equalTo(record::getMileage)
            .set(vehicleCondition).equalTo(record::getVehicleCondition)
            .set(minimumSellingPrice).equalTo(record::getMinimumSellingPrice)
            .set(auctionReservePrice).equalTo(record::getAuctionReservePrice)
            .set(expectedProfitLossOne).equalTo(record::getExpectedProfitLossOne)
            .set(expectedProfitLossTwo).equalTo(record::getExpectedProfitLossTwo)
            .set(currentMonthNetValue).equalTo(record::getCurrentMonthNetValue)
            .set(secondhandCarInvoiceUrl).equalTo(record::getSecondhandCarInvoiceUrl)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .set(fileUrl).equalTo(record::getFileUrl)
            .set(actualSaleFileUrl).equalTo(record::getActualSaleFileUrl)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    default int updateByPrimaryKeySelective(VehicleDisposalDetail record) {
        return update(c ->
            c.set(disposalId).equalToWhenPresent(record::getDisposalId)
            .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
            .set(vin).equalToWhenPresent(record::getVin)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
            .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
            .set(ownOrganizationName).equalToWhenPresent(record::getOwnOrganizationName)
            .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
            .set(usageOrganizationName).equalToWhenPresent(record::getUsageOrganizationName)
            .set(belongingTeam).equalToWhenPresent(record::getBelongingTeam)
            .set(startDate).equalToWhenPresent(record::getStartDate)
            .set(realRetirementDate).equalToWhenPresent(record::getRealRetirementDate)
            .set(licenseDate).equalToWhenPresent(record::getLicenseDate)
            .set(usageMonthLimit).equalToWhenPresent(record::getUsageMonthLimit)
            .set(usageIdRegistrationCard).equalToWhenPresent(record::getUsageIdRegistrationCard)
            .set(usedMonths).equalToWhenPresent(record::getUsedMonths)
            .set(estimatedMarketPrice).equalToWhenPresent(record::getEstimatedMarketPrice)
            .set(startingPrice).equalToWhenPresent(record::getStartingPrice)
            .set(estimatedNetSaleValue).equalToWhenPresent(record::getEstimatedNetSaleValue)
            .set(minimumAcceptablePrice).equalToWhenPresent(record::getMinimumAcceptablePrice)
            .set(handlingFee).equalToWhenPresent(record::getHandlingFee)
            .set(saleReason).equalToWhenPresent(record::getSaleReason)
            .set(actualSaleReason).equalToWhenPresent(record::getActualSaleReason)
            .set(usageNature).equalToWhenPresent(record::getUsageNature)
            .set(disposalType).equalToWhenPresent(record::getDisposalType)
            .set(estimatedVehicleLoss).equalToWhenPresent(record::getEstimatedVehicleLoss)
            .set(governmentSubsidyAmount).equalToWhenPresent(record::getGovernmentSubsidyAmount)
            .set(disposalReason).equalToWhenPresent(record::getDisposalReason)
            .set(originalValue).equalToWhenPresent(record::getOriginalValue)
            .set(accumulatedDepreciation).equalToWhenPresent(record::getAccumulatedDepreciation)
            .set(netValue).equalToWhenPresent(record::getNetValue)
            .set(financialEvaluation).equalToWhenPresent(record::getFinancialEvaluation)
            .set(actualSellingPrice).equalToWhenPresent(record::getActualSellingPrice)
            .set(actualNetPrice).equalToWhenPresent(record::getActualNetPrice)
            .set(netPriceDiff).equalToWhenPresent(record::getNetPriceDiff)
            .set(saleGainLoss).equalToWhenPresent(record::getSaleGainLoss)
            .set(actualSoldQuantity).equalToWhenPresent(record::getActualSoldQuantity)
            .set(quantityDiff).equalToWhenPresent(record::getQuantityDiff)
            .set(actualSaleDesc).equalToWhenPresent(record::getActualSaleDesc)
            .set(estimatedScrapLossAmount).equalToWhenPresent(record::getEstimatedScrapLossAmount)
            .set(isInsuranceAuction).equalToWhenPresent(record::getIsInsuranceAuction)
            .set(actualAuctionAmount).equalToWhenPresent(record::getActualAuctionAmount)
            .set(actualScrapProfitLoss).equalToWhenPresent(record::getActualScrapProfitLoss)
            .set(licenseTypeManual).equalToWhenPresent(record::getLicenseTypeManual)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(vehicleAbbreviationId).equalToWhenPresent(record::getVehicleAbbreviationId)
            .set(vehicleColorId).equalToWhenPresent(record::getVehicleColorId)
            .set(msrp).equalToWhenPresent(record::getMsrp)
            .set(mileage).equalToWhenPresent(record::getMileage)
            .set(vehicleCondition).equalToWhenPresent(record::getVehicleCondition)
            .set(minimumSellingPrice).equalToWhenPresent(record::getMinimumSellingPrice)
            .set(auctionReservePrice).equalToWhenPresent(record::getAuctionReservePrice)
            .set(expectedProfitLossOne).equalToWhenPresent(record::getExpectedProfitLossOne)
            .set(expectedProfitLossTwo).equalToWhenPresent(record::getExpectedProfitLossTwo)
            .set(currentMonthNetValue).equalToWhenPresent(record::getCurrentMonthNetValue)
            .set(secondhandCarInvoiceUrl).equalToWhenPresent(record::getSecondhandCarInvoiceUrl)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .set(fileUrl).equalToWhenPresent(record::getFileUrl)
            .set(actualSaleFileUrl).equalToWhenPresent(record::getActualSaleFileUrl)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_disposal_detail")
    default int insertMultiple(Collection<VehicleDisposalDetail> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, vehicleDisposalDetail, c -> c
                .map(disposalId).toProperty("disposalId")
                .map(vehicleAssetId).toProperty("vehicleAssetId")
                .map(vin).toProperty("vin")
                .map(licensePlate).toProperty("licensePlate")
                .map(vehicleModelId).toProperty("vehicleModelId")
                .map(assetCompanyId).toProperty("assetCompanyId")
                .map(assetCompanyName).toProperty("assetCompanyName")
                .map(ownOrganizationId).toProperty("ownOrganizationId")
                .map(ownOrganizationName).toProperty("ownOrganizationName")
                .map(usageOrganizationId).toProperty("usageOrganizationId")
                .map(usageOrganizationName).toProperty("usageOrganizationName")
                .map(belongingTeam).toProperty("belongingTeam")
                .map(startDate).toProperty("startDate")
                .map(realRetirementDate).toProperty("realRetirementDate")
                .map(usageMonthLimit).toProperty("usageMonthLimit")
                .map(usageIdRegistrationCard).toProperty("usageIdRegistrationCard")
                .map(usedMonths).toProperty("usedMonths")
                .map(estimatedMarketPrice).toProperty("estimatedMarketPrice")
                .map(startingPrice).toProperty("startingPrice")
                .map(estimatedNetSaleValue).toProperty("estimatedNetSaleValue")
                .map(minimumAcceptablePrice).toProperty("minimumAcceptablePrice")
                .map(handlingFee).toProperty("handlingFee")
                .map(saleReason).toProperty("saleReason")
                .map(actualSaleReason).toProperty("actualSaleReason")
                .map(saleGainLoss).toProperty("saleGainLoss")
                .map(usageNature).toProperty("usageNature")
                .map(disposalType).toProperty("disposalType")
                .map(estimatedVehicleLoss).toProperty("estimatedVehicleLoss")
                .map(governmentSubsidyAmount).toProperty("governmentSubsidyAmount")
                .map(disposalReason).toProperty("disposalReason")
                // 商售单
                .map(licenseDate).toProperty("licenseDate")
                .map(quotaType).toProperty("quotaType")
                .map(vehicleAbbreviationId).toProperty("vehicleAbbreviationId")
                .map(vehicleColorId).toProperty("vehicleColorId")
                .map(originalValue).toProperty("originalValue")
                .map(accumulatedDepreciation).toProperty("accumulatedDepreciation")
                .map(licenseTypeManual).toProperty("licenseTypeManual")
                .map(msrp).toProperty("msrp")
                .map(mileage).toProperty("mileage")
                .map(vehicleCondition).toProperty("vehicleCondition")
                .map(minimumSellingPrice).toProperty("minimumSellingPrice")
                .map(auctionReservePrice).toProperty("auctionReservePrice")
                .map(expectedProfitLossOne).toProperty("expectedProfitLossOne")
                .map(expectedProfitLossTwo).toProperty("expectedProfitLossTwo")
                .map(currentMonthNetValue).toProperty("currentMonthNetValue")
                .map(createTime).toProperty("createTime")
                .map(createOperId).toProperty("createOperId")
                .map(createOperName).toProperty("createOperName")
        );
    }
}