package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class DownloadFileInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    public static final DownloadFileInfo downloadFileInfo = new DownloadFileInfo();

    /**
     * Database Column Remarks:
     *   主键自增
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.id")
    public static final SqlColumn<Long> id = downloadFileInfo.id;

    /**
     * Database Column Remarks:
     *   文件来源 1=车辆主数据
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_source")
    public static final SqlColumn<Integer> fileSource = downloadFileInfo.fileSource;

    /**
     * Database Column Remarks:
     *   模块来源 1=车辆详情
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.model_source")
    public static final SqlColumn<Integer> modelSource = downloadFileInfo.modelSource;

    /**
     * Database Column Remarks:
     *   文件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_name")
    public static final SqlColumn<String> fileName = downloadFileInfo.fileName;

    /**
     * Database Column Remarks:
     *   文件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_path")
    public static final SqlColumn<String> filePath = downloadFileInfo.filePath;

    /**
     * Database Column Remarks:
     *   文件状态 1=生成中 2=生成成功 3=生成失败
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_status")
    public static final SqlColumn<Integer> fileStatus = downloadFileInfo.fileStatus;

    /**
     * Database Column Remarks:
     *   文件过期时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.expire_time")
    public static final SqlColumn<Date> expireTime = downloadFileInfo.expireTime;

    /**
     * Database Column Remarks:
     *   失败原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.remark")
    public static final SqlColumn<String> remark = downloadFileInfo.remark;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = downloadFileInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_time")
    public static final SqlColumn<Date> createTime = downloadFileInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = downloadFileInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_name")
    public static final SqlColumn<String> createOperName = downloadFileInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_time")
    public static final SqlColumn<Date> updateTime = downloadFileInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = downloadFileInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = downloadFileInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    public static final class DownloadFileInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> fileSource = column("file_source", JDBCType.INTEGER);

        public final SqlColumn<Integer> modelSource = column("model_source", JDBCType.INTEGER);

        public final SqlColumn<String> fileName = column("file_name", JDBCType.VARCHAR);

        public final SqlColumn<String> filePath = column("file_path", JDBCType.VARCHAR);

        public final SqlColumn<Integer> fileStatus = column("file_status", JDBCType.INTEGER);

        public final SqlColumn<Date> expireTime = column("expire_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public DownloadFileInfo() {
            super("t_download_file_info");
        }
    }
}