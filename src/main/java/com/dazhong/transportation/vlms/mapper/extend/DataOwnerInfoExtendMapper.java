package com.dazhong.transportation.vlms.mapper.extend;

import com.dazhong.transportation.vlms.mapper.DataOwnerInfoMapper;
import com.dazhong.transportation.vlms.model.DataOwnerInfo;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import javax.annotation.Generated;

import static com.dazhong.transportation.vlms.mapper.DataOwnerInfoDynamicSqlSupport.*;

@Mapper
public interface DataOwnerInfoExtendMapper extends DataOwnerInfoMapper {

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_owner_info")
    default int insertSelectiveWithId(DataOwnerInfo record) {
        return MyBatis3Utils.insert(this::insert, record, dataOwnerInfo, c ->
                c.map(id).toPropertyWhenPresent("id", record::getId)
                        .map(name).toPropertyWhenPresent("name", record::getName)
                        .map(address).toPropertyWhenPresent("address", record::getAddress)
                        .map(phone).toPropertyWhenPresent("phone", record::getPhone)
                        .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
                        .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
                        .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
                        .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
                        .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
                        .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
                        .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }
}
