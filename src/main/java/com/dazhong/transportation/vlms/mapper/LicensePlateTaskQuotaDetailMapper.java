package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.model.LicensePlateTaskQuotaDetail;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

import javax.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskQuotaDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface LicensePlateTaskQuotaDetailMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<LicensePlateTaskQuotaDetail> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNumber, quotaNumber, quotaPrintDate, quotaType, quotaAssetCompanyId, quotaAssetCompanyName, licensePlate, licensePlateWithdrawalDate, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<LicensePlateTaskQuotaDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LicensePlateTaskQuotaDetailResult")
    Optional<LicensePlateTaskQuotaDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LicensePlateTaskQuotaDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_number", property="taskNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_number", property="quotaNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_print_date", property="quotaPrintDate", jdbcType=JdbcType.DATE),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_asset_company_id", property="quotaAssetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_asset_company_name", property="quotaAssetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate_withdrawal_date", property="licensePlateWithdrawalDate", jdbcType=JdbcType.DATE),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<LicensePlateTaskQuotaDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, licensePlateTaskQuotaDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, licensePlateTaskQuotaDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default int insert(LicensePlateTaskQuotaDetail record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateTaskQuotaDetail, c ->
            c.map(taskNumber).toProperty("taskNumber")
            .map(quotaNumber).toProperty("quotaNumber")
            .map(quotaPrintDate).toProperty("quotaPrintDate")
            .map(quotaType).toProperty("quotaType")
            .map(quotaAssetCompanyId).toProperty("quotaAssetCompanyId")
            .map(quotaAssetCompanyName).toProperty("quotaAssetCompanyName")
            .map(licensePlate).toProperty("licensePlate")
            .map(licensePlateWithdrawalDate).toProperty("licensePlateWithdrawalDate")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default int insertSelective(LicensePlateTaskQuotaDetail record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateTaskQuotaDetail, c ->
            c.map(taskNumber).toPropertyWhenPresent("taskNumber", record::getTaskNumber)
            .map(quotaNumber).toPropertyWhenPresent("quotaNumber", record::getQuotaNumber)
            .map(quotaPrintDate).toPropertyWhenPresent("quotaPrintDate", record::getQuotaPrintDate)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(quotaAssetCompanyId).toPropertyWhenPresent("quotaAssetCompanyId", record::getQuotaAssetCompanyId)
            .map(quotaAssetCompanyName).toPropertyWhenPresent("quotaAssetCompanyName", record::getQuotaAssetCompanyName)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(licensePlateWithdrawalDate).toPropertyWhenPresent("licensePlateWithdrawalDate", record::getLicensePlateWithdrawalDate)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default Optional<LicensePlateTaskQuotaDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, licensePlateTaskQuotaDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default List<LicensePlateTaskQuotaDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, licensePlateTaskQuotaDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default List<LicensePlateTaskQuotaDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, licensePlateTaskQuotaDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default Optional<LicensePlateTaskQuotaDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, licensePlateTaskQuotaDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(LicensePlateTaskQuotaDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNumber).equalTo(record::getTaskNumber)
                .set(quotaNumber).equalTo(record::getQuotaNumber)
                .set(quotaPrintDate).equalTo(record::getQuotaPrintDate)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(quotaAssetCompanyId).equalTo(record::getQuotaAssetCompanyId)
                .set(quotaAssetCompanyName).equalTo(record::getQuotaAssetCompanyName)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(licensePlateWithdrawalDate).equalTo(record::getLicensePlateWithdrawalDate)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LicensePlateTaskQuotaDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNumber).equalToWhenPresent(record::getTaskNumber)
                .set(quotaNumber).equalToWhenPresent(record::getQuotaNumber)
                .set(quotaPrintDate).equalToWhenPresent(record::getQuotaPrintDate)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(quotaAssetCompanyId).equalToWhenPresent(record::getQuotaAssetCompanyId)
                .set(quotaAssetCompanyName).equalToWhenPresent(record::getQuotaAssetCompanyName)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(licensePlateWithdrawalDate).equalToWhenPresent(record::getLicensePlateWithdrawalDate)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default int updateByPrimaryKey(LicensePlateTaskQuotaDetail record) {
        return update(c ->
            c.set(taskNumber).equalTo(record::getTaskNumber)
            .set(quotaNumber).equalTo(record::getQuotaNumber)
            .set(quotaPrintDate).equalTo(record::getQuotaPrintDate)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(quotaAssetCompanyId).equalTo(record::getQuotaAssetCompanyId)
            .set(quotaAssetCompanyName).equalTo(record::getQuotaAssetCompanyName)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(licensePlateWithdrawalDate).equalTo(record::getLicensePlateWithdrawalDate)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    default int updateByPrimaryKeySelective(LicensePlateTaskQuotaDetail record) {
        return update(c ->
            c.set(taskNumber).equalToWhenPresent(record::getTaskNumber)
            .set(quotaNumber).equalToWhenPresent(record::getQuotaNumber)
            .set(quotaPrintDate).equalToWhenPresent(record::getQuotaPrintDate)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(quotaAssetCompanyId).equalToWhenPresent(record::getQuotaAssetCompanyId)
            .set(quotaAssetCompanyName).equalToWhenPresent(record::getQuotaAssetCompanyName)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(licensePlateWithdrawalDate).equalToWhenPresent(record::getLicensePlateWithdrawalDate)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_operation_apply_detail")
    default int insertMultiple(Collection<LicensePlateTaskQuotaDetail> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, licensePlateTaskQuotaDetail, c -> c
                .map(taskNumber).toProperty("taskNumber")
                .map(quotaNumber).toProperty("quotaNumber")
                .map(quotaPrintDate).toProperty("quotaPrintDate")
                .map(quotaType).toProperty("quotaType")
                .map(quotaAssetCompanyId).toProperty("quotaAssetCompanyId")
                .map(quotaAssetCompanyName).toProperty("quotaAssetCompanyName")
                .map(licensePlate).toProperty("licensePlate")
                .map(licensePlateWithdrawalDate).toProperty("licensePlateWithdrawalDate")
                .map(createOperId).toProperty("createOperId")
                .map(createOperName).toProperty("createOperName")
        );
    }
}