package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class UserInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_info")
    public static final UserInfo userInfo = new UserInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.id")
    public static final SqlColumn<Long> id = userInfo.id;

    /**
     * Database Column Remarks:
     *   用户账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.user_account")
    public static final SqlColumn<String> userAccount = userInfo.userAccount;

    /**
     * Database Column Remarks:
     *   手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.mobile_phone")
    public static final SqlColumn<String> mobilePhone = userInfo.mobilePhone;

    /**
     * Database Column Remarks:
     *   姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.name")
    public static final SqlColumn<String> name = userInfo.name;

    /**
     * Database Column Remarks:
     *   邮箱
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.email")
    public static final SqlColumn<String> email = userInfo.email;

    /**
     * Database Column Remarks:
     *   工号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.work_num")
    public static final SqlColumn<String> workNum = userInfo.workNum;

    /**
     * Database Column Remarks:
     *   钉钉号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.ding_talk_num")
    public static final SqlColumn<String> dingTalkNum = userInfo.dingTalkNum;

    /**
     * Database Column Remarks:
     *   人员状态 1：在职 2：离职
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.personnel_state")
    public static final SqlColumn<Integer> personnelState = userInfo.personnelState;

    /**
     * Database Column Remarks:
     *   机构code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.org_code")
    public static final SqlColumn<String> orgCode = userInfo.orgCode;

    /**
     * Database Column Remarks:
     *   系统编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.system_code")
    public static final SqlColumn<String> systemCode = userInfo.systemCode;

    /**
     * Database Column Remarks:
     *   是否超管 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.is_system_admin")
    public static final SqlColumn<Integer> isSystemAdmin = userInfo.isSystemAdmin;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = userInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.create_time")
    public static final SqlColumn<Date> createTime = userInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = userInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.create_oper_name")
    public static final SqlColumn<String> createOperName = userInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.update_time")
    public static final SqlColumn<Date> updateTime = userInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = userInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = userInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_info")
    public static final class UserInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> userAccount = column("user_account", JDBCType.VARCHAR);

        public final SqlColumn<String> mobilePhone = column("mobile_phone", JDBCType.VARCHAR);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> email = column("email", JDBCType.VARCHAR);

        public final SqlColumn<String> workNum = column("work_num", JDBCType.VARCHAR);

        public final SqlColumn<String> dingTalkNum = column("ding_talk_num", JDBCType.VARCHAR);

        public final SqlColumn<Integer> personnelState = column("personnel_state", JDBCType.INTEGER);

        public final SqlColumn<String> orgCode = column("org_code", JDBCType.VARCHAR);

        public final SqlColumn<String> systemCode = column("system_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isSystemAdmin = column("is_system_admin", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public UserInfo() {
            super("t_user_info");
        }
    }
}