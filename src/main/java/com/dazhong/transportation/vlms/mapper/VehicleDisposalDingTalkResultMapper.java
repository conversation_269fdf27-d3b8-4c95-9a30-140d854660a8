package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleDisposalDingTalkResultDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleDisposalDingTalkResult;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleDisposalDingTalkResultMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    BasicColumn[] selectList = BasicColumn.columnList(id, disposalId, originalValue, depreciationTaken, netValueOrSalvage, estimatedProfitLoss, actualVehiclesSold, actualTotalProfitLoss, actualPerVehicleProfitLoss, actualVsEstimatedDiffOne, actualVsEstimatedDiffTwo, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleDisposalDingTalkResult> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleDisposalDingTalkResultResult")
    Optional<VehicleDisposalDingTalkResult> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleDisposalDingTalkResultResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="disposal_id", property="disposalId", jdbcType=JdbcType.BIGINT),
        @Result(column="original_value", property="originalValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="depreciation_taken", property="depreciationTaken", jdbcType=JdbcType.DECIMAL),
        @Result(column="net_value_or_salvage", property="netValueOrSalvage", jdbcType=JdbcType.DECIMAL),
        @Result(column="estimated_profit_loss", property="estimatedProfitLoss", jdbcType=JdbcType.DECIMAL),
        @Result(column="actual_vehicles_sold", property="actualVehiclesSold", jdbcType=JdbcType.INTEGER),
        @Result(column="actual_total_profit_loss", property="actualTotalProfitLoss", jdbcType=JdbcType.DECIMAL),
        @Result(column="actual_per_vehicle_profit_loss", property="actualPerVehicleProfitLoss", jdbcType=JdbcType.DECIMAL),
        @Result(column="actual_vs_estimated_diff_one", property="actualVsEstimatedDiffOne", jdbcType=JdbcType.DECIMAL),
        @Result(column="actual_vs_estimated_diff_two", property="actualVsEstimatedDiffTwo", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleDisposalDingTalkResult> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleDisposalDingTalkResult, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleDisposalDingTalkResult, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default int insert(VehicleDisposalDingTalkResult record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDisposalDingTalkResult, c ->
            c.map(disposalId).toProperty("disposalId")
            .map(originalValue).toProperty("originalValue")
            .map(depreciationTaken).toProperty("depreciationTaken")
            .map(netValueOrSalvage).toProperty("netValueOrSalvage")
            .map(estimatedProfitLoss).toProperty("estimatedProfitLoss")
            .map(actualVehiclesSold).toProperty("actualVehiclesSold")
            .map(actualTotalProfitLoss).toProperty("actualTotalProfitLoss")
            .map(actualPerVehicleProfitLoss).toProperty("actualPerVehicleProfitLoss")
            .map(actualVsEstimatedDiffOne).toProperty("actualVsEstimatedDiffOne")
            .map(actualVsEstimatedDiffTwo).toProperty("actualVsEstimatedDiffTwo")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default int insertSelective(VehicleDisposalDingTalkResult record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDisposalDingTalkResult, c ->
            c.map(disposalId).toPropertyWhenPresent("disposalId", record::getDisposalId)
            .map(originalValue).toPropertyWhenPresent("originalValue", record::getOriginalValue)
            .map(depreciationTaken).toPropertyWhenPresent("depreciationTaken", record::getDepreciationTaken)
            .map(netValueOrSalvage).toPropertyWhenPresent("netValueOrSalvage", record::getNetValueOrSalvage)
            .map(estimatedProfitLoss).toPropertyWhenPresent("estimatedProfitLoss", record::getEstimatedProfitLoss)
            .map(actualVehiclesSold).toPropertyWhenPresent("actualVehiclesSold", record::getActualVehiclesSold)
            .map(actualTotalProfitLoss).toPropertyWhenPresent("actualTotalProfitLoss", record::getActualTotalProfitLoss)
            .map(actualPerVehicleProfitLoss).toPropertyWhenPresent("actualPerVehicleProfitLoss", record::getActualPerVehicleProfitLoss)
            .map(actualVsEstimatedDiffOne).toPropertyWhenPresent("actualVsEstimatedDiffOne", record::getActualVsEstimatedDiffOne)
            .map(actualVsEstimatedDiffTwo).toPropertyWhenPresent("actualVsEstimatedDiffTwo", record::getActualVsEstimatedDiffTwo)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default Optional<VehicleDisposalDingTalkResult> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleDisposalDingTalkResult, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default List<VehicleDisposalDingTalkResult> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleDisposalDingTalkResult, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default List<VehicleDisposalDingTalkResult> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleDisposalDingTalkResult, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default Optional<VehicleDisposalDingTalkResult> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleDisposalDingTalkResult, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleDisposalDingTalkResult record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(disposalId).equalTo(record::getDisposalId)
                .set(originalValue).equalTo(record::getOriginalValue)
                .set(depreciationTaken).equalTo(record::getDepreciationTaken)
                .set(netValueOrSalvage).equalTo(record::getNetValueOrSalvage)
                .set(estimatedProfitLoss).equalTo(record::getEstimatedProfitLoss)
                .set(actualVehiclesSold).equalTo(record::getActualVehiclesSold)
                .set(actualTotalProfitLoss).equalTo(record::getActualTotalProfitLoss)
                .set(actualPerVehicleProfitLoss).equalTo(record::getActualPerVehicleProfitLoss)
                .set(actualVsEstimatedDiffOne).equalTo(record::getActualVsEstimatedDiffOne)
                .set(actualVsEstimatedDiffTwo).equalTo(record::getActualVsEstimatedDiffTwo)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleDisposalDingTalkResult record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(disposalId).equalToWhenPresent(record::getDisposalId)
                .set(originalValue).equalToWhenPresent(record::getOriginalValue)
                .set(depreciationTaken).equalToWhenPresent(record::getDepreciationTaken)
                .set(netValueOrSalvage).equalToWhenPresent(record::getNetValueOrSalvage)
                .set(estimatedProfitLoss).equalToWhenPresent(record::getEstimatedProfitLoss)
                .set(actualVehiclesSold).equalToWhenPresent(record::getActualVehiclesSold)
                .set(actualTotalProfitLoss).equalToWhenPresent(record::getActualTotalProfitLoss)
                .set(actualPerVehicleProfitLoss).equalToWhenPresent(record::getActualPerVehicleProfitLoss)
                .set(actualVsEstimatedDiffOne).equalToWhenPresent(record::getActualVsEstimatedDiffOne)
                .set(actualVsEstimatedDiffTwo).equalToWhenPresent(record::getActualVsEstimatedDiffTwo)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default int updateByPrimaryKey(VehicleDisposalDingTalkResult record) {
        return update(c ->
            c.set(disposalId).equalTo(record::getDisposalId)
            .set(originalValue).equalTo(record::getOriginalValue)
            .set(depreciationTaken).equalTo(record::getDepreciationTaken)
            .set(netValueOrSalvage).equalTo(record::getNetValueOrSalvage)
            .set(estimatedProfitLoss).equalTo(record::getEstimatedProfitLoss)
            .set(actualVehiclesSold).equalTo(record::getActualVehiclesSold)
            .set(actualTotalProfitLoss).equalTo(record::getActualTotalProfitLoss)
            .set(actualPerVehicleProfitLoss).equalTo(record::getActualPerVehicleProfitLoss)
            .set(actualVsEstimatedDiffOne).equalTo(record::getActualVsEstimatedDiffOne)
            .set(actualVsEstimatedDiffTwo).equalTo(record::getActualVsEstimatedDiffTwo)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    default int updateByPrimaryKeySelective(VehicleDisposalDingTalkResult record) {
        return update(c ->
            c.set(disposalId).equalToWhenPresent(record::getDisposalId)
            .set(originalValue).equalToWhenPresent(record::getOriginalValue)
            .set(depreciationTaken).equalToWhenPresent(record::getDepreciationTaken)
            .set(netValueOrSalvage).equalToWhenPresent(record::getNetValueOrSalvage)
            .set(estimatedProfitLoss).equalToWhenPresent(record::getEstimatedProfitLoss)
            .set(actualVehiclesSold).equalToWhenPresent(record::getActualVehiclesSold)
            .set(actualTotalProfitLoss).equalToWhenPresent(record::getActualTotalProfitLoss)
            .set(actualPerVehicleProfitLoss).equalToWhenPresent(record::getActualPerVehicleProfitLoss)
            .set(actualVsEstimatedDiffOne).equalToWhenPresent(record::getActualVsEstimatedDiffOne)
            .set(actualVsEstimatedDiffTwo).equalToWhenPresent(record::getActualVsEstimatedDiffTwo)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}