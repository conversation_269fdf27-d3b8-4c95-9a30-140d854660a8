package com.dazhong.transportation.vlms.mapper.extend;

import com.dazhong.transportation.vlms.mapper.VehicleManagementLegacyInfoMapper;
import com.dazhong.transportation.vlms.model.VehicleManagementLegacyInfo;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import static com.dazhong.transportation.vlms.mapper.VehicleManagementLegacyInfoDynamicSqlSupport.*;

@Mapper
public interface VehicleManagementLegacyInfoExtendMapper extends VehicleManagementLegacyInfoMapper {

    default int insertSelectiveWithId(VehicleManagementLegacyInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleManagementLegacyInfo, c ->
                c.map(id).toPropertyWhenPresent("id", record::getId)
                        .map(vin).toPropertyWhenPresent("vin", record::getVin)
                        .map(operateTypeId).toPropertyWhenPresent("operateTypeId", record::getOperateTypeId)
                        .map(vehicleCategoryId).toPropertyWhenPresent("vehicleCategoryId", record::getVehicleCategoryId)
                        .map(operationCategoryId).toPropertyWhenPresent("operationCategoryId", record::getOperationCategoryId)
                        .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
                        .map(companyOwnerId).toPropertyWhenPresent("companyOwnerId", record::getCompanyOwnerId)
                        .map(assetOwnerId).toPropertyWhenPresent("assetOwnerId", record::getAssetOwnerId)
                        .map(contractTypeId).toPropertyWhenPresent("contractTypeId", record::getContractTypeId)
                        .map(operatingNo).toPropertyWhenPresent("operatingNo", record::getOperatingNo)
                        .map(startDate).toPropertyWhenPresent("startDate", record::getStartDate)
                        .map(purchaseDate).toPropertyWhenPresent("purchaseDate", record::getPurchaseDate)
                        .map(licenseDate).toPropertyWhenPresent("licenseDate", record::getLicenseDate)
                        .map(operationStartDate).toPropertyWhenPresent("operationStartDate", record::getOperationStartDate)
                        .map(hasRight).toPropertyWhenPresent("hasRight", record::getHasRight)
                        .map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", record::getVehicleModelName)
                        .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
                        .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
                        .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
                        .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
                        .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
                        .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
                        .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }
}
