package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class LicensePlateTaskQuotaDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    public static final LicensePlateTaskQuotaDetail licensePlateTaskQuotaDetail = new LicensePlateTaskQuotaDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.id")
    public static final SqlColumn<Long> id = licensePlateTaskQuotaDetail.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.task_number")
    public static final SqlColumn<String> taskNumber = licensePlateTaskQuotaDetail.taskNumber;

    /**
     * Database Column Remarks:
     *   额度编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.quota_number")
    public static final SqlColumn<String> quotaNumber = licensePlateTaskQuotaDetail.quotaNumber;

    /**
     * Database Column Remarks:
     *   额度单打印时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.quota_print_date")
    public static final SqlColumn<Date> quotaPrintDate = licensePlateTaskQuotaDetail.quotaPrintDate;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.quota_type")
    public static final SqlColumn<Integer> quotaType = licensePlateTaskQuotaDetail.quotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.quota_asset_company_id")
    public static final SqlColumn<Integer> quotaAssetCompanyId = licensePlateTaskQuotaDetail.quotaAssetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.quota_asset_company_name")
    public static final SqlColumn<String> quotaAssetCompanyName = licensePlateTaskQuotaDetail.quotaAssetCompanyName;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.license_plate")
    public static final SqlColumn<String> licensePlate = licensePlateTaskQuotaDetail.licensePlate;

    /**
     * Database Column Remarks:
     *   退牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.license_plate_withdrawal_date")
    public static final SqlColumn<Date> licensePlateWithdrawalDate = licensePlateTaskQuotaDetail.licensePlateWithdrawalDate;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.is_deleted")
    public static final SqlColumn<Integer> isDeleted = licensePlateTaskQuotaDetail.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.create_time")
    public static final SqlColumn<Date> createTime = licensePlateTaskQuotaDetail.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.create_oper_id")
    public static final SqlColumn<Long> createOperId = licensePlateTaskQuotaDetail.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.create_oper_name")
    public static final SqlColumn<String> createOperName = licensePlateTaskQuotaDetail.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.update_time")
    public static final SqlColumn<Date> updateTime = licensePlateTaskQuotaDetail.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.update_oper_id")
    public static final SqlColumn<Long> updateOperId = licensePlateTaskQuotaDetail.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_quota_detail.update_oper_name")
    public static final SqlColumn<String> updateOperName = licensePlateTaskQuotaDetail.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_quota_detail")
    public static final class LicensePlateTaskQuotaDetail extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNumber = column("task_number", JDBCType.VARCHAR);

        public final SqlColumn<String> quotaNumber = column("quota_number", JDBCType.VARCHAR);

        public final SqlColumn<Date> quotaPrintDate = column("quota_print_date", JDBCType.DATE);

        public final SqlColumn<Integer> quotaType = column("quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> quotaAssetCompanyId = column("quota_asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<String> quotaAssetCompanyName = column("quota_asset_company_name", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<Date> licensePlateWithdrawalDate = column("license_plate_withdrawal_date", JDBCType.DATE);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public LicensePlateTaskQuotaDetail() {
            super("t_license_plate_task_quota_detail");
        }
    }
}