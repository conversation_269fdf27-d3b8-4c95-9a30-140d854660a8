package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleTransferFixedApplyDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleTransferFixedApply;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleTransferFixedApplyMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    BasicColumn[] selectList = BasicColumn.columnList(id, applyNo, approvalNumber, applyStatus, applyName, applyRemark, transferQuantity, ownerId, applyOrgId, applyUserNo, applyUser, departmentCode, departmentName, applicantDepartmentCode, applicantDepartmentName, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleTransferFixedApply> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleTransferFixedApplyResult")
    Optional<VehicleTransferFixedApply> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleTransferFixedApplyResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="apply_no", property="applyNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="approval_number", property="approvalNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="apply_status", property="applyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="apply_name", property="applyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="apply_remark", property="applyRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="transfer_quantity", property="transferQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="apply_org_id", property="applyOrgId", jdbcType=JdbcType.BIGINT),
        @Result(column="apply_user_no", property="applyUserNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="apply_user", property="applyUser", jdbcType=JdbcType.VARCHAR),
        @Result(column="department_code", property="departmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="department_name", property="departmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="applicant_department_code", property="applicantDepartmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="applicant_department_name", property="applicantDepartmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleTransferFixedApply> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleTransferFixedApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleTransferFixedApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default int insert(VehicleTransferFixedApply record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleTransferFixedApply, c ->
            c.map(applyNo).toProperty("applyNo")
            .map(approvalNumber).toProperty("approvalNumber")
            .map(applyStatus).toProperty("applyStatus")
            .map(applyName).toProperty("applyName")
            .map(applyRemark).toProperty("applyRemark")
            .map(transferQuantity).toProperty("transferQuantity")
            .map(ownerId).toProperty("ownerId")
            .map(applyOrgId).toProperty("applyOrgId")
            .map(applyUserNo).toProperty("applyUserNo")
            .map(applyUser).toProperty("applyUser")
            .map(departmentCode).toProperty("departmentCode")
            .map(departmentName).toProperty("departmentName")
            .map(applicantDepartmentCode).toProperty("applicantDepartmentCode")
            .map(applicantDepartmentName).toProperty("applicantDepartmentName")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default int insertSelective(VehicleTransferFixedApply record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleTransferFixedApply, c ->
            c.map(applyNo).toPropertyWhenPresent("applyNo", record::getApplyNo)
            .map(approvalNumber).toPropertyWhenPresent("approvalNumber", record::getApprovalNumber)
            .map(applyStatus).toPropertyWhenPresent("applyStatus", record::getApplyStatus)
            .map(applyName).toPropertyWhenPresent("applyName", record::getApplyName)
            .map(applyRemark).toPropertyWhenPresent("applyRemark", record::getApplyRemark)
            .map(transferQuantity).toPropertyWhenPresent("transferQuantity", record::getTransferQuantity)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(applyOrgId).toPropertyWhenPresent("applyOrgId", record::getApplyOrgId)
            .map(applyUserNo).toPropertyWhenPresent("applyUserNo", record::getApplyUserNo)
            .map(applyUser).toPropertyWhenPresent("applyUser", record::getApplyUser)
            .map(departmentCode).toPropertyWhenPresent("departmentCode", record::getDepartmentCode)
            .map(departmentName).toPropertyWhenPresent("departmentName", record::getDepartmentName)
            .map(applicantDepartmentCode).toPropertyWhenPresent("applicantDepartmentCode", record::getApplicantDepartmentCode)
            .map(applicantDepartmentName).toPropertyWhenPresent("applicantDepartmentName", record::getApplicantDepartmentName)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default Optional<VehicleTransferFixedApply> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleTransferFixedApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default List<VehicleTransferFixedApply> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleTransferFixedApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default List<VehicleTransferFixedApply> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleTransferFixedApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default Optional<VehicleTransferFixedApply> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleTransferFixedApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleTransferFixedApply record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applyNo).equalTo(record::getApplyNo)
                .set(approvalNumber).equalTo(record::getApprovalNumber)
                .set(applyStatus).equalTo(record::getApplyStatus)
                .set(applyName).equalTo(record::getApplyName)
                .set(applyRemark).equalTo(record::getApplyRemark)
                .set(transferQuantity).equalTo(record::getTransferQuantity)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(applyOrgId).equalTo(record::getApplyOrgId)
                .set(applyUserNo).equalTo(record::getApplyUserNo)
                .set(applyUser).equalTo(record::getApplyUser)
                .set(departmentCode).equalTo(record::getDepartmentCode)
                .set(departmentName).equalTo(record::getDepartmentName)
                .set(applicantDepartmentCode).equalTo(record::getApplicantDepartmentCode)
                .set(applicantDepartmentName).equalTo(record::getApplicantDepartmentName)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleTransferFixedApply record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applyNo).equalToWhenPresent(record::getApplyNo)
                .set(approvalNumber).equalToWhenPresent(record::getApprovalNumber)
                .set(applyStatus).equalToWhenPresent(record::getApplyStatus)
                .set(applyName).equalToWhenPresent(record::getApplyName)
                .set(applyRemark).equalToWhenPresent(record::getApplyRemark)
                .set(transferQuantity).equalToWhenPresent(record::getTransferQuantity)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(applyOrgId).equalToWhenPresent(record::getApplyOrgId)
                .set(applyUserNo).equalToWhenPresent(record::getApplyUserNo)
                .set(applyUser).equalToWhenPresent(record::getApplyUser)
                .set(departmentCode).equalToWhenPresent(record::getDepartmentCode)
                .set(departmentName).equalToWhenPresent(record::getDepartmentName)
                .set(applicantDepartmentCode).equalToWhenPresent(record::getApplicantDepartmentCode)
                .set(applicantDepartmentName).equalToWhenPresent(record::getApplicantDepartmentName)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default int updateByPrimaryKey(VehicleTransferFixedApply record) {
        return update(c ->
            c.set(applyNo).equalTo(record::getApplyNo)
            .set(approvalNumber).equalTo(record::getApprovalNumber)
            .set(applyStatus).equalTo(record::getApplyStatus)
            .set(applyName).equalTo(record::getApplyName)
            .set(applyRemark).equalTo(record::getApplyRemark)
            .set(transferQuantity).equalTo(record::getTransferQuantity)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(applyOrgId).equalTo(record::getApplyOrgId)
            .set(applyUserNo).equalTo(record::getApplyUserNo)
            .set(applyUser).equalTo(record::getApplyUser)
            .set(departmentCode).equalTo(record::getDepartmentCode)
            .set(departmentName).equalTo(record::getDepartmentName)
            .set(applicantDepartmentCode).equalTo(record::getApplicantDepartmentCode)
            .set(applicantDepartmentName).equalTo(record::getApplicantDepartmentName)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    default int updateByPrimaryKeySelective(VehicleTransferFixedApply record) {
        return update(c ->
            c.set(applyNo).equalToWhenPresent(record::getApplyNo)
            .set(approvalNumber).equalToWhenPresent(record::getApprovalNumber)
            .set(applyStatus).equalToWhenPresent(record::getApplyStatus)
            .set(applyName).equalToWhenPresent(record::getApplyName)
            .set(applyRemark).equalToWhenPresent(record::getApplyRemark)
            .set(transferQuantity).equalToWhenPresent(record::getTransferQuantity)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(applyOrgId).equalToWhenPresent(record::getApplyOrgId)
            .set(applyUserNo).equalToWhenPresent(record::getApplyUserNo)
            .set(applyUser).equalToWhenPresent(record::getApplyUser)
            .set(departmentCode).equalToWhenPresent(record::getDepartmentCode)
            .set(departmentName).equalToWhenPresent(record::getDepartmentName)
            .set(applicantDepartmentCode).equalToWhenPresent(record::getApplicantDepartmentCode)
            .set(applicantDepartmentName).equalToWhenPresent(record::getApplicantDepartmentName)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}