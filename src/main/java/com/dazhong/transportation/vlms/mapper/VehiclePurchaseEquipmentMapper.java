package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseEquipmentDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehiclePurchaseEquipment;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehiclePurchaseEquipmentMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    BasicColumn[] selectList = BasicColumn.columnList(id, purchaseApplyId, equipmentName, manufacturerId, supplierId, unitPrice, quantity, otherCosts, totalPrice, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Integer.class)
    int insert(InsertStatementProvider<VehiclePurchaseEquipment> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehiclePurchaseEquipmentResult")
    Optional<VehiclePurchaseEquipment> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehiclePurchaseEquipmentResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="purchase_apply_id", property="purchaseApplyId", jdbcType=JdbcType.BIGINT),
        @Result(column="equipment_name", property="equipmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="manufacturer_id", property="manufacturerId", jdbcType=JdbcType.INTEGER),
        @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
        @Result(column="unit_price", property="unitPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="quantity", property="quantity", jdbcType=JdbcType.INTEGER),
        @Result(column="other_costs", property="otherCosts", jdbcType=JdbcType.DECIMAL),
        @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehiclePurchaseEquipment> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehiclePurchaseEquipment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehiclePurchaseEquipment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default int deleteByPrimaryKey(Integer id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default int insert(VehiclePurchaseEquipment record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseEquipment, c ->
            c.map(purchaseApplyId).toProperty("purchaseApplyId")
            .map(equipmentName).toProperty("equipmentName")
            .map(manufacturerId).toProperty("manufacturerId")
            .map(supplierId).toProperty("supplierId")
            .map(unitPrice).toProperty("unitPrice")
            .map(quantity).toProperty("quantity")
            .map(otherCosts).toProperty("otherCosts")
            .map(totalPrice).toProperty("totalPrice")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default int insertSelective(VehiclePurchaseEquipment record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseEquipment, c ->
            c.map(purchaseApplyId).toPropertyWhenPresent("purchaseApplyId", record::getPurchaseApplyId)
            .map(equipmentName).toPropertyWhenPresent("equipmentName", record::getEquipmentName)
            .map(manufacturerId).toPropertyWhenPresent("manufacturerId", record::getManufacturerId)
            .map(supplierId).toPropertyWhenPresent("supplierId", record::getSupplierId)
            .map(unitPrice).toPropertyWhenPresent("unitPrice", record::getUnitPrice)
            .map(quantity).toPropertyWhenPresent("quantity", record::getQuantity)
            .map(otherCosts).toPropertyWhenPresent("otherCosts", record::getOtherCosts)
            .map(totalPrice).toPropertyWhenPresent("totalPrice", record::getTotalPrice)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default Optional<VehiclePurchaseEquipment> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehiclePurchaseEquipment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default List<VehiclePurchaseEquipment> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehiclePurchaseEquipment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default List<VehiclePurchaseEquipment> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehiclePurchaseEquipment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default Optional<VehiclePurchaseEquipment> selectByPrimaryKey(Integer id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehiclePurchaseEquipment, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    static UpdateDSL<UpdateModel> updateAllColumns(VehiclePurchaseEquipment record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
                .set(equipmentName).equalTo(record::getEquipmentName)
                .set(manufacturerId).equalTo(record::getManufacturerId)
                .set(supplierId).equalTo(record::getSupplierId)
                .set(unitPrice).equalTo(record::getUnitPrice)
                .set(quantity).equalTo(record::getQuantity)
                .set(otherCosts).equalTo(record::getOtherCosts)
                .set(totalPrice).equalTo(record::getTotalPrice)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehiclePurchaseEquipment record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
                .set(equipmentName).equalToWhenPresent(record::getEquipmentName)
                .set(manufacturerId).equalToWhenPresent(record::getManufacturerId)
                .set(supplierId).equalToWhenPresent(record::getSupplierId)
                .set(unitPrice).equalToWhenPresent(record::getUnitPrice)
                .set(quantity).equalToWhenPresent(record::getQuantity)
                .set(otherCosts).equalToWhenPresent(record::getOtherCosts)
                .set(totalPrice).equalToWhenPresent(record::getTotalPrice)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default int updateByPrimaryKey(VehiclePurchaseEquipment record) {
        return update(c ->
            c.set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
            .set(equipmentName).equalTo(record::getEquipmentName)
            .set(manufacturerId).equalTo(record::getManufacturerId)
            .set(supplierId).equalTo(record::getSupplierId)
            .set(unitPrice).equalTo(record::getUnitPrice)
            .set(quantity).equalTo(record::getQuantity)
            .set(otherCosts).equalTo(record::getOtherCosts)
            .set(totalPrice).equalTo(record::getTotalPrice)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    default int updateByPrimaryKeySelective(VehiclePurchaseEquipment record) {
        return update(c ->
            c.set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
            .set(equipmentName).equalToWhenPresent(record::getEquipmentName)
            .set(manufacturerId).equalToWhenPresent(record::getManufacturerId)
            .set(supplierId).equalToWhenPresent(record::getSupplierId)
            .set(unitPrice).equalToWhenPresent(record::getUnitPrice)
            .set(quantity).equalToWhenPresent(record::getQuantity)
            .set(otherCosts).equalToWhenPresent(record::getOtherCosts)
            .set(totalPrice).equalToWhenPresent(record::getTotalPrice)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}