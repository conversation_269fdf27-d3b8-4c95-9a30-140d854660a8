package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.model.LicensePlateTaskInfo;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import javax.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface LicensePlateTaskInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNumber, taskType, useQuotaType, returnQuotaType, assetCompanyId, assetCompanyName, quotaType, matchType, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<LicensePlateTaskInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LicensePlateTaskInfoResult")
    Optional<LicensePlateTaskInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LicensePlateTaskInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_number", property="taskNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="task_type", property="taskType", jdbcType=JdbcType.INTEGER),
        @Result(column="use_quota_type", property="useQuotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="return_quota_type", property="returnQuotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_name", property="assetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="match_type", property="matchType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<LicensePlateTaskInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, licensePlateTaskInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, licensePlateTaskInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default int insert(LicensePlateTaskInfo record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateTaskInfo, c ->
            c.map(taskNumber).toProperty("taskNumber")
            .map(taskType).toProperty("taskType")
            .map(useQuotaType).toProperty("useQuotaType")
            .map(returnQuotaType).toProperty("returnQuotaType")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(assetCompanyName).toProperty("assetCompanyName")
            .map(quotaType).toProperty("quotaType")
            .map(matchType).toProperty("matchType")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default int insertSelective(LicensePlateTaskInfo record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateTaskInfo, c ->
            c.map(taskNumber).toPropertyWhenPresent("taskNumber", record::getTaskNumber)
            .map(taskType).toPropertyWhenPresent("taskType", record::getTaskType)
            .map(useQuotaType).toPropertyWhenPresent("useQuotaType", record::getUseQuotaType)
            .map(returnQuotaType).toPropertyWhenPresent("returnQuotaType", record::getReturnQuotaType)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(assetCompanyName).toPropertyWhenPresent("assetCompanyName", record::getAssetCompanyName)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(matchType).toPropertyWhenPresent("matchType", record::getMatchType)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default Optional<LicensePlateTaskInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, licensePlateTaskInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default List<LicensePlateTaskInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, licensePlateTaskInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default List<LicensePlateTaskInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, licensePlateTaskInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default Optional<LicensePlateTaskInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, licensePlateTaskInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    static UpdateDSL<UpdateModel> updateAllColumns(LicensePlateTaskInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNumber).equalTo(record::getTaskNumber)
                .set(taskType).equalTo(record::getTaskType)
                .set(useQuotaType).equalTo(record::getUseQuotaType)
                .set(returnQuotaType).equalTo(record::getReturnQuotaType)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(assetCompanyName).equalTo(record::getAssetCompanyName)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(matchType).equalTo(record::getMatchType)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LicensePlateTaskInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNumber).equalToWhenPresent(record::getTaskNumber)
                .set(taskType).equalToWhenPresent(record::getTaskType)
                .set(useQuotaType).equalToWhenPresent(record::getUseQuotaType)
                .set(returnQuotaType).equalToWhenPresent(record::getReturnQuotaType)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(matchType).equalToWhenPresent(record::getMatchType)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default int updateByPrimaryKey(LicensePlateTaskInfo record) {
        return update(c ->
            c.set(taskNumber).equalTo(record::getTaskNumber)
            .set(taskType).equalTo(record::getTaskType)
            .set(useQuotaType).equalTo(record::getUseQuotaType)
            .set(returnQuotaType).equalTo(record::getReturnQuotaType)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(assetCompanyName).equalTo(record::getAssetCompanyName)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(matchType).equalTo(record::getMatchType)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    default int updateByPrimaryKeySelective(LicensePlateTaskInfo record) {
        return update(c ->
            c.set(taskNumber).equalToWhenPresent(record::getTaskNumber)
            .set(taskType).equalToWhenPresent(record::getTaskType)
            .set(useQuotaType).equalToWhenPresent(record::getUseQuotaType)
            .set(returnQuotaType).equalToWhenPresent(record::getReturnQuotaType)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(matchType).equalToWhenPresent(record::getMatchType)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_license_plate_task_info")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "selectTaskVehicleDetailResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "task_number", property = "taskNumber", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_type", property = "taskType", jdbcType = JdbcType.INTEGER),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_oper_name", property = "createOperName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "license_plate", property = "licensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin", property = "vin", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicleModelId", property = "vehicleModelId", jdbcType = JdbcType.BIGINT),
            @Result(column = "assetCompanyId", property = "assetCompanyId", jdbcType = JdbcType.INTEGER),
            @Result(column = "ownOrganizationId", property = "ownOrganizationId", jdbcType = JdbcType.BIGINT),
            @Result(column = "usageOrganizationId", property = "usageOrganizationId", jdbcType = JdbcType.BIGINT),
            @Result(column = "returnQuotaType", property = "returnQuotaType", jdbcType = JdbcType.INTEGER),
            @Result(column = "quotaType", property = "quotaType", jdbcType = JdbcType.INTEGER),
            @Result(column = "quotaAssetCompanyId", property = "quotaAssetCompanyId", jdbcType = JdbcType.INTEGER),
            @Result(column = "quotaAssetCompanyName", property = "quotaAssetCompanyName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "issuanceDateRegistrationCard", property = "issuanceDateRegistrationCard", jdbcType = JdbcType.DATE),

            @Result(column = "quotaNumber", property = "quotaNumber", jdbcType = JdbcType.VARCHAR),
            @Result(column = "quotaPrintDate", property = "quotaPrintDate", jdbcType = JdbcType.DATE),
            @Result(column = "registration_time", property = "registrationTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "registrant", property = "registrant", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_type_license_plate", property = "vehicleTypeLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "use_nature_license_plate", property = "useNatureLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "registration_date_license_plate", property = "registrationDateLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "issuance_date_license_plate", property = "issuanceDateLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "fileNumber", property = "fileNumber", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mandatory_scrapping_date", property = "mandatoryScrappingDate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "annual_inspection_due_date", property = "annualInspectionDueDate", jdbcType = JdbcType.VARCHAR),
    })
    List<LicensePlateTaskVehicleDetailDto> selectTaskVehicleDetail(SelectStatementProvider selectStatement);

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_license_plate_task_info")
    default Optional<LicensePlateTaskInfo> selectByTaskNumber(String taskNumber_) {
        return selectOne(c ->
                c.where(taskNumber, isEqualTo(taskNumber_))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_license_plate_task_info")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "selectLatestVehicleDetailByVinResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "task_number", property = "taskNumber", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_type", property = "taskType", jdbcType = JdbcType.INTEGER),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_oper_name", property = "createOperName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "license_plate", property = "licensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin", property = "vin", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicleModelId", property = "vehicleModelId", jdbcType = JdbcType.BIGINT),
            @Result(column = "assetCompanyId", property = "assetCompanyId", jdbcType = JdbcType.INTEGER),
            @Result(column = "ownOrganizationId", property = "ownOrganizationId", jdbcType = JdbcType.BIGINT),
            @Result(column = "usageOrganizationId", property = "usageOrganizationId", jdbcType = JdbcType.BIGINT),
            @Result(column = "returnQuotaType", property = "returnQuotaType", jdbcType = JdbcType.INTEGER),
            @Result(column = "quotaType", property = "quotaType", jdbcType = JdbcType.INTEGER),
            @Result(column = "quotaAssetCompanyId", property = "quotaAssetCompanyId", jdbcType = JdbcType.INTEGER),
            @Result(column = "quotaAssetCompanyName", property = "quotaAssetCompanyName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "issuanceDateRegistrationCard", property = "issuanceDateRegistrationCard", jdbcType = JdbcType.DATE),

            @Result(column = "quotaNumber", property = "quotaNumber", jdbcType = JdbcType.VARCHAR),
            @Result(column = "quotaPrintDate", property = "quotaPrintDate", jdbcType = JdbcType.DATE),
            @Result(column = "registration_time", property = "registrationTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "registrant", property = "registrant", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_type_license_plate", property = "vehicleTypeLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "use_nature_license_plate", property = "useNatureLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "registration_date_license_plate", property = "registrationDateLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "issuance_date_license_plate", property = "issuanceDateLicensePlate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "fileNumber", property = "fileNumber", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mandatory_scrapping_date", property = "mandatoryScrappingDate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "annual_inspection_due_date", property = "annualInspectionDueDate", jdbcType = JdbcType.VARCHAR),
    })
    LicensePlateTaskVehicleDetailDto selectLatestVehicleDetailByVin(SelectStatementProvider selectStatement);
}