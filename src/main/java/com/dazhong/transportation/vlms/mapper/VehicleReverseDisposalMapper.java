package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalDetailListDto;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalListDto;
import com.dazhong.transportation.vlms.model.VehicleReverseDisposal;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import static com.dazhong.transportation.vlms.mapper.VehicleApplicationDynamicSqlSupport.dingTalkNo;
import static com.dazhong.transportation.vlms.mapper.VehicleReverseDisposalDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface VehicleReverseDisposalMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    BasicColumn[] selectList = BasicColumn.columnList(id, documentNo, documentTitle, documentStatus, productLine, originatorDeptId, originatorDeptName, organizationId, organizationName, ownerId, ownerName, departmentCode, departmentName, dingTalkNo, submitDate, remark, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleReverseDisposal> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleReverseDisposalResult")
    Optional<VehicleReverseDisposal> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleReverseDisposalResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="document_no", property="documentNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="document_title", property="documentTitle", jdbcType=JdbcType.VARCHAR),
        @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="originator_dept_id", property="originatorDeptId", jdbcType=JdbcType.BIGINT),
        @Result(column="originator_dept_name", property="originatorDeptName", jdbcType=JdbcType.VARCHAR),
        @Result(column="organization_id", property="organizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="organization_name", property="organizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_name", property="ownerName", jdbcType=JdbcType.VARCHAR),
        @Result(column="department_code", property="departmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="department_name", property="departmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleReverseDisposal> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleReverseDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleReverseDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default int insert(VehicleReverseDisposal record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleReverseDisposal, c ->
            c.map(documentNo).toProperty("documentNo")
            .map(documentTitle).toProperty("documentTitle")
            .map(documentStatus).toProperty("documentStatus")
            .map(productLine).toProperty("productLine")
            .map(originatorDeptId).toProperty("originatorDeptId")
            .map(originatorDeptName).toProperty("originatorDeptName")
            .map(organizationId).toProperty("organizationId")
            .map(organizationName).toProperty("organizationName")
            .map(ownerId).toProperty("ownerId")
            .map(ownerName).toProperty("ownerName")
            .map(departmentCode).toProperty("departmentCode")
            .map(departmentName).toProperty("departmentName")
            .map(dingTalkNo).toProperty("dingTalkNo")
            .map(submitDate).toProperty("submitDate")
            .map(remark).toProperty("remark")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default int insertSelective(VehicleReverseDisposal record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleReverseDisposal, c ->
            c.map(documentNo).toPropertyWhenPresent("documentNo", record::getDocumentNo)
            .map(documentTitle).toPropertyWhenPresent("documentTitle", record::getDocumentTitle)
            .map(documentStatus).toPropertyWhenPresent("documentStatus", record::getDocumentStatus)
            .map(productLine).toPropertyWhenPresent("productLine", record::getProductLine)
            .map(originatorDeptId).toPropertyWhenPresent("originatorDeptId", record::getOriginatorDeptId)
            .map(originatorDeptName).toPropertyWhenPresent("originatorDeptName", record::getOriginatorDeptName)
            .map(organizationId).toPropertyWhenPresent("organizationId", record::getOrganizationId)
            .map(organizationName).toPropertyWhenPresent("organizationName", record::getOrganizationName)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(ownerName).toPropertyWhenPresent("ownerName", record::getOwnerName)
            .map(departmentCode).toPropertyWhenPresent("departmentCode", record::getDepartmentCode)
            .map(departmentName).toPropertyWhenPresent("departmentName", record::getDepartmentName)
            .map(dingTalkNo).toPropertyWhenPresent("dingTalkNo", record::getDingTalkNo)
            .map(submitDate).toPropertyWhenPresent("submitDate", record::getSubmitDate)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default Optional<VehicleReverseDisposal> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleReverseDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default List<VehicleReverseDisposal> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleReverseDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default List<VehicleReverseDisposal> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleReverseDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default Optional<VehicleReverseDisposal> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleReverseDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleReverseDisposal record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(documentNo).equalTo(record::getDocumentNo)
                .set(documentTitle).equalTo(record::getDocumentTitle)
                .set(documentStatus).equalTo(record::getDocumentStatus)
                .set(productLine).equalTo(record::getProductLine)
                .set(originatorDeptId).equalTo(record::getOriginatorDeptId)
                .set(originatorDeptName).equalTo(record::getOriginatorDeptName)
                .set(organizationId).equalTo(record::getOrganizationId)
                .set(organizationName).equalTo(record::getOrganizationName)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(ownerName).equalTo(record::getOwnerName)
                .set(departmentCode).equalTo(record::getDepartmentCode)
                .set(departmentName).equalTo(record::getDepartmentName)
                .set(dingTalkNo).equalTo(record::getDingTalkNo)
                .set(submitDate).equalTo(record::getSubmitDate)
                .set(remark).equalTo(record::getRemark)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleReverseDisposal record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(documentNo).equalToWhenPresent(record::getDocumentNo)
                .set(documentTitle).equalToWhenPresent(record::getDocumentTitle)
                .set(documentStatus).equalToWhenPresent(record::getDocumentStatus)
                .set(productLine).equalToWhenPresent(record::getProductLine)
                .set(originatorDeptId).equalToWhenPresent(record::getOriginatorDeptId)
                .set(originatorDeptName).equalToWhenPresent(record::getOriginatorDeptName)
                .set(organizationId).equalToWhenPresent(record::getOrganizationId)
                .set(organizationName).equalToWhenPresent(record::getOrganizationName)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(ownerName).equalToWhenPresent(record::getOwnerName)
                .set(departmentCode).equalToWhenPresent(record::getDepartmentCode)
                .set(departmentName).equalToWhenPresent(record::getDepartmentName)
                .set(dingTalkNo).equalToWhenPresent(record::getDingTalkNo)
                .set(submitDate).equalToWhenPresent(record::getSubmitDate)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default int updateByPrimaryKey(VehicleReverseDisposal record) {
        return update(c ->
            c.set(documentNo).equalTo(record::getDocumentNo)
            .set(documentTitle).equalTo(record::getDocumentTitle)
            .set(documentStatus).equalTo(record::getDocumentStatus)
            .set(productLine).equalTo(record::getProductLine)
            .set(originatorDeptId).equalTo(record::getOriginatorDeptId)
            .set(originatorDeptName).equalTo(record::getOriginatorDeptName)
            .set(organizationId).equalTo(record::getOrganizationId)
            .set(organizationName).equalTo(record::getOrganizationName)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(ownerName).equalTo(record::getOwnerName)
            .set(departmentCode).equalTo(record::getDepartmentCode)
            .set(departmentName).equalTo(record::getDepartmentName)
            .set(dingTalkNo).equalTo(record::getDingTalkNo)
            .set(submitDate).equalTo(record::getSubmitDate)
            .set(remark).equalTo(record::getRemark)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    default int updateByPrimaryKeySelective(VehicleReverseDisposal record) {
        return update(c ->
            c.set(documentNo).equalToWhenPresent(record::getDocumentNo)
            .set(documentTitle).equalToWhenPresent(record::getDocumentTitle)
            .set(documentStatus).equalToWhenPresent(record::getDocumentStatus)
            .set(productLine).equalToWhenPresent(record::getProductLine)
            .set(originatorDeptId).equalToWhenPresent(record::getOriginatorDeptId)
            .set(originatorDeptName).equalToWhenPresent(record::getOriginatorDeptName)
            .set(organizationId).equalToWhenPresent(record::getOrganizationId)
            .set(organizationName).equalToWhenPresent(record::getOrganizationName)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(ownerName).equalToWhenPresent(record::getOwnerName)
            .set(departmentCode).equalToWhenPresent(record::getDepartmentCode)
            .set(departmentName).equalToWhenPresent(record::getDepartmentName)
            .set(dingTalkNo).equalToWhenPresent(record::getDingTalkNo)
            .set(submitDate).equalToWhenPresent(record::getSubmitDate)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleReverseDisposalListResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
            @Result(column="document_no", property="documentNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="document_type", property="documentType", jdbcType=JdbcType.INTEGER),
            @Result(column="document_title", property="documentTitle", jdbcType=JdbcType.VARCHAR),
            @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
            @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
            @Result(column="task_number", property="taskNumber", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleReverseDisposalListDto> selectVehicleReverseDisposalList(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id="VehicleReverseDisposalDetailListResult", value = {
            @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
            @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
    })
    List<VehicleReverseDisposalDetailListDto> selectVehicleReverseDisposalByVin(SelectStatementProvider selectStatement);


    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_disposal")
    default Optional<VehicleReverseDisposal> selectByDingTalkNo(String dingTalkNo_) {
        return selectOne(c ->
                c.where(dingTalkNo, isEqualTo(dingTalkNo_))
        );
    }
}