package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskVehicleDetailDynamicSqlSupport.createOperId;
import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskVehicleDetailDynamicSqlSupport.createOperName;
import static com.dazhong.transportation.vlms.mapper.VehicleLocationCanInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;
import com.dazhong.transportation.vlms.model.VehicleLocationCanInfo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

@Mapper
public interface VehicleLocationCanInfoMapper  extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<VehicleLocationCanInfo> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, licensePlate, terminalNo, latitude, longitude, mileage, oilPercent, electricPercent, collectionTime, terminalChannel, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleLocationCanInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleLocationCanInfoResult")
    Optional<VehicleLocationCanInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleLocationCanInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="terminal_no", property="terminalNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="latitude", property="latitude", jdbcType=JdbcType.VARCHAR),
        @Result(column="longitude", property="longitude", jdbcType=JdbcType.VARCHAR),
        @Result(column="mileage", property="mileage", jdbcType=JdbcType.DECIMAL),
        @Result(column="oil_percent", property="oilPercent", jdbcType=JdbcType.DECIMAL),
        @Result(column="electric_percent", property="electricPercent", jdbcType=JdbcType.DECIMAL),
        @Result(column="collection_time", property="collectionTime", jdbcType=JdbcType.VARCHAR),
        @Result(column="terminal_channel", property="terminalChannel", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleLocationCanInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleLocationCanInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleLocationCanInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default int insert(VehicleLocationCanInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleLocationCanInfo, c ->
            c.map(vin).toProperty("vin")
            .map(licensePlate).toProperty("licensePlate")
            .map(terminalNo).toProperty("terminalNo")
            .map(latitude).toProperty("latitude")
            .map(longitude).toProperty("longitude")
            .map(mileage).toProperty("mileage")
            .map(oilPercent).toProperty("oilPercent")
            .map(electricPercent).toProperty("electricPercent")
            .map(collectionTime).toProperty("collectionTime")
            .map(terminalChannel).toProperty("terminalChannel")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default int insertSelective(VehicleLocationCanInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleLocationCanInfo, c ->
            c.map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(terminalNo).toPropertyWhenPresent("terminalNo", record::getTerminalNo)
            .map(latitude).toPropertyWhenPresent("latitude", record::getLatitude)
            .map(longitude).toPropertyWhenPresent("longitude", record::getLongitude)
            .map(mileage).toPropertyWhenPresent("mileage", record::getMileage)
            .map(oilPercent).toPropertyWhenPresent("oilPercent", record::getOilPercent)
            .map(electricPercent).toPropertyWhenPresent("electricPercent", record::getElectricPercent)
            .map(collectionTime).toPropertyWhenPresent("collectionTime", record::getCollectionTime)
            .map(terminalChannel).toPropertyWhenPresent("terminalChannel", record::getTerminalChannel)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default Optional<VehicleLocationCanInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleLocationCanInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default List<VehicleLocationCanInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleLocationCanInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default List<VehicleLocationCanInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleLocationCanInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default Optional<VehicleLocationCanInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleLocationCanInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleLocationCanInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(record::getVin)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(terminalNo).equalTo(record::getTerminalNo)
                .set(latitude).equalTo(record::getLatitude)
                .set(longitude).equalTo(record::getLongitude)
                .set(mileage).equalTo(record::getMileage)
                .set(oilPercent).equalTo(record::getOilPercent)
                .set(electricPercent).equalTo(record::getElectricPercent)
                .set(collectionTime).equalTo(record::getCollectionTime)
                .set(terminalChannel).equalTo(record::getTerminalChannel)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleLocationCanInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(record::getVin)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(terminalNo).equalToWhenPresent(record::getTerminalNo)
                .set(latitude).equalToWhenPresent(record::getLatitude)
                .set(longitude).equalToWhenPresent(record::getLongitude)
                .set(mileage).equalToWhenPresent(record::getMileage)
                .set(oilPercent).equalToWhenPresent(record::getOilPercent)
                .set(electricPercent).equalToWhenPresent(record::getElectricPercent)
                .set(collectionTime).equalToWhenPresent(record::getCollectionTime)
                .set(terminalChannel).equalToWhenPresent(record::getTerminalChannel)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default int updateByPrimaryKey(VehicleLocationCanInfo record) {
        return update(c ->
            c.set(vin).equalTo(record::getVin)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(terminalNo).equalTo(record::getTerminalNo)
            .set(latitude).equalTo(record::getLatitude)
            .set(longitude).equalTo(record::getLongitude)
            .set(mileage).equalTo(record::getMileage)
            .set(oilPercent).equalTo(record::getOilPercent)
            .set(electricPercent).equalTo(record::getElectricPercent)
            .set(collectionTime).equalTo(record::getCollectionTime)
            .set(terminalChannel).equalTo(record::getTerminalChannel)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    default int updateByPrimaryKeySelective(VehicleLocationCanInfo record) {
        return update(c ->
            c.set(vin).equalToWhenPresent(record::getVin)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(terminalNo).equalToWhenPresent(record::getTerminalNo)
            .set(latitude).equalToWhenPresent(record::getLatitude)
            .set(longitude).equalToWhenPresent(record::getLongitude)
            .set(mileage).equalToWhenPresent(record::getMileage)
            .set(oilPercent).equalToWhenPresent(record::getOilPercent)
            .set(electricPercent).equalToWhenPresent(record::getElectricPercent)
            .set(collectionTime).equalToWhenPresent(record::getCollectionTime)
            .set(terminalChannel).equalToWhenPresent(record::getTerminalChannel)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_location_can_info")
    default int insertMultiple(Collection<VehicleLocationCanInfo> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, vehicleLocationCanInfo, c -> c
                        .map(vin).toProperty("vin")
                        .map(licensePlate).toProperty("licensePlate")
                        .map(terminalNo).toProperty("terminalNo")
                        .map(latitude).toProperty("latitude")
                        .map(longitude).toProperty("longitude")
                        .map(mileage).toProperty("mileage")
                        .map(oilPercent).toProperty("oilPercent")
                        .map(electricPercent).toProperty("electricPercent")
                        .map(collectionTime).toProperty("collectionTime")
                        .map(terminalChannel).toProperty("terminalChannel")
                        .map(isDeleted).toProperty("isDeleted")
                        .map(createTime).toProperty("createTime")
                        .map(createOperId).toProperty("createOperId")
                        .map(createOperName).toProperty("createOperName")
                        .map(updateTime).toProperty("updateTime")
                        .map(updateOperId).toProperty("updateOperId")
                        .map(updateOperName).toProperty("updateOperName")
        );
    }
}