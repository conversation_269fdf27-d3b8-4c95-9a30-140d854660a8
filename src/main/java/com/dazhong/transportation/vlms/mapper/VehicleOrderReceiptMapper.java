package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleOrderReceiptDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleOrderReceipt;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleOrderReceiptMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    BasicColumn[] selectList = BasicColumn.columnList(id, purchaseApplyId, applyDetailsNo, vin, engineNo, vehicleModelId, vehicleColorId, interiorColor, orderDate, receiptDate, supplierId, ownerId, businessLine, isRepurchase, repurchaseDate, repurchaseRequirements, vehicleRegistrationMark, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleOrderReceipt> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleOrderReceiptResult")
    Optional<VehicleOrderReceipt> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleOrderReceiptResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="purchase_apply_id", property="purchaseApplyId", jdbcType=JdbcType.BIGINT),
        @Result(column="apply_details_no", property="applyDetailsNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
        @Result(column="interior_color", property="interiorColor", jdbcType=JdbcType.VARCHAR),
        @Result(column="order_date", property="orderDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="receipt_date", property="receiptDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
        @Result(column="is_repurchase", property="isRepurchase", jdbcType=JdbcType.INTEGER),
        @Result(column="repurchase_date", property="repurchaseDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="repurchase_requirements", property="repurchaseRequirements", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_registration_mark", property="vehicleRegistrationMark", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleOrderReceipt> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleOrderReceipt, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleOrderReceipt, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default int insert(VehicleOrderReceipt record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleOrderReceipt, c ->
            c.map(purchaseApplyId).toProperty("purchaseApplyId")
            .map(applyDetailsNo).toProperty("applyDetailsNo")
            .map(vin).toProperty("vin")
            .map(engineNo).toProperty("engineNo")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(vehicleColorId).toProperty("vehicleColorId")
            .map(interiorColor).toProperty("interiorColor")
            .map(orderDate).toProperty("orderDate")
            .map(receiptDate).toProperty("receiptDate")
            .map(supplierId).toProperty("supplierId")
            .map(ownerId).toProperty("ownerId")
            .map(businessLine).toProperty("businessLine")
            .map(isRepurchase).toProperty("isRepurchase")
            .map(repurchaseDate).toProperty("repurchaseDate")
            .map(repurchaseRequirements).toProperty("repurchaseRequirements")
            .map(vehicleRegistrationMark).toProperty("vehicleRegistrationMark")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default int insertSelective(VehicleOrderReceipt record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleOrderReceipt, c ->
            c.map(purchaseApplyId).toPropertyWhenPresent("purchaseApplyId", record::getPurchaseApplyId)
            .map(applyDetailsNo).toPropertyWhenPresent("applyDetailsNo", record::getApplyDetailsNo)
            .map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(engineNo).toPropertyWhenPresent("engineNo", record::getEngineNo)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
            .map(vehicleColorId).toPropertyWhenPresent("vehicleColorId", record::getVehicleColorId)
            .map(interiorColor).toPropertyWhenPresent("interiorColor", record::getInteriorColor)
            .map(orderDate).toPropertyWhenPresent("orderDate", record::getOrderDate)
            .map(receiptDate).toPropertyWhenPresent("receiptDate", record::getReceiptDate)
            .map(supplierId).toPropertyWhenPresent("supplierId", record::getSupplierId)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(businessLine).toPropertyWhenPresent("businessLine", record::getBusinessLine)
            .map(isRepurchase).toPropertyWhenPresent("isRepurchase", record::getIsRepurchase)
            .map(repurchaseDate).toPropertyWhenPresent("repurchaseDate", record::getRepurchaseDate)
            .map(repurchaseRequirements).toPropertyWhenPresent("repurchaseRequirements", record::getRepurchaseRequirements)
            .map(vehicleRegistrationMark).toPropertyWhenPresent("vehicleRegistrationMark", record::getVehicleRegistrationMark)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default Optional<VehicleOrderReceipt> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleOrderReceipt, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default List<VehicleOrderReceipt> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleOrderReceipt, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default List<VehicleOrderReceipt> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleOrderReceipt, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default Optional<VehicleOrderReceipt> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleOrderReceipt, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleOrderReceipt record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
                .set(applyDetailsNo).equalTo(record::getApplyDetailsNo)
                .set(vin).equalTo(record::getVin)
                .set(engineNo).equalTo(record::getEngineNo)
                .set(vehicleModelId).equalTo(record::getVehicleModelId)
                .set(vehicleColorId).equalTo(record::getVehicleColorId)
                .set(interiorColor).equalTo(record::getInteriorColor)
                .set(orderDate).equalTo(record::getOrderDate)
                .set(receiptDate).equalTo(record::getReceiptDate)
                .set(supplierId).equalTo(record::getSupplierId)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(businessLine).equalTo(record::getBusinessLine)
                .set(isRepurchase).equalTo(record::getIsRepurchase)
                .set(repurchaseDate).equalTo(record::getRepurchaseDate)
                .set(repurchaseRequirements).equalTo(record::getRepurchaseRequirements)
                .set(vehicleRegistrationMark).equalTo(record::getVehicleRegistrationMark)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleOrderReceipt record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
                .set(applyDetailsNo).equalToWhenPresent(record::getApplyDetailsNo)
                .set(vin).equalToWhenPresent(record::getVin)
                .set(engineNo).equalToWhenPresent(record::getEngineNo)
                .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
                .set(vehicleColorId).equalToWhenPresent(record::getVehicleColorId)
                .set(interiorColor).equalToWhenPresent(record::getInteriorColor)
                .set(orderDate).equalToWhenPresent(record::getOrderDate)
                .set(receiptDate).equalToWhenPresent(record::getReceiptDate)
                .set(supplierId).equalToWhenPresent(record::getSupplierId)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(businessLine).equalToWhenPresent(record::getBusinessLine)
                .set(isRepurchase).equalToWhenPresent(record::getIsRepurchase)
                .set(repurchaseDate).equalToWhenPresent(record::getRepurchaseDate)
                .set(repurchaseRequirements).equalToWhenPresent(record::getRepurchaseRequirements)
                .set(vehicleRegistrationMark).equalToWhenPresent(record::getVehicleRegistrationMark)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default int updateByPrimaryKey(VehicleOrderReceipt record) {
        return update(c ->
            c.set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
            .set(applyDetailsNo).equalTo(record::getApplyDetailsNo)
            .set(vin).equalTo(record::getVin)
            .set(engineNo).equalTo(record::getEngineNo)
            .set(vehicleModelId).equalTo(record::getVehicleModelId)
            .set(vehicleColorId).equalTo(record::getVehicleColorId)
            .set(interiorColor).equalTo(record::getInteriorColor)
            .set(orderDate).equalTo(record::getOrderDate)
            .set(receiptDate).equalTo(record::getReceiptDate)
            .set(supplierId).equalTo(record::getSupplierId)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(businessLine).equalTo(record::getBusinessLine)
            .set(isRepurchase).equalTo(record::getIsRepurchase)
            .set(repurchaseDate).equalTo(record::getRepurchaseDate)
            .set(repurchaseRequirements).equalTo(record::getRepurchaseRequirements)
            .set(vehicleRegistrationMark).equalTo(record::getVehicleRegistrationMark)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    default int updateByPrimaryKeySelective(VehicleOrderReceipt record) {
        return update(c ->
            c.set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
            .set(applyDetailsNo).equalToWhenPresent(record::getApplyDetailsNo)
            .set(vin).equalToWhenPresent(record::getVin)
            .set(engineNo).equalToWhenPresent(record::getEngineNo)
            .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
            .set(vehicleColorId).equalToWhenPresent(record::getVehicleColorId)
            .set(interiorColor).equalToWhenPresent(record::getInteriorColor)
            .set(orderDate).equalToWhenPresent(record::getOrderDate)
            .set(receiptDate).equalToWhenPresent(record::getReceiptDate)
            .set(supplierId).equalToWhenPresent(record::getSupplierId)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(businessLine).equalToWhenPresent(record::getBusinessLine)
            .set(isRepurchase).equalToWhenPresent(record::getIsRepurchase)
            .set(repurchaseDate).equalToWhenPresent(record::getRepurchaseDate)
            .set(repurchaseRequirements).equalToWhenPresent(record::getRepurchaseRequirements)
            .set(vehicleRegistrationMark).equalToWhenPresent(record::getVehicleRegistrationMark)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}