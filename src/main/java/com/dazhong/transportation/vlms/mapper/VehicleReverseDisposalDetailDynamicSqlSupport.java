package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleReverseDisposalDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    public static final VehicleReverseDisposalDetail vehicleReverseDisposalDetail = new VehicleReverseDisposalDetail();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.id")
    public static final SqlColumn<Long> id = vehicleReverseDisposalDetail.id;

    /**
     * Database Column Remarks:
     *   逆处置单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.reverse_disposal_id")
    public static final SqlColumn<Long> reverseDisposalId = vehicleReverseDisposalDetail.reverseDisposalId;

    /**
     * Database Column Remarks:
     *   资产编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.vehicle_asset_id")
    public static final SqlColumn<String> vehicleAssetId = vehicleReverseDisposalDetail.vehicleAssetId;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.vin")
    public static final SqlColumn<String> vin = vehicleReverseDisposalDetail.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.license_plate")
    public static final SqlColumn<String> licensePlate = vehicleReverseDisposalDetail.licensePlate;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = vehicleReverseDisposalDetail.vehicleModelId;

    /**
     * Database Column Remarks:
     *   关联审批中处置单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.disposal_document_no")
    public static final SqlColumn<String> disposalDocumentNo = vehicleReverseDisposalDetail.disposalDocumentNo;

    /**
     * Database Column Remarks:
     *   资产所属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = vehicleReverseDisposalDetail.assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产所属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.asset_company_name")
    public static final SqlColumn<String> assetCompanyName = vehicleReverseDisposalDetail.assetCompanyName;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.own_organization_id")
    public static final SqlColumn<Long> ownOrganizationId = vehicleReverseDisposalDetail.ownOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.own_organization_name")
    public static final SqlColumn<String> ownOrganizationName = vehicleReverseDisposalDetail.ownOrganizationName;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.usage_organization_id")
    public static final SqlColumn<Long> usageOrganizationId = vehicleReverseDisposalDetail.usageOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.usage_organization_name")
    public static final SqlColumn<String> usageOrganizationName = vehicleReverseDisposalDetail.usageOrganizationName;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleReverseDisposalDetail.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.create_time")
    public static final SqlColumn<Date> createTime = vehicleReverseDisposalDetail.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleReverseDisposalDetail.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleReverseDisposalDetail.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.update_time")
    public static final SqlColumn<Date> updateTime = vehicleReverseDisposalDetail.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleReverseDisposalDetail.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal_detail.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleReverseDisposalDetail.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    public static final class VehicleReverseDisposalDetail extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> reverseDisposalId = column("reverse_disposal_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleAssetId = column("vehicle_asset_id", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<String> disposalDocumentNo = column("disposal_document_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<String> assetCompanyName = column("asset_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> ownOrganizationId = column("own_organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> ownOrganizationName = column("own_organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> usageOrganizationId = column("usage_organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> usageOrganizationName = column("usage_organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleReverseDisposalDetail() {
            super("t_vehicle_reverse_disposal_detail");
        }
    }
}