package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VinModelInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    public static final VinModelInfo vinModelInfo = new VinModelInfo();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.id")
    public static final SqlColumn<Long> id = vinModelInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.vin")
    public static final SqlColumn<String> vin = vinModelInfo.vin;

    /**
     * Database Column Remarks:
     *   车型名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_name")
    public static final SqlColumn<String> modelName = vinModelInfo.modelName;

    /**
     * Database Column Remarks:
     *   车型信息json
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_json")
    public static final SqlColumn<String> modelJson = vinModelInfo.modelJson;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    public static final class VinModelInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> modelName = column("model_name", JDBCType.VARCHAR);

        public final SqlColumn<String> modelJson = column("model_json", JDBCType.VARCHAR);

        public VinModelInfo() {
            super("t_vin_model_info");
        }
    }
}