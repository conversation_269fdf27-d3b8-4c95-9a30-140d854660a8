package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleDepreciationDataDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleDepreciationData;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleDepreciationDataMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, currentMonth, originalAmount, depreciationMonths, depreciationStartDate, accumulatedDepreciationMonths, depreciationAmount, remainingResidualValue, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleDepreciationData> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleDepreciationDataResult")
    Optional<VehicleDepreciationData> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleDepreciationDataResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="current_month", property="currentMonth", jdbcType=JdbcType.VARCHAR),
        @Result(column="original_amount", property="originalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="depreciation_months", property="depreciationMonths", jdbcType=JdbcType.INTEGER),
        @Result(column="depreciation_start_date", property="depreciationStartDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="accumulated_depreciation_months", property="accumulatedDepreciationMonths", jdbcType=JdbcType.INTEGER),
        @Result(column="depreciation_amount", property="depreciationAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="remaining_residual_value", property="remainingResidualValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleDepreciationData> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleDepreciationData, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleDepreciationData, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default int insert(VehicleDepreciationData record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDepreciationData, c ->
            c.map(vin).toProperty("vin")
            .map(currentMonth).toProperty("currentMonth")
            .map(originalAmount).toProperty("originalAmount")
            .map(depreciationMonths).toProperty("depreciationMonths")
            .map(depreciationStartDate).toProperty("depreciationStartDate")
            .map(accumulatedDepreciationMonths).toProperty("accumulatedDepreciationMonths")
            .map(depreciationAmount).toProperty("depreciationAmount")
            .map(remainingResidualValue).toProperty("remainingResidualValue")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default int insertSelective(VehicleDepreciationData record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDepreciationData, c ->
            c.map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(currentMonth).toPropertyWhenPresent("currentMonth", record::getCurrentMonth)
            .map(originalAmount).toPropertyWhenPresent("originalAmount", record::getOriginalAmount)
            .map(depreciationMonths).toPropertyWhenPresent("depreciationMonths", record::getDepreciationMonths)
            .map(depreciationStartDate).toPropertyWhenPresent("depreciationStartDate", record::getDepreciationStartDate)
            .map(accumulatedDepreciationMonths).toPropertyWhenPresent("accumulatedDepreciationMonths", record::getAccumulatedDepreciationMonths)
            .map(depreciationAmount).toPropertyWhenPresent("depreciationAmount", record::getDepreciationAmount)
            .map(remainingResidualValue).toPropertyWhenPresent("remainingResidualValue", record::getRemainingResidualValue)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default Optional<VehicleDepreciationData> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleDepreciationData, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default List<VehicleDepreciationData> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleDepreciationData, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default List<VehicleDepreciationData> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleDepreciationData, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default Optional<VehicleDepreciationData> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleDepreciationData, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleDepreciationData record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(record::getVin)
                .set(currentMonth).equalTo(record::getCurrentMonth)
                .set(originalAmount).equalTo(record::getOriginalAmount)
                .set(depreciationMonths).equalTo(record::getDepreciationMonths)
                .set(depreciationStartDate).equalTo(record::getDepreciationStartDate)
                .set(accumulatedDepreciationMonths).equalTo(record::getAccumulatedDepreciationMonths)
                .set(depreciationAmount).equalTo(record::getDepreciationAmount)
                .set(remainingResidualValue).equalTo(record::getRemainingResidualValue)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleDepreciationData record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(record::getVin)
                .set(currentMonth).equalToWhenPresent(record::getCurrentMonth)
                .set(originalAmount).equalToWhenPresent(record::getOriginalAmount)
                .set(depreciationMonths).equalToWhenPresent(record::getDepreciationMonths)
                .set(depreciationStartDate).equalToWhenPresent(record::getDepreciationStartDate)
                .set(accumulatedDepreciationMonths).equalToWhenPresent(record::getAccumulatedDepreciationMonths)
                .set(depreciationAmount).equalToWhenPresent(record::getDepreciationAmount)
                .set(remainingResidualValue).equalToWhenPresent(record::getRemainingResidualValue)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default int updateByPrimaryKey(VehicleDepreciationData record) {
        return update(c ->
            c.set(vin).equalTo(record::getVin)
            .set(currentMonth).equalTo(record::getCurrentMonth)
            .set(originalAmount).equalTo(record::getOriginalAmount)
            .set(depreciationMonths).equalTo(record::getDepreciationMonths)
            .set(depreciationStartDate).equalTo(record::getDepreciationStartDate)
            .set(accumulatedDepreciationMonths).equalTo(record::getAccumulatedDepreciationMonths)
            .set(depreciationAmount).equalTo(record::getDepreciationAmount)
            .set(remainingResidualValue).equalTo(record::getRemainingResidualValue)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    default int updateByPrimaryKeySelective(VehicleDepreciationData record) {
        return update(c ->
            c.set(vin).equalToWhenPresent(record::getVin)
            .set(currentMonth).equalToWhenPresent(record::getCurrentMonth)
            .set(originalAmount).equalToWhenPresent(record::getOriginalAmount)
            .set(depreciationMonths).equalToWhenPresent(record::getDepreciationMonths)
            .set(depreciationStartDate).equalToWhenPresent(record::getDepreciationStartDate)
            .set(accumulatedDepreciationMonths).equalToWhenPresent(record::getAccumulatedDepreciationMonths)
            .set(depreciationAmount).equalToWhenPresent(record::getDepreciationAmount)
            .set(remainingResidualValue).equalToWhenPresent(record::getRemainingResidualValue)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}