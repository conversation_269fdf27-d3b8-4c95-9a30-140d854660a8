package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleSyncExternalBusinessInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    public static final VehicleSyncExternalBusinessInfo vehicleSyncExternalBusinessInfo = new VehicleSyncExternalBusinessInfo();

    /**
     * Database Column Remarks:
     *   主键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.id")
    public static final SqlColumn<Long> id = vehicleSyncExternalBusinessInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.vin")
    public static final SqlColumn<String> vin = vehicleSyncExternalBusinessInfo.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.license_plate")
    public static final SqlColumn<String> licensePlate = vehicleSyncExternalBusinessInfo.licensePlate;

    /**
     * Database Column Remarks:
     *   业务类型 insurance-保险、illegal-违章、accident-事故
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.business_type")
    public static final SqlColumn<String> businessType = vehicleSyncExternalBusinessInfo.businessType;

    /**
     * Database Column Remarks:
     *   业务编号，保证唯一性
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.business_no")
    public static final SqlColumn<String> businessNo = vehicleSyncExternalBusinessInfo.businessNo;

    /**
     * Database Column Remarks:
     *   业务时间，业务发生时间 用于查询使用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.business_time")
    public static final SqlColumn<String> businessTime = vehicleSyncExternalBusinessInfo.businessTime;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleSyncExternalBusinessInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.create_time")
    public static final SqlColumn<Date> createTime = vehicleSyncExternalBusinessInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleSyncExternalBusinessInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleSyncExternalBusinessInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.update_time")
    public static final SqlColumn<Date> updateTime = vehicleSyncExternalBusinessInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleSyncExternalBusinessInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleSyncExternalBusinessInfo.updateOperName;

    /**
     * Database Column Remarks:
     *   同步的业务数据 JSON存储
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_sync_external_business_info.business_info")
    public static final SqlColumn<String> businessInfo = vehicleSyncExternalBusinessInfo.businessInfo;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    public static final class VehicleSyncExternalBusinessInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<String> businessType = column("business_type", JDBCType.VARCHAR);

        public final SqlColumn<String> businessNo = column("business_no", JDBCType.VARCHAR);

        public final SqlColumn<String> businessTime = column("business_time", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<String> businessInfo = column("business_info", JDBCType.LONGVARCHAR);

        public VehicleSyncExternalBusinessInfo() {
            super("t_vehicle_sync_external_business_info");
        }
    }
}