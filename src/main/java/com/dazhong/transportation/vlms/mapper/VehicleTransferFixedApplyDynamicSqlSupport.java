package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleTransferFixedApplyDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    public static final VehicleTransferFixedApply vehicleTransferFixedApply = new VehicleTransferFixedApply();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.id")
    public static final SqlColumn<Long> id = vehicleTransferFixedApply.id;

    /**
     * Database Column Remarks:
     *   转固申请编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.apply_no")
    public static final SqlColumn<String> applyNo = vehicleTransferFixedApply.applyNo;

    /**
     * Database Column Remarks:
     *   钉钉审批编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.approval_number")
    public static final SqlColumn<String> approvalNumber = vehicleTransferFixedApply.approvalNumber;

    /**
     * Database Column Remarks:
     *   转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.apply_status")
    public static final SqlColumn<Integer> applyStatus = vehicleTransferFixedApply.applyStatus;

    /**
     * Database Column Remarks:
     *   申请名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.apply_name")
    public static final SqlColumn<String> applyName = vehicleTransferFixedApply.applyName;

    /**
     * Database Column Remarks:
     *   申请备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.apply_remark")
    public static final SqlColumn<String> applyRemark = vehicleTransferFixedApply.applyRemark;

    /**
     * Database Column Remarks:
     *   申请车辆数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.transfer_quantity")
    public static final SqlColumn<Integer> transferQuantity = vehicleTransferFixedApply.transferQuantity;

    /**
     * Database Column Remarks:
     *   单据所属资产公司D
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.owner_id")
    public static final SqlColumn<Integer> ownerId = vehicleTransferFixedApply.ownerId;

    /**
     * Database Column Remarks:
     *   单据所属机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.apply_org_id")
    public static final SqlColumn<Long> applyOrgId = vehicleTransferFixedApply.applyOrgId;

    /**
     * Database Column Remarks:
     *   申请人工号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.apply_user_no")
    public static final SqlColumn<String> applyUserNo = vehicleTransferFixedApply.applyUserNo;

    /**
     * Database Column Remarks:
     *   申请人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.apply_user")
    public static final SqlColumn<String> applyUser = vehicleTransferFixedApply.applyUser;

    /**
     * Database Column Remarks:
     *   资产公司所属部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.department_code")
    public static final SqlColumn<String> departmentCode = vehicleTransferFixedApply.departmentCode;

    /**
     * Database Column Remarks:
     *   资产公司所属部门名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.department_name")
    public static final SqlColumn<String> departmentName = vehicleTransferFixedApply.departmentName;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.applicant_department_code")
    public static final SqlColumn<String> applicantDepartmentCode = vehicleTransferFixedApply.applicantDepartmentCode;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.applicant_department_name")
    public static final SqlColumn<String> applicantDepartmentName = vehicleTransferFixedApply.applicantDepartmentName;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleTransferFixedApply.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.create_time")
    public static final SqlColumn<Date> createTime = vehicleTransferFixedApply.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleTransferFixedApply.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleTransferFixedApply.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.update_time")
    public static final SqlColumn<Date> updateTime = vehicleTransferFixedApply.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleTransferFixedApply.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_apply.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleTransferFixedApply.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_apply")
    public static final class VehicleTransferFixedApply extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> applyNo = column("apply_no", JDBCType.VARCHAR);

        public final SqlColumn<String> approvalNumber = column("approval_number", JDBCType.VARCHAR);

        public final SqlColumn<Integer> applyStatus = column("apply_status", JDBCType.INTEGER);

        public final SqlColumn<String> applyName = column("apply_name", JDBCType.VARCHAR);

        public final SqlColumn<String> applyRemark = column("apply_remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> transferQuantity = column("transfer_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<Long> applyOrgId = column("apply_org_id", JDBCType.BIGINT);

        public final SqlColumn<String> applyUserNo = column("apply_user_no", JDBCType.VARCHAR);

        public final SqlColumn<String> applyUser = column("apply_user", JDBCType.VARCHAR);

        public final SqlColumn<String> departmentCode = column("department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> departmentName = column("department_name", JDBCType.VARCHAR);

        public final SqlColumn<String> applicantDepartmentCode = column("applicant_department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> applicantDepartmentName = column("applicant_department_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleTransferFixedApply() {
            super("t_vehicle_transfer_fixed_apply");
        }
    }
}