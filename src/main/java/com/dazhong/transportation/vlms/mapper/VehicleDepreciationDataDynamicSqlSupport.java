package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleDepreciationDataDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    public static final VehicleDepreciationData vehicleDepreciationData = new VehicleDepreciationData();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.id")
    public static final SqlColumn<Long> id = vehicleDepreciationData.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.vin")
    public static final SqlColumn<String> vin = vehicleDepreciationData.vin;

    /**
     * Database Column Remarks:
     *   当前年月
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.current_month")
    public static final SqlColumn<String> currentMonth = vehicleDepreciationData.currentMonth;

    /**
     * Database Column Remarks:
     *   原值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.original_amount")
    public static final SqlColumn<BigDecimal> originalAmount = vehicleDepreciationData.originalAmount;

    /**
     * Database Column Remarks:
     *   折旧月
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.depreciation_months")
    public static final SqlColumn<Integer> depreciationMonths = vehicleDepreciationData.depreciationMonths;

    /**
     * Database Column Remarks:
     *   折旧月开始时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.depreciation_start_date")
    public static final SqlColumn<Date> depreciationStartDate = vehicleDepreciationData.depreciationStartDate;

    /**
     * Database Column Remarks:
     *   已折旧月
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.accumulated_depreciation_months")
    public static final SqlColumn<Integer> accumulatedDepreciationMonths = vehicleDepreciationData.accumulatedDepreciationMonths;

    /**
     * Database Column Remarks:
     *   折旧金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.depreciation_amount")
    public static final SqlColumn<BigDecimal> depreciationAmount = vehicleDepreciationData.depreciationAmount;

    /**
     * Database Column Remarks:
     *   剩余残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.remaining_residual_value")
    public static final SqlColumn<BigDecimal> remainingResidualValue = vehicleDepreciationData.remainingResidualValue;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleDepreciationData.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.create_time")
    public static final SqlColumn<Date> createTime = vehicleDepreciationData.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleDepreciationData.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleDepreciationData.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.update_time")
    public static final SqlColumn<Date> updateTime = vehicleDepreciationData.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleDepreciationData.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_depreciation_data.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleDepreciationData.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_depreciation_data")
    public static final class VehicleDepreciationData extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> currentMonth = column("current_month", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> originalAmount = column("original_amount", JDBCType.DECIMAL);

        public final SqlColumn<Integer> depreciationMonths = column("depreciation_months", JDBCType.INTEGER);

        public final SqlColumn<Date> depreciationStartDate = column("depreciation_start_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> accumulatedDepreciationMonths = column("accumulated_depreciation_months", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> depreciationAmount = column("depreciation_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> remainingResidualValue = column("remaining_residual_value", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleDepreciationData() {
            super("t_vehicle_depreciation_data");
        }
    }
}