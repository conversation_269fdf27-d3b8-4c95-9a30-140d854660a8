package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.model.VehicleReverseDisposalDetail;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

import javax.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleReverseDisposalDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface VehicleReverseDisposalDetailMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<VehicleReverseDisposalDetail>  {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, reverseDisposalId, vehicleAssetId, vin, licensePlate, vehicleModelId, disposalDocumentNo, assetCompanyId, assetCompanyName, ownOrganizationId, ownOrganizationName, usageOrganizationId, usageOrganizationName, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleReverseDisposalDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleReverseDisposalDetailResult")
    Optional<VehicleReverseDisposalDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleReverseDisposalDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="reverse_disposal_id", property="reverseDisposalId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="disposal_document_no", property="disposalDocumentNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_name", property="assetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="own_organization_name", property="ownOrganizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="usage_organization_name", property="usageOrganizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleReverseDisposalDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleReverseDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleReverseDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default int insert(VehicleReverseDisposalDetail record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleReverseDisposalDetail, c ->
            c.map(reverseDisposalId).toProperty("reverseDisposalId")
            .map(vehicleAssetId).toProperty("vehicleAssetId")
            .map(vin).toProperty("vin")
            .map(licensePlate).toProperty("licensePlate")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(disposalDocumentNo).toProperty("disposalDocumentNo")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(assetCompanyName).toProperty("assetCompanyName")
            .map(ownOrganizationId).toProperty("ownOrganizationId")
            .map(ownOrganizationName).toProperty("ownOrganizationName")
            .map(usageOrganizationId).toProperty("usageOrganizationId")
            .map(usageOrganizationName).toProperty("usageOrganizationName")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default int insertSelective(VehicleReverseDisposalDetail record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleReverseDisposalDetail, c ->
            c.map(reverseDisposalId).toPropertyWhenPresent("reverseDisposalId", record::getReverseDisposalId)
            .map(vehicleAssetId).toPropertyWhenPresent("vehicleAssetId", record::getVehicleAssetId)
            .map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
            .map(disposalDocumentNo).toPropertyWhenPresent("disposalDocumentNo", record::getDisposalDocumentNo)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(assetCompanyName).toPropertyWhenPresent("assetCompanyName", record::getAssetCompanyName)
            .map(ownOrganizationId).toPropertyWhenPresent("ownOrganizationId", record::getOwnOrganizationId)
            .map(ownOrganizationName).toPropertyWhenPresent("ownOrganizationName", record::getOwnOrganizationName)
            .map(usageOrganizationId).toPropertyWhenPresent("usageOrganizationId", record::getUsageOrganizationId)
            .map(usageOrganizationName).toPropertyWhenPresent("usageOrganizationName", record::getUsageOrganizationName)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default Optional<VehicleReverseDisposalDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleReverseDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default List<VehicleReverseDisposalDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleReverseDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default List<VehicleReverseDisposalDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleReverseDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default Optional<VehicleReverseDisposalDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleReverseDisposalDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleReverseDisposalDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(reverseDisposalId).equalTo(record::getReverseDisposalId)
                .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
                .set(vin).equalTo(record::getVin)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(vehicleModelId).equalTo(record::getVehicleModelId)
                .set(disposalDocumentNo).equalTo(record::getDisposalDocumentNo)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(assetCompanyName).equalTo(record::getAssetCompanyName)
                .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
                .set(ownOrganizationName).equalTo(record::getOwnOrganizationName)
                .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
                .set(usageOrganizationName).equalTo(record::getUsageOrganizationName)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleReverseDisposalDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(reverseDisposalId).equalToWhenPresent(record::getReverseDisposalId)
                .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
                .set(vin).equalToWhenPresent(record::getVin)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
                .set(disposalDocumentNo).equalToWhenPresent(record::getDisposalDocumentNo)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
                .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
                .set(ownOrganizationName).equalToWhenPresent(record::getOwnOrganizationName)
                .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
                .set(usageOrganizationName).equalToWhenPresent(record::getUsageOrganizationName)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default int updateByPrimaryKey(VehicleReverseDisposalDetail record) {
        return update(c ->
            c.set(reverseDisposalId).equalTo(record::getReverseDisposalId)
            .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
            .set(vin).equalTo(record::getVin)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(vehicleModelId).equalTo(record::getVehicleModelId)
            .set(disposalDocumentNo).equalTo(record::getDisposalDocumentNo)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(assetCompanyName).equalTo(record::getAssetCompanyName)
            .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
            .set(ownOrganizationName).equalTo(record::getOwnOrganizationName)
            .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
            .set(usageOrganizationName).equalTo(record::getUsageOrganizationName)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal_detail")
    default int updateByPrimaryKeySelective(VehicleReverseDisposalDetail record) {
        return update(c ->
            c.set(reverseDisposalId).equalToWhenPresent(record::getReverseDisposalId)
            .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
            .set(vin).equalToWhenPresent(record::getVin)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
            .set(disposalDocumentNo).equalToWhenPresent(record::getDisposalDocumentNo)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
            .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
            .set(ownOrganizationName).equalToWhenPresent(record::getOwnOrganizationName)
            .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
            .set(usageOrganizationName).equalToWhenPresent(record::getUsageOrganizationName)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_reverse_disposal_detail")
    default int insertMultiple(Collection<VehicleReverseDisposalDetail> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, vehicleReverseDisposalDetail, c -> c
                .map(reverseDisposalId).toProperty("reverseDisposalId")
                .map(vehicleAssetId).toProperty("vehicleAssetId")
                .map(vin).toProperty("vin")
                .map(licensePlate).toProperty("licensePlate")
                .map(vehicleModelId).toProperty("vehicleModelId")
                .map(disposalDocumentNo).toProperty("disposalDocumentNo")
                .map(assetCompanyId).toProperty("assetCompanyId")
                .map(assetCompanyName).toProperty("assetCompanyName")
                .map(ownOrganizationId).toProperty("ownOrganizationId")
                .map(ownOrganizationName).toProperty("ownOrganizationName")
                .map(usageOrganizationId).toProperty("usageOrganizationId")
                .map(usageOrganizationName).toProperty("usageOrganizationName")
                .map(createTime).toProperty("createTime")
                .map(createOperId).toProperty("createOperId")
                .map(createOperName).toProperty("createOperName")
        );
    }
}