package com.dazhong.transportation.vlms.mapper.extend;

import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleModelDto;
import com.dazhong.transportation.vlms.mapper.VehicleModelInfoMapper;
import com.dazhong.transportation.vlms.model.VehicleModelInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleModelInfoDynamicSqlSupport.*;

@Mapper
public interface VehicleModelInfoExtendMapper extends VehicleModelInfoMapper {

    default int insertSelectiveWithId(VehicleModelInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleModelInfo, c ->
                c.map(id).toPropertyWhenPresent("id", record::getId)
                        .map(autohomeVehicleModelId).toPropertyWhenPresent("autohomeVehicleModelId", record::getAutohomeVehicleModelId)
                        .map(vehicleBrandName).toPropertyWhenPresent("vehicleBrandName", record::getVehicleBrandName)
                        .map(vehicleSeriesName).toPropertyWhenPresent("vehicleSeriesName", record::getVehicleSeriesName)
                        .map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", record::getVehicleModelName)
                        .map(financialModelName).toPropertyWhenPresent("financialModelName", record::getFinancialModelName)
                        .map(vehicleAbbreviationId).toPropertyWhenPresent("vehicleAbbreviationId", record::getVehicleAbbreviationId)
                        .map(assessPassenger).toPropertyWhenPresent("assessPassenger", record::getAssessPassenger)
                        .map(busAssessPassenger).toPropertyWhenPresent("busAssessPassenger", record::getBusAssessPassenger)
                        .map(engineModelNo).toPropertyWhenPresent("engineModelNo", record::getEngineModelNo)
                        .map(gasTypeId).toPropertyWhenPresent("gasTypeId", record::getGasTypeId)
                        .map(vehicleLevel).toPropertyWhenPresent("vehicleLevel", record::getVehicleLevel)
                        .map(wheelParam).toPropertyWhenPresent("wheelParam", record::getWheelParam)
                        .map(exhaustId).toPropertyWhenPresent("exhaustId", record::getExhaustId)
                        .map(ttmMonth).toPropertyWhenPresent("ttmMonth", record::getTtmMonth)
                        .map(outLength).toPropertyWhenPresent("outLength", record::getOutLength)
                        .map(outWidth).toPropertyWhenPresent("outWidth", record::getOutWidth)
                        .map(outHeight).toPropertyWhenPresent("outHeight", record::getOutHeight)
                        .map(wheelBase1).toPropertyWhenPresent("wheelBase1", record::getWheelBase1)
                        .map(totalMass).toPropertyWhenPresent("totalMass", record::getTotalMass)
                        .map(fuelTankCapacity).toPropertyWhenPresent("fuelTankCapacity", record::getFuelTankCapacity)
                        .map(fuelLabelName).toPropertyWhenPresent("fuelLabelName", record::getFuelLabelName)
                        .map(capacity).toPropertyWhenPresent("capacity", record::getCapacity)
                        .map(batteryCapacity).toPropertyWhenPresent("batteryCapacity", record::getBatteryCapacity)
                        .map(vehicleRange).toPropertyWhenPresent("vehicleRange", record::getVehicleRange)
                        .map(fastChargingTime).toPropertyWhenPresent("fastChargingTime", record::getFastChargingTime)
                        .map(slowChargingTime).toPropertyWhenPresent("slowChargingTime", record::getSlowChargingTime)
                        .map(fuelEconomyMiit).toPropertyWhenPresent("fuelEconomyMiit", record::getFuelEconomyMiit)
                        .map(priceOnAutohome).toPropertyWhenPresent("priceOnAutohome", record::getPriceOnAutohome)
                        .map(vehicleModelNo).toPropertyWhenPresent("vehicleModelNo", record::getVehicleModelNo)
                        .map(power).toPropertyWhenPresent("power", record::getPower)
                        .map(treadFront).toPropertyWhenPresent("treadFront", record::getTreadFront)
                        .map(treadRear).toPropertyWhenPresent("treadRear", record::getTreadRear)
                        .map(wheelQuantity).toPropertyWhenPresent("wheelQuantity", record::getWheelQuantity)
                        .map(springLamination).toPropertyWhenPresent("springLamination", record::getSpringLamination)
                        .map(wheelBase2).toPropertyWhenPresent("wheelBase2", record::getWheelBase2)
                        .map(wheelBase3).toPropertyWhenPresent("wheelBase3", record::getWheelBase3)
                        .map(axleQuantity).toPropertyWhenPresent("axleQuantity", record::getAxleQuantity)
                        .map(containerLength).toPropertyWhenPresent("containerLength", record::getContainerLength)
                        .map(containerWidth).toPropertyWhenPresent("containerWidth", record::getContainerWidth)
                        .map(containerHeight).toPropertyWhenPresent("containerHeight", record::getContainerHeight)
                        .map(assessMass).toPropertyWhenPresent("assessMass", record::getAssessMass)
                        .map(tractionMass).toPropertyWhenPresent("tractionMass", record::getTractionMass)
                        .map(cabPassenger).toPropertyWhenPresent("cabPassenger", record::getCabPassenger)
                        .map(manufactureLocation).toPropertyWhenPresent("manufactureLocation", record::getManufactureLocation)
                        .map(doorQuantity).toPropertyWhenPresent("doorQuantity", record::getDoorQuantity)
                        .map(gearQuantity).toPropertyWhenPresent("gearQuantity", record::getGearQuantity)
                        .map(acceleration).toPropertyWhenPresent("acceleration", record::getAcceleration)
                        .map(speed).toPropertyWhenPresent("speed", record::getSpeed)
                        .map(turningRadius).toPropertyWhenPresent("turningRadius", record::getTurningRadius)
                        .map(roadClearance).toPropertyWhenPresent("roadClearance", record::getRoadClearance)
                        .map(gradient).toPropertyWhenPresent("gradient", record::getGradient)
                        .map(fuelEconomy).toPropertyWhenPresent("fuelEconomy", record::getFuelEconomy)
                        .map(torque).toPropertyWhenPresent("torque", record::getTorque)
                        .map(compressionRatio).toPropertyWhenPresent("compressionRatio", record::getCompressionRatio)
                        .map(manufacturerId).toPropertyWhenPresent("manufacturerId", record::getManufacturerId)
                        .map(vehicleBrandId).toPropertyWhenPresent("vehicleBrandId", record::getVehicleBrandId)
                        .map(vehicleTypeId).toPropertyWhenPresent("vehicleTypeId", record::getVehicleTypeId)
                        .map(wheelDriveId).toPropertyWhenPresent("wheelDriveId", record::getWheelDriveId)
                        .map(breakModeId).toPropertyWhenPresent("breakModeId", record::getBreakModeId)
                        .map(turnModeId).toPropertyWhenPresent("turnModeId", record::getTurnModeId)
                        .map(drivePositionId).toPropertyWhenPresent("drivePositionId", record::getDrivePositionId)
                        .map(enginePositionId).toPropertyWhenPresent("enginePositionId", record::getEnginePositionId)
                        .map(gearBoxTypeId).toPropertyWhenPresent("gearBoxTypeId", record::getGearBoxTypeId)
                        .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
                        .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
                        .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
                        .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
                        .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
                        .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
                        .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SyncDatabaseVehicleModelResult", value = {
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="vehicle_model_no", property="modelNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_model_no", property="engineModelNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="capacity", property="capacity", jdbcType=JdbcType.DECIMAL),
            @Result(column="power", property="power", jdbcType=JdbcType.INTEGER),
            @Result(column="tread_front", property="treadFront", jdbcType=JdbcType.INTEGER),
            @Result(column="tread_rear", property="treadRear", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_quantity", property="wheelQuantity", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_param", property="wheelParam", jdbcType=JdbcType.VARCHAR),
            @Result(column="spring_lamination", property="springLamination", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_base1", property="wheelBase1", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_base2", property="wheelBase2", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_base3", property="wheelBase3", jdbcType=JdbcType.INTEGER),
            @Result(column="axle_quantity", property="axleQuantity", jdbcType=JdbcType.INTEGER),
            @Result(column="out_length", property="outLength", jdbcType=JdbcType.INTEGER),
            @Result(column="out_width", property="outWidth", jdbcType=JdbcType.INTEGER),
            @Result(column="out_height", property="outHeight", jdbcType=JdbcType.INTEGER),
            @Result(column="container_length", property="containerLength", jdbcType=JdbcType.INTEGER),
            @Result(column="container_width", property="containerWidth", jdbcType=JdbcType.INTEGER),
            @Result(column="container_height", property="containerHeight", jdbcType=JdbcType.INTEGER),
            @Result(column="total_mass", property="totalMass", jdbcType=JdbcType.INTEGER),
            @Result(column="assess_mass", property="assessMass", jdbcType=JdbcType.INTEGER),
            @Result(column="assess_passenger", property="assessPassenger", jdbcType=JdbcType.INTEGER),
            @Result(column="traction_mass", property="tractionMass", jdbcType=JdbcType.INTEGER),
            @Result(column="cab_passenger", property="cabPassenger", jdbcType=JdbcType.INTEGER),
            @Result(column="manufacture_location", property="manufactureLocation", jdbcType=JdbcType.VARCHAR),
            @Result(column="door_quantity", property="doorQuantity", jdbcType=JdbcType.INTEGER),
            @Result(column="gear_quantity", property="gearQuantity", jdbcType=JdbcType.INTEGER),
            @Result(column="acceleration", property="acceleration", jdbcType=JdbcType.DECIMAL),
            @Result(column="speed", property="speed", jdbcType=JdbcType.INTEGER),
            @Result(column="turning_radius", property="turningRadius", jdbcType=JdbcType.DECIMAL),
            @Result(column="road_clearance", property="roadClearance", jdbcType=JdbcType.INTEGER),
            @Result(column="gradient", property="gradient", jdbcType=JdbcType.INTEGER),
            @Result(column="fuel_economy", property="fuelEconomy", jdbcType=JdbcType.DECIMAL),
            @Result(column="fuel_economy_miit", property="fuelEconomyMiit", jdbcType=JdbcType.DECIMAL),
            @Result(column="torque", property="torque", jdbcType=JdbcType.INTEGER),
            @Result(column="compression_ratio", property="compressionRatio", jdbcType=JdbcType.VARCHAR),
            @Result(column="manufacturer_id", property="manufacturerId", jdbcType=JdbcType.INTEGER),
            @Result(column="gas_type_id", property="gasTypeId", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_brand_id", property="vehicleBrandId", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_type_id", property="vehicleTypeId", jdbcType=JdbcType.INTEGER),
            @Result(column="wheel_drive_id", property="wheelDriveId", jdbcType=JdbcType.INTEGER),
            @Result(column="break_mode_id", property="breakModeId", jdbcType=JdbcType.INTEGER),
            @Result(column="turn_mode_id", property="turnModeId", jdbcType=JdbcType.INTEGER),
            @Result(column="drive_position_id", property="drivePositionId", jdbcType=JdbcType.INTEGER),
            @Result(column="engine_position_id", property="enginePositionId", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_abbreviation_id", property="vehicleAbbreviationId", jdbcType=JdbcType.INTEGER),
            @Result(column="exhaust_id", property="exhaustId", jdbcType=JdbcType.INTEGER),
            @Result(column="gear_box_type_id", property="gearBoxTypeId", jdbcType=JdbcType.INTEGER)
    })
    List<SyncDatabaseVehicleModelDto> searchVehicleModelDatabaseSyncData(SelectStatementProvider selectStatement);
}
