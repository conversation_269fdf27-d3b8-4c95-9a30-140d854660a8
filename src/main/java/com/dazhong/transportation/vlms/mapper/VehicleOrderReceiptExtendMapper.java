package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.dto.VehiclePurchaseReceiptDto;
import com.dazhong.transportation.vlms.dto.VehicleReceivingDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import java.util.List;

@Mapper
public interface VehicleOrderReceiptExtendMapper extends VehicleOrderReceiptMapper{

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SearchInboundVehicleReceiptResult", value = {
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_model", property="engineModel", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
            @Result(column="interior_color", property="interiorColor", jdbcType=JdbcType.VARCHAR),
            @Result(column="order_date", property="orderDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="receipt_date", property="receiptDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
            @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
            @Result(column="is_repurchase", property="isRepurchase", jdbcType=JdbcType.INTEGER),
            @Result(column="repurchase_date", property="repurchaseDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="repurchase_requirements", property="repurchaseRequirements", jdbcType=JdbcType.VARCHAR),
            @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_no", property="vehicleModelNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="approval_number", property="approvalNumber", jdbcType=JdbcType.VARCHAR),
            @Result(column="usage_age_limit", property="usageAgeLimit", jdbcType=JdbcType.INTEGER),
            @Result(column="depreciation_age_limit", property="depreciationAgeLimit", jdbcType=JdbcType.INTEGER),
            @Result(column="purchase_price", property="purchasePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="purchase_tax", property="purchaseTax", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate_price", property="licensePlatePrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate_other_price", property="licensePlateOtherPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="upholster_price", property="upholsterPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
    })
    VehiclePurchaseReceiptDto queryVehiclePurchaseReceipt(SelectStatementProvider selectStatement);

    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SearchVehicleOrderReceiptResult", value = {
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
            @Result(column="asset_company_id", property="ownerId", jdbcType=JdbcType.INTEGER),
            @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="engine_model", property="engineModel", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
            @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
            @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
            @Result(column="interior_color", property="interiorColor", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_details_no", property="applyDetailsNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="order_date", property="orderDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="receipt_date", property="receiptDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
            @Result(column="is_repurchase", property="isRepurchase", jdbcType=JdbcType.INTEGER),
            @Result(column="repurchase_date", property="repurchaseDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="repurchase_requirements", property="repurchaseRequirements", jdbcType=JdbcType.VARCHAR),
            @Result(column="business_type", property="businessType", jdbcType=JdbcType.INTEGER),
            @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
            @Result(column="vehicle_model_no", property="vehicleModelNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
    })
    List<VehicleReceivingDto> qeuryVehicleOrderReceiptList(SelectStatementProvider selectStatement);

}