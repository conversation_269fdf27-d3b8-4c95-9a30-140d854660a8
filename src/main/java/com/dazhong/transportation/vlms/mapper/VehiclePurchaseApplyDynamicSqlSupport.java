package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehiclePurchaseApplyDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    public static final VehiclePurchaseApply vehiclePurchaseApply = new VehiclePurchaseApply();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.id")
    public static final SqlColumn<Long> id = vehiclePurchaseApply.id;

    /**
     * Database Column Remarks:
     *   采购申请编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_no")
    public static final SqlColumn<String> purchaseApplyNo = vehiclePurchaseApply.purchaseApplyNo;

    /**
     * Database Column Remarks:
     *   钉钉审批编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.approval_number")
    public static final SqlColumn<String> approvalNumber = vehiclePurchaseApply.approvalNumber;

    /**
     * Database Column Remarks:
     *   采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_status")
    public static final SqlColumn<Integer> purchaseApplyStatus = vehiclePurchaseApply.purchaseApplyStatus;

    /**
     * Database Column Remarks:
     *   采购产品线 1-寻网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_product_line")
    public static final SqlColumn<Integer> applyProductLine = vehiclePurchaseApply.applyProductLine;

    /**
     * Database Column Remarks:
     *   采购名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_name")
    public static final SqlColumn<String> applyName = vehiclePurchaseApply.applyName;

    /**
     * Database Column Remarks:
     *   采购备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_remark")
    public static final SqlColumn<String> applyRemark = vehiclePurchaseApply.applyRemark;

    /**
     * Database Column Remarks:
     *   采购车辆数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_quantity")
    public static final SqlColumn<Integer> purchaseQuantity = vehiclePurchaseApply.purchaseQuantity;

    /**
     * Database Column Remarks:
     *   收货车辆数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.take_delivery_quantity")
    public static final SqlColumn<Integer> takeDeliveryQuantity = vehiclePurchaseApply.takeDeliveryQuantity;

    /**
     * Database Column Remarks:
     *   预占用数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.pre_occupied")
    public static final SqlColumn<Integer> preOccupied = vehiclePurchaseApply.preOccupied;

    /**
     * Database Column Remarks:
     *   车辆上牌占用数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.vehicle_occupied")
    public static final SqlColumn<Integer> vehicleOccupied = vehiclePurchaseApply.vehicleOccupied;

    /**
     * Database Column Remarks:
     *   单据所属资产公司ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.owner_id")
    public static final SqlColumn<Integer> ownerId = vehiclePurchaseApply.ownerId;

    /**
     * Database Column Remarks:
     *   单据所属机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_id")
    public static final SqlColumn<Long> applyOrgId = vehiclePurchaseApply.applyOrgId;

    /**
     * Database Column Remarks:
     *   申请公司code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_code")
    public static final SqlColumn<String> applyOrgCode = vehiclePurchaseApply.applyOrgCode;

    /**
     * Database Column Remarks:
     *   申请人工号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user_no")
    public static final SqlColumn<String> applyUserNo = vehiclePurchaseApply.applyUserNo;

    /**
     * Database Column Remarks:
     *   申请人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user")
    public static final SqlColumn<String> applyUser = vehiclePurchaseApply.applyUser;

    /**
     * Database Column Remarks:
     *   申购公司code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_code")
    public static final SqlColumn<String> subscriptionCompanyCode = vehiclePurchaseApply.subscriptionCompanyCode;

    /**
     * Database Column Remarks:
     *   申购公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_name")
    public static final SqlColumn<String> subscriptionCompanyName = vehiclePurchaseApply.subscriptionCompanyName;

    /**
     * Database Column Remarks:
     *   供应商大类
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.supplier_type")
    public static final SqlColumn<Integer> supplierType = vehiclePurchaseApply.supplierType;

    /**
     * Database Column Remarks:
     *   采购意向单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.intention_no")
    public static final SqlColumn<String> intentionNo = vehiclePurchaseApply.intentionNo;

    /**
     * Database Column Remarks:
     *   提交时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.submit_date")
    public static final SqlColumn<Date> submitDate = vehiclePurchaseApply.submitDate;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_code")
    public static final SqlColumn<String> applicantDepartmentCode = vehiclePurchaseApply.applicantDepartmentCode;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_name")
    public static final SqlColumn<String> applicantDepartmentName = vehiclePurchaseApply.applicantDepartmentName;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehiclePurchaseApply.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_time")
    public static final SqlColumn<Date> createTime = vehiclePurchaseApply.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehiclePurchaseApply.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_name")
    public static final SqlColumn<String> createOperName = vehiclePurchaseApply.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_time")
    public static final SqlColumn<Date> updateTime = vehiclePurchaseApply.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehiclePurchaseApply.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehiclePurchaseApply.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    public static final class VehiclePurchaseApply extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> purchaseApplyNo = column("purchase_apply_no", JDBCType.VARCHAR);

        public final SqlColumn<String> approvalNumber = column("approval_number", JDBCType.VARCHAR);

        public final SqlColumn<Integer> purchaseApplyStatus = column("purchase_apply_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> applyProductLine = column("apply_product_line", JDBCType.INTEGER);

        public final SqlColumn<String> applyName = column("apply_name", JDBCType.VARCHAR);

        public final SqlColumn<String> applyRemark = column("apply_remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> purchaseQuantity = column("purchase_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> takeDeliveryQuantity = column("take_delivery_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> preOccupied = column("pre_occupied", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleOccupied = column("vehicle_occupied", JDBCType.INTEGER);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<Long> applyOrgId = column("apply_org_id", JDBCType.BIGINT);

        public final SqlColumn<String> applyOrgCode = column("apply_org_code", JDBCType.VARCHAR);

        public final SqlColumn<String> applyUserNo = column("apply_user_no", JDBCType.VARCHAR);

        public final SqlColumn<String> applyUser = column("apply_user", JDBCType.VARCHAR);

        public final SqlColumn<String> subscriptionCompanyCode = column("subscription_company_code", JDBCType.VARCHAR);

        public final SqlColumn<String> subscriptionCompanyName = column("subscription_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> supplierType = column("supplier_type", JDBCType.INTEGER);

        public final SqlColumn<String> intentionNo = column("intention_no", JDBCType.VARCHAR);

        public final SqlColumn<Date> submitDate = column("submit_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> applicantDepartmentCode = column("applicant_department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> applicantDepartmentName = column("applicant_department_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehiclePurchaseApply() {
            super("t_vehicle_purchase_apply");
        }
    }
}