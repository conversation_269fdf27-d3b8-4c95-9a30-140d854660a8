package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.model.VehicleApplicationDetail;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

import javax.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleApplicationDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface VehicleApplicationDetailMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<VehicleApplicationDetail> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, applicationId, vehicleAssetId, vin, licensePlate, vehicleModelId, assetCompanyId, assetCompanyName, assetCompanyIdUpdate, assetCompanyNameUpdate, ownOrganizationId, ownOrganizationName, ownOrganizationIdUpdate, ownOrganizationNameUpdate, usageOrganizationId, usageOrganizationName, usageOrganizationIdUpdate, usageOrganizationNameUpdate, productLine, productLineUpdate, businessLine, businessLineUpdate, operatingStatus, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleApplicationDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleApplicationDetailResult")
    Optional<VehicleApplicationDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleApplicationDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="application_id", property="applicationId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_name", property="assetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="asset_company_id_update", property="assetCompanyIdUpdate", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_name_update", property="assetCompanyNameUpdate", jdbcType=JdbcType.VARCHAR),
        @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="own_organization_name", property="ownOrganizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="own_organization_id_update", property="ownOrganizationIdUpdate", jdbcType=JdbcType.BIGINT),
        @Result(column="own_organization_name_update", property="ownOrganizationNameUpdate", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="usage_organization_name", property="usageOrganizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_organization_id_update", property="usageOrganizationIdUpdate", jdbcType=JdbcType.BIGINT),
        @Result(column="usage_organization_name_update", property="usageOrganizationNameUpdate", jdbcType=JdbcType.VARCHAR),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="product_line_update", property="productLineUpdate", jdbcType=JdbcType.INTEGER),
        @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
        @Result(column="business_line_update", property="businessLineUpdate", jdbcType=JdbcType.INTEGER),
        @Result(column="operating_status", property="operatingStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleApplicationDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleApplicationDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleApplicationDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default int insert(VehicleApplicationDetail record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleApplicationDetail, c ->
            c.map(applicationId).toProperty("applicationId")
            .map(vehicleAssetId).toProperty("vehicleAssetId")
            .map(vin).toProperty("vin")
            .map(licensePlate).toProperty("licensePlate")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(assetCompanyName).toProperty("assetCompanyName")
            .map(assetCompanyIdUpdate).toProperty("assetCompanyIdUpdate")
            .map(assetCompanyNameUpdate).toProperty("assetCompanyNameUpdate")
            .map(ownOrganizationId).toProperty("ownOrganizationId")
            .map(ownOrganizationName).toProperty("ownOrganizationName")
            .map(ownOrganizationIdUpdate).toProperty("ownOrganizationIdUpdate")
            .map(ownOrganizationNameUpdate).toProperty("ownOrganizationNameUpdate")
            .map(usageOrganizationId).toProperty("usageOrganizationId")
            .map(usageOrganizationName).toProperty("usageOrganizationName")
            .map(usageOrganizationIdUpdate).toProperty("usageOrganizationIdUpdate")
            .map(usageOrganizationNameUpdate).toProperty("usageOrganizationNameUpdate")
            .map(productLine).toProperty("productLine")
            .map(productLineUpdate).toProperty("productLineUpdate")
            .map(businessLine).toProperty("businessLine")
            .map(businessLineUpdate).toProperty("businessLineUpdate")
            .map(operatingStatus).toProperty("operatingStatus")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default int insertSelective(VehicleApplicationDetail record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleApplicationDetail, c ->
            c.map(applicationId).toPropertyWhenPresent("applicationId", record::getApplicationId)
            .map(vehicleAssetId).toPropertyWhenPresent("vehicleAssetId", record::getVehicleAssetId)
            .map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(assetCompanyName).toPropertyWhenPresent("assetCompanyName", record::getAssetCompanyName)
            .map(assetCompanyIdUpdate).toPropertyWhenPresent("assetCompanyIdUpdate", record::getAssetCompanyIdUpdate)
            .map(assetCompanyNameUpdate).toPropertyWhenPresent("assetCompanyNameUpdate", record::getAssetCompanyNameUpdate)
            .map(ownOrganizationId).toPropertyWhenPresent("ownOrganizationId", record::getOwnOrganizationId)
            .map(ownOrganizationName).toPropertyWhenPresent("ownOrganizationName", record::getOwnOrganizationName)
            .map(ownOrganizationIdUpdate).toPropertyWhenPresent("ownOrganizationIdUpdate", record::getOwnOrganizationIdUpdate)
            .map(ownOrganizationNameUpdate).toPropertyWhenPresent("ownOrganizationNameUpdate", record::getOwnOrganizationNameUpdate)
            .map(usageOrganizationId).toPropertyWhenPresent("usageOrganizationId", record::getUsageOrganizationId)
            .map(usageOrganizationName).toPropertyWhenPresent("usageOrganizationName", record::getUsageOrganizationName)
            .map(usageOrganizationIdUpdate).toPropertyWhenPresent("usageOrganizationIdUpdate", record::getUsageOrganizationIdUpdate)
            .map(usageOrganizationNameUpdate).toPropertyWhenPresent("usageOrganizationNameUpdate", record::getUsageOrganizationNameUpdate)
            .map(productLine).toPropertyWhenPresent("productLine", record::getProductLine)
            .map(productLineUpdate).toPropertyWhenPresent("productLineUpdate", record::getProductLineUpdate)
            .map(businessLine).toPropertyWhenPresent("businessLine", record::getBusinessLine)
            .map(businessLineUpdate).toPropertyWhenPresent("businessLineUpdate", record::getBusinessLineUpdate)
            .map(operatingStatus).toPropertyWhenPresent("operatingStatus", record::getOperatingStatus)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default Optional<VehicleApplicationDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleApplicationDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default List<VehicleApplicationDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleApplicationDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default List<VehicleApplicationDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleApplicationDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default Optional<VehicleApplicationDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleApplicationDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleApplicationDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applicationId).equalTo(record::getApplicationId)
                .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
                .set(vin).equalTo(record::getVin)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(vehicleModelId).equalTo(record::getVehicleModelId)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(assetCompanyName).equalTo(record::getAssetCompanyName)
                .set(assetCompanyIdUpdate).equalTo(record::getAssetCompanyIdUpdate)
                .set(assetCompanyNameUpdate).equalTo(record::getAssetCompanyNameUpdate)
                .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
                .set(ownOrganizationName).equalTo(record::getOwnOrganizationName)
                .set(ownOrganizationIdUpdate).equalTo(record::getOwnOrganizationIdUpdate)
                .set(ownOrganizationNameUpdate).equalTo(record::getOwnOrganizationNameUpdate)
                .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
                .set(usageOrganizationName).equalTo(record::getUsageOrganizationName)
                .set(usageOrganizationIdUpdate).equalTo(record::getUsageOrganizationIdUpdate)
                .set(usageOrganizationNameUpdate).equalTo(record::getUsageOrganizationNameUpdate)
                .set(productLine).equalTo(record::getProductLine)
                .set(productLineUpdate).equalTo(record::getProductLineUpdate)
                .set(businessLine).equalTo(record::getBusinessLine)
                .set(businessLineUpdate).equalTo(record::getBusinessLineUpdate)
                .set(operatingStatus).equalTo(record::getOperatingStatus)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleApplicationDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(applicationId).equalToWhenPresent(record::getApplicationId)
                .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
                .set(vin).equalToWhenPresent(record::getVin)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
                .set(assetCompanyIdUpdate).equalToWhenPresent(record::getAssetCompanyIdUpdate)
                .set(assetCompanyNameUpdate).equalToWhenPresent(record::getAssetCompanyNameUpdate)
                .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
                .set(ownOrganizationName).equalToWhenPresent(record::getOwnOrganizationName)
                .set(ownOrganizationIdUpdate).equalToWhenPresent(record::getOwnOrganizationIdUpdate)
                .set(ownOrganizationNameUpdate).equalToWhenPresent(record::getOwnOrganizationNameUpdate)
                .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
                .set(usageOrganizationName).equalToWhenPresent(record::getUsageOrganizationName)
                .set(usageOrganizationIdUpdate).equalToWhenPresent(record::getUsageOrganizationIdUpdate)
                .set(usageOrganizationNameUpdate).equalToWhenPresent(record::getUsageOrganizationNameUpdate)
                .set(productLine).equalToWhenPresent(record::getProductLine)
                .set(productLineUpdate).equalToWhenPresent(record::getProductLineUpdate)
                .set(businessLine).equalToWhenPresent(record::getBusinessLine)
                .set(businessLineUpdate).equalToWhenPresent(record::getBusinessLineUpdate)
                .set(operatingStatus).equalToWhenPresent(record::getOperatingStatus)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default int updateByPrimaryKey(VehicleApplicationDetail record) {
        return update(c ->
            c.set(applicationId).equalTo(record::getApplicationId)
            .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
            .set(vin).equalTo(record::getVin)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(vehicleModelId).equalTo(record::getVehicleModelId)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(assetCompanyName).equalTo(record::getAssetCompanyName)
            .set(assetCompanyIdUpdate).equalTo(record::getAssetCompanyIdUpdate)
            .set(assetCompanyNameUpdate).equalTo(record::getAssetCompanyNameUpdate)
            .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
            .set(ownOrganizationName).equalTo(record::getOwnOrganizationName)
            .set(ownOrganizationIdUpdate).equalTo(record::getOwnOrganizationIdUpdate)
            .set(ownOrganizationNameUpdate).equalTo(record::getOwnOrganizationNameUpdate)
            .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
            .set(usageOrganizationName).equalTo(record::getUsageOrganizationName)
            .set(usageOrganizationIdUpdate).equalTo(record::getUsageOrganizationIdUpdate)
            .set(usageOrganizationNameUpdate).equalTo(record::getUsageOrganizationNameUpdate)
            .set(productLine).equalTo(record::getProductLine)
            .set(productLineUpdate).equalTo(record::getProductLineUpdate)
            .set(businessLine).equalTo(record::getBusinessLine)
            .set(businessLineUpdate).equalTo(record::getBusinessLineUpdate)
            .set(operatingStatus).equalTo(record::getOperatingStatus)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    default int updateByPrimaryKeySelective(VehicleApplicationDetail record) {
        return update(c ->
            c.set(applicationId).equalToWhenPresent(record::getApplicationId)
            .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
            .set(vin).equalToWhenPresent(record::getVin)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
            .set(assetCompanyIdUpdate).equalToWhenPresent(record::getAssetCompanyIdUpdate)
            .set(assetCompanyNameUpdate).equalToWhenPresent(record::getAssetCompanyNameUpdate)
            .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
            .set(ownOrganizationName).equalToWhenPresent(record::getOwnOrganizationName)
            .set(ownOrganizationIdUpdate).equalToWhenPresent(record::getOwnOrganizationIdUpdate)
            .set(ownOrganizationNameUpdate).equalToWhenPresent(record::getOwnOrganizationNameUpdate)
            .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
            .set(usageOrganizationName).equalToWhenPresent(record::getUsageOrganizationName)
            .set(usageOrganizationIdUpdate).equalToWhenPresent(record::getUsageOrganizationIdUpdate)
            .set(usageOrganizationNameUpdate).equalToWhenPresent(record::getUsageOrganizationNameUpdate)
            .set(productLine).equalToWhenPresent(record::getProductLine)
            .set(productLineUpdate).equalToWhenPresent(record::getProductLineUpdate)
            .set(businessLine).equalToWhenPresent(record::getBusinessLine)
            .set(businessLineUpdate).equalToWhenPresent(record::getBusinessLineUpdate)
            .set(operatingStatus).equalToWhenPresent(record::getOperatingStatus)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_application_detail")
    default int insertMultiple(Collection<VehicleApplicationDetail> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, vehicleApplicationDetail, c -> c
                .map(applicationId).toProperty("applicationId")
                .map(vehicleAssetId).toProperty("vehicleAssetId")
                .map(vin).toProperty("vin")
                .map(licensePlate).toProperty("licensePlate")
                .map(vehicleModelId).toProperty("vehicleModelId")
                .map(assetCompanyId).toProperty("assetCompanyId")
                .map(assetCompanyName).toProperty("assetCompanyName")
                .map(assetCompanyIdUpdate).toProperty("assetCompanyIdUpdate")
                .map(assetCompanyNameUpdate).toProperty("assetCompanyNameUpdate")
                .map(ownOrganizationId).toProperty("ownOrganizationId")
                .map(ownOrganizationName).toProperty("ownOrganizationName")
                .map(ownOrganizationIdUpdate).toProperty("ownOrganizationIdUpdate")
                .map(ownOrganizationNameUpdate).toProperty("ownOrganizationNameUpdate")
                .map(usageOrganizationId).toProperty("usageOrganizationId")
                .map(usageOrganizationName).toProperty("usageOrganizationName")
                .map(usageOrganizationIdUpdate).toProperty("usageOrganizationIdUpdate")
                .map(usageOrganizationNameUpdate).toProperty("usageOrganizationNameUpdate")
                .map(productLine).toProperty("productLine")
                .map(productLineUpdate).toProperty("productLineUpdate")
                .map(businessLine).toProperty("businessLine")
                .map(businessLineUpdate).toProperty("businessLineUpdate")
                .map(operatingStatus).toProperty("operatingStatus")
                .map(createOperId).toProperty("createOperId")
                .map(createOperName).toProperty("createOperName")
        );
    }
}