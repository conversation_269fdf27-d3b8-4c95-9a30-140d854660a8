package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class CompanyExtraInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_company_extra_info")
    public static final CompanyExtraInfo companyExtraInfo = new CompanyExtraInfo();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.id")
    public static final SqlColumn<Long> id = companyExtraInfo.id;

    /**
     * Database Column Remarks:
     *   外键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.foreign_id")
    public static final SqlColumn<Long> foreignId = companyExtraInfo.foreignId;

    /**
     * Database Column Remarks:
     *   业务类型 1-组织架构 2-资产所有者
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.business_type")
    public static final SqlColumn<Integer> businessType = companyExtraInfo.businessType;

    /**
     * Database Column Remarks:
     *   总经理名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.ceo_name")
    public static final SqlColumn<String> ceoName = companyExtraInfo.ceoName;

    /**
     * Database Column Remarks:
     *   总经理手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.ceo_phone")
    public static final SqlColumn<String> ceoPhone = companyExtraInfo.ceoPhone;

    /**
     * Database Column Remarks:
     *   钉钉号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.ding_talk_no")
    public static final SqlColumn<String> dingTalkNo = companyExtraInfo.dingTalkNo;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = companyExtraInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.create_time")
    public static final SqlColumn<Date> createTime = companyExtraInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = companyExtraInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.create_oper_name")
    public static final SqlColumn<String> createOperName = companyExtraInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.update_time")
    public static final SqlColumn<Date> updateTime = companyExtraInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = companyExtraInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_company_extra_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = companyExtraInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_company_extra_info")
    public static final class CompanyExtraInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> foreignId = column("foreign_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> businessType = column("business_type", JDBCType.INTEGER);

        public final SqlColumn<String> ceoName = column("ceo_name", JDBCType.VARCHAR);

        public final SqlColumn<String> ceoPhone = column("ceo_phone", JDBCType.VARCHAR);

        public final SqlColumn<String> dingTalkNo = column("ding_talk_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public CompanyExtraInfo() {
            super("t_company_extra_info");
        }
    }
}