package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class UserOrgInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    public static final UserOrgInfo userOrgInfo = new UserOrgInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.id")
    public static final SqlColumn<Long> id = userOrgInfo.id;

    /**
     * Database Column Remarks:
     *   用户id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.user_id")
    public static final SqlColumn<Long> userId = userOrgInfo.userId;

    /**
     * Database Column Remarks:
     *   组织架构id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.org_id")
    public static final SqlColumn<Long> orgId = userOrgInfo.orgId;

    /**
     * Database Column Remarks:
     *   关联机构类型 1-Organization 2-owner
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.org_type")
    public static final SqlColumn<Integer> orgType = userOrgInfo.orgType;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = userOrgInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.create_time")
    public static final SqlColumn<Date> createTime = userOrgInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = userOrgInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.create_oper_name")
    public static final SqlColumn<String> createOperName = userOrgInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.update_time")
    public static final SqlColumn<Date> updateTime = userOrgInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = userOrgInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_user_org_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = userOrgInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    public static final class UserOrgInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> userId = column("user_id", JDBCType.BIGINT);

        public final SqlColumn<Long> orgId = column("org_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> orgType = column("org_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public UserOrgInfo() {
            super("t_user_org_info");
        }
    }
}