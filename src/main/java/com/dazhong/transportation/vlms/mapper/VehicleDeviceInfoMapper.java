package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleDeviceInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleDeviceInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleDeviceInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, deviceType, deviceSeq, deviceBrand, deviceModel, simCardNumber, installTime, installLocation, vin, activationTime, expirationTime, latestLocationTime, longitude, latitude, latestLocation, latestMileageUpdateTime, totalMileage, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleDeviceInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleDeviceInfoResult")
    Optional<VehicleDeviceInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleDeviceInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="device_type", property="deviceType", jdbcType=JdbcType.VARCHAR),
        @Result(column="device_seq", property="deviceSeq", jdbcType=JdbcType.VARCHAR),
        @Result(column="device_brand", property="deviceBrand", jdbcType=JdbcType.VARCHAR),
        @Result(column="device_model", property="deviceModel", jdbcType=JdbcType.VARCHAR),
        @Result(column="sim_card_number", property="simCardNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="install_time", property="installTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="install_location", property="installLocation", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="activation_time", property="activationTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="expiration_time", property="expirationTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="latest_location_time", property="latestLocationTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="longitude", property="longitude", jdbcType=JdbcType.DECIMAL),
        @Result(column="latitude", property="latitude", jdbcType=JdbcType.DECIMAL),
        @Result(column="latest_location", property="latestLocation", jdbcType=JdbcType.VARCHAR),
        @Result(column="latest_mileage_update_time", property="latestMileageUpdateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="total_mileage", property="totalMileage", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleDeviceInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleDeviceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleDeviceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default int insert(VehicleDeviceInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDeviceInfo, c ->
            c.map(deviceType).toProperty("deviceType")
            .map(deviceSeq).toProperty("deviceSeq")
            .map(deviceBrand).toProperty("deviceBrand")
            .map(deviceModel).toProperty("deviceModel")
            .map(simCardNumber).toProperty("simCardNumber")
            .map(installTime).toProperty("installTime")
            .map(installLocation).toProperty("installLocation")
            .map(vin).toProperty("vin")
            .map(activationTime).toProperty("activationTime")
            .map(expirationTime).toProperty("expirationTime")
            .map(latestLocationTime).toProperty("latestLocationTime")
            .map(longitude).toProperty("longitude")
            .map(latitude).toProperty("latitude")
            .map(latestLocation).toProperty("latestLocation")
            .map(latestMileageUpdateTime).toProperty("latestMileageUpdateTime")
            .map(totalMileage).toProperty("totalMileage")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default int insertSelective(VehicleDeviceInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDeviceInfo, c ->
            c.map(deviceType).toPropertyWhenPresent("deviceType", record::getDeviceType)
            .map(deviceSeq).toPropertyWhenPresent("deviceSeq", record::getDeviceSeq)
            .map(deviceBrand).toPropertyWhenPresent("deviceBrand", record::getDeviceBrand)
            .map(deviceModel).toPropertyWhenPresent("deviceModel", record::getDeviceModel)
            .map(simCardNumber).toPropertyWhenPresent("simCardNumber", record::getSimCardNumber)
            .map(installTime).toPropertyWhenPresent("installTime", record::getInstallTime)
            .map(installLocation).toPropertyWhenPresent("installLocation", record::getInstallLocation)
            .map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(activationTime).toPropertyWhenPresent("activationTime", record::getActivationTime)
            .map(expirationTime).toPropertyWhenPresent("expirationTime", record::getExpirationTime)
            .map(latestLocationTime).toPropertyWhenPresent("latestLocationTime", record::getLatestLocationTime)
            .map(longitude).toPropertyWhenPresent("longitude", record::getLongitude)
            .map(latitude).toPropertyWhenPresent("latitude", record::getLatitude)
            .map(latestLocation).toPropertyWhenPresent("latestLocation", record::getLatestLocation)
            .map(latestMileageUpdateTime).toPropertyWhenPresent("latestMileageUpdateTime", record::getLatestMileageUpdateTime)
            .map(totalMileage).toPropertyWhenPresent("totalMileage", record::getTotalMileage)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default Optional<VehicleDeviceInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleDeviceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default List<VehicleDeviceInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleDeviceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default List<VehicleDeviceInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleDeviceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default Optional<VehicleDeviceInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleDeviceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleDeviceInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(deviceType).equalTo(record::getDeviceType)
                .set(deviceSeq).equalTo(record::getDeviceSeq)
                .set(deviceBrand).equalTo(record::getDeviceBrand)
                .set(deviceModel).equalTo(record::getDeviceModel)
                .set(simCardNumber).equalTo(record::getSimCardNumber)
                .set(installTime).equalTo(record::getInstallTime)
                .set(installLocation).equalTo(record::getInstallLocation)
                .set(vin).equalTo(record::getVin)
                .set(activationTime).equalTo(record::getActivationTime)
                .set(expirationTime).equalTo(record::getExpirationTime)
                .set(latestLocationTime).equalTo(record::getLatestLocationTime)
                .set(longitude).equalTo(record::getLongitude)
                .set(latitude).equalTo(record::getLatitude)
                .set(latestLocation).equalTo(record::getLatestLocation)
                .set(latestMileageUpdateTime).equalTo(record::getLatestMileageUpdateTime)
                .set(totalMileage).equalTo(record::getTotalMileage)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleDeviceInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(deviceType).equalToWhenPresent(record::getDeviceType)
                .set(deviceSeq).equalToWhenPresent(record::getDeviceSeq)
                .set(deviceBrand).equalToWhenPresent(record::getDeviceBrand)
                .set(deviceModel).equalToWhenPresent(record::getDeviceModel)
                .set(simCardNumber).equalToWhenPresent(record::getSimCardNumber)
                .set(installTime).equalToWhenPresent(record::getInstallTime)
                .set(installLocation).equalToWhenPresent(record::getInstallLocation)
                .set(vin).equalToWhenPresent(record::getVin)
                .set(activationTime).equalToWhenPresent(record::getActivationTime)
                .set(expirationTime).equalToWhenPresent(record::getExpirationTime)
                .set(latestLocationTime).equalToWhenPresent(record::getLatestLocationTime)
                .set(longitude).equalToWhenPresent(record::getLongitude)
                .set(latitude).equalToWhenPresent(record::getLatitude)
                .set(latestLocation).equalToWhenPresent(record::getLatestLocation)
                .set(latestMileageUpdateTime).equalToWhenPresent(record::getLatestMileageUpdateTime)
                .set(totalMileage).equalToWhenPresent(record::getTotalMileage)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default int updateByPrimaryKey(VehicleDeviceInfo record) {
        return update(c ->
            c.set(deviceType).equalTo(record::getDeviceType)
            .set(deviceSeq).equalTo(record::getDeviceSeq)
            .set(deviceBrand).equalTo(record::getDeviceBrand)
            .set(deviceModel).equalTo(record::getDeviceModel)
            .set(simCardNumber).equalTo(record::getSimCardNumber)
            .set(installTime).equalTo(record::getInstallTime)
            .set(installLocation).equalTo(record::getInstallLocation)
            .set(vin).equalTo(record::getVin)
            .set(activationTime).equalTo(record::getActivationTime)
            .set(expirationTime).equalTo(record::getExpirationTime)
            .set(latestLocationTime).equalTo(record::getLatestLocationTime)
            .set(longitude).equalTo(record::getLongitude)
            .set(latitude).equalTo(record::getLatitude)
            .set(latestLocation).equalTo(record::getLatestLocation)
            .set(latestMileageUpdateTime).equalTo(record::getLatestMileageUpdateTime)
            .set(totalMileage).equalTo(record::getTotalMileage)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    default int updateByPrimaryKeySelective(VehicleDeviceInfo record) {
        return update(c ->
            c.set(deviceType).equalToWhenPresent(record::getDeviceType)
            .set(deviceSeq).equalToWhenPresent(record::getDeviceSeq)
            .set(deviceBrand).equalToWhenPresent(record::getDeviceBrand)
            .set(deviceModel).equalToWhenPresent(record::getDeviceModel)
            .set(simCardNumber).equalToWhenPresent(record::getSimCardNumber)
            .set(installTime).equalToWhenPresent(record::getInstallTime)
            .set(installLocation).equalToWhenPresent(record::getInstallLocation)
            .set(vin).equalToWhenPresent(record::getVin)
            .set(activationTime).equalToWhenPresent(record::getActivationTime)
            .set(expirationTime).equalToWhenPresent(record::getExpirationTime)
            .set(latestLocationTime).equalToWhenPresent(record::getLatestLocationTime)
            .set(longitude).equalToWhenPresent(record::getLongitude)
            .set(latitude).equalToWhenPresent(record::getLatitude)
            .set(latestLocation).equalToWhenPresent(record::getLatestLocation)
            .set(latestMileageUpdateTime).equalToWhenPresent(record::getLatestMileageUpdateTime)
            .set(totalMileage).equalToWhenPresent(record::getTotalMileage)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}