package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleDisposalFileDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_file")
    public static final VehicleDisposalFile vehicleDisposalFile = new VehicleDisposalFile();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.id")
    public static final SqlColumn<Long> id = vehicleDisposalFile.id;

    /**
     * Database Column Remarks:
     *   处置单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.disposal_id")
    public static final SqlColumn<Long> disposalId = vehicleDisposalFile.disposalId;

    /**
     * Database Column Remarks:
     *   文件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.file_name")
    public static final SqlColumn<String> fileName = vehicleDisposalFile.fileName;

    /**
     * Database Column Remarks:
     *   文件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.file_url")
    public static final SqlColumn<String> fileUrl = vehicleDisposalFile.fileUrl;

    /**
     * Database Column Remarks:
     *   文件描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.file_desc")
    public static final SqlColumn<String> fileDesc = vehicleDisposalFile.fileDesc;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleDisposalFile.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.create_time")
    public static final SqlColumn<Date> createTime = vehicleDisposalFile.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleDisposalFile.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleDisposalFile.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.update_time")
    public static final SqlColumn<Date> updateTime = vehicleDisposalFile.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleDisposalFile.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_file.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleDisposalFile.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_file")
    public static final class VehicleDisposalFile extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> disposalId = column("disposal_id", JDBCType.BIGINT);

        public final SqlColumn<String> fileName = column("file_name", JDBCType.VARCHAR);

        public final SqlColumn<String> fileUrl = column("file_url", JDBCType.VARCHAR);

        public final SqlColumn<String> fileDesc = column("file_desc", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleDisposalFile() {
            super("t_vehicle_disposal_file");
        }
    }
}