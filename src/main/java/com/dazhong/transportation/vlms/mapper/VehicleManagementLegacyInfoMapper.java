package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleManagementLegacyInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleManagementLegacyInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleManagementLegacyInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, operateTypeId, vehicleCategoryId, operationCategoryId, ownerId, companyOwnerId, assetOwnerId, contractTypeId, operatingNo, startDate, purchaseDate, licenseDate, operationStartDate, hasRight, vehicleModelName, fromCompany, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleManagementLegacyInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleManagementLegacyInfoResult")
    Optional<VehicleManagementLegacyInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleManagementLegacyInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="operate_type_id", property="operateTypeId", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_category_id", property="vehicleCategoryId", jdbcType=JdbcType.INTEGER),
        @Result(column="operation_category_id", property="operationCategoryId", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="company_owner_id", property="companyOwnerId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_owner_id", property="assetOwnerId", jdbcType=JdbcType.INTEGER),
        @Result(column="contract_type_id", property="contractTypeId", jdbcType=JdbcType.INTEGER),
        @Result(column="operating_no", property="operatingNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="purchase_date", property="purchaseDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="license_date", property="licenseDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="operation_start_date", property="operationStartDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="has_right", property="hasRight", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="from_company", property="fromCompany", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleManagementLegacyInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleManagementLegacyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleManagementLegacyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default int insert(VehicleManagementLegacyInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleManagementLegacyInfo, c ->
            c.map(vin).toProperty("vin")
            .map(operateTypeId).toProperty("operateTypeId")
            .map(vehicleCategoryId).toProperty("vehicleCategoryId")
            .map(operationCategoryId).toProperty("operationCategoryId")
            .map(ownerId).toProperty("ownerId")
            .map(companyOwnerId).toProperty("companyOwnerId")
            .map(assetOwnerId).toProperty("assetOwnerId")
            .map(contractTypeId).toProperty("contractTypeId")
            .map(operatingNo).toProperty("operatingNo")
            .map(startDate).toProperty("startDate")
            .map(purchaseDate).toProperty("purchaseDate")
            .map(licenseDate).toProperty("licenseDate")
            .map(operationStartDate).toProperty("operationStartDate")
            .map(hasRight).toProperty("hasRight")
            .map(vehicleModelName).toProperty("vehicleModelName")
            .map(fromCompany).toProperty("fromCompany")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default int insertSelective(VehicleManagementLegacyInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleManagementLegacyInfo, c ->
            c.map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(operateTypeId).toPropertyWhenPresent("operateTypeId", record::getOperateTypeId)
            .map(vehicleCategoryId).toPropertyWhenPresent("vehicleCategoryId", record::getVehicleCategoryId)
            .map(operationCategoryId).toPropertyWhenPresent("operationCategoryId", record::getOperationCategoryId)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(companyOwnerId).toPropertyWhenPresent("companyOwnerId", record::getCompanyOwnerId)
            .map(assetOwnerId).toPropertyWhenPresent("assetOwnerId", record::getAssetOwnerId)
            .map(contractTypeId).toPropertyWhenPresent("contractTypeId", record::getContractTypeId)
            .map(operatingNo).toPropertyWhenPresent("operatingNo", record::getOperatingNo)
            .map(startDate).toPropertyWhenPresent("startDate", record::getStartDate)
            .map(purchaseDate).toPropertyWhenPresent("purchaseDate", record::getPurchaseDate)
            .map(licenseDate).toPropertyWhenPresent("licenseDate", record::getLicenseDate)
            .map(operationStartDate).toPropertyWhenPresent("operationStartDate", record::getOperationStartDate)
            .map(hasRight).toPropertyWhenPresent("hasRight", record::getHasRight)
            .map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", record::getVehicleModelName)
            .map(fromCompany).toPropertyWhenPresent("fromCompany", record::getFromCompany)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default Optional<VehicleManagementLegacyInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleManagementLegacyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default List<VehicleManagementLegacyInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleManagementLegacyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default List<VehicleManagementLegacyInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleManagementLegacyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default Optional<VehicleManagementLegacyInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleManagementLegacyInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleManagementLegacyInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(record::getVin)
                .set(operateTypeId).equalTo(record::getOperateTypeId)
                .set(vehicleCategoryId).equalTo(record::getVehicleCategoryId)
                .set(operationCategoryId).equalTo(record::getOperationCategoryId)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(companyOwnerId).equalTo(record::getCompanyOwnerId)
                .set(assetOwnerId).equalTo(record::getAssetOwnerId)
                .set(contractTypeId).equalTo(record::getContractTypeId)
                .set(operatingNo).equalTo(record::getOperatingNo)
                .set(startDate).equalTo(record::getStartDate)
                .set(purchaseDate).equalTo(record::getPurchaseDate)
                .set(licenseDate).equalTo(record::getLicenseDate)
                .set(operationStartDate).equalTo(record::getOperationStartDate)
                .set(hasRight).equalTo(record::getHasRight)
                .set(vehicleModelName).equalTo(record::getVehicleModelName)
                .set(fromCompany).equalTo(record::getFromCompany)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleManagementLegacyInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(record::getVin)
                .set(operateTypeId).equalToWhenPresent(record::getOperateTypeId)
                .set(vehicleCategoryId).equalToWhenPresent(record::getVehicleCategoryId)
                .set(operationCategoryId).equalToWhenPresent(record::getOperationCategoryId)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(companyOwnerId).equalToWhenPresent(record::getCompanyOwnerId)
                .set(assetOwnerId).equalToWhenPresent(record::getAssetOwnerId)
                .set(contractTypeId).equalToWhenPresent(record::getContractTypeId)
                .set(operatingNo).equalToWhenPresent(record::getOperatingNo)
                .set(startDate).equalToWhenPresent(record::getStartDate)
                .set(purchaseDate).equalToWhenPresent(record::getPurchaseDate)
                .set(licenseDate).equalToWhenPresent(record::getLicenseDate)
                .set(operationStartDate).equalToWhenPresent(record::getOperationStartDate)
                .set(hasRight).equalToWhenPresent(record::getHasRight)
                .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
                .set(fromCompany).equalToWhenPresent(record::getFromCompany)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default int updateByPrimaryKey(VehicleManagementLegacyInfo record) {
        return update(c ->
            c.set(vin).equalTo(record::getVin)
            .set(operateTypeId).equalTo(record::getOperateTypeId)
            .set(vehicleCategoryId).equalTo(record::getVehicleCategoryId)
            .set(operationCategoryId).equalTo(record::getOperationCategoryId)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(companyOwnerId).equalTo(record::getCompanyOwnerId)
            .set(assetOwnerId).equalTo(record::getAssetOwnerId)
            .set(contractTypeId).equalTo(record::getContractTypeId)
            .set(operatingNo).equalTo(record::getOperatingNo)
            .set(startDate).equalTo(record::getStartDate)
            .set(purchaseDate).equalTo(record::getPurchaseDate)
            .set(licenseDate).equalTo(record::getLicenseDate)
            .set(operationStartDate).equalTo(record::getOperationStartDate)
            .set(hasRight).equalTo(record::getHasRight)
            .set(vehicleModelName).equalTo(record::getVehicleModelName)
            .set(fromCompany).equalTo(record::getFromCompany)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    default int updateByPrimaryKeySelective(VehicleManagementLegacyInfo record) {
        return update(c ->
            c.set(vin).equalToWhenPresent(record::getVin)
            .set(operateTypeId).equalToWhenPresent(record::getOperateTypeId)
            .set(vehicleCategoryId).equalToWhenPresent(record::getVehicleCategoryId)
            .set(operationCategoryId).equalToWhenPresent(record::getOperationCategoryId)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(companyOwnerId).equalToWhenPresent(record::getCompanyOwnerId)
            .set(assetOwnerId).equalToWhenPresent(record::getAssetOwnerId)
            .set(contractTypeId).equalToWhenPresent(record::getContractTypeId)
            .set(operatingNo).equalToWhenPresent(record::getOperatingNo)
            .set(startDate).equalToWhenPresent(record::getStartDate)
            .set(purchaseDate).equalToWhenPresent(record::getPurchaseDate)
            .set(licenseDate).equalToWhenPresent(record::getLicenseDate)
            .set(operationStartDate).equalToWhenPresent(record::getOperationStartDate)
            .set(hasRight).equalToWhenPresent(record::getHasRight)
            .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
            .set(fromCompany).equalToWhenPresent(record::getFromCompany)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}