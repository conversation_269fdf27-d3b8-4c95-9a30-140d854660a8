package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.OrgInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.OrgInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface OrgInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyCode, companyName, checkedState, parentId, position, orgType, vgUid, pgUid, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<OrgInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("OrgInfoResult")
    Optional<OrgInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="OrgInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_code", property="companyCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="company_name", property="companyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="checked_state", property="checkedState", jdbcType=JdbcType.INTEGER),
        @Result(column="parent_id", property="parentId", jdbcType=JdbcType.BIGINT),
        @Result(column="position", property="position", jdbcType=JdbcType.INTEGER),
        @Result(column="org_type", property="orgType", jdbcType=JdbcType.VARCHAR),
        @Result(column="vg_uid", property="vgUid", jdbcType=JdbcType.VARCHAR),
        @Result(column="pg_uid", property="pgUid", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<OrgInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, orgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, orgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default int insert(OrgInfo record) {
        return MyBatis3Utils.insert(this::insert, record, orgInfo, c ->
            c.map(companyCode).toProperty("companyCode")
            .map(companyName).toProperty("companyName")
            .map(checkedState).toProperty("checkedState")
            .map(parentId).toProperty("parentId")
            .map(position).toProperty("position")
            .map(orgType).toProperty("orgType")
            .map(vgUid).toProperty("vgUid")
            .map(pgUid).toProperty("pgUid")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default int insertSelective(OrgInfo record) {
        return MyBatis3Utils.insert(this::insert, record, orgInfo, c ->
            c.map(id).toPropertyWhenPresent("id", record::getId)
            .map(companyCode).toPropertyWhenPresent("companyCode", record::getCompanyCode)
            .map(companyName).toPropertyWhenPresent("companyName", record::getCompanyName)
            .map(checkedState).toPropertyWhenPresent("checkedState", record::getCheckedState)
            .map(parentId).toPropertyWhenPresent("parentId", record::getParentId)
            .map(position).toPropertyWhenPresent("position", record::getPosition)
            .map(orgType).toPropertyWhenPresent("orgType", record::getOrgType)
            .map(vgUid).toPropertyWhenPresent("vgUid", record::getVgUid)
            .map(pgUid).toPropertyWhenPresent("pgUid", record::getPgUid)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default Optional<OrgInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, orgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default List<OrgInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, orgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default List<OrgInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, orgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default Optional<OrgInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, orgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    static UpdateDSL<UpdateModel> updateAllColumns(OrgInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyCode).equalTo(record::getCompanyCode)
                .set(companyName).equalTo(record::getCompanyName)
                .set(checkedState).equalTo(record::getCheckedState)
                .set(parentId).equalTo(record::getParentId)
                .set(position).equalTo(record::getPosition)
                .set(orgType).equalTo(record::getOrgType)
                .set(vgUid).equalTo(record::getVgUid)
                .set(pgUid).equalTo(record::getPgUid)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(OrgInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyCode).equalToWhenPresent(record::getCompanyCode)
                .set(companyName).equalToWhenPresent(record::getCompanyName)
                .set(checkedState).equalToWhenPresent(record::getCheckedState)
                .set(parentId).equalToWhenPresent(record::getParentId)
                .set(position).equalToWhenPresent(record::getPosition)
                .set(orgType).equalToWhenPresent(record::getOrgType)
                .set(vgUid).equalToWhenPresent(record::getVgUid)
                .set(pgUid).equalToWhenPresent(record::getPgUid)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default int updateByPrimaryKey(OrgInfo record) {
        return update(c ->
            c.set(companyCode).equalTo(record::getCompanyCode)
            .set(companyName).equalTo(record::getCompanyName)
            .set(checkedState).equalTo(record::getCheckedState)
            .set(parentId).equalTo(record::getParentId)
            .set(position).equalTo(record::getPosition)
            .set(orgType).equalTo(record::getOrgType)
            .set(vgUid).equalTo(record::getVgUid)
            .set(pgUid).equalTo(record::getPgUid)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    default int updateByPrimaryKeySelective(OrgInfo record) {
        return update(c ->
            c.set(companyCode).equalToWhenPresent(record::getCompanyCode)
            .set(companyName).equalToWhenPresent(record::getCompanyName)
            .set(checkedState).equalToWhenPresent(record::getCheckedState)
            .set(parentId).equalToWhenPresent(record::getParentId)
            .set(position).equalToWhenPresent(record::getPosition)
            .set(orgType).equalToWhenPresent(record::getOrgType)
            .set(vgUid).equalToWhenPresent(record::getVgUid)
            .set(pgUid).equalToWhenPresent(record::getPgUid)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}