package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.UserOrgInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.UserOrgInfo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface UserOrgInfoMapper extends CommonInsertMapper<UserOrgInfo> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, userId, orgId, orgType, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<UserOrgInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("UserOrgInfoResult")
    Optional<UserOrgInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="UserOrgInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="user_id", property="userId", jdbcType=JdbcType.BIGINT),
        @Result(column="org_id", property="orgId", jdbcType=JdbcType.BIGINT),
        @Result(column="org_type", property="orgType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<UserOrgInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, userOrgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, userOrgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int insert(UserOrgInfo record) {
        return MyBatis3Utils.insert(this::insert, record, userOrgInfo, c ->
            c.map(userId).toProperty("userId")
            .map(orgId).toProperty("orgId")
            .map(orgType).toProperty("orgType")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int insertMultiple(Collection<UserOrgInfo> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, userOrgInfo, c ->
                c.map(userId).toProperty("userId")
                        .map(orgType).toProperty("orgType")
                        .map(orgId).toProperty("orgId")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int insertSelective(UserOrgInfo record) {
        return MyBatis3Utils.insert(this::insert, record, userOrgInfo, c ->
            c.map(userId).toPropertyWhenPresent("userId", record::getUserId)
            .map(orgId).toPropertyWhenPresent("orgId", record::getOrgId)
            .map(orgType).toPropertyWhenPresent("orgType", record::getOrgType)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default Optional<UserOrgInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, userOrgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default List<UserOrgInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, userOrgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default List<UserOrgInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, userOrgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default Optional<UserOrgInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, userOrgInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    static UpdateDSL<UpdateModel> updateAllColumns(UserOrgInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(userId).equalTo(record::getUserId)
                .set(orgId).equalTo(record::getOrgId)
                .set(orgType).equalTo(record::getOrgType)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(UserOrgInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(userId).equalToWhenPresent(record::getUserId)
                .set(orgId).equalToWhenPresent(record::getOrgId)
                .set(orgType).equalToWhenPresent(record::getOrgType)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int updateByPrimaryKey(UserOrgInfo record) {
        return update(c ->
            c.set(userId).equalTo(record::getUserId)
            .set(orgId).equalTo(record::getOrgId)
            .set(orgType).equalTo(record::getOrgType)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_user_org_info")
    default int updateByPrimaryKeySelective(UserOrgInfo record) {
        return update(c ->
            c.set(userId).equalToWhenPresent(record::getUserId)
            .set(orgId).equalToWhenPresent(record::getOrgId)
            .set(orgType).equalToWhenPresent(record::getOrgType)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}