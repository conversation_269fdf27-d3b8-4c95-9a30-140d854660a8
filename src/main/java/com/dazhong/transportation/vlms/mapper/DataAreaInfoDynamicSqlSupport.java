package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class DataAreaInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_area_info")
    public static final DataAreaInfo dataAreaInfo = new DataAreaInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.id")
    public static final SqlColumn<Long> id = dataAreaInfo.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.name")
    public static final SqlColumn<String> name = dataAreaInfo.name;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.level_code")
    public static final SqlColumn<String> levelCode = dataAreaInfo.levelCode;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.parent_id")
    public static final SqlColumn<Long> parentId = dataAreaInfo.parentId;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = dataAreaInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.create_time")
    public static final SqlColumn<Date> createTime = dataAreaInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = dataAreaInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.create_oper_name")
    public static final SqlColumn<String> createOperName = dataAreaInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.update_time")
    public static final SqlColumn<Date> updateTime = dataAreaInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = dataAreaInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_area_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = dataAreaInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_area_info")
    public static final class DataAreaInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> levelCode = column("level_code", JDBCType.VARCHAR);

        public final SqlColumn<Long> parentId = column("parent_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public DataAreaInfo() {
            super("t_data_area_info");
        }
    }
}