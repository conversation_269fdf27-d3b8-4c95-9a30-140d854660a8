package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class SsoUserInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_sso_user_info")
    public static final SsoUserInfo ssoUserInfo = new SsoUserInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.id")
    public static final SqlColumn<Long> id = ssoUserInfo.id;

    /**
     * Database Column Remarks:
     *   用户id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.user_id")
    public static final SqlColumn<String> userId = ssoUserInfo.userId;

    /**
     * Database Column Remarks:
     *   手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.mobile_phone")
    public static final SqlColumn<String> mobilePhone = ssoUserInfo.mobilePhone;

    /**
     * Database Column Remarks:
     *   姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.name")
    public static final SqlColumn<String> name = ssoUserInfo.name;

    /**
     * Database Column Remarks:
     *   公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.company_id")
    public static final SqlColumn<String> companyId = ssoUserInfo.companyId;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = ssoUserInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.create_time")
    public static final SqlColumn<Date> createTime = ssoUserInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = ssoUserInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.create_oper_name")
    public static final SqlColumn<String> createOperName = ssoUserInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.update_time")
    public static final SqlColumn<Date> updateTime = ssoUserInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = ssoUserInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_sso_user_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = ssoUserInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_sso_user_info")
    public static final class SsoUserInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> userId = column("user_id", JDBCType.VARCHAR);

        public final SqlColumn<String> mobilePhone = column("mobile_phone", JDBCType.VARCHAR);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> companyId = column("company_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public SsoUserInfo() {
            super("t_sso_user_info");
        }
    }
}