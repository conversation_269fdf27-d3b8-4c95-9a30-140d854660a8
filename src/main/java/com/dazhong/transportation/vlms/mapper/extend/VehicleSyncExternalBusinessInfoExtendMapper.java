package com.dazhong.transportation.vlms.mapper.extend;

import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.businessInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.businessNo;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.businessTime;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.businessType;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.createOperId;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.createOperName;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.createTime;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.isDeleted;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.licensePlate;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.updateOperId;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.updateOperName;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.updateTime;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.vehicleSyncExternalBusinessInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.vin;

import java.util.Collection;

import javax.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoMapper;
import com.dazhong.transportation.vlms.model.VehicleSyncExternalBusinessInfo;

@Mapper
public interface VehicleSyncExternalBusinessInfoExtendMapper extends VehicleSyncExternalBusinessInfoMapper {

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_sync_external_business_info")
    default int insertMultiple(Collection<VehicleSyncExternalBusinessInfo> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, vehicleSyncExternalBusinessInfo, c -> c
                .map(vin).toProperty("vin")
                .map(licensePlate).toProperty("licensePlate")
                .map(businessType).toProperty("businessType")
                .map(businessNo).toProperty("businessNo")
                .map(businessTime).toProperty("businessTime")
                .map(isDeleted).toProperty("isDeleted")
                .map(createTime).toProperty("createTime")
                .map(createOperId).toProperty("createOperId")
                .map(createOperName).toProperty("createOperName")
                .map(updateTime).toProperty("updateTime")
                .map(updateOperId).toProperty("updateOperId")
                .map(updateOperName).toProperty("updateOperName")
                .map(businessInfo).toProperty("businessInfo")
        );
    }

}