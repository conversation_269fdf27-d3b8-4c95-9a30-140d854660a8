package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehiclePurchaseEquipmentDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    public static final VehiclePurchaseEquipment vehiclePurchaseEquipment = new VehiclePurchaseEquipment();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.id")
    public static final SqlColumn<Integer> id = vehiclePurchaseEquipment.id;

    /**
     * Database Column Remarks:
     *   采购申id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.purchase_apply_id")
    public static final SqlColumn<Long> purchaseApplyId = vehiclePurchaseEquipment.purchaseApplyId;

    /**
     * Database Column Remarks:
     *   设备名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.equipment_name")
    public static final SqlColumn<String> equipmentName = vehiclePurchaseEquipment.equipmentName;

    /**
     * Database Column Remarks:
     *   制造商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.manufacturer_id")
    public static final SqlColumn<Integer> manufacturerId = vehiclePurchaseEquipment.manufacturerId;

    /**
     * Database Column Remarks:
     *   供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.supplier_id")
    public static final SqlColumn<Integer> supplierId = vehiclePurchaseEquipment.supplierId;

    /**
     * Database Column Remarks:
     *   单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.unit_price")
    public static final SqlColumn<BigDecimal> unitPrice = vehiclePurchaseEquipment.unitPrice;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.quantity")
    public static final SqlColumn<Integer> quantity = vehiclePurchaseEquipment.quantity;

    /**
     * Database Column Remarks:
     *   其他费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.other_costs")
    public static final SqlColumn<BigDecimal> otherCosts = vehiclePurchaseEquipment.otherCosts;

    /**
     * Database Column Remarks:
     *   设备总价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.total_price")
    public static final SqlColumn<BigDecimal> totalPrice = vehiclePurchaseEquipment.totalPrice;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehiclePurchaseEquipment.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_time")
    public static final SqlColumn<Date> createTime = vehiclePurchaseEquipment.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehiclePurchaseEquipment.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_name")
    public static final SqlColumn<String> createOperName = vehiclePurchaseEquipment.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_time")
    public static final SqlColumn<Date> updateTime = vehiclePurchaseEquipment.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehiclePurchaseEquipment.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehiclePurchaseEquipment.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    public static final class VehiclePurchaseEquipment extends SqlTable {
        public final SqlColumn<Integer> id = column("id", JDBCType.INTEGER);

        public final SqlColumn<Long> purchaseApplyId = column("purchase_apply_id", JDBCType.BIGINT);

        public final SqlColumn<String> equipmentName = column("equipment_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> manufacturerId = column("manufacturer_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> supplierId = column("supplier_id", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> unitPrice = column("unit_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> quantity = column("quantity", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> otherCosts = column("other_costs", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> totalPrice = column("total_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehiclePurchaseEquipment() {
            super("t_vehicle_purchase_equipment");
        }
    }
}