package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyDetailsDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehiclePurchaseApplyDetails;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehiclePurchaseApplyDetailsMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    BasicColumn[] selectList = BasicColumn.columnList(id, purchaseApplyId, applyDetailsNo, budgetStatus, purchaseType, purchaseModelId, unitPrice, quantity, receivedQuantity, otherCosts, totalPrice, businessType, ownerId, manufacturerId, supplierId, usedYears, usedKm, expectedReturnDate, expectedDeliveryDate, isQuotaOccupied, quotaType, quotaAssetOwnership, preOccupied, returnOnInvestment, monthlyBicycleRevenue, vehicleResidualValueRate, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehiclePurchaseApplyDetails> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehiclePurchaseApplyDetailsResult")
    Optional<VehiclePurchaseApplyDetails> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehiclePurchaseApplyDetailsResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="purchase_apply_id", property="purchaseApplyId", jdbcType=JdbcType.BIGINT),
        @Result(column="apply_details_no", property="applyDetailsNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="budget_status", property="budgetStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="purchase_type", property="purchaseType", jdbcType=JdbcType.INTEGER),
        @Result(column="purchase_model_id", property="purchaseModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="unit_price", property="unitPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="quantity", property="quantity", jdbcType=JdbcType.INTEGER),
        @Result(column="received_quantity", property="receivedQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="other_costs", property="otherCosts", jdbcType=JdbcType.DECIMAL),
        @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="business_type", property="businessType", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="manufacturer_id", property="manufacturerId", jdbcType=JdbcType.INTEGER),
        @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
        @Result(column="used_years", property="usedYears", jdbcType=JdbcType.VARCHAR),
        @Result(column="used_km", property="usedKm", jdbcType=JdbcType.VARCHAR),
        @Result(column="expected_return_date", property="expectedReturnDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="expected_delivery_date", property="expectedDeliveryDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_quota_occupied", property="isQuotaOccupied", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_asset_ownership", property="quotaAssetOwnership", jdbcType=JdbcType.INTEGER),
        @Result(column="pre_occupied", property="preOccupied", jdbcType=JdbcType.INTEGER),
        @Result(column="return_on_investment", property="returnOnInvestment", jdbcType=JdbcType.VARCHAR),
        @Result(column="monthly_bicycle_revenue", property="monthlyBicycleRevenue", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_residual_value_rate", property="vehicleResidualValueRate", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehiclePurchaseApplyDetails> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehiclePurchaseApplyDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehiclePurchaseApplyDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default int insert(VehiclePurchaseApplyDetails record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseApplyDetails, c ->
            c.map(purchaseApplyId).toProperty("purchaseApplyId")
            .map(applyDetailsNo).toProperty("applyDetailsNo")
            .map(budgetStatus).toProperty("budgetStatus")
            .map(purchaseType).toProperty("purchaseType")
            .map(purchaseModelId).toProperty("purchaseModelId")
            .map(unitPrice).toProperty("unitPrice")
            .map(quantity).toProperty("quantity")
            .map(receivedQuantity).toProperty("receivedQuantity")
            .map(otherCosts).toProperty("otherCosts")
            .map(totalPrice).toProperty("totalPrice")
            .map(businessType).toProperty("businessType")
            .map(ownerId).toProperty("ownerId")
            .map(manufacturerId).toProperty("manufacturerId")
            .map(supplierId).toProperty("supplierId")
            .map(usedYears).toProperty("usedYears")
            .map(usedKm).toProperty("usedKm")
            .map(expectedReturnDate).toProperty("expectedReturnDate")
            .map(expectedDeliveryDate).toProperty("expectedDeliveryDate")
            .map(isQuotaOccupied).toProperty("isQuotaOccupied")
            .map(quotaType).toProperty("quotaType")
            .map(quotaAssetOwnership).toProperty("quotaAssetOwnership")
            .map(preOccupied).toProperty("preOccupied")
            .map(returnOnInvestment).toProperty("returnOnInvestment")
            .map(monthlyBicycleRevenue).toProperty("monthlyBicycleRevenue")
            .map(vehicleResidualValueRate).toProperty("vehicleResidualValueRate")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default int insertSelective(VehiclePurchaseApplyDetails record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseApplyDetails, c ->
            c.map(purchaseApplyId).toPropertyWhenPresent("purchaseApplyId", record::getPurchaseApplyId)
            .map(applyDetailsNo).toPropertyWhenPresent("applyDetailsNo", record::getApplyDetailsNo)
            .map(budgetStatus).toPropertyWhenPresent("budgetStatus", record::getBudgetStatus)
            .map(purchaseType).toPropertyWhenPresent("purchaseType", record::getPurchaseType)
            .map(purchaseModelId).toPropertyWhenPresent("purchaseModelId", record::getPurchaseModelId)
            .map(unitPrice).toPropertyWhenPresent("unitPrice", record::getUnitPrice)
            .map(quantity).toPropertyWhenPresent("quantity", record::getQuantity)
            .map(receivedQuantity).toPropertyWhenPresent("receivedQuantity", record::getReceivedQuantity)
            .map(otherCosts).toPropertyWhenPresent("otherCosts", record::getOtherCosts)
            .map(totalPrice).toPropertyWhenPresent("totalPrice", record::getTotalPrice)
            .map(businessType).toPropertyWhenPresent("businessType", record::getBusinessType)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(manufacturerId).toPropertyWhenPresent("manufacturerId", record::getManufacturerId)
            .map(supplierId).toPropertyWhenPresent("supplierId", record::getSupplierId)
            .map(usedYears).toPropertyWhenPresent("usedYears", record::getUsedYears)
            .map(usedKm).toPropertyWhenPresent("usedKm", record::getUsedKm)
            .map(expectedReturnDate).toPropertyWhenPresent("expectedReturnDate", record::getExpectedReturnDate)
            .map(expectedDeliveryDate).toPropertyWhenPresent("expectedDeliveryDate", record::getExpectedDeliveryDate)
            .map(isQuotaOccupied).toPropertyWhenPresent("isQuotaOccupied", record::getIsQuotaOccupied)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(quotaAssetOwnership).toPropertyWhenPresent("quotaAssetOwnership", record::getQuotaAssetOwnership)
            .map(preOccupied).toPropertyWhenPresent("preOccupied", record::getPreOccupied)
            .map(returnOnInvestment).toPropertyWhenPresent("returnOnInvestment", record::getReturnOnInvestment)
            .map(monthlyBicycleRevenue).toPropertyWhenPresent("monthlyBicycleRevenue", record::getMonthlyBicycleRevenue)
            .map(vehicleResidualValueRate).toPropertyWhenPresent("vehicleResidualValueRate", record::getVehicleResidualValueRate)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default Optional<VehiclePurchaseApplyDetails> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehiclePurchaseApplyDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default List<VehiclePurchaseApplyDetails> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehiclePurchaseApplyDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default List<VehiclePurchaseApplyDetails> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehiclePurchaseApplyDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default Optional<VehiclePurchaseApplyDetails> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehiclePurchaseApplyDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    static UpdateDSL<UpdateModel> updateAllColumns(VehiclePurchaseApplyDetails record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
                .set(applyDetailsNo).equalTo(record::getApplyDetailsNo)
                .set(budgetStatus).equalTo(record::getBudgetStatus)
                .set(purchaseType).equalTo(record::getPurchaseType)
                .set(purchaseModelId).equalTo(record::getPurchaseModelId)
                .set(unitPrice).equalTo(record::getUnitPrice)
                .set(quantity).equalTo(record::getQuantity)
                .set(receivedQuantity).equalTo(record::getReceivedQuantity)
                .set(otherCosts).equalTo(record::getOtherCosts)
                .set(totalPrice).equalTo(record::getTotalPrice)
                .set(businessType).equalTo(record::getBusinessType)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(manufacturerId).equalTo(record::getManufacturerId)
                .set(supplierId).equalTo(record::getSupplierId)
                .set(usedYears).equalTo(record::getUsedYears)
                .set(usedKm).equalTo(record::getUsedKm)
                .set(expectedReturnDate).equalTo(record::getExpectedReturnDate)
                .set(expectedDeliveryDate).equalTo(record::getExpectedDeliveryDate)
                .set(isQuotaOccupied).equalTo(record::getIsQuotaOccupied)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(quotaAssetOwnership).equalTo(record::getQuotaAssetOwnership)
                .set(preOccupied).equalTo(record::getPreOccupied)
                .set(returnOnInvestment).equalTo(record::getReturnOnInvestment)
                .set(monthlyBicycleRevenue).equalTo(record::getMonthlyBicycleRevenue)
                .set(vehicleResidualValueRate).equalTo(record::getVehicleResidualValueRate)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehiclePurchaseApplyDetails record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
                .set(applyDetailsNo).equalToWhenPresent(record::getApplyDetailsNo)
                .set(budgetStatus).equalToWhenPresent(record::getBudgetStatus)
                .set(purchaseType).equalToWhenPresent(record::getPurchaseType)
                .set(purchaseModelId).equalToWhenPresent(record::getPurchaseModelId)
                .set(unitPrice).equalToWhenPresent(record::getUnitPrice)
                .set(quantity).equalToWhenPresent(record::getQuantity)
                .set(receivedQuantity).equalToWhenPresent(record::getReceivedQuantity)
                .set(otherCosts).equalToWhenPresent(record::getOtherCosts)
                .set(totalPrice).equalToWhenPresent(record::getTotalPrice)
                .set(businessType).equalToWhenPresent(record::getBusinessType)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(manufacturerId).equalToWhenPresent(record::getManufacturerId)
                .set(supplierId).equalToWhenPresent(record::getSupplierId)
                .set(usedYears).equalToWhenPresent(record::getUsedYears)
                .set(usedKm).equalToWhenPresent(record::getUsedKm)
                .set(expectedReturnDate).equalToWhenPresent(record::getExpectedReturnDate)
                .set(expectedDeliveryDate).equalToWhenPresent(record::getExpectedDeliveryDate)
                .set(isQuotaOccupied).equalToWhenPresent(record::getIsQuotaOccupied)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(quotaAssetOwnership).equalToWhenPresent(record::getQuotaAssetOwnership)
                .set(preOccupied).equalToWhenPresent(record::getPreOccupied)
                .set(returnOnInvestment).equalToWhenPresent(record::getReturnOnInvestment)
                .set(monthlyBicycleRevenue).equalToWhenPresent(record::getMonthlyBicycleRevenue)
                .set(vehicleResidualValueRate).equalToWhenPresent(record::getVehicleResidualValueRate)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default int updateByPrimaryKey(VehiclePurchaseApplyDetails record) {
        return update(c ->
            c.set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
            .set(applyDetailsNo).equalTo(record::getApplyDetailsNo)
            .set(budgetStatus).equalTo(record::getBudgetStatus)
            .set(purchaseType).equalTo(record::getPurchaseType)
            .set(purchaseModelId).equalTo(record::getPurchaseModelId)
            .set(unitPrice).equalTo(record::getUnitPrice)
            .set(quantity).equalTo(record::getQuantity)
            .set(receivedQuantity).equalTo(record::getReceivedQuantity)
            .set(otherCosts).equalTo(record::getOtherCosts)
            .set(totalPrice).equalTo(record::getTotalPrice)
            .set(businessType).equalTo(record::getBusinessType)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(manufacturerId).equalTo(record::getManufacturerId)
            .set(supplierId).equalTo(record::getSupplierId)
            .set(usedYears).equalTo(record::getUsedYears)
            .set(usedKm).equalTo(record::getUsedKm)
            .set(expectedReturnDate).equalTo(record::getExpectedReturnDate)
            .set(expectedDeliveryDate).equalTo(record::getExpectedDeliveryDate)
            .set(isQuotaOccupied).equalTo(record::getIsQuotaOccupied)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(quotaAssetOwnership).equalTo(record::getQuotaAssetOwnership)
            .set(preOccupied).equalTo(record::getPreOccupied)
            .set(returnOnInvestment).equalTo(record::getReturnOnInvestment)
            .set(monthlyBicycleRevenue).equalTo(record::getMonthlyBicycleRevenue)
            .set(vehicleResidualValueRate).equalTo(record::getVehicleResidualValueRate)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    default int updateByPrimaryKeySelective(VehiclePurchaseApplyDetails record) {
        return update(c ->
            c.set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
            .set(applyDetailsNo).equalToWhenPresent(record::getApplyDetailsNo)
            .set(budgetStatus).equalToWhenPresent(record::getBudgetStatus)
            .set(purchaseType).equalToWhenPresent(record::getPurchaseType)
            .set(purchaseModelId).equalToWhenPresent(record::getPurchaseModelId)
            .set(unitPrice).equalToWhenPresent(record::getUnitPrice)
            .set(quantity).equalToWhenPresent(record::getQuantity)
            .set(receivedQuantity).equalToWhenPresent(record::getReceivedQuantity)
            .set(otherCosts).equalToWhenPresent(record::getOtherCosts)
            .set(totalPrice).equalToWhenPresent(record::getTotalPrice)
            .set(businessType).equalToWhenPresent(record::getBusinessType)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(manufacturerId).equalToWhenPresent(record::getManufacturerId)
            .set(supplierId).equalToWhenPresent(record::getSupplierId)
            .set(usedYears).equalToWhenPresent(record::getUsedYears)
            .set(usedKm).equalToWhenPresent(record::getUsedKm)
            .set(expectedReturnDate).equalToWhenPresent(record::getExpectedReturnDate)
            .set(expectedDeliveryDate).equalToWhenPresent(record::getExpectedDeliveryDate)
            .set(isQuotaOccupied).equalToWhenPresent(record::getIsQuotaOccupied)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(quotaAssetOwnership).equalToWhenPresent(record::getQuotaAssetOwnership)
            .set(preOccupied).equalToWhenPresent(record::getPreOccupied)
            .set(returnOnInvestment).equalToWhenPresent(record::getReturnOnInvestment)
            .set(monthlyBicycleRevenue).equalToWhenPresent(record::getMonthlyBicycleRevenue)
            .set(vehicleResidualValueRate).equalToWhenPresent(record::getVehicleResidualValueRate)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}