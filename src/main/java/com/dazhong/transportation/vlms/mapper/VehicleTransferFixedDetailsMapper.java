package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleTransferFixedDetailsDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.model.VehicleTransferFixedDetails;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleTransferFixedDetailsMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    BasicColumn[] selectList = BasicColumn.columnList(id, transferFixedApplyId, vin, productLine, businessLine, approvalStatus, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleTransferFixedDetails> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleTransferFixedDetailsResult")
    Optional<VehicleTransferFixedDetails> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleTransferFixedDetailsResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="transfer_fixed__apply_id", property="transferFixedApplyId", jdbcType=JdbcType.BIGINT),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
        @Result(column="approval_status", property="approvalStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleTransferFixedDetails> selectMany(SelectStatementProvider selectStatement);

    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="QueryTransferFixedDetailsByVinResult", value = {
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_no", property="applyNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="apply_status", property="applyStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
    })
    List<VehicleTransferFixedResponse> queryTransferFixedDetailsByVin(SelectStatementProvider selectStatement);


    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleTransferFixedDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleTransferFixedDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default int insert(VehicleTransferFixedDetails record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleTransferFixedDetails, c ->
            c.map(transferFixedApplyId).toProperty("transferFixedApplyId")
            .map(vin).toProperty("vin")
            .map(productLine).toProperty("productLine")
            .map(businessLine).toProperty("businessLine")
            .map(approvalStatus).toProperty("approvalStatus")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default int insertSelective(VehicleTransferFixedDetails record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleTransferFixedDetails, c ->
            c.map(transferFixedApplyId).toPropertyWhenPresent("transferFixedApplyId", record::getTransferFixedApplyId)
            .map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(productLine).toPropertyWhenPresent("productLine", record::getProductLine)
            .map(businessLine).toPropertyWhenPresent("businessLine", record::getBusinessLine)
            .map(approvalStatus).toPropertyWhenPresent("approvalStatus", record::getApprovalStatus)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default Optional<VehicleTransferFixedDetails> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleTransferFixedDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default List<VehicleTransferFixedDetails> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleTransferFixedDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default List<VehicleTransferFixedDetails> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleTransferFixedDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default Optional<VehicleTransferFixedDetails> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleTransferFixedDetails, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleTransferFixedDetails record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(transferFixedApplyId).equalTo(record::getTransferFixedApplyId)
                .set(vin).equalTo(record::getVin)
                .set(productLine).equalTo(record::getProductLine)
                .set(businessLine).equalTo(record::getBusinessLine)
                .set(approvalStatus).equalTo(record::getApprovalStatus)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleTransferFixedDetails record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(transferFixedApplyId).equalToWhenPresent(record::getTransferFixedApplyId)
                .set(vin).equalToWhenPresent(record::getVin)
                .set(productLine).equalToWhenPresent(record::getProductLine)
                .set(businessLine).equalToWhenPresent(record::getBusinessLine)
                .set(approvalStatus).equalToWhenPresent(record::getApprovalStatus)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default int updateByPrimaryKey(VehicleTransferFixedDetails record) {
        return update(c ->
            c.set(transferFixedApplyId).equalTo(record::getTransferFixedApplyId)
            .set(vin).equalTo(record::getVin)
            .set(productLine).equalTo(record::getProductLine)
            .set(businessLine).equalTo(record::getBusinessLine)
            .set(approvalStatus).equalTo(record::getApprovalStatus)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    default int updateByPrimaryKeySelective(VehicleTransferFixedDetails record) {
        return update(c ->
            c.set(transferFixedApplyId).equalToWhenPresent(record::getTransferFixedApplyId)
            .set(vin).equalToWhenPresent(record::getVin)
            .set(productLine).equalToWhenPresent(record::getProductLine)
            .set(businessLine).equalToWhenPresent(record::getBusinessLine)
            .set(approvalStatus).equalToWhenPresent(record::getApprovalStatus)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}