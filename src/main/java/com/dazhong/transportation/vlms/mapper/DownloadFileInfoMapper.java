package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.DownloadFileInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.DownloadFileInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DownloadFileInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, fileSource, modelSource, fileName, filePath, fileStatus, expireTime, remark, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<DownloadFileInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DownloadFileInfoResult")
    Optional<DownloadFileInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DownloadFileInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="file_source", property="fileSource", jdbcType=JdbcType.INTEGER),
        @Result(column="model_source", property="modelSource", jdbcType=JdbcType.INTEGER),
        @Result(column="file_name", property="fileName", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_path", property="filePath", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_status", property="fileStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="expire_time", property="expireTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<DownloadFileInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, downloadFileInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, downloadFileInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default int insert(DownloadFileInfo record) {
        return MyBatis3Utils.insert(this::insert, record, downloadFileInfo, c ->
            c.map(fileSource).toProperty("fileSource")
            .map(modelSource).toProperty("modelSource")
            .map(fileName).toProperty("fileName")
            .map(filePath).toProperty("filePath")
            .map(fileStatus).toProperty("fileStatus")
            .map(expireTime).toProperty("expireTime")
            .map(remark).toProperty("remark")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default int insertSelective(DownloadFileInfo record) {
        return MyBatis3Utils.insert(this::insert, record, downloadFileInfo, c ->
            c.map(fileSource).toPropertyWhenPresent("fileSource", record::getFileSource)
            .map(modelSource).toPropertyWhenPresent("modelSource", record::getModelSource)
            .map(fileName).toPropertyWhenPresent("fileName", record::getFileName)
            .map(filePath).toPropertyWhenPresent("filePath", record::getFilePath)
            .map(fileStatus).toPropertyWhenPresent("fileStatus", record::getFileStatus)
            .map(expireTime).toPropertyWhenPresent("expireTime", record::getExpireTime)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default Optional<DownloadFileInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, downloadFileInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default List<DownloadFileInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, downloadFileInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default List<DownloadFileInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, downloadFileInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default Optional<DownloadFileInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, downloadFileInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    static UpdateDSL<UpdateModel> updateAllColumns(DownloadFileInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(fileSource).equalTo(record::getFileSource)
                .set(modelSource).equalTo(record::getModelSource)
                .set(fileName).equalTo(record::getFileName)
                .set(filePath).equalTo(record::getFilePath)
                .set(fileStatus).equalTo(record::getFileStatus)
                .set(expireTime).equalTo(record::getExpireTime)
                .set(remark).equalTo(record::getRemark)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DownloadFileInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(fileSource).equalToWhenPresent(record::getFileSource)
                .set(modelSource).equalToWhenPresent(record::getModelSource)
                .set(fileName).equalToWhenPresent(record::getFileName)
                .set(filePath).equalToWhenPresent(record::getFilePath)
                .set(fileStatus).equalToWhenPresent(record::getFileStatus)
                .set(expireTime).equalToWhenPresent(record::getExpireTime)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default int updateByPrimaryKey(DownloadFileInfo record) {
        return update(c ->
            c.set(fileSource).equalTo(record::getFileSource)
            .set(modelSource).equalTo(record::getModelSource)
            .set(fileName).equalTo(record::getFileName)
            .set(filePath).equalTo(record::getFilePath)
            .set(fileStatus).equalTo(record::getFileStatus)
            .set(expireTime).equalTo(record::getExpireTime)
            .set(remark).equalTo(record::getRemark)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    default int updateByPrimaryKeySelective(DownloadFileInfo record) {
        return update(c ->
            c.set(fileSource).equalToWhenPresent(record::getFileSource)
            .set(modelSource).equalToWhenPresent(record::getModelSource)
            .set(fileName).equalToWhenPresent(record::getFileName)
            .set(filePath).equalToWhenPresent(record::getFilePath)
            .set(fileStatus).equalToWhenPresent(record::getFileStatus)
            .set(expireTime).equalToWhenPresent(record::getExpireTime)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}