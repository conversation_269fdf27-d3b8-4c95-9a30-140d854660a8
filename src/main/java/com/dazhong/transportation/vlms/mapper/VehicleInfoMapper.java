package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, vehicleAssetId, relateAssetId, engineNo, engineModel, vehicleModelId, licensePlate, useQuotaType, quotaType, vehicleColorId, interiorColor, supplierId, isRepurchase, repurchaseDate, repurchaseRequirements, usageAgeLimit, depreciationAgeLimit, realRetirementDate, purchasePrice, purchaseTax, licensePlatePrice, licensePlateOtherPrice, upholsterPrice, totalPrice, remainPrice, secondHandPrice, propertyStatus, productLine, businessLine, operatingStatus, subscriptionCompanyCode, subscriptionCompanyName, quotaAssetCompanyId, assetCompanyId, ownOrganizationId, usageOrganizationId, belongingTeam, obtainWayId, areaId, depreciationDataId, latestPosition, latestTotalMileage, usageIdRegistrationCard, vehicleTypeRegistrationCard, registrationDateRegistrationCard, issuanceDateRegistrationCard, retirementDateRegistrationCard, annualInspectionDueDateRegistrationCard, fileNumber, productDate, issuanceDate, certificateNumber, vehicleLicenseUrl, certificateOwnershipUrl, certificateConformityUrl, vehicleInvoiceUrl, purchaseTaxUrl, operatingPermitUrl, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleInfoResult")
    Optional<VehicleInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_asset_id", property="vehicleAssetId", jdbcType=JdbcType.VARCHAR),
        @Result(column="relate_asset_id", property="relateAssetId", jdbcType=JdbcType.BIGINT),
        @Result(column="engine_no", property="engineNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="engine_model", property="engineModel", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="use_quota_type", property="useQuotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_color_id", property="vehicleColorId", jdbcType=JdbcType.INTEGER),
        @Result(column="interior_color", property="interiorColor", jdbcType=JdbcType.VARCHAR),
        @Result(column="supplier_id", property="supplierId", jdbcType=JdbcType.INTEGER),
        @Result(column="is_repurchase", property="isRepurchase", jdbcType=JdbcType.INTEGER),
        @Result(column="repurchase_date", property="repurchaseDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="repurchase_requirements", property="repurchaseRequirements", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_age_limit", property="usageAgeLimit", jdbcType=JdbcType.INTEGER),
        @Result(column="depreciation_age_limit", property="depreciationAgeLimit", jdbcType=JdbcType.INTEGER),
        @Result(column="real_retirement_date", property="realRetirementDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="purchase_price", property="purchasePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="purchase_tax", property="purchaseTax", jdbcType=JdbcType.DECIMAL),
        @Result(column="license_plate_price", property="licensePlatePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="license_plate_other_price", property="licensePlateOtherPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="upholster_price", property="upholsterPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="total_price", property="totalPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="remain_price", property="remainPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="second_hand_price", property="secondHandPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="business_line", property="businessLine", jdbcType=JdbcType.INTEGER),
        @Result(column="operating_status", property="operatingStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="subscription_company_code", property="subscriptionCompanyCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="subscription_company_name", property="subscriptionCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_asset_company_id", property="quotaAssetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="belonging_team", property="belongingTeam", jdbcType=JdbcType.VARCHAR),
        @Result(column="obtain_way_id", property="obtainWayId", jdbcType=JdbcType.INTEGER),
        @Result(column="area_id", property="areaId", jdbcType=JdbcType.INTEGER),
        @Result(column="depreciation_data_id", property="depreciationDataId", jdbcType=JdbcType.BIGINT),
        @Result(column="latest_position", property="latestPosition", jdbcType=JdbcType.VARCHAR),
        @Result(column="latest_total_mileage", property="latestTotalMileage", jdbcType=JdbcType.VARCHAR),
        @Result(column="usage_id_registration_card", property="usageIdRegistrationCard", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_type_registration_card", property="vehicleTypeRegistrationCard", jdbcType=JdbcType.INTEGER),
        @Result(column="registration_date_registration_card", property="registrationDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="issuance_date_registration_card", property="issuanceDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="retirement_date_registration_card", property="retirementDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="annual_inspection_due_date_registration_card", property="annualInspectionDueDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="file_number", property="fileNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="product_date", property="productDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="issuance_date", property="issuanceDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="certificate_number", property="certificateNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_license_url", property="vehicleLicenseUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="certificate_ownership_url", property="certificateOwnershipUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="certificate_conformity_url", property="certificateConformityUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_invoice_url", property="vehicleInvoiceUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="purchase_tax_url", property="purchaseTaxUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="operating_permit_url", property="operatingPermitUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default int insert(VehicleInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleInfo, c ->
            c.map(vin).toProperty("vin")
            .map(vehicleAssetId).toProperty("vehicleAssetId")
            .map(relateAssetId).toProperty("relateAssetId")
            .map(engineNo).toProperty("engineNo")
            .map(engineModel).toProperty("engineModel")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(licensePlate).toProperty("licensePlate")
            .map(useQuotaType).toProperty("useQuotaType")
            .map(quotaType).toProperty("quotaType")
            .map(vehicleColorId).toProperty("vehicleColorId")
            .map(interiorColor).toProperty("interiorColor")
            .map(supplierId).toProperty("supplierId")
            .map(isRepurchase).toProperty("isRepurchase")
            .map(repurchaseDate).toProperty("repurchaseDate")
            .map(repurchaseRequirements).toProperty("repurchaseRequirements")
            .map(usageAgeLimit).toProperty("usageAgeLimit")
            .map(depreciationAgeLimit).toProperty("depreciationAgeLimit")
            .map(realRetirementDate).toProperty("realRetirementDate")
            .map(purchasePrice).toProperty("purchasePrice")
            .map(purchaseTax).toProperty("purchaseTax")
            .map(licensePlatePrice).toProperty("licensePlatePrice")
            .map(licensePlateOtherPrice).toProperty("licensePlateOtherPrice")
            .map(upholsterPrice).toProperty("upholsterPrice")
            .map(totalPrice).toProperty("totalPrice")
            .map(remainPrice).toProperty("remainPrice")
            .map(secondHandPrice).toProperty("secondHandPrice")
            .map(propertyStatus).toProperty("propertyStatus")
            .map(productLine).toProperty("productLine")
            .map(businessLine).toProperty("businessLine")
            .map(operatingStatus).toProperty("operatingStatus")
            .map(subscriptionCompanyCode).toProperty("subscriptionCompanyCode")
            .map(subscriptionCompanyName).toProperty("subscriptionCompanyName")
            .map(quotaAssetCompanyId).toProperty("quotaAssetCompanyId")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(ownOrganizationId).toProperty("ownOrganizationId")
            .map(usageOrganizationId).toProperty("usageOrganizationId")
            .map(belongingTeam).toProperty("belongingTeam")
            .map(obtainWayId).toProperty("obtainWayId")
            .map(areaId).toProperty("areaId")
            .map(depreciationDataId).toProperty("depreciationDataId")
            .map(latestPosition).toProperty("latestPosition")
            .map(latestTotalMileage).toProperty("latestTotalMileage")
            .map(usageIdRegistrationCard).toProperty("usageIdRegistrationCard")
            .map(vehicleTypeRegistrationCard).toProperty("vehicleTypeRegistrationCard")
            .map(registrationDateRegistrationCard).toProperty("registrationDateRegistrationCard")
            .map(issuanceDateRegistrationCard).toProperty("issuanceDateRegistrationCard")
            .map(retirementDateRegistrationCard).toProperty("retirementDateRegistrationCard")
            .map(annualInspectionDueDateRegistrationCard).toProperty("annualInspectionDueDateRegistrationCard")
            .map(fileNumber).toProperty("fileNumber")
            .map(productDate).toProperty("productDate")
            .map(issuanceDate).toProperty("issuanceDate")
            .map(certificateNumber).toProperty("certificateNumber")
            .map(vehicleLicenseUrl).toProperty("vehicleLicenseUrl")
            .map(certificateOwnershipUrl).toProperty("certificateOwnershipUrl")
            .map(certificateConformityUrl).toProperty("certificateConformityUrl")
            .map(vehicleInvoiceUrl).toProperty("vehicleInvoiceUrl")
            .map(purchaseTaxUrl).toProperty("purchaseTaxUrl")
            .map(operatingPermitUrl).toProperty("operatingPermitUrl")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default int insertSelective(VehicleInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleInfo, c ->
            c.map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(vehicleAssetId).toPropertyWhenPresent("vehicleAssetId", record::getVehicleAssetId)
            .map(relateAssetId).toPropertyWhenPresent("relateAssetId", record::getRelateAssetId)
            .map(engineNo).toPropertyWhenPresent("engineNo", record::getEngineNo)
            .map(engineModel).toPropertyWhenPresent("engineModel", record::getEngineModel)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(useQuotaType).toPropertyWhenPresent("useQuotaType", record::getUseQuotaType)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(vehicleColorId).toPropertyWhenPresent("vehicleColorId", record::getVehicleColorId)
            .map(interiorColor).toPropertyWhenPresent("interiorColor", record::getInteriorColor)
            .map(supplierId).toPropertyWhenPresent("supplierId", record::getSupplierId)
            .map(isRepurchase).toPropertyWhenPresent("isRepurchase", record::getIsRepurchase)
            .map(repurchaseDate).toPropertyWhenPresent("repurchaseDate", record::getRepurchaseDate)
            .map(repurchaseRequirements).toPropertyWhenPresent("repurchaseRequirements", record::getRepurchaseRequirements)
            .map(usageAgeLimit).toPropertyWhenPresent("usageAgeLimit", record::getUsageAgeLimit)
            .map(depreciationAgeLimit).toPropertyWhenPresent("depreciationAgeLimit", record::getDepreciationAgeLimit)
            .map(realRetirementDate).toPropertyWhenPresent("realRetirementDate", record::getRealRetirementDate)
            .map(purchasePrice).toPropertyWhenPresent("purchasePrice", record::getPurchasePrice)
            .map(purchaseTax).toPropertyWhenPresent("purchaseTax", record::getPurchaseTax)
            .map(licensePlatePrice).toPropertyWhenPresent("licensePlatePrice", record::getLicensePlatePrice)
            .map(licensePlateOtherPrice).toPropertyWhenPresent("licensePlateOtherPrice", record::getLicensePlateOtherPrice)
            .map(upholsterPrice).toPropertyWhenPresent("upholsterPrice", record::getUpholsterPrice)
            .map(totalPrice).toPropertyWhenPresent("totalPrice", record::getTotalPrice)
            .map(remainPrice).toPropertyWhenPresent("remainPrice", record::getRemainPrice)
            .map(secondHandPrice).toPropertyWhenPresent("secondHandPrice", record::getSecondHandPrice)
            .map(propertyStatus).toPropertyWhenPresent("propertyStatus", record::getPropertyStatus)
            .map(productLine).toPropertyWhenPresent("productLine", record::getProductLine)
            .map(businessLine).toPropertyWhenPresent("businessLine", record::getBusinessLine)
            .map(operatingStatus).toPropertyWhenPresent("operatingStatus", record::getOperatingStatus)
            .map(subscriptionCompanyCode).toPropertyWhenPresent("subscriptionCompanyCode", record::getSubscriptionCompanyCode)
            .map(subscriptionCompanyName).toPropertyWhenPresent("subscriptionCompanyName", record::getSubscriptionCompanyName)
            .map(quotaAssetCompanyId).toPropertyWhenPresent("quotaAssetCompanyId", record::getQuotaAssetCompanyId)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(ownOrganizationId).toPropertyWhenPresent("ownOrganizationId", record::getOwnOrganizationId)
            .map(usageOrganizationId).toPropertyWhenPresent("usageOrganizationId", record::getUsageOrganizationId)
            .map(belongingTeam).toPropertyWhenPresent("belongingTeam", record::getBelongingTeam)
            .map(obtainWayId).toPropertyWhenPresent("obtainWayId", record::getObtainWayId)
            .map(areaId).toPropertyWhenPresent("areaId", record::getAreaId)
            .map(depreciationDataId).toPropertyWhenPresent("depreciationDataId", record::getDepreciationDataId)
            .map(latestPosition).toPropertyWhenPresent("latestPosition", record::getLatestPosition)
            .map(latestTotalMileage).toPropertyWhenPresent("latestTotalMileage", record::getLatestTotalMileage)
            .map(usageIdRegistrationCard).toPropertyWhenPresent("usageIdRegistrationCard", record::getUsageIdRegistrationCard)
            .map(vehicleTypeRegistrationCard).toPropertyWhenPresent("vehicleTypeRegistrationCard", record::getVehicleTypeRegistrationCard)
            .map(registrationDateRegistrationCard).toPropertyWhenPresent("registrationDateRegistrationCard", record::getRegistrationDateRegistrationCard)
            .map(issuanceDateRegistrationCard).toPropertyWhenPresent("issuanceDateRegistrationCard", record::getIssuanceDateRegistrationCard)
            .map(retirementDateRegistrationCard).toPropertyWhenPresent("retirementDateRegistrationCard", record::getRetirementDateRegistrationCard)
            .map(annualInspectionDueDateRegistrationCard).toPropertyWhenPresent("annualInspectionDueDateRegistrationCard", record::getAnnualInspectionDueDateRegistrationCard)
            .map(fileNumber).toPropertyWhenPresent("fileNumber", record::getFileNumber)
            .map(productDate).toPropertyWhenPresent("productDate", record::getProductDate)
            .map(issuanceDate).toPropertyWhenPresent("issuanceDate", record::getIssuanceDate)
            .map(certificateNumber).toPropertyWhenPresent("certificateNumber", record::getCertificateNumber)
            .map(vehicleLicenseUrl).toPropertyWhenPresent("vehicleLicenseUrl", record::getVehicleLicenseUrl)
            .map(certificateOwnershipUrl).toPropertyWhenPresent("certificateOwnershipUrl", record::getCertificateOwnershipUrl)
            .map(certificateConformityUrl).toPropertyWhenPresent("certificateConformityUrl", record::getCertificateConformityUrl)
            .map(vehicleInvoiceUrl).toPropertyWhenPresent("vehicleInvoiceUrl", record::getVehicleInvoiceUrl)
            .map(purchaseTaxUrl).toPropertyWhenPresent("purchaseTaxUrl", record::getPurchaseTaxUrl)
            .map(operatingPermitUrl).toPropertyWhenPresent("operatingPermitUrl", record::getOperatingPermitUrl)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default Optional<VehicleInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default List<VehicleInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default List<VehicleInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default Optional<VehicleInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(record::getVin)
                .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
                .set(relateAssetId).equalTo(record::getRelateAssetId)
                .set(engineNo).equalTo(record::getEngineNo)
                .set(engineModel).equalTo(record::getEngineModel)
                .set(vehicleModelId).equalTo(record::getVehicleModelId)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(useQuotaType).equalTo(record::getUseQuotaType)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(vehicleColorId).equalTo(record::getVehicleColorId)
                .set(interiorColor).equalTo(record::getInteriorColor)
                .set(supplierId).equalTo(record::getSupplierId)
                .set(isRepurchase).equalTo(record::getIsRepurchase)
                .set(repurchaseDate).equalTo(record::getRepurchaseDate)
                .set(repurchaseRequirements).equalTo(record::getRepurchaseRequirements)
                .set(usageAgeLimit).equalTo(record::getUsageAgeLimit)
                .set(depreciationAgeLimit).equalTo(record::getDepreciationAgeLimit)
                .set(realRetirementDate).equalTo(record::getRealRetirementDate)
                .set(purchasePrice).equalTo(record::getPurchasePrice)
                .set(purchaseTax).equalTo(record::getPurchaseTax)
                .set(licensePlatePrice).equalTo(record::getLicensePlatePrice)
                .set(licensePlateOtherPrice).equalTo(record::getLicensePlateOtherPrice)
                .set(upholsterPrice).equalTo(record::getUpholsterPrice)
                .set(totalPrice).equalTo(record::getTotalPrice)
                .set(remainPrice).equalTo(record::getRemainPrice)
                .set(secondHandPrice).equalTo(record::getSecondHandPrice)
                .set(propertyStatus).equalTo(record::getPropertyStatus)
                .set(productLine).equalTo(record::getProductLine)
                .set(businessLine).equalTo(record::getBusinessLine)
                .set(operatingStatus).equalTo(record::getOperatingStatus)
                .set(subscriptionCompanyCode).equalTo(record::getSubscriptionCompanyCode)
                .set(subscriptionCompanyName).equalTo(record::getSubscriptionCompanyName)
                .set(quotaAssetCompanyId).equalTo(record::getQuotaAssetCompanyId)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
                .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
                .set(belongingTeam).equalTo(record::getBelongingTeam)
                .set(obtainWayId).equalTo(record::getObtainWayId)
                .set(areaId).equalTo(record::getAreaId)
                .set(depreciationDataId).equalTo(record::getDepreciationDataId)
                .set(latestPosition).equalTo(record::getLatestPosition)
                .set(latestTotalMileage).equalTo(record::getLatestTotalMileage)
                .set(usageIdRegistrationCard).equalTo(record::getUsageIdRegistrationCard)
                .set(vehicleTypeRegistrationCard).equalTo(record::getVehicleTypeRegistrationCard)
                .set(registrationDateRegistrationCard).equalTo(record::getRegistrationDateRegistrationCard)
                .set(issuanceDateRegistrationCard).equalTo(record::getIssuanceDateRegistrationCard)
                .set(retirementDateRegistrationCard).equalTo(record::getRetirementDateRegistrationCard)
                .set(annualInspectionDueDateRegistrationCard).equalTo(record::getAnnualInspectionDueDateRegistrationCard)
                .set(fileNumber).equalTo(record::getFileNumber)
                .set(productDate).equalTo(record::getProductDate)
                .set(issuanceDate).equalTo(record::getIssuanceDate)
                .set(certificateNumber).equalTo(record::getCertificateNumber)
                .set(vehicleLicenseUrl).equalTo(record::getVehicleLicenseUrl)
                .set(certificateOwnershipUrl).equalTo(record::getCertificateOwnershipUrl)
                .set(certificateConformityUrl).equalTo(record::getCertificateConformityUrl)
                .set(vehicleInvoiceUrl).equalTo(record::getVehicleInvoiceUrl)
                .set(purchaseTaxUrl).equalTo(record::getPurchaseTaxUrl)
                .set(operatingPermitUrl).equalTo(record::getOperatingPermitUrl)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(record::getVin)
                .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
                .set(relateAssetId).equalToWhenPresent(record::getRelateAssetId)
                .set(engineNo).equalToWhenPresent(record::getEngineNo)
                .set(engineModel).equalToWhenPresent(record::getEngineModel)
                .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(useQuotaType).equalToWhenPresent(record::getUseQuotaType)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(vehicleColorId).equalToWhenPresent(record::getVehicleColorId)
                .set(interiorColor).equalToWhenPresent(record::getInteriorColor)
                .set(supplierId).equalToWhenPresent(record::getSupplierId)
                .set(isRepurchase).equalToWhenPresent(record::getIsRepurchase)
                .set(repurchaseDate).equalToWhenPresent(record::getRepurchaseDate)
                .set(repurchaseRequirements).equalToWhenPresent(record::getRepurchaseRequirements)
                .set(usageAgeLimit).equalToWhenPresent(record::getUsageAgeLimit)
                .set(depreciationAgeLimit).equalToWhenPresent(record::getDepreciationAgeLimit)
                .set(realRetirementDate).equalToWhenPresent(record::getRealRetirementDate)
                .set(purchasePrice).equalToWhenPresent(record::getPurchasePrice)
                .set(purchaseTax).equalToWhenPresent(record::getPurchaseTax)
                .set(licensePlatePrice).equalToWhenPresent(record::getLicensePlatePrice)
                .set(licensePlateOtherPrice).equalToWhenPresent(record::getLicensePlateOtherPrice)
                .set(upholsterPrice).equalToWhenPresent(record::getUpholsterPrice)
                .set(totalPrice).equalToWhenPresent(record::getTotalPrice)
                .set(remainPrice).equalToWhenPresent(record::getRemainPrice)
                .set(secondHandPrice).equalToWhenPresent(record::getSecondHandPrice)
                .set(propertyStatus).equalToWhenPresent(record::getPropertyStatus)
                .set(productLine).equalToWhenPresent(record::getProductLine)
                .set(businessLine).equalToWhenPresent(record::getBusinessLine)
                .set(operatingStatus).equalToWhenPresent(record::getOperatingStatus)
                .set(subscriptionCompanyCode).equalToWhenPresent(record::getSubscriptionCompanyCode)
                .set(subscriptionCompanyName).equalToWhenPresent(record::getSubscriptionCompanyName)
                .set(quotaAssetCompanyId).equalToWhenPresent(record::getQuotaAssetCompanyId)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
                .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
                .set(belongingTeam).equalToWhenPresent(record::getBelongingTeam)
                .set(obtainWayId).equalToWhenPresent(record::getObtainWayId)
                .set(areaId).equalToWhenPresent(record::getAreaId)
                .set(depreciationDataId).equalToWhenPresent(record::getDepreciationDataId)
                .set(latestPosition).equalToWhenPresent(record::getLatestPosition)
                .set(latestTotalMileage).equalToWhenPresent(record::getLatestTotalMileage)
                .set(usageIdRegistrationCard).equalToWhenPresent(record::getUsageIdRegistrationCard)
                .set(vehicleTypeRegistrationCard).equalToWhenPresent(record::getVehicleTypeRegistrationCard)
                .set(registrationDateRegistrationCard).equalToWhenPresent(record::getRegistrationDateRegistrationCard)
                .set(issuanceDateRegistrationCard).equalToWhenPresent(record::getIssuanceDateRegistrationCard)
                .set(retirementDateRegistrationCard).equalToWhenPresent(record::getRetirementDateRegistrationCard)
                .set(annualInspectionDueDateRegistrationCard).equalToWhenPresent(record::getAnnualInspectionDueDateRegistrationCard)
                .set(fileNumber).equalToWhenPresent(record::getFileNumber)
                .set(productDate).equalToWhenPresent(record::getProductDate)
                .set(issuanceDate).equalToWhenPresent(record::getIssuanceDate)
                .set(certificateNumber).equalToWhenPresent(record::getCertificateNumber)
                .set(vehicleLicenseUrl).equalToWhenPresent(record::getVehicleLicenseUrl)
                .set(certificateOwnershipUrl).equalToWhenPresent(record::getCertificateOwnershipUrl)
                .set(certificateConformityUrl).equalToWhenPresent(record::getCertificateConformityUrl)
                .set(vehicleInvoiceUrl).equalToWhenPresent(record::getVehicleInvoiceUrl)
                .set(purchaseTaxUrl).equalToWhenPresent(record::getPurchaseTaxUrl)
                .set(operatingPermitUrl).equalToWhenPresent(record::getOperatingPermitUrl)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default int updateByPrimaryKey(VehicleInfo record) {
        return update(c ->
            c.set(vin).equalTo(record::getVin)
            .set(vehicleAssetId).equalTo(record::getVehicleAssetId)
            .set(relateAssetId).equalTo(record::getRelateAssetId)
            .set(engineNo).equalTo(record::getEngineNo)
            .set(engineModel).equalTo(record::getEngineModel)
            .set(vehicleModelId).equalTo(record::getVehicleModelId)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(useQuotaType).equalTo(record::getUseQuotaType)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(vehicleColorId).equalTo(record::getVehicleColorId)
            .set(interiorColor).equalTo(record::getInteriorColor)
            .set(supplierId).equalTo(record::getSupplierId)
            .set(isRepurchase).equalTo(record::getIsRepurchase)
            .set(repurchaseDate).equalTo(record::getRepurchaseDate)
            .set(repurchaseRequirements).equalTo(record::getRepurchaseRequirements)
            .set(usageAgeLimit).equalTo(record::getUsageAgeLimit)
            .set(depreciationAgeLimit).equalTo(record::getDepreciationAgeLimit)
            .set(realRetirementDate).equalTo(record::getRealRetirementDate)
            .set(purchasePrice).equalTo(record::getPurchasePrice)
            .set(purchaseTax).equalTo(record::getPurchaseTax)
            .set(licensePlatePrice).equalTo(record::getLicensePlatePrice)
            .set(licensePlateOtherPrice).equalTo(record::getLicensePlateOtherPrice)
            .set(upholsterPrice).equalTo(record::getUpholsterPrice)
            .set(totalPrice).equalTo(record::getTotalPrice)
            .set(remainPrice).equalTo(record::getRemainPrice)
            .set(secondHandPrice).equalTo(record::getSecondHandPrice)
            .set(propertyStatus).equalTo(record::getPropertyStatus)
            .set(productLine).equalTo(record::getProductLine)
            .set(businessLine).equalTo(record::getBusinessLine)
            .set(operatingStatus).equalTo(record::getOperatingStatus)
            .set(subscriptionCompanyCode).equalTo(record::getSubscriptionCompanyCode)
            .set(subscriptionCompanyName).equalTo(record::getSubscriptionCompanyName)
            .set(quotaAssetCompanyId).equalTo(record::getQuotaAssetCompanyId)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
            .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
            .set(belongingTeam).equalTo(record::getBelongingTeam)
            .set(obtainWayId).equalTo(record::getObtainWayId)
            .set(areaId).equalTo(record::getAreaId)
            .set(depreciationDataId).equalTo(record::getDepreciationDataId)
            .set(latestPosition).equalTo(record::getLatestPosition)
            .set(latestTotalMileage).equalTo(record::getLatestTotalMileage)
            .set(usageIdRegistrationCard).equalTo(record::getUsageIdRegistrationCard)
            .set(vehicleTypeRegistrationCard).equalTo(record::getVehicleTypeRegistrationCard)
            .set(registrationDateRegistrationCard).equalTo(record::getRegistrationDateRegistrationCard)
            .set(issuanceDateRegistrationCard).equalTo(record::getIssuanceDateRegistrationCard)
            .set(retirementDateRegistrationCard).equalTo(record::getRetirementDateRegistrationCard)
            .set(annualInspectionDueDateRegistrationCard).equalTo(record::getAnnualInspectionDueDateRegistrationCard)
            .set(fileNumber).equalTo(record::getFileNumber)
            .set(productDate).equalTo(record::getProductDate)
            .set(issuanceDate).equalTo(record::getIssuanceDate)
            .set(certificateNumber).equalTo(record::getCertificateNumber)
            .set(vehicleLicenseUrl).equalTo(record::getVehicleLicenseUrl)
            .set(certificateOwnershipUrl).equalTo(record::getCertificateOwnershipUrl)
            .set(certificateConformityUrl).equalTo(record::getCertificateConformityUrl)
            .set(vehicleInvoiceUrl).equalTo(record::getVehicleInvoiceUrl)
            .set(purchaseTaxUrl).equalTo(record::getPurchaseTaxUrl)
            .set(operatingPermitUrl).equalTo(record::getOperatingPermitUrl)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    default int updateByPrimaryKeySelective(VehicleInfo record) {
        return update(c ->
            c.set(vin).equalToWhenPresent(record::getVin)
            .set(vehicleAssetId).equalToWhenPresent(record::getVehicleAssetId)
            .set(relateAssetId).equalToWhenPresent(record::getRelateAssetId)
            .set(engineNo).equalToWhenPresent(record::getEngineNo)
            .set(engineModel).equalToWhenPresent(record::getEngineModel)
            .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(useQuotaType).equalToWhenPresent(record::getUseQuotaType)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(vehicleColorId).equalToWhenPresent(record::getVehicleColorId)
            .set(interiorColor).equalToWhenPresent(record::getInteriorColor)
            .set(supplierId).equalToWhenPresent(record::getSupplierId)
            .set(isRepurchase).equalToWhenPresent(record::getIsRepurchase)
            .set(repurchaseDate).equalToWhenPresent(record::getRepurchaseDate)
            .set(repurchaseRequirements).equalToWhenPresent(record::getRepurchaseRequirements)
            .set(usageAgeLimit).equalToWhenPresent(record::getUsageAgeLimit)
            .set(depreciationAgeLimit).equalToWhenPresent(record::getDepreciationAgeLimit)
            .set(realRetirementDate).equalToWhenPresent(record::getRealRetirementDate)
            .set(purchasePrice).equalToWhenPresent(record::getPurchasePrice)
            .set(purchaseTax).equalToWhenPresent(record::getPurchaseTax)
            .set(licensePlatePrice).equalToWhenPresent(record::getLicensePlatePrice)
            .set(licensePlateOtherPrice).equalToWhenPresent(record::getLicensePlateOtherPrice)
            .set(upholsterPrice).equalToWhenPresent(record::getUpholsterPrice)
            .set(totalPrice).equalToWhenPresent(record::getTotalPrice)
            .set(remainPrice).equalToWhenPresent(record::getRemainPrice)
            .set(secondHandPrice).equalToWhenPresent(record::getSecondHandPrice)
            .set(propertyStatus).equalToWhenPresent(record::getPropertyStatus)
            .set(productLine).equalToWhenPresent(record::getProductLine)
            .set(businessLine).equalToWhenPresent(record::getBusinessLine)
            .set(operatingStatus).equalToWhenPresent(record::getOperatingStatus)
            .set(subscriptionCompanyCode).equalToWhenPresent(record::getSubscriptionCompanyCode)
            .set(subscriptionCompanyName).equalToWhenPresent(record::getSubscriptionCompanyName)
            .set(quotaAssetCompanyId).equalToWhenPresent(record::getQuotaAssetCompanyId)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
            .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
            .set(belongingTeam).equalToWhenPresent(record::getBelongingTeam)
            .set(obtainWayId).equalToWhenPresent(record::getObtainWayId)
            .set(areaId).equalToWhenPresent(record::getAreaId)
            .set(depreciationDataId).equalToWhenPresent(record::getDepreciationDataId)
            .set(latestPosition).equalToWhenPresent(record::getLatestPosition)
            .set(latestTotalMileage).equalToWhenPresent(record::getLatestTotalMileage)
            .set(usageIdRegistrationCard).equalToWhenPresent(record::getUsageIdRegistrationCard)
            .set(vehicleTypeRegistrationCard).equalToWhenPresent(record::getVehicleTypeRegistrationCard)
            .set(registrationDateRegistrationCard).equalToWhenPresent(record::getRegistrationDateRegistrationCard)
            .set(issuanceDateRegistrationCard).equalToWhenPresent(record::getIssuanceDateRegistrationCard)
            .set(retirementDateRegistrationCard).equalToWhenPresent(record::getRetirementDateRegistrationCard)
            .set(annualInspectionDueDateRegistrationCard).equalToWhenPresent(record::getAnnualInspectionDueDateRegistrationCard)
            .set(fileNumber).equalToWhenPresent(record::getFileNumber)
            .set(productDate).equalToWhenPresent(record::getProductDate)
            .set(issuanceDate).equalToWhenPresent(record::getIssuanceDate)
            .set(certificateNumber).equalToWhenPresent(record::getCertificateNumber)
            .set(vehicleLicenseUrl).equalToWhenPresent(record::getVehicleLicenseUrl)
            .set(certificateOwnershipUrl).equalToWhenPresent(record::getCertificateOwnershipUrl)
            .set(certificateConformityUrl).equalToWhenPresent(record::getCertificateConformityUrl)
            .set(vehicleInvoiceUrl).equalToWhenPresent(record::getVehicleInvoiceUrl)
            .set(purchaseTaxUrl).equalToWhenPresent(record::getPurchaseTaxUrl)
            .set(operatingPermitUrl).equalToWhenPresent(record::getOperatingPermitUrl)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}