package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehiclePurchaseIntentionDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    public static final VehiclePurchaseIntention vehiclePurchaseIntention = new VehiclePurchaseIntention();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.id")
    public static final SqlColumn<Long> id = vehiclePurchaseIntention.id;

    /**
     * Database Column Remarks:
     *   采购意向单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.intention_no")
    public static final SqlColumn<String> intentionNo = vehiclePurchaseIntention.intentionNo;

    /**
     * Database Column Remarks:
     *   合同号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_no")
    public static final SqlColumn<String> contractNo = vehiclePurchaseIntention.contractNo;

    /**
     * Database Column Remarks:
     *   合同开始日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_start_date")
    public static final SqlColumn<Date> contractStartDate = vehiclePurchaseIntention.contractStartDate;

    /**
     * Database Column Remarks:
     *   合同结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_end_date")
    public static final SqlColumn<Date> contractEndDate = vehiclePurchaseIntention.contractEndDate;

    /**
     * Database Column Remarks:
     *   客户信息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.customer_user")
    public static final SqlColumn<String> customerUser = vehiclePurchaseIntention.customerUser;

    /**
     * Database Column Remarks:
     *   客户信息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.sale_name")
    public static final SqlColumn<String> saleName = vehiclePurchaseIntention.saleName;

    /**
     * Database Column Remarks:
     *   车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_name")
    public static final SqlColumn<String> vehicleModelName = vehiclePurchaseIntention.vehicleModelName;

    /**
     * Database Column Remarks:
     *   车型id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = vehiclePurchaseIntention.vehicleModelId;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.quantity")
    public static final SqlColumn<Integer> quantity = vehiclePurchaseIntention.quantity;

    /**
     * Database Column Remarks:
     *   指导价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.guide_price")
    public static final SqlColumn<BigDecimal> guidePrice = vehiclePurchaseIntention.guidePrice;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_body_color")
    public static final SqlColumn<String> vehicleBodyColor = vehiclePurchaseIntention.vehicleBodyColor;

    /**
     * Database Column Remarks:
     *   内饰颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_interior_color")
    public static final SqlColumn<String> vehicleInteriorColor = vehiclePurchaseIntention.vehicleInteriorColor;

    /**
     * Database Column Remarks:
     *   期望到车日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_date")
    public static final SqlColumn<Date> expectedArrivalDate = vehiclePurchaseIntention.expectedArrivalDate;

    /**
     * Database Column Remarks:
     *   期望到车最后日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_last_date")
    public static final SqlColumn<Date> expectedArrivalLastDate = vehiclePurchaseIntention.expectedArrivalLastDate;

    /**
     * Database Column Remarks:
     *   合同约定牌照属性
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_attribute")
    public static final SqlColumn<String> licensePlateAttribute = vehiclePurchaseIntention.licensePlateAttribute;

    /**
     * Database Column Remarks:
     *   合同约定牌照所属地
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_location")
    public static final SqlColumn<String> licensePlateLocation = vehiclePurchaseIntention.licensePlateLocation;

    /**
     * Database Column Remarks:
     *   装潢需求
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.decoration_demand")
    public static final SqlColumn<String> decorationDemand = vehiclePurchaseIntention.decorationDemand;

    /**
     * Database Column Remarks:
     *   数据同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.data_sync_time")
    public static final SqlColumn<Date> dataSyncTime = vehiclePurchaseIntention.dataSyncTime;

    /**
     * Database Column Remarks:
     *   是否有单号 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.apply_status")
    public static final SqlColumn<Integer> applyStatus = vehiclePurchaseIntention.applyStatus;

    /**
     * Database Column Remarks:
     *   关联采购申请id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_apply_id")
    public static final SqlColumn<Long> purchaseApplyId = vehiclePurchaseIntention.purchaseApplyId;

    /**
     * Database Column Remarks:
     *   所属公司code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_code")
    public static final SqlColumn<String> orgCode = vehiclePurchaseIntention.orgCode;

    /**
     * Database Column Remarks:
     *   所属公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_name")
    public static final SqlColumn<String> orgName = vehiclePurchaseIntention.orgName;

    /**
     * Database Column Remarks:
     *   所属公司ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_id")
    public static final SqlColumn<Long> orgId = vehiclePurchaseIntention.orgId;

    /**
     * Database Column Remarks:
     *   采购备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_remark")
    public static final SqlColumn<String> purchaseRemark = vehiclePurchaseIntention.purchaseRemark;

    /**
     * Database Column Remarks:
     *   钉钉号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.ding_talk_id")
    public static final SqlColumn<String> dingTalkId = vehiclePurchaseIntention.dingTalkId;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehiclePurchaseIntention.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_time")
    public static final SqlColumn<Date> createTime = vehiclePurchaseIntention.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehiclePurchaseIntention.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_name")
    public static final SqlColumn<String> createOperName = vehiclePurchaseIntention.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_time")
    public static final SqlColumn<Date> updateTime = vehiclePurchaseIntention.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehiclePurchaseIntention.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehiclePurchaseIntention.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    public static final class VehiclePurchaseIntention extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> intentionNo = column("intention_no", JDBCType.VARCHAR);

        public final SqlColumn<String> contractNo = column("contract_no", JDBCType.VARCHAR);

        public final SqlColumn<Date> contractStartDate = column("contract_start_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> contractEndDate = column("contract_end_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> customerUser = column("customer_user", JDBCType.VARCHAR);

        public final SqlColumn<String> saleName = column("sale_name", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleModelName = column("vehicle_model_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> quantity = column("quantity", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> guidePrice = column("guide_price", JDBCType.DECIMAL);

        public final SqlColumn<String> vehicleBodyColor = column("vehicle_body_color", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleInteriorColor = column("vehicle_interior_color", JDBCType.VARCHAR);

        public final SqlColumn<Date> expectedArrivalDate = column("expected_arrival_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> expectedArrivalLastDate = column("expected_arrival_last_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> licensePlateAttribute = column("license_plate_attribute", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlateLocation = column("license_plate_location", JDBCType.VARCHAR);

        public final SqlColumn<String> decorationDemand = column("decoration_demand", JDBCType.VARCHAR);

        public final SqlColumn<Date> dataSyncTime = column("data_sync_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> applyStatus = column("apply_status", JDBCType.INTEGER);

        public final SqlColumn<Long> purchaseApplyId = column("purchase_apply_id", JDBCType.BIGINT);

        public final SqlColumn<String> orgCode = column("org_code", JDBCType.VARCHAR);

        public final SqlColumn<String> orgName = column("org_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> orgId = column("org_id", JDBCType.BIGINT);

        public final SqlColumn<String> purchaseRemark = column("purchase_remark", JDBCType.VARCHAR);

        public final SqlColumn<String> dingTalkId = column("ding_talk_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehiclePurchaseIntention() {
            super("t_vehicle_purchase_intention");
        }
    }
}