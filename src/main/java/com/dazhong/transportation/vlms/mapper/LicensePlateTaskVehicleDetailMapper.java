package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.model.LicensePlateTaskVehicleDetail;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

import javax.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskVehicleDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface LicensePlateTaskVehicleDetailMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<LicensePlateTaskVehicleDetail> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNumber, licensePlate, vin, vehicleModelId, vehicleModelName, assetCompanyId, ownOrganizationId, usageOrganizationId, returnQuotaType, quotaType, quotaAssetCompanyId, quotaAssetCompanyName, quotaNumber, quotaPrintDate, vehicleTypeRegistrationCard, usageIdRegistrationCard, registrationDateRegistrationCard, issuanceDateRegistrationCard, fileNumber, retirementDateRegistrationCard, annualInspectionDueDateRegistrationCard, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<LicensePlateTaskVehicleDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LicensePlateTaskVehicleDetailResult")
    Optional<LicensePlateTaskVehicleDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LicensePlateTaskVehicleDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_number", property="taskNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="own_organization_id", property="ownOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="usage_organization_id", property="usageOrganizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="return_quota_type", property="returnQuotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_asset_company_id", property="quotaAssetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_asset_company_name", property="quotaAssetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_number", property="quotaNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_print_date", property="quotaPrintDate", jdbcType=JdbcType.DATE),
        @Result(column="vehicle_type_registration_card", property="vehicleTypeRegistrationCard", jdbcType=JdbcType.INTEGER),
        @Result(column="usage_id_registration_card", property="usageIdRegistrationCard", jdbcType=JdbcType.INTEGER),
        @Result(column="registration_date_registration_card", property="registrationDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="issuance_date_registration_card", property="issuanceDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="file_number", property="fileNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="retirement_date_registration_card", property="retirementDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="annual_inspection_due_date_registration_card", property="annualInspectionDueDateRegistrationCard", jdbcType=JdbcType.DATE),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<LicensePlateTaskVehicleDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, licensePlateTaskVehicleDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, licensePlateTaskVehicleDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default int insert(LicensePlateTaskVehicleDetail record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateTaskVehicleDetail, c ->
            c.map(taskNumber).toProperty("taskNumber")
            .map(licensePlate).toProperty("licensePlate")
            .map(vin).toProperty("vin")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(vehicleModelName).toProperty("vehicleModelName")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(ownOrganizationId).toProperty("ownOrganizationId")
            .map(usageOrganizationId).toProperty("usageOrganizationId")
            .map(returnQuotaType).toProperty("returnQuotaType")
            .map(quotaType).toProperty("quotaType")
            .map(quotaAssetCompanyId).toProperty("quotaAssetCompanyId")
            .map(quotaAssetCompanyName).toProperty("quotaAssetCompanyName")
            .map(quotaNumber).toProperty("quotaNumber")
            .map(quotaPrintDate).toProperty("quotaPrintDate")
            .map(vehicleTypeRegistrationCard).toProperty("vehicleTypeRegistrationCard")
            .map(usageIdRegistrationCard).toProperty("usageIdRegistrationCard")
            .map(registrationDateRegistrationCard).toProperty("registrationDateRegistrationCard")
            .map(issuanceDateRegistrationCard).toProperty("issuanceDateRegistrationCard")
            .map(fileNumber).toProperty("fileNumber")
            .map(retirementDateRegistrationCard).toProperty("retirementDateRegistrationCard")
            .map(annualInspectionDueDateRegistrationCard).toProperty("annualInspectionDueDateRegistrationCard")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default int insertSelective(LicensePlateTaskVehicleDetail record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateTaskVehicleDetail, c ->
            c.map(taskNumber).toPropertyWhenPresent("taskNumber", record::getTaskNumber)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
            .map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", record::getVehicleModelName)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(ownOrganizationId).toPropertyWhenPresent("ownOrganizationId", record::getOwnOrganizationId)
            .map(usageOrganizationId).toPropertyWhenPresent("usageOrganizationId", record::getUsageOrganizationId)
            .map(returnQuotaType).toPropertyWhenPresent("returnQuotaType", record::getReturnQuotaType)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(quotaAssetCompanyId).toPropertyWhenPresent("quotaAssetCompanyId", record::getQuotaAssetCompanyId)
            .map(quotaAssetCompanyName).toPropertyWhenPresent("quotaAssetCompanyName", record::getQuotaAssetCompanyName)
            .map(quotaNumber).toPropertyWhenPresent("quotaNumber", record::getQuotaNumber)
            .map(quotaPrintDate).toPropertyWhenPresent("quotaPrintDate", record::getQuotaPrintDate)
            .map(vehicleTypeRegistrationCard).toPropertyWhenPresent("vehicleTypeRegistrationCard", record::getVehicleTypeRegistrationCard)
            .map(usageIdRegistrationCard).toPropertyWhenPresent("usageIdRegistrationCard", record::getUsageIdRegistrationCard)
            .map(registrationDateRegistrationCard).toPropertyWhenPresent("registrationDateRegistrationCard", record::getRegistrationDateRegistrationCard)
            .map(issuanceDateRegistrationCard).toPropertyWhenPresent("issuanceDateRegistrationCard", record::getIssuanceDateRegistrationCard)
            .map(fileNumber).toPropertyWhenPresent("fileNumber", record::getFileNumber)
            .map(retirementDateRegistrationCard).toPropertyWhenPresent("retirementDateRegistrationCard", record::getRetirementDateRegistrationCard)
            .map(annualInspectionDueDateRegistrationCard).toPropertyWhenPresent("annualInspectionDueDateRegistrationCard", record::getAnnualInspectionDueDateRegistrationCard)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default Optional<LicensePlateTaskVehicleDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, licensePlateTaskVehicleDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default List<LicensePlateTaskVehicleDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, licensePlateTaskVehicleDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default List<LicensePlateTaskVehicleDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, licensePlateTaskVehicleDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default Optional<LicensePlateTaskVehicleDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, licensePlateTaskVehicleDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(LicensePlateTaskVehicleDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNumber).equalTo(record::getTaskNumber)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(vin).equalTo(record::getVin)
                .set(vehicleModelId).equalTo(record::getVehicleModelId)
                .set(vehicleModelName).equalTo(record::getVehicleModelName)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
                .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
                .set(returnQuotaType).equalTo(record::getReturnQuotaType)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(quotaAssetCompanyId).equalTo(record::getQuotaAssetCompanyId)
                .set(quotaAssetCompanyName).equalTo(record::getQuotaAssetCompanyName)
                .set(quotaNumber).equalTo(record::getQuotaNumber)
                .set(quotaPrintDate).equalTo(record::getQuotaPrintDate)
                .set(vehicleTypeRegistrationCard).equalTo(record::getVehicleTypeRegistrationCard)
                .set(usageIdRegistrationCard).equalTo(record::getUsageIdRegistrationCard)
                .set(registrationDateRegistrationCard).equalTo(record::getRegistrationDateRegistrationCard)
                .set(issuanceDateRegistrationCard).equalTo(record::getIssuanceDateRegistrationCard)
                .set(fileNumber).equalTo(record::getFileNumber)
                .set(retirementDateRegistrationCard).equalTo(record::getRetirementDateRegistrationCard)
                .set(annualInspectionDueDateRegistrationCard).equalTo(record::getAnnualInspectionDueDateRegistrationCard)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LicensePlateTaskVehicleDetail record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNumber).equalToWhenPresent(record::getTaskNumber)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(vin).equalToWhenPresent(record::getVin)
                .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
                .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
                .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
                .set(returnQuotaType).equalToWhenPresent(record::getReturnQuotaType)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(quotaAssetCompanyId).equalToWhenPresent(record::getQuotaAssetCompanyId)
                .set(quotaAssetCompanyName).equalToWhenPresent(record::getQuotaAssetCompanyName)
                .set(quotaNumber).equalToWhenPresent(record::getQuotaNumber)
                .set(quotaPrintDate).equalToWhenPresent(record::getQuotaPrintDate)
                .set(vehicleTypeRegistrationCard).equalToWhenPresent(record::getVehicleTypeRegistrationCard)
                .set(usageIdRegistrationCard).equalToWhenPresent(record::getUsageIdRegistrationCard)
                .set(registrationDateRegistrationCard).equalToWhenPresent(record::getRegistrationDateRegistrationCard)
                .set(issuanceDateRegistrationCard).equalToWhenPresent(record::getIssuanceDateRegistrationCard)
                .set(fileNumber).equalToWhenPresent(record::getFileNumber)
                .set(retirementDateRegistrationCard).equalToWhenPresent(record::getRetirementDateRegistrationCard)
                .set(annualInspectionDueDateRegistrationCard).equalToWhenPresent(record::getAnnualInspectionDueDateRegistrationCard)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default int updateByPrimaryKey(LicensePlateTaskVehicleDetail record) {
        return update(c ->
            c.set(taskNumber).equalTo(record::getTaskNumber)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(vin).equalTo(record::getVin)
            .set(vehicleModelId).equalTo(record::getVehicleModelId)
            .set(vehicleModelName).equalTo(record::getVehicleModelName)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(ownOrganizationId).equalTo(record::getOwnOrganizationId)
            .set(usageOrganizationId).equalTo(record::getUsageOrganizationId)
            .set(returnQuotaType).equalTo(record::getReturnQuotaType)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(quotaAssetCompanyId).equalTo(record::getQuotaAssetCompanyId)
            .set(quotaAssetCompanyName).equalTo(record::getQuotaAssetCompanyName)
            .set(quotaNumber).equalTo(record::getQuotaNumber)
            .set(quotaPrintDate).equalTo(record::getQuotaPrintDate)
            .set(vehicleTypeRegistrationCard).equalTo(record::getVehicleTypeRegistrationCard)
            .set(usageIdRegistrationCard).equalTo(record::getUsageIdRegistrationCard)
            .set(registrationDateRegistrationCard).equalTo(record::getRegistrationDateRegistrationCard)
            .set(issuanceDateRegistrationCard).equalTo(record::getIssuanceDateRegistrationCard)
            .set(fileNumber).equalTo(record::getFileNumber)
            .set(retirementDateRegistrationCard).equalTo(record::getRetirementDateRegistrationCard)
            .set(annualInspectionDueDateRegistrationCard).equalTo(record::getAnnualInspectionDueDateRegistrationCard)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    default int updateByPrimaryKeySelective(LicensePlateTaskVehicleDetail record) {
        return update(c ->
            c.set(taskNumber).equalToWhenPresent(record::getTaskNumber)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(vin).equalToWhenPresent(record::getVin)
            .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
            .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(ownOrganizationId).equalToWhenPresent(record::getOwnOrganizationId)
            .set(usageOrganizationId).equalToWhenPresent(record::getUsageOrganizationId)
            .set(returnQuotaType).equalToWhenPresent(record::getReturnQuotaType)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(quotaAssetCompanyId).equalToWhenPresent(record::getQuotaAssetCompanyId)
            .set(quotaAssetCompanyName).equalToWhenPresent(record::getQuotaAssetCompanyName)
            .set(quotaNumber).equalToWhenPresent(record::getQuotaNumber)
            .set(quotaPrintDate).equalToWhenPresent(record::getQuotaPrintDate)
            .set(vehicleTypeRegistrationCard).equalToWhenPresent(record::getVehicleTypeRegistrationCard)
            .set(usageIdRegistrationCard).equalToWhenPresent(record::getUsageIdRegistrationCard)
            .set(registrationDateRegistrationCard).equalToWhenPresent(record::getRegistrationDateRegistrationCard)
            .set(issuanceDateRegistrationCard).equalToWhenPresent(record::getIssuanceDateRegistrationCard)
            .set(fileNumber).equalToWhenPresent(record::getFileNumber)
            .set(retirementDateRegistrationCard).equalToWhenPresent(record::getRetirementDateRegistrationCard)
            .set(annualInspectionDueDateRegistrationCard).equalToWhenPresent(record::getAnnualInspectionDueDateRegistrationCard)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_license_plate_task_vehicle_detail")
    default int insertMultiple(Collection<LicensePlateTaskVehicleDetail> rows) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, rows, licensePlateTaskVehicleDetail, c -> c
                .map(taskNumber).toProperty("taskNumber")
                .map(licensePlate).toProperty("licensePlate")
                .map(vin).toProperty("vin")
                .map(vehicleModelId).toProperty("vehicleModelId")
                .map(vehicleModelName).toProperty("vehicleModelName")
                .map(assetCompanyId).toProperty("assetCompanyId")
                .map(ownOrganizationId).toProperty("ownOrganizationId")
                .map(usageOrganizationId).toProperty("usageOrganizationId")
                .map(returnQuotaType).toProperty("returnQuotaType")
                .map(quotaType).toProperty("quotaType")
                .map(quotaAssetCompanyId).toProperty("quotaAssetCompanyId")
                .map(quotaAssetCompanyName).toProperty("quotaAssetCompanyName")
                .map(quotaNumber).toProperty("quotaNumber")
                .map(quotaPrintDate).toProperty("quotaPrintDate")
                .map(vehicleTypeRegistrationCard).toProperty("vehicleTypeRegistrationCard")
                .map(usageIdRegistrationCard).toProperty("usageIdRegistrationCard")
                .map(registrationDateRegistrationCard).toProperty("registrationDateRegistrationCard")
                .map(issuanceDateRegistrationCard).toProperty("issuanceDateRegistrationCard")
                .map(fileNumber).toProperty("fileNumber")
                .map(retirementDateRegistrationCard).toProperty("retirementDateRegistrationCard")
                .map(annualInspectionDueDateRegistrationCard).toProperty("annualInspectionDueDateRegistrationCard")
                .map(createOperId).toProperty("createOperId")
                .map(createOperName).toProperty("createOperName")
        );
    }
}