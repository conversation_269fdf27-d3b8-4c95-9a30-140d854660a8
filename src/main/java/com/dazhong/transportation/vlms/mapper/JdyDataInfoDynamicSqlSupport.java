package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class JdyDataInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    public static final JdyDataInfo jdyDataInfo = new JdyDataInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.id")
    public static final SqlColumn<Long> id = jdyDataInfo.id;

    /**
     * Database Column Remarks:
     *   数据ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.data_id")
    public static final SqlColumn<String> dataId = jdyDataInfo.dataId;

    /**
     * Database Column Remarks:
     *   表单ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.entry_id")
    public static final SqlColumn<String> entryId = jdyDataInfo.entryId;

    /**
     * Database Column Remarks:
     *   发起月份
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602495")
    public static final SqlColumn<Date> widget1732281602495 = jdyDataInfo.widget1732281602495;

    /**
     * Database Column Remarks:
     *   发起日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235702")
    public static final SqlColumn<Date> widget1732271235702 = jdyDataInfo.widget1732271235702;

    /**
     * Database Column Remarks:
     *   条线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235713")
    public static final SqlColumn<String> widget1732271235713 = jdyDataInfo.widget1732271235713;

    /**
     * Database Column Remarks:
     *   出售车辆单位
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235722")
    public static final SqlColumn<String> widget1732271235722 = jdyDataInfo.widget1732271235722;

    /**
     * Database Column Remarks:
     *   公司区域
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051627")
    public static final SqlColumn<String> widget1736926051627 = jdyDataInfo.widget1736926051627;

    /**
     * Database Column Remarks:
     *   当地财务审批人员
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051635")
    public static final SqlColumn<String> widget1736926051635 = jdyDataInfo.widget1736926051635;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235709")
    public static final SqlColumn<String> widget1732271235709 = jdyDataInfo.widget1732271235709;

    /**
     * Database Column Remarks:
     *   所属分公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235742")
    public static final SqlColumn<String> widget1732271235742 = jdyDataInfo.widget1732271235742;

    /**
     * Database Column Remarks:
     *   所属车队
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235741")
    public static final SqlColumn<String> widget1732271235741 = jdyDataInfo.widget1732271235741;

    /**
     * Database Column Remarks:
     *   品牌车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235750")
    public static final SqlColumn<String> widget1732271235750 = jdyDataInfo.widget1732271235750;

    /**
     * Database Column Remarks:
     *   车型配置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743895605231")
    public static final SqlColumn<String> widget1743895605231 = jdyDataInfo.widget1743895605231;

    /**
     * Database Column Remarks:
     *   车型代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235734")
    public static final SqlColumn<String> widget1732271235734 = jdyDataInfo.widget1732271235734;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744687012604")
    public static final SqlColumn<String> widget1744687012604 = jdyDataInfo.widget1744687012604;

    /**
     * Database Column Remarks:
     *   颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544071")
    public static final SqlColumn<String> widget1737338544071 = jdyDataInfo.widget1737338544071;

    /**
     * Database Column Remarks:
     *   牌照性质
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602514")
    public static final SqlColumn<String> widget1732281602514 = jdyDataInfo.widget1732281602514;

    /**
     * Database Column Remarks:
     *   出厂日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544074")
    public static final SqlColumn<Date> widget1737338544074 = jdyDataInfo.widget1737338544074;

    /**
     * Database Column Remarks:
     *   上牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235755")
    public static final SqlColumn<Date> widget1732271235755 = jdyDataInfo.widget1732271235755;

    /**
     * Database Column Remarks:
     *   上牌年份
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1745227149952")
    public static final SqlColumn<String> widget1745227149952 = jdyDataInfo.widget1745227149952;

    /**
     * Database Column Remarks:
     *   里程数（万公里）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747774937598")
    public static final SqlColumn<BigDecimal> widget1747774937598 = jdyDataInfo.widget1747774937598;

    /**
     * Database Column Remarks:
     *   车况评级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743581186765")
    public static final SqlColumn<String> widget1743581186765 = jdyDataInfo.widget1743581186765;

    /**
     * Database Column Remarks:
     *   评估报告
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579518411")
    public static final SqlColumn<String> widget1743579518411 = jdyDataInfo.widget1743579518411;

    /**
     * Database Column Remarks:
     *   牌照性质（简易）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1750208093989")
    public static final SqlColumn<String> widget1750208093989 = jdyDataInfo.widget1750208093989;

    /**
     * Database Column Remarks:
     *   官方指导价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605137")
    public static final SqlColumn<BigDecimal> widget1747775605137 = jdyDataInfo.widget1747775605137;

    /**
     * Database Column Remarks:
     *   采购裸车价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760832")
    public static final SqlColumn<BigDecimal> widget1732583760832 = jdyDataInfo.widget1732583760832;

    /**
     * Database Column Remarks:
     *   购置费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760833")
    public static final SqlColumn<BigDecimal> widget1732583760833 = jdyDataInfo.widget1732583760833;

    /**
     * Database Column Remarks:
     *   上牌杂费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760834")
    public static final SqlColumn<BigDecimal> widget1732583760834 = jdyDataInfo.widget1732583760834;

    /**
     * Database Column Remarks:
     *   装潢费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760838")
    public static final SqlColumn<BigDecimal> widget1732583760838 = jdyDataInfo.widget1732583760838;

    /**
     * Database Column Remarks:
     *   购置总价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760842")
    public static final SqlColumn<BigDecimal> widget1732583760842 = jdyDataInfo.widget1732583760842;

    /**
     * Database Column Remarks:
     *   最低出售价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605138")
    public static final SqlColumn<BigDecimal> widget1747775605138 = jdyDataInfo.widget1747775605138;

    /**
     * Database Column Remarks:
     *   预估净售价1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732280180068")
    public static final SqlColumn<BigDecimal> widget1732280180068 = jdyDataInfo.widget1732280180068;

    /**
     * Database Column Remarks:
     *   预估残值率1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199512")
    public static final SqlColumn<BigDecimal> widget1732285199512 = jdyDataInfo.widget1732285199512;

    /**
     * Database Column Remarks:
     *   拍卖保留价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605139")
    public static final SqlColumn<BigDecimal> widget1747775605139 = jdyDataInfo.widget1747775605139;

    /**
     * Database Column Remarks:
     *   预估净售价2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642987")
    public static final SqlColumn<BigDecimal> widget1744027642987 = jdyDataInfo.widget1744027642987;

    /**
     * Database Column Remarks:
     *   预估残值率2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642988")
    public static final SqlColumn<BigDecimal> widget1744027642988 = jdyDataInfo.widget1744027642988;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579339407")
    public static final SqlColumn<String> widget1743579339407 = jdyDataInfo.widget1743579339407;

    /**
     * Database Column Remarks:
     *   使用年限（月）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235785")
    public static final SqlColumn<Integer> widget1732271235785 = jdyDataInfo.widget1732271235785;

    /**
     * Database Column Remarks:
     *   已用月份数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235772")
    public static final SqlColumn<Integer> widget1732271235772 = jdyDataInfo.widget1732271235772;

    /**
     * Database Column Remarks:
     *   车龄（年）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602529")
    public static final SqlColumn<String> widget1732281602529 = jdyDataInfo.widget1732281602529;

    /**
     * Database Column Remarks:
     *   原值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235786")
    public static final SqlColumn<BigDecimal> widget1732271235786 = jdyDataInfo.widget1732271235786;

    /**
     * Database Column Remarks:
     *   已提折旧
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235787")
    public static final SqlColumn<BigDecimal> widget1732271235787 = jdyDataInfo.widget1732271235787;

    /**
     * Database Column Remarks:
     *   净值/残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235788")
    public static final SqlColumn<BigDecimal> widget1732271235788 = jdyDataInfo.widget1732271235788;

    /**
     * Database Column Remarks:
     *   财务评估损益
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235789")
    public static final SqlColumn<BigDecimal> widget1732271235789 = jdyDataInfo.widget1732271235789;

    /**
     * Database Column Remarks:
     *   拍卖日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684293")
    public static final SqlColumn<Date> widget1743641684293 = jdyDataInfo.widget1743641684293;

    /**
     * Database Column Remarks:
     *   拍卖次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799865")
    public static final SqlColumn<Integer> widget1743602799865 = jdyDataInfo.widget1743602799865;

    /**
     * Database Column Remarks:
     *   出售底价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445365")
    public static final SqlColumn<BigDecimal> widget1743562445365 = jdyDataInfo.widget1743562445365;

    /**
     * Database Column Remarks:
     *   拍卖保留价-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862109")
    public static final SqlColumn<BigDecimal> widget1743387862109 = jdyDataInfo.widget1743387862109;

    /**
     * Database Column Remarks:
     *   拍卖最高价-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445367")
    public static final SqlColumn<BigDecimal> widget1743562445367 = jdyDataInfo.widget1743562445367;

    /**
     * Database Column Remarks:
     *   实际成交价-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445368")
    public static final SqlColumn<BigDecimal> widget1743562445368 = jdyDataInfo.widget1743562445368;

    /**
     * Database Column Remarks:
     *   溢价率（底价）-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862100")
    public static final SqlColumn<BigDecimal> widget1743387862100 = jdyDataInfo.widget1743387862100;

    /**
     * Database Column Remarks:
     *   溢价率（保留价）-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799863")
    public static final SqlColumn<BigDecimal> widget1743602799863 = jdyDataInfo.widget1743602799863;

    /**
     * Database Column Remarks:
     *   实际残值率-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199519")
    public static final SqlColumn<BigDecimal> widget1732285199519 = jdyDataInfo.widget1732285199519;

    /**
     * Database Column Remarks:
     *   成交方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365624")
    public static final SqlColumn<String> widget1743983365624 = jdyDataInfo.widget1743983365624;

    /**
     * Database Column Remarks:
     *   成交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365636")
    public static final SqlColumn<Date> widget1743983365636 = jdyDataInfo.widget1743983365636;

    /**
     * Database Column Remarks:
     *   拍卖日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684294")
    public static final SqlColumn<Date> widget1743641684294 = jdyDataInfo.widget1743641684294;

    /**
     * Database Column Remarks:
     *   拍卖次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235810")
    public static final SqlColumn<Integer> widget1732271235810 = jdyDataInfo.widget1732271235810;

    /**
     * Database Column Remarks:
     *   出售底价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799866")
    public static final SqlColumn<BigDecimal> widget1743602799866 = jdyDataInfo.widget1743602799866;

    /**
     * Database Column Remarks:
     *   拍卖保留价-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445378")
    public static final SqlColumn<BigDecimal> widget1743562445378 = jdyDataInfo.widget1743562445378;

    /**
     * Database Column Remarks:
     *   拍卖最高价-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445379")
    public static final SqlColumn<BigDecimal> widget1743562445379 = jdyDataInfo.widget1743562445379;

    /**
     * Database Column Remarks:
     *   实际成交价-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445380")
    public static final SqlColumn<BigDecimal> widget1743562445380 = jdyDataInfo.widget1743562445380;

    /**
     * Database Column Remarks:
     *   溢价率（底价）-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799867")
    public static final SqlColumn<BigDecimal> widget1743602799867 = jdyDataInfo.widget1743602799867;

    /**
     * Database Column Remarks:
     *   溢价率（保留价）-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445383")
    public static final SqlColumn<BigDecimal> widget1743562445383 = jdyDataInfo.widget1743562445383;

    /**
     * Database Column Remarks:
     *   实际残值率-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445384")
    public static final SqlColumn<BigDecimal> widget1743562445384 = jdyDataInfo.widget1743562445384;

    /**
     * Database Column Remarks:
     *   成交方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365631")
    public static final SqlColumn<String> widget1743983365631 = jdyDataInfo.widget1743983365631;

    /**
     * Database Column Remarks:
     *   成交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365635")
    public static final SqlColumn<Date> widget1743983365635 = jdyDataInfo.widget1743983365635;

    /**
     * Database Column Remarks:
     *   拍卖日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684295")
    public static final SqlColumn<Date> widget1743641684295 = jdyDataInfo.widget1743641684295;

    /**
     * Database Column Remarks:
     *   拍卖次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445375")
    public static final SqlColumn<Integer> widget1743562445375 = jdyDataInfo.widget1743562445375;

    /**
     * Database Column Remarks:
     *   出售底价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862103")
    public static final SqlColumn<BigDecimal> widget1743387862103 = jdyDataInfo.widget1743387862103;

    /**
     * Database Column Remarks:
     *   拍卖保留价-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445366")
    public static final SqlColumn<BigDecimal> widget1743562445366 = jdyDataInfo.widget1743562445366;

    /**
     * Database Column Remarks:
     *   拍卖最高价-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862104")
    public static final SqlColumn<BigDecimal> widget1743387862104 = jdyDataInfo.widget1743387862104;

    /**
     * Database Column Remarks:
     *   实际成交价-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862105")
    public static final SqlColumn<BigDecimal> widget1743387862105 = jdyDataInfo.widget1743387862105;

    /**
     * Database Column Remarks:
     *   溢价率（底价）-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445369")
    public static final SqlColumn<BigDecimal> widget1743562445369 = jdyDataInfo.widget1743562445369;

    /**
     * Database Column Remarks:
     *   溢价率（保留价）-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235811")
    public static final SqlColumn<BigDecimal> widget1732271235811 = jdyDataInfo.widget1732271235811;

    /**
     * Database Column Remarks:
     *   实际残值率-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445372")
    public static final SqlColumn<BigDecimal> widget1743562445372 = jdyDataInfo.widget1743562445372;

    /**
     * Database Column Remarks:
     *   成交方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365633")
    public static final SqlColumn<String> widget1743983365633 = jdyDataInfo.widget1743983365633;

    /**
     * Database Column Remarks:
     *   成交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365637")
    public static final SqlColumn<Date> widget1743983365637 = jdyDataInfo.widget1743983365637;

    /**
     * Database Column Remarks:
     *   流程状态 0-进行中 1 完成
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.flow_state")
    public static final SqlColumn<Integer> flowState = jdyDataInfo.flowState;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = jdyDataInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_time")
    public static final SqlColumn<Date> createTime = jdyDataInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = jdyDataInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_name")
    public static final SqlColumn<String> createOperName = jdyDataInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_time")
    public static final SqlColumn<Date> updateTime = jdyDataInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = jdyDataInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = jdyDataInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    public static final class JdyDataInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> dataId = column("data_id", JDBCType.VARCHAR);

        public final SqlColumn<String> entryId = column("entry_id", JDBCType.VARCHAR);

        public final SqlColumn<Date> widget1732281602495 = column("_widget_1732281602495", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> widget1732271235702 = column("_widget_1732271235702", JDBCType.TIMESTAMP);

        public final SqlColumn<String> widget1732271235713 = column("_widget_1732271235713", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1732271235722 = column("_widget_1732271235722", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1736926051627 = column("_widget_1736926051627", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1736926051635 = column("_widget_1736926051635", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1732271235709 = column("_widget_1732271235709", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1732271235742 = column("_widget_1732271235742", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1732271235741 = column("_widget_1732271235741", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1732271235750 = column("_widget_1732271235750", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1743895605231 = column("_widget_1743895605231", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1732271235734 = column("_widget_1732271235734", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1744687012604 = column("_widget_1744687012604", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1737338544071 = column("_widget_1737338544071", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1732281602514 = column("_widget_1732281602514", JDBCType.VARCHAR);

        public final SqlColumn<Date> widget1737338544074 = column("_widget_1737338544074", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> widget1732271235755 = column("_widget_1732271235755", JDBCType.TIMESTAMP);

        public final SqlColumn<String> widget1745227149952 = column("_widget_1745227149952", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> widget1747774937598 = column("_widget_1747774937598", JDBCType.DECIMAL);

        public final SqlColumn<String> widget1743581186765 = column("_widget_1743581186765", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1743579518411 = column("_widget_1743579518411", JDBCType.VARCHAR);

        public final SqlColumn<String> widget1750208093989 = column("_widget_1750208093989", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> widget1747775605137 = column("_widget_1747775605137", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732583760832 = column("_widget_1732583760832", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732583760833 = column("_widget_1732583760833", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732583760834 = column("_widget_1732583760834", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732583760838 = column("_widget_1732583760838", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732583760842 = column("_widget_1732583760842", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1747775605138 = column("_widget_1747775605138", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732280180068 = column("_widget_1732280180068", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732285199512 = column("_widget_1732285199512", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1747775605139 = column("_widget_1747775605139", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1744027642987 = column("_widget_1744027642987", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1744027642988 = column("_widget_1744027642988", JDBCType.DECIMAL);

        public final SqlColumn<String> widget1743579339407 = column("_widget_1743579339407", JDBCType.VARCHAR);

        public final SqlColumn<Integer> widget1732271235785 = column("_widget_1732271235785", JDBCType.INTEGER);

        public final SqlColumn<Integer> widget1732271235772 = column("_widget_1732271235772", JDBCType.INTEGER);

        public final SqlColumn<String> widget1732281602529 = column("_widget_1732281602529", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> widget1732271235786 = column("_widget_1732271235786", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732271235787 = column("_widget_1732271235787", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732271235788 = column("_widget_1732271235788", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732271235789 = column("_widget_1732271235789", JDBCType.DECIMAL);

        public final SqlColumn<Date> widget1743641684293 = column("_widget_1743641684293", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> widget1743602799865 = column("_widget_1743602799865", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> widget1743562445365 = column("_widget_1743562445365", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743387862109 = column("_widget_1743387862109", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445367 = column("_widget_1743562445367", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445368 = column("_widget_1743562445368", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743387862100 = column("_widget_1743387862100", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743602799863 = column("_widget_1743602799863", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732285199519 = column("_widget_1732285199519", JDBCType.DECIMAL);

        public final SqlColumn<String> widget1743983365624 = column("_widget_1743983365624", JDBCType.VARCHAR);

        public final SqlColumn<Date> widget1743983365636 = column("_widget_1743983365636", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> widget1743641684294 = column("_widget_1743641684294", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> widget1732271235810 = column("_widget_1732271235810", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> widget1743602799866 = column("_widget_1743602799866", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445378 = column("_widget_1743562445378", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445379 = column("_widget_1743562445379", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445380 = column("_widget_1743562445380", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743602799867 = column("_widget_1743602799867", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445383 = column("_widget_1743562445383", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445384 = column("_widget_1743562445384", JDBCType.DECIMAL);

        public final SqlColumn<String> widget1743983365631 = column("_widget_1743983365631", JDBCType.VARCHAR);

        public final SqlColumn<Date> widget1743983365635 = column("_widget_1743983365635", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> widget1743641684295 = column("_widget_1743641684295", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> widget1743562445375 = column("_widget_1743562445375", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> widget1743387862103 = column("_widget_1743387862103", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445366 = column("_widget_1743562445366", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743387862104 = column("_widget_1743387862104", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743387862105 = column("_widget_1743387862105", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445369 = column("_widget_1743562445369", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1732271235811 = column("_widget_1732271235811", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> widget1743562445372 = column("_widget_1743562445372", JDBCType.DECIMAL);

        public final SqlColumn<String> widget1743983365633 = column("_widget_1743983365633", JDBCType.VARCHAR);

        public final SqlColumn<Date> widget1743983365637 = column("_widget_1743983365637", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> flowState = column("flow_state", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public JdyDataInfo() {
            super("t_jdy_data_info");
        }
    }
}