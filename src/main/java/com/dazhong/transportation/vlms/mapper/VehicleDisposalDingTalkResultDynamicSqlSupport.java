package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleDisposalDingTalkResultDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    public static final VehicleDisposalDingTalkResult vehicleDisposalDingTalkResult = new VehicleDisposalDingTalkResult();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.id")
    public static final SqlColumn<Long> id = vehicleDisposalDingTalkResult.id;

    /**
     * Database Column Remarks:
     *   处置单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.disposal_id")
    public static final SqlColumn<Long> disposalId = vehicleDisposalDingTalkResult.disposalId;

    /**
     * Database Column Remarks:
     *   原值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.original_value")
    public static final SqlColumn<BigDecimal> originalValue = vehicleDisposalDingTalkResult.originalValue;

    /**
     * Database Column Remarks:
     *   已提折旧（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.depreciation_taken")
    public static final SqlColumn<BigDecimal> depreciationTaken = vehicleDisposalDingTalkResult.depreciationTaken;

    /**
     * Database Column Remarks:
     *   净值/残值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.net_value_or_salvage")
    public static final SqlColumn<BigDecimal> netValueOrSalvage = vehicleDisposalDingTalkResult.netValueOrSalvage;

    /**
     * Database Column Remarks:
     *   预计收益/损失（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.estimated_profit_loss")
    public static final SqlColumn<BigDecimal> estimatedProfitLoss = vehicleDisposalDingTalkResult.estimatedProfitLoss;

    /**
     * Database Column Remarks:
     *   实际出售车辆数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vehicles_sold")
    public static final SqlColumn<Integer> actualVehiclesSold = vehicleDisposalDingTalkResult.actualVehiclesSold;

    /**
     * Database Column Remarks:
     *   实际总盈亏（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_total_profit_loss")
    public static final SqlColumn<BigDecimal> actualTotalProfitLoss = vehicleDisposalDingTalkResult.actualTotalProfitLoss;

    /**
     * Database Column Remarks:
     *   实际单车盈亏（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_per_vehicle_profit_loss")
    public static final SqlColumn<BigDecimal> actualPerVehicleProfitLoss = vehicleDisposalDingTalkResult.actualPerVehicleProfitLoss;

    /**
     * Database Column Remarks:
     *   实预1差额/扣除撤拍车辆预计总盈亏1（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_one")
    public static final SqlColumn<BigDecimal> actualVsEstimatedDiffOne = vehicleDisposalDingTalkResult.actualVsEstimatedDiffOne;

    /**
     * Database Column Remarks:
     *   实预2差额/扣除撤拍车辆预计总盈亏2（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_two")
    public static final SqlColumn<BigDecimal> actualVsEstimatedDiffTwo = vehicleDisposalDingTalkResult.actualVsEstimatedDiffTwo;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleDisposalDingTalkResult.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_time")
    public static final SqlColumn<Date> createTime = vehicleDisposalDingTalkResult.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleDisposalDingTalkResult.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleDisposalDingTalkResult.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_time")
    public static final SqlColumn<Date> updateTime = vehicleDisposalDingTalkResult.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleDisposalDingTalkResult.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleDisposalDingTalkResult.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    public static final class VehicleDisposalDingTalkResult extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> disposalId = column("disposal_id", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> originalValue = column("original_value", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> depreciationTaken = column("depreciation_taken", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> netValueOrSalvage = column("net_value_or_salvage", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> estimatedProfitLoss = column("estimated_profit_loss", JDBCType.DECIMAL);

        public final SqlColumn<Integer> actualVehiclesSold = column("actual_vehicles_sold", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> actualTotalProfitLoss = column("actual_total_profit_loss", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> actualPerVehicleProfitLoss = column("actual_per_vehicle_profit_loss", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> actualVsEstimatedDiffOne = column("actual_vs_estimated_diff_one", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> actualVsEstimatedDiffTwo = column("actual_vs_estimated_diff_two", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleDisposalDingTalkResult() {
            super("t_vehicle_disposal_ding_talk_result");
        }
    }
}