package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class LicensePlateQuotaOperateLogDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    public static final LicensePlateQuotaOperateLog licensePlateQuotaOperateLog = new LicensePlateQuotaOperateLog();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.id")
    public static final SqlColumn<Long> id = licensePlateQuotaOperateLog.id;

    /**
     * Database Column Remarks:
     *   调整类型 1-总数增加 2-总数减少 3-类型调整
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_type")
    public static final SqlColumn<Integer> adjustType = licensePlateQuotaOperateLog.adjustType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = licensePlateQuotaOperateLog.assetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_name")
    public static final SqlColumn<String> assetCompanyName = licensePlateQuotaOperateLog.assetCompanyName;

    /**
     * Database Column Remarks:
     *   调整原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_reason")
    public static final SqlColumn<String> adjustReason = licensePlateQuotaOperateLog.adjustReason;

    /**
     * Database Column Remarks:
     *   操作内容
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.operate_content")
    public static final SqlColumn<String> operateContent = licensePlateQuotaOperateLog.operateContent;

    /**
     * Database Column Remarks:
     *   删除标记（0：正常 1：删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.is_deleted")
    public static final SqlColumn<Integer> isDeleted = licensePlateQuotaOperateLog.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_time")
    public static final SqlColumn<Date> createTime = licensePlateQuotaOperateLog.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_id")
    public static final SqlColumn<Long> createOperId = licensePlateQuotaOperateLog.createOperId;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_name")
    public static final SqlColumn<String> createOperName = licensePlateQuotaOperateLog.createOperName;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_time")
    public static final SqlColumn<Date> updateTime = licensePlateQuotaOperateLog.updateTime;

    /**
     * Database Column Remarks:
     *   修改人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_id")
    public static final SqlColumn<Long> updateOperId = licensePlateQuotaOperateLog.updateOperId;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_name")
    public static final SqlColumn<String> updateOperName = licensePlateQuotaOperateLog.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    public static final class LicensePlateQuotaOperateLog extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> adjustType = column("adjust_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<String> assetCompanyName = column("asset_company_name", JDBCType.VARCHAR);

        public final SqlColumn<String> adjustReason = column("adjust_reason", JDBCType.VARCHAR);

        public final SqlColumn<String> operateContent = column("operate_content", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public LicensePlateQuotaOperateLog() {
            super("t_license_plate_quota_operate_log");
        }
    }
}