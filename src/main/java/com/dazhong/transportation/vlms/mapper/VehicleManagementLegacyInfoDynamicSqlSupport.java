package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleManagementLegacyInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    public static final VehicleManagementLegacyInfo vehicleManagementLegacyInfo = new VehicleManagementLegacyInfo();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.id")
    public static final SqlColumn<Long> id = vehicleManagementLegacyInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.vin")
    public static final SqlColumn<String> vin = vehicleManagementLegacyInfo.vin;

    /**
     * Database Column Remarks:
     *   营运/非营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.operate_type_id")
    public static final SqlColumn<Integer> operateTypeId = vehicleManagementLegacyInfo.operateTypeId;

    /**
     * Database Column Remarks:
     *   号牌种类 1-小型汽车 2-大型汽车 3-教练车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.vehicle_category_id")
    public static final SqlColumn<Integer> vehicleCategoryId = vehicleManagementLegacyInfo.vehicleCategoryId;

    /**
     * Database Column Remarks:
     *   运营类别 1-营运车辆 2-生产用车 3-公务车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.operation_category_id")
    public static final SqlColumn<Integer> operationCategoryId = vehicleManagementLegacyInfo.operationCategoryId;

    /**
     * Database Column Remarks:
     *   机动车所有者ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.owner_id")
    public static final SqlColumn<Integer> ownerId = vehicleManagementLegacyInfo.ownerId;

    /**
     * Database Column Remarks:
     *   营运证企业ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.company_owner_id")
    public static final SqlColumn<Integer> companyOwnerId = vehicleManagementLegacyInfo.companyOwnerId;

    /**
     * Database Column Remarks:
     *   资产所有
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.asset_owner_id")
    public static final SqlColumn<Integer> assetOwnerId = vehicleManagementLegacyInfo.assetOwnerId;

    /**
     * Database Column Remarks:
     *   合同形式 默认通用合同
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.contract_type_id")
    public static final SqlColumn<Integer> contractTypeId = vehicleManagementLegacyInfo.contractTypeId;

    /**
     * Database Column Remarks:
     *   营运证号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.operating_no")
    public static final SqlColumn<String> operatingNo = vehicleManagementLegacyInfo.operatingNo;

    /**
     * Database Column Remarks:
     *   投产日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.start_date")
    public static final SqlColumn<Date> startDate = vehicleManagementLegacyInfo.startDate;

    /**
     * Database Column Remarks:
     *   购买日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.purchase_date")
    public static final SqlColumn<Date> purchaseDate = vehicleManagementLegacyInfo.purchaseDate;

    /**
     * Database Column Remarks:
     *   上牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.license_date")
    public static final SqlColumn<Date> licenseDate = vehicleManagementLegacyInfo.licenseDate;

    /**
     * Database Column Remarks:
     *   投运日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.operation_start_date")
    public static final SqlColumn<Date> operationStartDate = vehicleManagementLegacyInfo.operationStartDate;

    /**
     * Database Column Remarks:
     *   是否有产权证
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.has_right")
    public static final SqlColumn<Integer> hasRight = vehicleManagementLegacyInfo.hasRight;

    /**
     * Database Column Remarks:
     *   老车管车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.vehicle_model_name")
    public static final SqlColumn<String> vehicleModelName = vehicleManagementLegacyInfo.vehicleModelName;

    /**
     * Database Column Remarks:
     *   过入单位
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.from_company")
    public static final SqlColumn<String> fromCompany = vehicleManagementLegacyInfo.fromCompany;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleManagementLegacyInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.create_time")
    public static final SqlColumn<Date> createTime = vehicleManagementLegacyInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleManagementLegacyInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleManagementLegacyInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.update_time")
    public static final SqlColumn<Date> updateTime = vehicleManagementLegacyInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleManagementLegacyInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_management_legacy_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleManagementLegacyInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_management_legacy_info")
    public static final class VehicleManagementLegacyInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Integer> operateTypeId = column("operate_type_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleCategoryId = column("vehicle_category_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> operationCategoryId = column("operation_category_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> companyOwnerId = column("company_owner_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> assetOwnerId = column("asset_owner_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> contractTypeId = column("contract_type_id", JDBCType.INTEGER);

        public final SqlColumn<String> operatingNo = column("operating_no", JDBCType.VARCHAR);

        public final SqlColumn<Date> startDate = column("start_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> purchaseDate = column("purchase_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> licenseDate = column("license_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> operationStartDate = column("operation_start_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> hasRight = column("has_right", JDBCType.INTEGER);

        public final SqlColumn<String> vehicleModelName = column("vehicle_model_name", JDBCType.VARCHAR);

        public final SqlColumn<String> fromCompany = column("from_company", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleManagementLegacyInfo() {
            super("t_vehicle_management_legacy_info");
        }
    }
}