package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleDecorationDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_decoration")
    public static final VehicleDecoration vehicleDecoration = new VehicleDecoration();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.id")
    public static final SqlColumn<Long> id = vehicleDecoration.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.vin")
    public static final SqlColumn<String> vin = vehicleDecoration.vin;

    /**
     * Database Column Remarks:
     *   装潢类型 1-贴膜 2-脚垫 .....(数据字典详细描叙)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.decoration_type")
    public static final SqlColumn<Integer> decorationType = vehicleDecoration.decorationType;

    /**
     * Database Column Remarks:
     *   装潢内容
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.decoration_content")
    public static final SqlColumn<String> decorationContent = vehicleDecoration.decorationContent;

    /**
     * Database Column Remarks:
     *   基本价格
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.base_price")
    public static final SqlColumn<BigDecimal> basePrice = vehicleDecoration.basePrice;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleDecoration.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.create_time")
    public static final SqlColumn<Date> createTime = vehicleDecoration.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleDecoration.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleDecoration.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.update_time")
    public static final SqlColumn<Date> updateTime = vehicleDecoration.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleDecoration.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_decoration.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleDecoration.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_decoration")
    public static final class VehicleDecoration extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Integer> decorationType = column("decoration_type", JDBCType.INTEGER);

        public final SqlColumn<String> decorationContent = column("decoration_content", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> basePrice = column("base_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleDecoration() {
            super("t_vehicle_decoration");
        }
    }
}