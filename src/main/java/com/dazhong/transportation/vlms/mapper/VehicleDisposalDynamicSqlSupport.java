package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleDisposalDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    public static final VehicleDisposal vehicleDisposal = new VehicleDisposal();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.id")
    public static final SqlColumn<Long> id = vehicleDisposal.id;

    /**
     * Database Column Remarks:
     *   单据号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.document_no")
    public static final SqlColumn<String> documentNo = vehicleDisposal.documentNo;

    /**
     * Database Column Remarks:
     *   单据类型 1-出售申请 2-报废申请
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.document_type")
    public static final SqlColumn<Integer> documentType = vehicleDisposal.documentType;

    /**
     * Database Column Remarks:
     *   单据标题
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.document_title")
    public static final SqlColumn<String> documentTitle = vehicleDisposal.documentTitle;

    /**
     * Database Column Remarks:
     *   单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.document_status")
    public static final SqlColumn<Integer> documentStatus = vehicleDisposal.documentStatus;

    /**
     * Database Column Remarks:
     *   条线 1-巡网业务线 2-商务业务线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.product_line")
    public static final SqlColumn<Integer> productLine = vehicleDisposal.productLine;

    /**
     * Database Column Remarks:
     *   申请人审批部门id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.originator_dept_id")
    public static final SqlColumn<Long> originatorDeptId = vehicleDisposal.originatorDeptId;

    /**
     * Database Column Remarks:
     *   申请人审批部门名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.originator_dept_name")
    public static final SqlColumn<String> originatorDeptName = vehicleDisposal.originatorDeptName;

    /**
     * Database Column Remarks:
     *   所在部门id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.organization_id")
    public static final SqlColumn<Long> organizationId = vehicleDisposal.organizationId;

    /**
     * Database Column Remarks:
     *   所在部门
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.organization_name")
    public static final SqlColumn<String> organizationName = vehicleDisposal.organizationName;

    /**
     * Database Column Remarks:
     *   单据所属资产公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.owner_id")
    public static final SqlColumn<Integer> ownerId = vehicleDisposal.ownerId;

    /**
     * Database Column Remarks:
     *   单据所属资产公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.owner_name")
    public static final SqlColumn<String> ownerName = vehicleDisposal.ownerName;

    /**
     * Database Column Remarks:
     *   出售车辆公司编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.selling_company_code")
    public static final SqlColumn<String> sellingCompanyCode = vehicleDisposal.sellingCompanyCode;

    /**
     * Database Column Remarks:
     *   出售车辆公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.selling_company_name")
    public static final SqlColumn<String> sellingCompanyName = vehicleDisposal.sellingCompanyName;

    /**
     * Database Column Remarks:
     *   车辆使用部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.use_department_code")
    public static final SqlColumn<String> useDepartmentCode = vehicleDisposal.useDepartmentCode;

    /**
     * Database Column Remarks:
     *   车辆使用部门名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.use_department_name")
    public static final SqlColumn<String> useDepartmentName = vehicleDisposal.useDepartmentName;

    /**
     * Database Column Remarks:
     *   车辆所属部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.department_code")
    public static final SqlColumn<String> departmentCode = vehicleDisposal.departmentCode;

    /**
     * Database Column Remarks:
     *   车辆所属部门名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.department_name")
    public static final SqlColumn<String> departmentName = vehicleDisposal.departmentName;

    /**
     * Database Column Remarks:
     *   钉钉审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.ding_talk_no")
    public static final SqlColumn<String> dingTalkNo = vehicleDisposal.dingTalkNo;

    /**
     * Database Column Remarks:
     *   提交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.submit_date")
    public static final SqlColumn<Date> submitDate = vehicleDisposal.submitDate;

    /**
     * Database Column Remarks:
     *   关联任务编号（退牌任务）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.task_number")
    public static final SqlColumn<String> taskNumber = vehicleDisposal.taskNumber;

    /**
     * Database Column Remarks:
     *   本月批次
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.month_batch")
    public static final SqlColumn<String> monthBatch = vehicleDisposal.monthBatch;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.remark")
    public static final SqlColumn<String> remark = vehicleDisposal.remark;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleDisposal.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.create_time")
    public static final SqlColumn<Date> createTime = vehicleDisposal.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleDisposal.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleDisposal.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.update_time")
    public static final SqlColumn<Date> updateTime = vehicleDisposal.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleDisposal.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleDisposal.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    public static final class VehicleDisposal extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> documentNo = column("document_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> documentType = column("document_type", JDBCType.INTEGER);

        public final SqlColumn<String> documentTitle = column("document_title", JDBCType.VARCHAR);

        public final SqlColumn<Integer> documentStatus = column("document_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Long> originatorDeptId = column("originator_dept_id", JDBCType.BIGINT);

        public final SqlColumn<String> originatorDeptName = column("originator_dept_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> organizationId = column("organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> organizationName = column("organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<String> ownerName = column("owner_name", JDBCType.VARCHAR);

        public final SqlColumn<String> sellingCompanyCode = column("selling_company_code", JDBCType.VARCHAR);

        public final SqlColumn<String> sellingCompanyName = column("selling_company_name", JDBCType.VARCHAR);

        public final SqlColumn<String> useDepartmentCode = column("use_department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> useDepartmentName = column("use_department_name", JDBCType.VARCHAR);

        public final SqlColumn<String> departmentCode = column("department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> departmentName = column("department_name", JDBCType.VARCHAR);

        public final SqlColumn<String> dingTalkNo = column("ding_talk_no", JDBCType.VARCHAR);

        public final SqlColumn<Date> submitDate = column("submit_date", JDBCType.DATE);

        public final SqlColumn<String> taskNumber = column("task_number", JDBCType.VARCHAR);

        public final SqlColumn<String> monthBatch = column("month_batch", JDBCType.VARCHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleDisposal() {
            super("t_vehicle_disposal");
        }
    }
}