package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class LicensePlateTaskInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    public static final LicensePlateTaskInfo licensePlateTaskInfo = new LicensePlateTaskInfo();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.id")
    public static final SqlColumn<Long> id = licensePlateTaskInfo.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_number")
    public static final SqlColumn<String> taskNumber = licensePlateTaskInfo.taskNumber;

    /**
     * Database Column Remarks:
     *   任务类型 1-上牌任务 2-退牌任务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_type")
    public static final SqlColumn<Integer> taskType = licensePlateTaskInfo.taskType;

    /**
     * Database Column Remarks:
     *   是否使用额度（上牌） 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.use_quota_type")
    public static final SqlColumn<Integer> useQuotaType = licensePlateTaskInfo.useQuotaType;

    /**
     * Database Column Remarks:
     *   是否使用额度（退牌） 1-退还 2-不退还 3-不涉及
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.return_quota_type")
    public static final SqlColumn<Integer> returnQuotaType = licensePlateTaskInfo.returnQuotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = licensePlateTaskInfo.assetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_name")
    public static final SqlColumn<String> assetCompanyName = licensePlateTaskInfo.assetCompanyName;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.quota_type")
    public static final SqlColumn<Integer> quotaType = licensePlateTaskInfo.quotaType;

    /**
     * Database Column Remarks:
     *   匹配类型 1-明确匹配额度编号 2-未明确匹配额度编号 3-不知道额度编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.match_type")
    public static final SqlColumn<Integer> matchType = licensePlateTaskInfo.matchType;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = licensePlateTaskInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_time")
    public static final SqlColumn<Date> createTime = licensePlateTaskInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = licensePlateTaskInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_name")
    public static final SqlColumn<String> createOperName = licensePlateTaskInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_time")
    public static final SqlColumn<Date> updateTime = licensePlateTaskInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = licensePlateTaskInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = licensePlateTaskInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    public static final class LicensePlateTaskInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNumber = column("task_number", JDBCType.VARCHAR);

        public final SqlColumn<Integer> taskType = column("task_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> useQuotaType = column("use_quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> returnQuotaType = column("return_quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<String> assetCompanyName = column("asset_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> quotaType = column("quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> matchType = column("match_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public LicensePlateTaskInfo() {
            super("t_license_plate_task_info");
        }
    }
}