package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleOrderReceiptDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    public static final VehicleOrderReceipt vehicleOrderReceipt = new VehicleOrderReceipt();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.id")
    public static final SqlColumn<Long> id = vehicleOrderReceipt.id;

    /**
     * Database Column Remarks:
     *   采购申id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.purchase_apply_id")
    public static final SqlColumn<Long> purchaseApplyId = vehicleOrderReceipt.purchaseApplyId;

    /**
     * Database Column Remarks:
     *   采购申请明细编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.apply_details_no")
    public static final SqlColumn<String> applyDetailsNo = vehicleOrderReceipt.applyDetailsNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.vin")
    public static final SqlColumn<String> vin = vehicleOrderReceipt.vin;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.engine_no")
    public static final SqlColumn<String> engineNo = vehicleOrderReceipt.engineNo;

    /**
     * Database Column Remarks:
     *   车型id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = vehicleOrderReceipt.vehicleModelId;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.vehicle_color_id")
    public static final SqlColumn<Integer> vehicleColorId = vehicleOrderReceipt.vehicleColorId;

    /**
     * Database Column Remarks:
     *   内饰颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.interior_color")
    public static final SqlColumn<String> interiorColor = vehicleOrderReceipt.interiorColor;

    /**
     * Database Column Remarks:
     *   下单日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.order_date")
    public static final SqlColumn<Date> orderDate = vehicleOrderReceipt.orderDate;

    /**
     * Database Column Remarks:
     *   收货日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.receipt_date")
    public static final SqlColumn<Date> receiptDate = vehicleOrderReceipt.receiptDate;

    /**
     * Database Column Remarks:
     *   供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.supplier_id")
    public static final SqlColumn<Integer> supplierId = vehicleOrderReceipt.supplierId;

    /**
     * Database Column Remarks:
     *   收货所属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.owner_id")
    public static final SqlColumn<Integer> ownerId = vehicleOrderReceipt.ownerId;

    /**
     * Database Column Remarks:
     *   业务类型枚举值 1-巡讯 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.business_line")
    public static final SqlColumn<Integer> businessLine = vehicleOrderReceipt.businessLine;

    /**
     * Database Column Remarks:
     *   是否回购 1：是 2： 否 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.is_repurchase")
    public static final SqlColumn<Integer> isRepurchase = vehicleOrderReceipt.isRepurchase;

    /**
     * Database Column Remarks:
     *   回购时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.repurchase_date")
    public static final SqlColumn<Date> repurchaseDate = vehicleOrderReceipt.repurchaseDate;

    /**
     * Database Column Remarks:
     *   回购要求
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.repurchase_requirements")
    public static final SqlColumn<String> repurchaseRequirements = vehicleOrderReceipt.repurchaseRequirements;

    /**
     * Database Column Remarks:
     *   车辆上牌标记 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.vehicle_registration_mark")
    public static final SqlColumn<Integer> vehicleRegistrationMark = vehicleOrderReceipt.vehicleRegistrationMark;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleOrderReceipt.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.create_time")
    public static final SqlColumn<Date> createTime = vehicleOrderReceipt.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleOrderReceipt.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleOrderReceipt.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.update_time")
    public static final SqlColumn<Date> updateTime = vehicleOrderReceipt.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleOrderReceipt.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_order_receipt.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleOrderReceipt.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_order_receipt")
    public static final class VehicleOrderReceipt extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> purchaseApplyId = column("purchase_apply_id", JDBCType.BIGINT);

        public final SqlColumn<String> applyDetailsNo = column("apply_details_no", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> engineNo = column("engine_no", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> vehicleColorId = column("vehicle_color_id", JDBCType.INTEGER);

        public final SqlColumn<String> interiorColor = column("interior_color", JDBCType.VARCHAR);

        public final SqlColumn<Date> orderDate = column("order_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> receiptDate = column("receipt_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> supplierId = column("supplier_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> businessLine = column("business_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> isRepurchase = column("is_repurchase", JDBCType.INTEGER);

        public final SqlColumn<Date> repurchaseDate = column("repurchase_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> repurchaseRequirements = column("repurchase_requirements", JDBCType.VARCHAR);

        public final SqlColumn<Integer> vehicleRegistrationMark = column("vehicle_registration_mark", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleOrderReceipt() {
            super("t_vehicle_order_receipt");
        }
    }
}