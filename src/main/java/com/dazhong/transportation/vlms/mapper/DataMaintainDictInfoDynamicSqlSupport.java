package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class DataMaintainDictInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_maintain_dict_info")
    public static final DataMaintainDictInfo dataMaintainDictInfo = new DataMaintainDictInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.id")
    public static final SqlColumn<Long> id = dataMaintainDictInfo.id;

    /**
     * Database Column Remarks:
     *   数据字典名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.data_name")
    public static final SqlColumn<String> dataName = dataMaintainDictInfo.dataName;

    /**
     * Database Column Remarks:
     *   数据字典编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.data_code")
    public static final SqlColumn<String> dataCode = dataMaintainDictInfo.dataCode;

    /**
     * Database Column Remarks:
     *   字典值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.data_value")
    public static final SqlColumn<String> dataValue = dataMaintainDictInfo.dataValue;

    /**
     * Database Column Remarks:
     *   系统编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.system_code")
    public static final SqlColumn<String> systemCode = dataMaintainDictInfo.systemCode;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = dataMaintainDictInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.create_time")
    public static final SqlColumn<Date> createTime = dataMaintainDictInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = dataMaintainDictInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.create_oper_name")
    public static final SqlColumn<String> createOperName = dataMaintainDictInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.update_time")
    public static final SqlColumn<Date> updateTime = dataMaintainDictInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = dataMaintainDictInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_maintain_dict_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = dataMaintainDictInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_maintain_dict_info")
    public static final class DataMaintainDictInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> dataName = column("data_name", JDBCType.VARCHAR);

        public final SqlColumn<String> dataCode = column("data_code", JDBCType.VARCHAR);

        public final SqlColumn<String> dataValue = column("data_value", JDBCType.VARCHAR);

        public final SqlColumn<String> systemCode = column("system_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public DataMaintainDictInfo() {
            super("t_data_maintain_dict_info");
        }
    }
}