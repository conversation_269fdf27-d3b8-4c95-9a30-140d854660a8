package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaOperateLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.LicensePlateQuotaOperateLog;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface LicensePlateQuotaOperateLogMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    BasicColumn[] selectList = BasicColumn.columnList(id, adjustType, assetCompanyId, assetCompanyName, adjustReason, operateContent, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<LicensePlateQuotaOperateLog> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LicensePlateQuotaOperateLogResult")
    Optional<LicensePlateQuotaOperateLog> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LicensePlateQuotaOperateLogResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="adjust_type", property="adjustType", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_name", property="assetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="adjust_reason", property="adjustReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="operate_content", property="operateContent", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<LicensePlateQuotaOperateLog> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, licensePlateQuotaOperateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, licensePlateQuotaOperateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default int insert(LicensePlateQuotaOperateLog record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateQuotaOperateLog, c ->
            c.map(adjustType).toProperty("adjustType")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(assetCompanyName).toProperty("assetCompanyName")
            .map(adjustReason).toProperty("adjustReason")
            .map(operateContent).toProperty("operateContent")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default int insertSelective(LicensePlateQuotaOperateLog record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateQuotaOperateLog, c ->
            c.map(adjustType).toPropertyWhenPresent("adjustType", record::getAdjustType)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(assetCompanyName).toPropertyWhenPresent("assetCompanyName", record::getAssetCompanyName)
            .map(adjustReason).toPropertyWhenPresent("adjustReason", record::getAdjustReason)
            .map(operateContent).toPropertyWhenPresent("operateContent", record::getOperateContent)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default Optional<LicensePlateQuotaOperateLog> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, licensePlateQuotaOperateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default List<LicensePlateQuotaOperateLog> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, licensePlateQuotaOperateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default List<LicensePlateQuotaOperateLog> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, licensePlateQuotaOperateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default Optional<LicensePlateQuotaOperateLog> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, licensePlateQuotaOperateLog, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    static UpdateDSL<UpdateModel> updateAllColumns(LicensePlateQuotaOperateLog record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(adjustType).equalTo(record::getAdjustType)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(assetCompanyName).equalTo(record::getAssetCompanyName)
                .set(adjustReason).equalTo(record::getAdjustReason)
                .set(operateContent).equalTo(record::getOperateContent)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LicensePlateQuotaOperateLog record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(adjustType).equalToWhenPresent(record::getAdjustType)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
                .set(adjustReason).equalToWhenPresent(record::getAdjustReason)
                .set(operateContent).equalToWhenPresent(record::getOperateContent)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default int updateByPrimaryKey(LicensePlateQuotaOperateLog record) {
        return update(c ->
            c.set(adjustType).equalTo(record::getAdjustType)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(assetCompanyName).equalTo(record::getAssetCompanyName)
            .set(adjustReason).equalTo(record::getAdjustReason)
            .set(operateContent).equalTo(record::getOperateContent)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    default int updateByPrimaryKeySelective(LicensePlateQuotaOperateLog record) {
        return update(c ->
            c.set(adjustType).equalToWhenPresent(record::getAdjustType)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
            .set(adjustReason).equalToWhenPresent(record::getAdjustReason)
            .set(operateContent).equalToWhenPresent(record::getOperateContent)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}