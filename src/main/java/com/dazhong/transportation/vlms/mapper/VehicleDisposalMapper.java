package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.dto.VehicleDisposalDetailListDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalListDto;
import com.dazhong.transportation.vlms.model.VehicleDisposal;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import javax.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleApplicationDynamicSqlSupport.dingTalkNo;
import static com.dazhong.transportation.vlms.mapper.VehicleDisposalDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface VehicleDisposalMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    BasicColumn[] selectList = BasicColumn.columnList(id, documentNo, documentType, documentTitle, documentStatus, productLine, originatorDeptId, originatorDeptName, organizationId, organizationName, ownerId, ownerName, sellingCompanyCode, sellingCompanyName, useDepartmentCode, useDepartmentName, departmentCode, departmentName, dingTalkNo, submitDate, taskNumber, monthBatch, remark, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleDisposal> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleDisposalResult")
    Optional<VehicleDisposal> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleDisposalResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="document_no", property="documentNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="document_type", property="documentType", jdbcType=JdbcType.INTEGER),
        @Result(column="document_title", property="documentTitle", jdbcType=JdbcType.VARCHAR),
        @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="originator_dept_id", property="originatorDeptId", jdbcType=JdbcType.BIGINT),
        @Result(column="originator_dept_name", property="originatorDeptName", jdbcType=JdbcType.VARCHAR),
        @Result(column="organization_id", property="organizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="organization_name", property="organizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_name", property="ownerName", jdbcType=JdbcType.VARCHAR),
        @Result(column="selling_company_code", property="sellingCompanyCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="selling_company_name", property="sellingCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="use_department_code", property="useDepartmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="use_department_name", property="useDepartmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="department_code", property="departmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="department_name", property="departmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
        @Result(column="task_number", property="taskNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="month_batch", property="monthBatch", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleDisposal> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default int insert(VehicleDisposal record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDisposal, c ->
            c.map(documentNo).toProperty("documentNo")
            .map(documentType).toProperty("documentType")
            .map(documentTitle).toProperty("documentTitle")
            .map(documentStatus).toProperty("documentStatus")
            .map(productLine).toProperty("productLine")
            .map(originatorDeptId).toProperty("originatorDeptId")
            .map(originatorDeptName).toProperty("originatorDeptName")
            .map(organizationId).toProperty("organizationId")
            .map(organizationName).toProperty("organizationName")
            .map(ownerId).toProperty("ownerId")
            .map(ownerName).toProperty("ownerName")
            .map(sellingCompanyCode).toProperty("sellingCompanyCode")
            .map(sellingCompanyName).toProperty("sellingCompanyName")
            .map(useDepartmentCode).toProperty("useDepartmentCode")
            .map(useDepartmentName).toProperty("useDepartmentName")
            .map(departmentCode).toProperty("departmentCode")
            .map(departmentName).toProperty("departmentName")
            .map(dingTalkNo).toProperty("dingTalkNo")
            .map(submitDate).toProperty("submitDate")
            .map(taskNumber).toProperty("taskNumber")
            .map(monthBatch).toProperty("monthBatch")
            .map(remark).toProperty("remark")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default int insertSelective(VehicleDisposal record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleDisposal, c ->
            c.map(documentNo).toPropertyWhenPresent("documentNo", record::getDocumentNo)
            .map(documentType).toPropertyWhenPresent("documentType", record::getDocumentType)
            .map(documentTitle).toPropertyWhenPresent("documentTitle", record::getDocumentTitle)
            .map(documentStatus).toPropertyWhenPresent("documentStatus", record::getDocumentStatus)
            .map(productLine).toPropertyWhenPresent("productLine", record::getProductLine)
            .map(originatorDeptId).toPropertyWhenPresent("originatorDeptId", record::getOriginatorDeptId)
            .map(originatorDeptName).toPropertyWhenPresent("originatorDeptName", record::getOriginatorDeptName)
            .map(organizationId).toPropertyWhenPresent("organizationId", record::getOrganizationId)
            .map(organizationName).toPropertyWhenPresent("organizationName", record::getOrganizationName)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(ownerName).toPropertyWhenPresent("ownerName", record::getOwnerName)
            .map(sellingCompanyCode).toPropertyWhenPresent("sellingCompanyCode", record::getSellingCompanyCode)
            .map(sellingCompanyName).toPropertyWhenPresent("sellingCompanyName", record::getSellingCompanyName)
            .map(useDepartmentCode).toPropertyWhenPresent("useDepartmentCode", record::getUseDepartmentCode)
            .map(useDepartmentName).toPropertyWhenPresent("useDepartmentName", record::getUseDepartmentName)
            .map(departmentCode).toPropertyWhenPresent("departmentCode", record::getDepartmentCode)
            .map(departmentName).toPropertyWhenPresent("departmentName", record::getDepartmentName)
            .map(dingTalkNo).toPropertyWhenPresent("dingTalkNo", record::getDingTalkNo)
            .map(submitDate).toPropertyWhenPresent("submitDate", record::getSubmitDate)
            .map(taskNumber).toPropertyWhenPresent("taskNumber", record::getTaskNumber)
            .map(monthBatch).toPropertyWhenPresent("monthBatch", record::getMonthBatch)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default Optional<VehicleDisposal> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default List<VehicleDisposal> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default List<VehicleDisposal> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default Optional<VehicleDisposal> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleDisposal, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleDisposal record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(documentNo).equalTo(record::getDocumentNo)
                .set(documentType).equalTo(record::getDocumentType)
                .set(documentTitle).equalTo(record::getDocumentTitle)
                .set(documentStatus).equalTo(record::getDocumentStatus)
                .set(productLine).equalTo(record::getProductLine)
                .set(originatorDeptId).equalTo(record::getOriginatorDeptId)
                .set(originatorDeptName).equalTo(record::getOriginatorDeptName)
                .set(organizationId).equalTo(record::getOrganizationId)
                .set(organizationName).equalTo(record::getOrganizationName)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(ownerName).equalTo(record::getOwnerName)
                .set(sellingCompanyCode).equalTo(record::getSellingCompanyCode)
                .set(sellingCompanyName).equalTo(record::getSellingCompanyName)
                .set(useDepartmentCode).equalTo(record::getUseDepartmentCode)
                .set(useDepartmentName).equalTo(record::getUseDepartmentName)
                .set(departmentCode).equalTo(record::getDepartmentCode)
                .set(departmentName).equalTo(record::getDepartmentName)
                .set(dingTalkNo).equalTo(record::getDingTalkNo)
                .set(submitDate).equalTo(record::getSubmitDate)
                .set(taskNumber).equalTo(record::getTaskNumber)
                .set(monthBatch).equalTo(record::getMonthBatch)
                .set(remark).equalTo(record::getRemark)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleDisposal record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(documentNo).equalToWhenPresent(record::getDocumentNo)
                .set(documentType).equalToWhenPresent(record::getDocumentType)
                .set(documentTitle).equalToWhenPresent(record::getDocumentTitle)
                .set(documentStatus).equalToWhenPresent(record::getDocumentStatus)
                .set(productLine).equalToWhenPresent(record::getProductLine)
                .set(originatorDeptId).equalToWhenPresent(record::getOriginatorDeptId)
                .set(originatorDeptName).equalToWhenPresent(record::getOriginatorDeptName)
                .set(organizationId).equalToWhenPresent(record::getOrganizationId)
                .set(organizationName).equalToWhenPresent(record::getOrganizationName)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(ownerName).equalToWhenPresent(record::getOwnerName)
                .set(sellingCompanyCode).equalToWhenPresent(record::getSellingCompanyCode)
                .set(sellingCompanyName).equalToWhenPresent(record::getSellingCompanyName)
                .set(useDepartmentCode).equalToWhenPresent(record::getUseDepartmentCode)
                .set(useDepartmentName).equalToWhenPresent(record::getUseDepartmentName)
                .set(departmentCode).equalToWhenPresent(record::getDepartmentCode)
                .set(departmentName).equalToWhenPresent(record::getDepartmentName)
                .set(dingTalkNo).equalToWhenPresent(record::getDingTalkNo)
                .set(submitDate).equalToWhenPresent(record::getSubmitDate)
                .set(taskNumber).equalToWhenPresent(record::getTaskNumber)
                .set(monthBatch).equalToWhenPresent(record::getMonthBatch)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default int updateByPrimaryKey(VehicleDisposal record) {
        return update(c ->
            c.set(documentNo).equalTo(record::getDocumentNo)
            .set(documentType).equalTo(record::getDocumentType)
            .set(documentTitle).equalTo(record::getDocumentTitle)
            .set(documentStatus).equalTo(record::getDocumentStatus)
            .set(productLine).equalTo(record::getProductLine)
            .set(originatorDeptId).equalTo(record::getOriginatorDeptId)
            .set(originatorDeptName).equalTo(record::getOriginatorDeptName)
            .set(organizationId).equalTo(record::getOrganizationId)
            .set(organizationName).equalTo(record::getOrganizationName)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(ownerName).equalTo(record::getOwnerName)
            .set(sellingCompanyCode).equalTo(record::getSellingCompanyCode)
            .set(sellingCompanyName).equalTo(record::getSellingCompanyName)
            .set(useDepartmentCode).equalTo(record::getUseDepartmentCode)
            .set(useDepartmentName).equalTo(record::getUseDepartmentName)
            .set(departmentCode).equalTo(record::getDepartmentCode)
            .set(departmentName).equalTo(record::getDepartmentName)
            .set(dingTalkNo).equalTo(record::getDingTalkNo)
            .set(submitDate).equalTo(record::getSubmitDate)
            .set(taskNumber).equalTo(record::getTaskNumber)
            .set(monthBatch).equalTo(record::getMonthBatch)
            .set(remark).equalTo(record::getRemark)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    default int updateByPrimaryKeySelective(VehicleDisposal record) {
        return update(c ->
            c.set(documentNo).equalToWhenPresent(record::getDocumentNo)
            .set(documentType).equalToWhenPresent(record::getDocumentType)
            .set(documentTitle).equalToWhenPresent(record::getDocumentTitle)
            .set(documentStatus).equalToWhenPresent(record::getDocumentStatus)
            .set(productLine).equalToWhenPresent(record::getProductLine)
            .set(originatorDeptId).equalToWhenPresent(record::getOriginatorDeptId)
            .set(originatorDeptName).equalToWhenPresent(record::getOriginatorDeptName)
            .set(organizationId).equalToWhenPresent(record::getOrganizationId)
            .set(organizationName).equalToWhenPresent(record::getOrganizationName)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(ownerName).equalToWhenPresent(record::getOwnerName)
            .set(sellingCompanyCode).equalToWhenPresent(record::getSellingCompanyCode)
            .set(sellingCompanyName).equalToWhenPresent(record::getSellingCompanyName)
            .set(useDepartmentCode).equalToWhenPresent(record::getUseDepartmentCode)
            .set(useDepartmentName).equalToWhenPresent(record::getUseDepartmentName)
            .set(departmentCode).equalToWhenPresent(record::getDepartmentCode)
            .set(departmentName).equalToWhenPresent(record::getDepartmentName)
            .set(dingTalkNo).equalToWhenPresent(record::getDingTalkNo)
            .set(submitDate).equalToWhenPresent(record::getSubmitDate)
            .set(taskNumber).equalToWhenPresent(record::getTaskNumber)
            .set(monthBatch).equalToWhenPresent(record::getMonthBatch)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleDisposalListResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
            @Result(column="document_no", property="documentNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="document_type", property="documentType", jdbcType=JdbcType.INTEGER),
            @Result(column="document_title", property="documentTitle", jdbcType=JdbcType.VARCHAR),
            @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
            @Result(column="selling_company_code", property="sellingCompanyCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="selling_company_name", property="sellingCompanyName", jdbcType=JdbcType.VARCHAR),
            @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
            @Result(column="task_number", property="taskNumber", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleDisposalListDto> selectVehicleDisposalList(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id="VehicleDisposalDetailListResult", value = {
            @Result(column="document_no", property="documentNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="document_type", property="documentType", jdbcType=JdbcType.INTEGER),
            @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
            @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="selling_company_code", property="sellingCompanyCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="selling_company_name", property="sellingCompanyName", jdbcType=JdbcType.VARCHAR),
            @Result(column="original_value", property="originalValue", jdbcType=JdbcType.DECIMAL),
            @Result(column="accumulated_depreciation", property="accumulatedDepreciation", jdbcType=JdbcType.DECIMAL),
            @Result(column="net_value", property="netValue", jdbcType=JdbcType.DECIMAL),
            @Result(column="actual_selling_price", property="actualSellingPrice", jdbcType=JdbcType.DECIMAL),
            @Result(column="sale_gain_loss", property="saleGainLoss", jdbcType=JdbcType.DECIMAL)
    })
    List<VehicleDisposalDetailListDto> selectVehicleDisposalByVin(SelectStatementProvider selectStatement);


    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_disposal")
    default Optional<VehicleDisposal> selectByDingTalkNo(String dingTalkNo_) {
        return selectOne(c ->
                c.where(dingTalkNo, isEqualTo(dingTalkNo_))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_disposal")
    default Optional<VehicleDisposal> selectByDocumentNo(String documentNo) {
        return selectOne(c -> c.where(vehicleDisposal.documentNo, isEqualTo(documentNo)));
    }
}