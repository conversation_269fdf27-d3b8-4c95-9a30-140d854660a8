package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class LicensePlateTaskVehicleDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    public static final LicensePlateTaskVehicleDetail licensePlateTaskVehicleDetail = new LicensePlateTaskVehicleDetail();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.id")
    public static final SqlColumn<Long> id = licensePlateTaskVehicleDetail.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.task_number")
    public static final SqlColumn<String> taskNumber = licensePlateTaskVehicleDetail.taskNumber;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.license_plate")
    public static final SqlColumn<String> licensePlate = licensePlateTaskVehicleDetail.licensePlate;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vin")
    public static final SqlColumn<String> vin = licensePlateTaskVehicleDetail.vin;

    /**
     * Database Column Remarks:
     *   车型id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = licensePlateTaskVehicleDetail.vehicleModelId;

    /**
     * Database Column Remarks:
     *   车型名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_name")
    public static final SqlColumn<String> vehicleModelName = licensePlateTaskVehicleDetail.vehicleModelName;

    /**
     * Database Column Remarks:
     *   资产所属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = licensePlateTaskVehicleDetail.assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产机构id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.own_organization_id")
    public static final SqlColumn<Long> ownOrganizationId = licensePlateTaskVehicleDetail.ownOrganizationId;

    /**
     * Database Column Remarks:
     *   使用机构id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_organization_id")
    public static final SqlColumn<Long> usageOrganizationId = licensePlateTaskVehicleDetail.usageOrganizationId;

    /**
     * Database Column Remarks:
     *   退还额度类型 1-退还 2-不退还 3-不涉及
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.return_quota_type")
    public static final SqlColumn<Integer> returnQuotaType = licensePlateTaskVehicleDetail.returnQuotaType;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_type")
    public static final SqlColumn<Integer> quotaType = licensePlateTaskVehicleDetail.quotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_id")
    public static final SqlColumn<Integer> quotaAssetCompanyId = licensePlateTaskVehicleDetail.quotaAssetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_name")
    public static final SqlColumn<String> quotaAssetCompanyName = licensePlateTaskVehicleDetail.quotaAssetCompanyName;

    /**
     * Database Column Remarks:
     *   额度编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_number")
    public static final SqlColumn<String> quotaNumber = licensePlateTaskVehicleDetail.quotaNumber;

    /**
     * Database Column Remarks:
     *   额度单打印时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_print_date")
    public static final SqlColumn<Date> quotaPrintDate = licensePlateTaskVehicleDetail.quotaPrintDate;

    /**
     * Database Column Remarks:
     *   车辆类型 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_type_registration_card")
    public static final SqlColumn<Integer> vehicleTypeRegistrationCard = licensePlateTaskVehicleDetail.vehicleTypeRegistrationCard;

    /**
     * Database Column Remarks:
     *   使用性质 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_id_registration_card")
    public static final SqlColumn<Integer> usageIdRegistrationCard = licensePlateTaskVehicleDetail.usageIdRegistrationCard;

    /**
     * Database Column Remarks:
     *   注册日期(行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.registration_date_registration_card")
    public static final SqlColumn<Date> registrationDateRegistrationCard = licensePlateTaskVehicleDetail.registrationDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   发证日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.issuance_date_registration_card")
    public static final SqlColumn<Date> issuanceDateRegistrationCard = licensePlateTaskVehicleDetail.issuanceDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   档案编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.file_number")
    public static final SqlColumn<String> fileNumber = licensePlateTaskVehicleDetail.fileNumber;

    /**
     * Database Column Remarks:
     *   强制报废日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.retirement_date_registration_card")
    public static final SqlColumn<Date> retirementDateRegistrationCard = licensePlateTaskVehicleDetail.retirementDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   年检到期日 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.annual_inspection_due_date_registration_card")
    public static final SqlColumn<Date> annualInspectionDueDateRegistrationCard = licensePlateTaskVehicleDetail.annualInspectionDueDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.is_deleted")
    public static final SqlColumn<Integer> isDeleted = licensePlateTaskVehicleDetail.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_time")
    public static final SqlColumn<Date> createTime = licensePlateTaskVehicleDetail.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_id")
    public static final SqlColumn<Long> createOperId = licensePlateTaskVehicleDetail.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_name")
    public static final SqlColumn<String> createOperName = licensePlateTaskVehicleDetail.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_time")
    public static final SqlColumn<Date> updateTime = licensePlateTaskVehicleDetail.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_id")
    public static final SqlColumn<Long> updateOperId = licensePlateTaskVehicleDetail.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_name")
    public static final SqlColumn<String> updateOperName = licensePlateTaskVehicleDetail.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    public static final class LicensePlateTaskVehicleDetail extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNumber = column("task_number", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleModelName = column("vehicle_model_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<Long> ownOrganizationId = column("own_organization_id", JDBCType.BIGINT);

        public final SqlColumn<Long> usageOrganizationId = column("usage_organization_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> returnQuotaType = column("return_quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> quotaType = column("quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> quotaAssetCompanyId = column("quota_asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<String> quotaAssetCompanyName = column("quota_asset_company_name", JDBCType.VARCHAR);

        public final SqlColumn<String> quotaNumber = column("quota_number", JDBCType.VARCHAR);

        public final SqlColumn<Date> quotaPrintDate = column("quota_print_date", JDBCType.DATE);

        public final SqlColumn<Integer> vehicleTypeRegistrationCard = column("vehicle_type_registration_card", JDBCType.INTEGER);

        public final SqlColumn<Integer> usageIdRegistrationCard = column("usage_id_registration_card", JDBCType.INTEGER);

        public final SqlColumn<Date> registrationDateRegistrationCard = column("registration_date_registration_card", JDBCType.DATE);

        public final SqlColumn<Date> issuanceDateRegistrationCard = column("issuance_date_registration_card", JDBCType.DATE);

        public final SqlColumn<String> fileNumber = column("file_number", JDBCType.VARCHAR);

        public final SqlColumn<Date> retirementDateRegistrationCard = column("retirement_date_registration_card", JDBCType.DATE);

        public final SqlColumn<Date> annualInspectionDueDateRegistrationCard = column("annual_inspection_due_date_registration_card", JDBCType.DATE);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public LicensePlateTaskVehicleDetail() {
            super("t_license_plate_task_vehicle_detail");
        }
    }
}