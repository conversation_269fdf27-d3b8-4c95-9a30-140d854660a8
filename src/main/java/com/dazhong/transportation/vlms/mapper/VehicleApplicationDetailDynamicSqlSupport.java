package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleApplicationDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    public static final VehicleApplicationDetail vehicleApplicationDetail = new VehicleApplicationDetail();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.id")
    public static final SqlColumn<Long> id = vehicleApplicationDetail.id;

    /**
     * Database Column Remarks:
     *   申请单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.application_id")
    public static final SqlColumn<Long> applicationId = vehicleApplicationDetail.applicationId;

    /**
     * Database Column Remarks:
     *   资产编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_asset_id")
    public static final SqlColumn<String> vehicleAssetId = vehicleApplicationDetail.vehicleAssetId;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vin")
    public static final SqlColumn<String> vin = vehicleApplicationDetail.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.license_plate")
    public static final SqlColumn<String> licensePlate = vehicleApplicationDetail.licensePlate;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = vehicleApplicationDetail.vehicleModelId;

    /**
     * Database Column Remarks:
     *   资产所属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = vehicleApplicationDetail.assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产所属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name")
    public static final SqlColumn<String> assetCompanyName = vehicleApplicationDetail.assetCompanyName;

    /**
     * Database Column Remarks:
     *   资产所属公司id修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id_update")
    public static final SqlColumn<Integer> assetCompanyIdUpdate = vehicleApplicationDetail.assetCompanyIdUpdate;

    /**
     * Database Column Remarks:
     *   资产所属公司名修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name_update")
    public static final SqlColumn<String> assetCompanyNameUpdate = vehicleApplicationDetail.assetCompanyNameUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id")
    public static final SqlColumn<Long> ownOrganizationId = vehicleApplicationDetail.ownOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name")
    public static final SqlColumn<String> ownOrganizationName = vehicleApplicationDetail.ownOrganizationName;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）id修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id_update")
    public static final SqlColumn<Long> ownOrganizationIdUpdate = vehicleApplicationDetail.ownOrganizationIdUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）名修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name_update")
    public static final SqlColumn<String> ownOrganizationNameUpdate = vehicleApplicationDetail.ownOrganizationNameUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id")
    public static final SqlColumn<Long> usageOrganizationId = vehicleApplicationDetail.usageOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name")
    public static final SqlColumn<String> usageOrganizationName = vehicleApplicationDetail.usageOrganizationName;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）id修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id_update")
    public static final SqlColumn<Long> usageOrganizationIdUpdate = vehicleApplicationDetail.usageOrganizationIdUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）名修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name_update")
    public static final SqlColumn<String> usageOrganizationNameUpdate = vehicleApplicationDetail.usageOrganizationNameUpdate;

    /**
     * Database Column Remarks:
     *   产品线 1-巡网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line")
    public static final SqlColumn<Integer> productLine = vehicleApplicationDetail.productLine;

    /**
     * Database Column Remarks:
     *   产品线修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line_update")
    public static final SqlColumn<Integer> productLineUpdate = vehicleApplicationDetail.productLineUpdate;

    /**
     * Database Column Remarks:
     *   业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line")
    public static final SqlColumn<Integer> businessLine = vehicleApplicationDetail.businessLine;

    /**
     * Database Column Remarks:
     *   业务线修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line_update")
    public static final SqlColumn<Integer> businessLineUpdate = vehicleApplicationDetail.businessLineUpdate;

    /**
     * Database Column Remarks:
     *   运营状态 1-待运 2-租出
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.operating_status")
    public static final SqlColumn<Integer> operatingStatus = vehicleApplicationDetail.operatingStatus;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleApplicationDetail.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_time")
    public static final SqlColumn<Date> createTime = vehicleApplicationDetail.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleApplicationDetail.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleApplicationDetail.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_time")
    public static final SqlColumn<Date> updateTime = vehicleApplicationDetail.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleApplicationDetail.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleApplicationDetail.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    public static final class VehicleApplicationDetail extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> applicationId = column("application_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleAssetId = column("vehicle_asset_id", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<String> assetCompanyName = column("asset_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> assetCompanyIdUpdate = column("asset_company_id_update", JDBCType.INTEGER);

        public final SqlColumn<String> assetCompanyNameUpdate = column("asset_company_name_update", JDBCType.VARCHAR);

        public final SqlColumn<Long> ownOrganizationId = column("own_organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> ownOrganizationName = column("own_organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> ownOrganizationIdUpdate = column("own_organization_id_update", JDBCType.BIGINT);

        public final SqlColumn<String> ownOrganizationNameUpdate = column("own_organization_name_update", JDBCType.VARCHAR);

        public final SqlColumn<Long> usageOrganizationId = column("usage_organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> usageOrganizationName = column("usage_organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> usageOrganizationIdUpdate = column("usage_organization_id_update", JDBCType.BIGINT);

        public final SqlColumn<String> usageOrganizationNameUpdate = column("usage_organization_name_update", JDBCType.VARCHAR);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> productLineUpdate = column("product_line_update", JDBCType.INTEGER);

        public final SqlColumn<Integer> businessLine = column("business_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> businessLineUpdate = column("business_line_update", JDBCType.INTEGER);

        public final SqlColumn<Integer> operatingStatus = column("operating_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleApplicationDetail() {
            super("t_vehicle_application_detail");
        }
    }
}