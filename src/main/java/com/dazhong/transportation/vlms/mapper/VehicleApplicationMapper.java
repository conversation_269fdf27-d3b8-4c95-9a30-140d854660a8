package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.dto.VehicleApplicationListDto;
import com.dazhong.transportation.vlms.model.VehicleApplication;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import javax.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleApplicationDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface VehicleApplicationMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    BasicColumn[] selectList = BasicColumn.columnList(id, dingTalkNo, applicationType, documentTitle, documentStatus, originatorDeptId, originatorDeptName, transferFromDepartmentCode, transferFromDepartmentName, transferToDepartmentCode, transferToDepartmentName, organizationId, organizationName, ownerId, ownerName, submitDate, productLine, costBearer, contractNumber, updateReason, remark, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleApplication> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleApplicationResult")
    Optional<VehicleApplication> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleApplicationResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="application_type", property="applicationType", jdbcType=JdbcType.INTEGER),
        @Result(column="document_title", property="documentTitle", jdbcType=JdbcType.VARCHAR),
        @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="originator_dept_id", property="originatorDeptId", jdbcType=JdbcType.BIGINT),
        @Result(column="originator_dept_name", property="originatorDeptName", jdbcType=JdbcType.VARCHAR),
        @Result(column="transfer_from_department_code", property="transferFromDepartmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="transfer_from_department_name", property="transferFromDepartmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="transfer_to_department_code", property="transferToDepartmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="transfer_to_department_name", property="transferToDepartmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="organization_id", property="organizationId", jdbcType=JdbcType.BIGINT),
        @Result(column="organization_name", property="organizationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_name", property="ownerName", jdbcType=JdbcType.VARCHAR),
        @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="cost_bearer", property="costBearer", jdbcType=JdbcType.VARCHAR),
        @Result(column="contract_number", property="contractNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_reason", property="updateReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleApplication> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleApplication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleApplication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default int insert(VehicleApplication record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleApplication, c ->
            c.map(dingTalkNo).toProperty("dingTalkNo")
            .map(applicationType).toProperty("applicationType")
            .map(documentTitle).toProperty("documentTitle")
            .map(documentStatus).toProperty("documentStatus")
            .map(originatorDeptId).toProperty("originatorDeptId")
            .map(originatorDeptName).toProperty("originatorDeptName")
            .map(transferFromDepartmentCode).toProperty("transferFromDepartmentCode")
            .map(transferFromDepartmentName).toProperty("transferFromDepartmentName")
            .map(transferToDepartmentCode).toProperty("transferToDepartmentCode")
            .map(transferToDepartmentName).toProperty("transferToDepartmentName")
            .map(organizationId).toProperty("organizationId")
            .map(organizationName).toProperty("organizationName")
            .map(ownerId).toProperty("ownerId")
            .map(ownerName).toProperty("ownerName")
            .map(submitDate).toProperty("submitDate")
            .map(productLine).toProperty("productLine")
            .map(costBearer).toProperty("costBearer")
            .map(contractNumber).toProperty("contractNumber")
            .map(updateReason).toProperty("updateReason")
            .map(remark).toProperty("remark")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default int insertSelective(VehicleApplication record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleApplication, c ->
            c.map(dingTalkNo).toPropertyWhenPresent("dingTalkNo", record::getDingTalkNo)
            .map(applicationType).toPropertyWhenPresent("applicationType", record::getApplicationType)
            .map(documentTitle).toPropertyWhenPresent("documentTitle", record::getDocumentTitle)
            .map(documentStatus).toPropertyWhenPresent("documentStatus", record::getDocumentStatus)
            .map(originatorDeptId).toPropertyWhenPresent("originatorDeptId", record::getOriginatorDeptId)
            .map(originatorDeptName).toPropertyWhenPresent("originatorDeptName", record::getOriginatorDeptName)
            .map(transferFromDepartmentCode).toPropertyWhenPresent("transferFromDepartmentCode", record::getTransferFromDepartmentCode)
            .map(transferFromDepartmentName).toPropertyWhenPresent("transferFromDepartmentName", record::getTransferFromDepartmentName)
            .map(transferToDepartmentCode).toPropertyWhenPresent("transferToDepartmentCode", record::getTransferToDepartmentCode)
            .map(transferToDepartmentName).toPropertyWhenPresent("transferToDepartmentName", record::getTransferToDepartmentName)
            .map(organizationId).toPropertyWhenPresent("organizationId", record::getOrganizationId)
            .map(organizationName).toPropertyWhenPresent("organizationName", record::getOrganizationName)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(ownerName).toPropertyWhenPresent("ownerName", record::getOwnerName)
            .map(submitDate).toPropertyWhenPresent("submitDate", record::getSubmitDate)
            .map(productLine).toPropertyWhenPresent("productLine", record::getProductLine)
            .map(costBearer).toPropertyWhenPresent("costBearer", record::getCostBearer)
            .map(contractNumber).toPropertyWhenPresent("contractNumber", record::getContractNumber)
            .map(updateReason).toPropertyWhenPresent("updateReason", record::getUpdateReason)
            .map(remark).toPropertyWhenPresent("remark", record::getRemark)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default Optional<VehicleApplication> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleApplication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default List<VehicleApplication> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleApplication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default List<VehicleApplication> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleApplication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default Optional<VehicleApplication> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleApplication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleApplication record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dingTalkNo).equalTo(record::getDingTalkNo)
                .set(applicationType).equalTo(record::getApplicationType)
                .set(documentTitle).equalTo(record::getDocumentTitle)
                .set(documentStatus).equalTo(record::getDocumentStatus)
                .set(originatorDeptId).equalTo(record::getOriginatorDeptId)
                .set(originatorDeptName).equalTo(record::getOriginatorDeptName)
                .set(transferFromDepartmentCode).equalTo(record::getTransferFromDepartmentCode)
                .set(transferFromDepartmentName).equalTo(record::getTransferFromDepartmentName)
                .set(transferToDepartmentCode).equalTo(record::getTransferToDepartmentCode)
                .set(transferToDepartmentName).equalTo(record::getTransferToDepartmentName)
                .set(organizationId).equalTo(record::getOrganizationId)
                .set(organizationName).equalTo(record::getOrganizationName)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(ownerName).equalTo(record::getOwnerName)
                .set(submitDate).equalTo(record::getSubmitDate)
                .set(productLine).equalTo(record::getProductLine)
                .set(costBearer).equalTo(record::getCostBearer)
                .set(contractNumber).equalTo(record::getContractNumber)
                .set(updateReason).equalTo(record::getUpdateReason)
                .set(remark).equalTo(record::getRemark)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleApplication record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dingTalkNo).equalToWhenPresent(record::getDingTalkNo)
                .set(applicationType).equalToWhenPresent(record::getApplicationType)
                .set(documentTitle).equalToWhenPresent(record::getDocumentTitle)
                .set(documentStatus).equalToWhenPresent(record::getDocumentStatus)
                .set(originatorDeptId).equalToWhenPresent(record::getOriginatorDeptId)
                .set(originatorDeptName).equalToWhenPresent(record::getOriginatorDeptName)
                .set(transferFromDepartmentCode).equalToWhenPresent(record::getTransferFromDepartmentCode)
                .set(transferFromDepartmentName).equalToWhenPresent(record::getTransferFromDepartmentName)
                .set(transferToDepartmentCode).equalToWhenPresent(record::getTransferToDepartmentCode)
                .set(transferToDepartmentName).equalToWhenPresent(record::getTransferToDepartmentName)
                .set(organizationId).equalToWhenPresent(record::getOrganizationId)
                .set(organizationName).equalToWhenPresent(record::getOrganizationName)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(ownerName).equalToWhenPresent(record::getOwnerName)
                .set(submitDate).equalToWhenPresent(record::getSubmitDate)
                .set(productLine).equalToWhenPresent(record::getProductLine)
                .set(costBearer).equalToWhenPresent(record::getCostBearer)
                .set(contractNumber).equalToWhenPresent(record::getContractNumber)
                .set(updateReason).equalToWhenPresent(record::getUpdateReason)
                .set(remark).equalToWhenPresent(record::getRemark)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default int updateByPrimaryKey(VehicleApplication record) {
        return update(c ->
            c.set(dingTalkNo).equalTo(record::getDingTalkNo)
            .set(applicationType).equalTo(record::getApplicationType)
            .set(documentTitle).equalTo(record::getDocumentTitle)
            .set(documentStatus).equalTo(record::getDocumentStatus)
            .set(originatorDeptId).equalTo(record::getOriginatorDeptId)
            .set(originatorDeptName).equalTo(record::getOriginatorDeptName)
            .set(transferFromDepartmentCode).equalTo(record::getTransferFromDepartmentCode)
            .set(transferFromDepartmentName).equalTo(record::getTransferFromDepartmentName)
            .set(transferToDepartmentCode).equalTo(record::getTransferToDepartmentCode)
            .set(transferToDepartmentName).equalTo(record::getTransferToDepartmentName)
            .set(organizationId).equalTo(record::getOrganizationId)
            .set(organizationName).equalTo(record::getOrganizationName)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(ownerName).equalTo(record::getOwnerName)
            .set(submitDate).equalTo(record::getSubmitDate)
            .set(productLine).equalTo(record::getProductLine)
            .set(costBearer).equalTo(record::getCostBearer)
            .set(contractNumber).equalTo(record::getContractNumber)
            .set(updateReason).equalTo(record::getUpdateReason)
            .set(remark).equalTo(record::getRemark)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    default int updateByPrimaryKeySelective(VehicleApplication record) {
        return update(c ->
            c.set(dingTalkNo).equalToWhenPresent(record::getDingTalkNo)
            .set(applicationType).equalToWhenPresent(record::getApplicationType)
            .set(documentTitle).equalToWhenPresent(record::getDocumentTitle)
            .set(documentStatus).equalToWhenPresent(record::getDocumentStatus)
            .set(originatorDeptId).equalToWhenPresent(record::getOriginatorDeptId)
            .set(originatorDeptName).equalToWhenPresent(record::getOriginatorDeptName)
            .set(transferFromDepartmentCode).equalToWhenPresent(record::getTransferFromDepartmentCode)
            .set(transferFromDepartmentName).equalToWhenPresent(record::getTransferFromDepartmentName)
            .set(transferToDepartmentCode).equalToWhenPresent(record::getTransferToDepartmentCode)
            .set(transferToDepartmentName).equalToWhenPresent(record::getTransferToDepartmentName)
            .set(organizationId).equalToWhenPresent(record::getOrganizationId)
            .set(organizationName).equalToWhenPresent(record::getOrganizationName)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(ownerName).equalToWhenPresent(record::getOwnerName)
            .set(submitDate).equalToWhenPresent(record::getSubmitDate)
            .set(productLine).equalToWhenPresent(record::getProductLine)
            .set(costBearer).equalToWhenPresent(record::getCostBearer)
            .set(contractNumber).equalToWhenPresent(record::getContractNumber)
            .set(updateReason).equalToWhenPresent(record::getUpdateReason)
            .set(remark).equalToWhenPresent(record::getRemark)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleApplicationListResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
            @Result(column="application_type", property="applicationType", jdbcType=JdbcType.INTEGER),
            @Result(column="document_title", property="documentTitle", jdbcType=JdbcType.VARCHAR),
            @Result(column="document_status", property="documentStatus", jdbcType=JdbcType.INTEGER),
            @Result(column="ding_talk_no", property="dingTalkNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.DATE),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
    })
    List<VehicleApplicationListDto> selectVehicleApplicationList(SelectStatementProvider selectStatement);

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_vehicle_application")
    default Optional<VehicleApplication> selectByDingTalkNo(String dingTalkNo_) {
        return selectOne(c ->
                c.where(dingTalkNo, isEqualTo(dingTalkNo_))
        );
    }
}