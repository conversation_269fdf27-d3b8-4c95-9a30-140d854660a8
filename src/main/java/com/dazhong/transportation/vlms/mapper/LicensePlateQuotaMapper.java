package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaResponse;
import com.dazhong.transportation.vlms.model.LicensePlateQuota;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;

import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

@Mapper
public interface LicensePlateQuotaMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<LicensePlateQuota> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    BasicColumn[] selectList = BasicColumn.columnList(id, assetCompanyId, quotaType, quota, preOccupied, occupied, remaining, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<LicensePlateQuota> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LicensePlateQuotaResult")
    Optional<LicensePlateQuota> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LicensePlateQuotaResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="quota", property="quota", jdbcType=JdbcType.INTEGER),
        @Result(column="pre_occupied", property="preOccupied", jdbcType=JdbcType.INTEGER),
        @Result(column="occupied", property="occupied", jdbcType=JdbcType.INTEGER),
        @Result(column="remaining", property="remaining", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<LicensePlateQuota> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, licensePlateQuota, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, licensePlateQuota, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default int insert(LicensePlateQuota record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateQuota, c ->
            c.map(assetCompanyId).toProperty("assetCompanyId")
            .map(quotaType).toProperty("quotaType")
            .map(quota).toProperty("quota")
            .map(preOccupied).toProperty("preOccupied")
            .map(occupied).toProperty("occupied")
            .map(remaining).toProperty("remaining")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default int insertSelective(LicensePlateQuota record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateQuota, c ->
            c.map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(quota).toPropertyWhenPresent("quota", record::getQuota)
            .map(preOccupied).toPropertyWhenPresent("preOccupied", record::getPreOccupied)
            .map(occupied).toPropertyWhenPresent("occupied", record::getOccupied)
            .map(remaining).toPropertyWhenPresent("remaining", record::getRemaining)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default Optional<LicensePlateQuota> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, licensePlateQuota, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default List<LicensePlateQuota> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, licensePlateQuota, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default List<LicensePlateQuota> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, licensePlateQuota, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default Optional<LicensePlateQuota> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, licensePlateQuota, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    static UpdateDSL<UpdateModel> updateAllColumns(LicensePlateQuota record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(quota).equalTo(record::getQuota)
                .set(preOccupied).equalTo(record::getPreOccupied)
                .set(occupied).equalTo(record::getOccupied)
                .set(remaining).equalTo(record::getRemaining)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LicensePlateQuota record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(quota).equalToWhenPresent(record::getQuota)
                .set(preOccupied).equalToWhenPresent(record::getPreOccupied)
                .set(occupied).equalToWhenPresent(record::getOccupied)
                .set(remaining).equalToWhenPresent(record::getRemaining)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default int updateByPrimaryKey(LicensePlateQuota record) {
        return update(c ->
            c.set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(quota).equalTo(record::getQuota)
            .set(preOccupied).equalTo(record::getPreOccupied)
            .set(occupied).equalTo(record::getOccupied)
            .set(remaining).equalTo(record::getRemaining)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    default int updateByPrimaryKeySelective(LicensePlateQuota record) {
        return update(c ->
            c.set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(quota).equalToWhenPresent(record::getQuota)
            .set(preOccupied).equalToWhenPresent(record::getPreOccupied)
            .set(occupied).equalToWhenPresent(record::getOccupied)
            .set(remaining).equalToWhenPresent(record::getRemaining)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LicensePlateQuotaResponseResult", value = {
            @Result(column="quota", property="quota", jdbcType=JdbcType.INTEGER),
            @Result(column="preOccupied", property="preOccupied", jdbcType=JdbcType.INTEGER),
            @Result(column="occupied", property="occupied", jdbcType=JdbcType.INTEGER),
            @Result(column="remaining", property="remaining", jdbcType=JdbcType.INTEGER),
    })
    LicensePlateQuotaResponse selectLicensePlateQuotaResponse(SelectStatementProvider selectStatement);
}