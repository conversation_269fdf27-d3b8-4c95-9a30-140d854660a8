package com.dazhong.transportation.vlms.mapper.extend;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.mapper.VehicleDeviceInfoMapper;

@Mapper
public interface VehicleDeviceInfoExtendMapper extends VehicleDeviceInfoMapper {


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SearchVehicleDeviceListResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="device_type", property="deviceType", jdbcType=JdbcType.VARCHAR),
            @Result(column="device_seq", property="deviceSeq", jdbcType=JdbcType.VARCHAR),
            @Result(column="device_brand", property="deviceBrand", jdbcType=JdbcType.VARCHAR),
            @Result(column="device_model", property="deviceModel", jdbcType=JdbcType.VARCHAR),
            @Result(column="sim_card_number", property="simCardNumber", jdbcType=JdbcType.VARCHAR),
            @Result(column="install_time", property="installTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="install_location", property="installLocation", jdbcType=JdbcType.VARCHAR),
            @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
            @Result(column="activation_time", property="activationTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="expiration_time", property="expirationTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="latest_location_time", property="latestLocationTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="longitude", property="longitude", jdbcType=JdbcType.DECIMAL),
            @Result(column="latitude", property="latitude", jdbcType=JdbcType.DECIMAL),
            @Result(column="latest_location", property="latestLocation", jdbcType=JdbcType.VARCHAR),
            @Result(column="latest_mileage_update_time", property="latestMileageUpdateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="total_mileage", property="totalMileage", jdbcType=JdbcType.DECIMAL),
            @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
    })
    List<VehicleDeviceInfoResponse> searchVehicleDeviceList(SelectStatementProvider selectStatement);
}
