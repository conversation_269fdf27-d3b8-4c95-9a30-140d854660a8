package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseIntentionDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehiclePurchaseIntention;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehiclePurchaseIntentionMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    BasicColumn[] selectList = BasicColumn.columnList(id, intentionNo, contractNo, contractStartDate, contractEndDate, customerUser, saleName, vehicleModelName, vehicleModelId, quantity, guidePrice, vehicleBodyColor, vehicleInteriorColor, expectedArrivalDate, expectedArrivalLastDate, licensePlateAttribute, licensePlateLocation, decorationDemand, dataSyncTime, applyStatus, purchaseApplyId, orgCode, orgName, orgId, purchaseRemark, dingTalkId, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehiclePurchaseIntention> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehiclePurchaseIntentionResult")
    Optional<VehiclePurchaseIntention> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehiclePurchaseIntentionResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="intention_no", property="intentionNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="contract_no", property="contractNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="contract_start_date", property="contractStartDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="contract_end_date", property="contractEndDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="customer_user", property="customerUser", jdbcType=JdbcType.VARCHAR),
        @Result(column="sale_name", property="saleName", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_id", property="vehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="quantity", property="quantity", jdbcType=JdbcType.INTEGER),
        @Result(column="guide_price", property="guidePrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_body_color", property="vehicleBodyColor", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_interior_color", property="vehicleInteriorColor", jdbcType=JdbcType.VARCHAR),
        @Result(column="expected_arrival_date", property="expectedArrivalDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="expected_arrival_last_date", property="expectedArrivalLastDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="license_plate_attribute", property="licensePlateAttribute", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate_location", property="licensePlateLocation", jdbcType=JdbcType.VARCHAR),
        @Result(column="decoration_demand", property="decorationDemand", jdbcType=JdbcType.VARCHAR),
        @Result(column="data_sync_time", property="dataSyncTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="apply_status", property="applyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="purchase_apply_id", property="purchaseApplyId", jdbcType=JdbcType.BIGINT),
        @Result(column="org_code", property="orgCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_id", property="orgId", jdbcType=JdbcType.BIGINT),
        @Result(column="purchase_remark", property="purchaseRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="ding_talk_id", property="dingTalkId", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehiclePurchaseIntention> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehiclePurchaseIntention, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehiclePurchaseIntention, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default int insert(VehiclePurchaseIntention record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseIntention, c ->
            c.map(intentionNo).toProperty("intentionNo")
            .map(contractNo).toProperty("contractNo")
            .map(contractStartDate).toProperty("contractStartDate")
            .map(contractEndDate).toProperty("contractEndDate")
            .map(customerUser).toProperty("customerUser")
            .map(saleName).toProperty("saleName")
            .map(vehicleModelName).toProperty("vehicleModelName")
            .map(vehicleModelId).toProperty("vehicleModelId")
            .map(quantity).toProperty("quantity")
            .map(guidePrice).toProperty("guidePrice")
            .map(vehicleBodyColor).toProperty("vehicleBodyColor")
            .map(vehicleInteriorColor).toProperty("vehicleInteriorColor")
            .map(expectedArrivalDate).toProperty("expectedArrivalDate")
            .map(expectedArrivalLastDate).toProperty("expectedArrivalLastDate")
            .map(licensePlateAttribute).toProperty("licensePlateAttribute")
            .map(licensePlateLocation).toProperty("licensePlateLocation")
            .map(decorationDemand).toProperty("decorationDemand")
            .map(dataSyncTime).toProperty("dataSyncTime")
            .map(applyStatus).toProperty("applyStatus")
            .map(purchaseApplyId).toProperty("purchaseApplyId")
            .map(orgCode).toProperty("orgCode")
            .map(orgName).toProperty("orgName")
            .map(orgId).toProperty("orgId")
            .map(purchaseRemark).toProperty("purchaseRemark")
            .map(dingTalkId).toProperty("dingTalkId")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default int insertSelective(VehiclePurchaseIntention record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseIntention, c ->
            c.map(intentionNo).toPropertyWhenPresent("intentionNo", record::getIntentionNo)
            .map(contractNo).toPropertyWhenPresent("contractNo", record::getContractNo)
            .map(contractStartDate).toPropertyWhenPresent("contractStartDate", record::getContractStartDate)
            .map(contractEndDate).toPropertyWhenPresent("contractEndDate", record::getContractEndDate)
            .map(customerUser).toPropertyWhenPresent("customerUser", record::getCustomerUser)
            .map(saleName).toPropertyWhenPresent("saleName", record::getSaleName)
            .map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", record::getVehicleModelName)
            .map(vehicleModelId).toPropertyWhenPresent("vehicleModelId", record::getVehicleModelId)
            .map(quantity).toPropertyWhenPresent("quantity", record::getQuantity)
            .map(guidePrice).toPropertyWhenPresent("guidePrice", record::getGuidePrice)
            .map(vehicleBodyColor).toPropertyWhenPresent("vehicleBodyColor", record::getVehicleBodyColor)
            .map(vehicleInteriorColor).toPropertyWhenPresent("vehicleInteriorColor", record::getVehicleInteriorColor)
            .map(expectedArrivalDate).toPropertyWhenPresent("expectedArrivalDate", record::getExpectedArrivalDate)
            .map(expectedArrivalLastDate).toPropertyWhenPresent("expectedArrivalLastDate", record::getExpectedArrivalLastDate)
            .map(licensePlateAttribute).toPropertyWhenPresent("licensePlateAttribute", record::getLicensePlateAttribute)
            .map(licensePlateLocation).toPropertyWhenPresent("licensePlateLocation", record::getLicensePlateLocation)
            .map(decorationDemand).toPropertyWhenPresent("decorationDemand", record::getDecorationDemand)
            .map(dataSyncTime).toPropertyWhenPresent("dataSyncTime", record::getDataSyncTime)
            .map(applyStatus).toPropertyWhenPresent("applyStatus", record::getApplyStatus)
            .map(purchaseApplyId).toPropertyWhenPresent("purchaseApplyId", record::getPurchaseApplyId)
            .map(orgCode).toPropertyWhenPresent("orgCode", record::getOrgCode)
            .map(orgName).toPropertyWhenPresent("orgName", record::getOrgName)
            .map(orgId).toPropertyWhenPresent("orgId", record::getOrgId)
            .map(purchaseRemark).toPropertyWhenPresent("purchaseRemark", record::getPurchaseRemark)
            .map(dingTalkId).toPropertyWhenPresent("dingTalkId", record::getDingTalkId)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default Optional<VehiclePurchaseIntention> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehiclePurchaseIntention, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default List<VehiclePurchaseIntention> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehiclePurchaseIntention, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default List<VehiclePurchaseIntention> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehiclePurchaseIntention, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default Optional<VehiclePurchaseIntention> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehiclePurchaseIntention, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    static UpdateDSL<UpdateModel> updateAllColumns(VehiclePurchaseIntention record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(intentionNo).equalTo(record::getIntentionNo)
                .set(contractNo).equalTo(record::getContractNo)
                .set(contractStartDate).equalTo(record::getContractStartDate)
                .set(contractEndDate).equalTo(record::getContractEndDate)
                .set(customerUser).equalTo(record::getCustomerUser)
                .set(saleName).equalTo(record::getSaleName)
                .set(vehicleModelName).equalTo(record::getVehicleModelName)
                .set(vehicleModelId).equalTo(record::getVehicleModelId)
                .set(quantity).equalTo(record::getQuantity)
                .set(guidePrice).equalTo(record::getGuidePrice)
                .set(vehicleBodyColor).equalTo(record::getVehicleBodyColor)
                .set(vehicleInteriorColor).equalTo(record::getVehicleInteriorColor)
                .set(expectedArrivalDate).equalTo(record::getExpectedArrivalDate)
                .set(expectedArrivalLastDate).equalTo(record::getExpectedArrivalLastDate)
                .set(licensePlateAttribute).equalTo(record::getLicensePlateAttribute)
                .set(licensePlateLocation).equalTo(record::getLicensePlateLocation)
                .set(decorationDemand).equalTo(record::getDecorationDemand)
                .set(dataSyncTime).equalTo(record::getDataSyncTime)
                .set(applyStatus).equalTo(record::getApplyStatus)
                .set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
                .set(orgCode).equalTo(record::getOrgCode)
                .set(orgName).equalTo(record::getOrgName)
                .set(orgId).equalTo(record::getOrgId)
                .set(purchaseRemark).equalTo(record::getPurchaseRemark)
                .set(dingTalkId).equalTo(record::getDingTalkId)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehiclePurchaseIntention record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(intentionNo).equalToWhenPresent(record::getIntentionNo)
                .set(contractNo).equalToWhenPresent(record::getContractNo)
                .set(contractStartDate).equalToWhenPresent(record::getContractStartDate)
                .set(contractEndDate).equalToWhenPresent(record::getContractEndDate)
                .set(customerUser).equalToWhenPresent(record::getCustomerUser)
                .set(saleName).equalToWhenPresent(record::getSaleName)
                .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
                .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
                .set(quantity).equalToWhenPresent(record::getQuantity)
                .set(guidePrice).equalToWhenPresent(record::getGuidePrice)
                .set(vehicleBodyColor).equalToWhenPresent(record::getVehicleBodyColor)
                .set(vehicleInteriorColor).equalToWhenPresent(record::getVehicleInteriorColor)
                .set(expectedArrivalDate).equalToWhenPresent(record::getExpectedArrivalDate)
                .set(expectedArrivalLastDate).equalToWhenPresent(record::getExpectedArrivalLastDate)
                .set(licensePlateAttribute).equalToWhenPresent(record::getLicensePlateAttribute)
                .set(licensePlateLocation).equalToWhenPresent(record::getLicensePlateLocation)
                .set(decorationDemand).equalToWhenPresent(record::getDecorationDemand)
                .set(dataSyncTime).equalToWhenPresent(record::getDataSyncTime)
                .set(applyStatus).equalToWhenPresent(record::getApplyStatus)
                .set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
                .set(orgCode).equalToWhenPresent(record::getOrgCode)
                .set(orgName).equalToWhenPresent(record::getOrgName)
                .set(orgId).equalToWhenPresent(record::getOrgId)
                .set(purchaseRemark).equalToWhenPresent(record::getPurchaseRemark)
                .set(dingTalkId).equalToWhenPresent(record::getDingTalkId)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default int updateByPrimaryKey(VehiclePurchaseIntention record) {
        return update(c ->
            c.set(intentionNo).equalTo(record::getIntentionNo)
            .set(contractNo).equalTo(record::getContractNo)
            .set(contractStartDate).equalTo(record::getContractStartDate)
            .set(contractEndDate).equalTo(record::getContractEndDate)
            .set(customerUser).equalTo(record::getCustomerUser)
            .set(saleName).equalTo(record::getSaleName)
            .set(vehicleModelName).equalTo(record::getVehicleModelName)
            .set(vehicleModelId).equalTo(record::getVehicleModelId)
            .set(quantity).equalTo(record::getQuantity)
            .set(guidePrice).equalTo(record::getGuidePrice)
            .set(vehicleBodyColor).equalTo(record::getVehicleBodyColor)
            .set(vehicleInteriorColor).equalTo(record::getVehicleInteriorColor)
            .set(expectedArrivalDate).equalTo(record::getExpectedArrivalDate)
            .set(expectedArrivalLastDate).equalTo(record::getExpectedArrivalLastDate)
            .set(licensePlateAttribute).equalTo(record::getLicensePlateAttribute)
            .set(licensePlateLocation).equalTo(record::getLicensePlateLocation)
            .set(decorationDemand).equalTo(record::getDecorationDemand)
            .set(dataSyncTime).equalTo(record::getDataSyncTime)
            .set(applyStatus).equalTo(record::getApplyStatus)
            .set(purchaseApplyId).equalTo(record::getPurchaseApplyId)
            .set(orgCode).equalTo(record::getOrgCode)
            .set(orgName).equalTo(record::getOrgName)
            .set(orgId).equalTo(record::getOrgId)
            .set(purchaseRemark).equalTo(record::getPurchaseRemark)
            .set(dingTalkId).equalTo(record::getDingTalkId)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    default int updateByPrimaryKeySelective(VehiclePurchaseIntention record) {
        return update(c ->
            c.set(intentionNo).equalToWhenPresent(record::getIntentionNo)
            .set(contractNo).equalToWhenPresent(record::getContractNo)
            .set(contractStartDate).equalToWhenPresent(record::getContractStartDate)
            .set(contractEndDate).equalToWhenPresent(record::getContractEndDate)
            .set(customerUser).equalToWhenPresent(record::getCustomerUser)
            .set(saleName).equalToWhenPresent(record::getSaleName)
            .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
            .set(vehicleModelId).equalToWhenPresent(record::getVehicleModelId)
            .set(quantity).equalToWhenPresent(record::getQuantity)
            .set(guidePrice).equalToWhenPresent(record::getGuidePrice)
            .set(vehicleBodyColor).equalToWhenPresent(record::getVehicleBodyColor)
            .set(vehicleInteriorColor).equalToWhenPresent(record::getVehicleInteriorColor)
            .set(expectedArrivalDate).equalToWhenPresent(record::getExpectedArrivalDate)
            .set(expectedArrivalLastDate).equalToWhenPresent(record::getExpectedArrivalLastDate)
            .set(licensePlateAttribute).equalToWhenPresent(record::getLicensePlateAttribute)
            .set(licensePlateLocation).equalToWhenPresent(record::getLicensePlateLocation)
            .set(decorationDemand).equalToWhenPresent(record::getDecorationDemand)
            .set(dataSyncTime).equalToWhenPresent(record::getDataSyncTime)
            .set(applyStatus).equalToWhenPresent(record::getApplyStatus)
            .set(purchaseApplyId).equalToWhenPresent(record::getPurchaseApplyId)
            .set(orgCode).equalToWhenPresent(record::getOrgCode)
            .set(orgName).equalToWhenPresent(record::getOrgName)
            .set(orgId).equalToWhenPresent(record::getOrgId)
            .set(purchaseRemark).equalToWhenPresent(record::getPurchaseRemark)
            .set(dingTalkId).equalToWhenPresent(record::getDingTalkId)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}