package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class ResourceInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    public static final ResourceInfo resourceInfo = new ResourceInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.id")
    public static final SqlColumn<Long> id = resourceInfo.id;

    /**
     * Database Column Remarks:
     *   资源key(推荐uuid)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_key")
    public static final SqlColumn<String> resourceKey = resourceInfo.resourceKey;

    /**
     * Database Column Remarks:
     *   资源名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_name")
    public static final SqlColumn<String> resourceName = resourceInfo.resourceName;

    /**
     * Database Column Remarks:
     *   资源code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_code")
    public static final SqlColumn<String> resourceCode = resourceInfo.resourceCode;

    /**
     * Database Column Remarks:
     *   资源url
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_url")
    public static final SqlColumn<String> resourceUrl = resourceInfo.resourceUrl;

    /**
     * Database Column Remarks:
     *   资源类型 0-系统 1-静态资源 2-功能点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_type")
    public static final SqlColumn<Integer> resourceType = resourceInfo.resourceType;

    /**
     * Database Column Remarks:
     *   资源图标url
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_icon_url")
    public static final SqlColumn<String> resourceIconUrl = resourceInfo.resourceIconUrl;

    /**
     * Database Column Remarks:
     *   父资源id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.parent_resource_id")
    public static final SqlColumn<Long> parentResourceId = resourceInfo.parentResourceId;

    /**
     * Database Column Remarks:
     *   系统编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.system_code")
    public static final SqlColumn<String> systemCode = resourceInfo.systemCode;

    /**
     * Database Column Remarks:
     *   排序标记
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.sort")
    public static final SqlColumn<Integer> sort = resourceInfo.sort;

    /**
     * Database Column Remarks:
     *   菜单是否隐藏 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide")
    public static final SqlColumn<Integer> hide = resourceInfo.hide;

    /**
     * Database Column Remarks:
     *   标题是否隐藏 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide_title")
    public static final SqlColumn<Integer> hideTitle = resourceInfo.hideTitle;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.misc_desc")
    public static final SqlColumn<String> miscDesc = resourceInfo.miscDesc;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = resourceInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_time")
    public static final SqlColumn<Date> createTime = resourceInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = resourceInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_name")
    public static final SqlColumn<String> createOperName = resourceInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_time")
    public static final SqlColumn<Date> updateTime = resourceInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = resourceInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = resourceInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    public static final class ResourceInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> resourceKey = column("resource_key", JDBCType.VARCHAR);

        public final SqlColumn<String> resourceName = column("resource_name", JDBCType.VARCHAR);

        public final SqlColumn<String> resourceCode = column("resource_code", JDBCType.VARCHAR);

        public final SqlColumn<String> resourceUrl = column("resource_url", JDBCType.VARCHAR);

        public final SqlColumn<Integer> resourceType = column("resource_type", JDBCType.INTEGER);

        public final SqlColumn<String> resourceIconUrl = column("resource_icon_url", JDBCType.VARCHAR);

        public final SqlColumn<Long> parentResourceId = column("parent_resource_id", JDBCType.BIGINT);

        public final SqlColumn<String> systemCode = column("system_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> sort = column("sort", JDBCType.INTEGER);

        public final SqlColumn<Integer> hide = column("hide", JDBCType.INTEGER);

        public final SqlColumn<Integer> hideTitle = column("hide_title", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_desc", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public ResourceInfo() {
            super("t_resource_info");
        }
    }
}