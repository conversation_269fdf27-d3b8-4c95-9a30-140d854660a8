package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleDisposalDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    public static final VehicleDisposalDetail vehicleDisposalDetail = new VehicleDisposalDetail();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.id")
    public static final SqlColumn<Long> id = vehicleDisposalDetail.id;

    /**
     * Database Column Remarks:
     *   处置单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_id")
    public static final SqlColumn<Long> disposalId = vehicleDisposalDetail.disposalId;

    /**
     * Database Column Remarks:
     *   资产编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_asset_id")
    public static final SqlColumn<String> vehicleAssetId = vehicleDisposalDetail.vehicleAssetId;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vin")
    public static final SqlColumn<String> vin = vehicleDisposalDetail.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_plate")
    public static final SqlColumn<String> licensePlate = vehicleDisposalDetail.licensePlate;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = vehicleDisposalDetail.vehicleModelId;

    /**
     * Database Column Remarks:
     *   资产所属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = vehicleDisposalDetail.assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产所属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_name")
    public static final SqlColumn<String> assetCompanyName = vehicleDisposalDetail.assetCompanyName;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_id")
    public static final SqlColumn<Long> ownOrganizationId = vehicleDisposalDetail.ownOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_name")
    public static final SqlColumn<String> ownOrganizationName = vehicleDisposalDetail.ownOrganizationName;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_id")
    public static final SqlColumn<Long> usageOrganizationId = vehicleDisposalDetail.usageOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_name")
    public static final SqlColumn<String> usageOrganizationName = vehicleDisposalDetail.usageOrganizationName;

    /**
     * Database Column Remarks:
     *   所属车队
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.belonging_team")
    public static final SqlColumn<String> belongingTeam = vehicleDisposalDetail.belongingTeam;

    /**
     * Database Column Remarks:
     *   投产日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.start_date")
    public static final SqlColumn<Date> startDate = vehicleDisposalDetail.startDate;

    /**
     * Database Column Remarks:
     *   退役日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.real_retirement_date")
    public static final SqlColumn<Date> realRetirementDate = vehicleDisposalDetail.realRetirementDate;

    /**
     * Database Column Remarks:
     *   上牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_date")
    public static final SqlColumn<Date> licenseDate = vehicleDisposalDetail.licenseDate;

    /**
     * Database Column Remarks:
     *   使用期限（月）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_month_limit")
    public static final SqlColumn<Integer> usageMonthLimit = vehicleDisposalDetail.usageMonthLimit;

    /**
     * Database Column Remarks:
     *   使用性质 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_id_registration_card")
    public static final SqlColumn<Integer> usageIdRegistrationCard = vehicleDisposalDetail.usageIdRegistrationCard;

    /**
     * Database Column Remarks:
     *   到填表日已用月数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.used_months")
    public static final SqlColumn<Integer> usedMonths = vehicleDisposalDetail.usedMonths;

    /**
     * Database Column Remarks:
     *   市场预估价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_market_price")
    public static final SqlColumn<BigDecimal> estimatedMarketPrice = vehicleDisposalDetail.estimatedMarketPrice;

    /**
     * Database Column Remarks:
     *   起拍价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.starting_price")
    public static final SqlColumn<BigDecimal> startingPrice = vehicleDisposalDetail.startingPrice;

    /**
     * Database Column Remarks:
     *   预估净售值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_net_sale_value")
    public static final SqlColumn<BigDecimal> estimatedNetSaleValue = vehicleDisposalDetail.estimatedNetSaleValue;

    /**
     * Database Column Remarks:
     *   最低成交价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_acceptable_price")
    public static final SqlColumn<BigDecimal> minimumAcceptablePrice = vehicleDisposalDetail.minimumAcceptablePrice;

    /**
     * Database Column Remarks:
     *   手续费（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.handling_fee")
    public static final SqlColumn<BigDecimal> handlingFee = vehicleDisposalDetail.handlingFee;

    /**
     * Database Column Remarks:
     *   出售原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_reason")
    public static final SqlColumn<String> saleReason = vehicleDisposalDetail.saleReason;

    /**
     * Database Column Remarks:
     *   实际出售原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_reason")
    public static final SqlColumn<String> actualSaleReason = vehicleDisposalDetail.actualSaleReason;

    /**
     * Database Column Remarks:
     *   使用性质
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_nature")
    public static final SqlColumn<String> usageNature = vehicleDisposalDetail.usageNature;

    /**
     * Database Column Remarks:
     *   报废类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_type")
    public static final SqlColumn<Integer> disposalType = vehicleDisposalDetail.disposalType;

    /**
     * Database Column Remarks:
     *   预估车辆损失（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_vehicle_loss")
    public static final SqlColumn<BigDecimal> estimatedVehicleLoss = vehicleDisposalDetail.estimatedVehicleLoss;

    /**
     * Database Column Remarks:
     *   政府补贴金额（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.government_subsidy_amount")
    public static final SqlColumn<BigDecimal> governmentSubsidyAmount = vehicleDisposalDetail.governmentSubsidyAmount;

    /**
     * Database Column Remarks:
     *   报废原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_reason")
    public static final SqlColumn<String> disposalReason = vehicleDisposalDetail.disposalReason;

    /**
     * Database Column Remarks:
     *   原值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.original_value")
    public static final SqlColumn<BigDecimal> originalValue = vehicleDisposalDetail.originalValue;

    /**
     * Database Column Remarks:
     *   已提折旧（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.accumulated_depreciation")
    public static final SqlColumn<String> accumulatedDepreciation = vehicleDisposalDetail.accumulatedDepreciation;

    /**
     * Database Column Remarks:
     *   净值/残值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_value")
    public static final SqlColumn<BigDecimal> netValue = vehicleDisposalDetail.netValue;

    /**
     * Database Column Remarks:
     *   财务评估
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.financial_evaluation")
    public static final SqlColumn<String> financialEvaluation = vehicleDisposalDetail.financialEvaluation;

    /**
     * Database Column Remarks:
     *   实际售价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_selling_price")
    public static final SqlColumn<BigDecimal> actualSellingPrice = vehicleDisposalDetail.actualSellingPrice;

    /**
     * Database Column Remarks:
     *   实际净售价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_net_price")
    public static final SqlColumn<BigDecimal> actualNetPrice = vehicleDisposalDetail.actualNetPrice;

    /**
     * Database Column Remarks:
     *   实际净售价差值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_price_diff")
    public static final SqlColumn<BigDecimal> netPriceDiff = vehicleDisposalDetail.netPriceDiff;

    /**
     * Database Column Remarks:
     *   实际出售损益
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_gain_loss")
    public static final SqlColumn<BigDecimal> saleGainLoss = vehicleDisposalDetail.saleGainLoss;

    /**
     * Database Column Remarks:
     *   实际出售数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sold_quantity")
    public static final SqlColumn<Integer> actualSoldQuantity = vehicleDisposalDetail.actualSoldQuantity;

    /**
     * Database Column Remarks:
     *   计划数量与实际数量差值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quantity_diff")
    public static final SqlColumn<Integer> quantityDiff = vehicleDisposalDetail.quantityDiff;

    /**
     * Database Column Remarks:
     *   实际出售说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_desc")
    public static final SqlColumn<String> actualSaleDesc = vehicleDisposalDetail.actualSaleDesc;

    /**
     * Database Column Remarks:
     *   预估报废损失金额（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_scrap_loss_amount")
    public static final SqlColumn<BigDecimal> estimatedScrapLossAmount = vehicleDisposalDetail.estimatedScrapLossAmount;

    /**
     * Database Column Remarks:
     *   是否保险公司拍卖 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_insurance_auction")
    public static final SqlColumn<Integer> isInsuranceAuction = vehicleDisposalDetail.isInsuranceAuction;

    /**
     * Database Column Remarks:
     *   实际拍卖金额（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_auction_amount")
    public static final SqlColumn<BigDecimal> actualAuctionAmount = vehicleDisposalDetail.actualAuctionAmount;

    /**
     * Database Column Remarks:
     *   实际报废损益（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_scrap_profit_loss")
    public static final SqlColumn<BigDecimal> actualScrapProfitLoss = vehicleDisposalDetail.actualScrapProfitLoss;

    /**
     * Database Column Remarks:
     *   牌照性质（手工）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_type_manual")
    public static final SqlColumn<String> licenseTypeManual = vehicleDisposalDetail.licenseTypeManual;

    /**
     * Database Column Remarks:
     *   额度类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quota_type")
    public static final SqlColumn<Integer> quotaType = vehicleDisposalDetail.quotaType;

    /**
     * Database Column Remarks:
     *   商品车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_abbreviation_id")
    public static final SqlColumn<Integer> vehicleAbbreviationId = vehicleDisposalDetail.vehicleAbbreviationId;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_color_id")
    public static final SqlColumn<Integer> vehicleColorId = vehicleDisposalDetail.vehicleColorId;

    /**
     * Database Column Remarks:
     *   msrp
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.msrp")
    public static final SqlColumn<String> msrp = vehicleDisposalDetail.msrp;

    /**
     * Database Column Remarks:
     *   公里数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.mileage")
    public static final SqlColumn<BigDecimal> mileage = vehicleDisposalDetail.mileage;

    /**
     * Database Column Remarks:
     *   车况
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_condition")
    public static final SqlColumn<String> vehicleCondition = vehicleDisposalDetail.vehicleCondition;

    /**
     * Database Column Remarks:
     *   最低出售价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_selling_price")
    public static final SqlColumn<BigDecimal> minimumSellingPrice = vehicleDisposalDetail.minimumSellingPrice;

    /**
     * Database Column Remarks:
     *   拍卖保留价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.auction_reserve_price")
    public static final SqlColumn<BigDecimal> auctionReservePrice = vehicleDisposalDetail.auctionReservePrice;

    /**
     * Database Column Remarks:
     *   预期盈亏1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_one")
    public static final SqlColumn<BigDecimal> expectedProfitLossOne = vehicleDisposalDetail.expectedProfitLossOne;

    /**
     * Database Column Remarks:
     *   预期盈亏2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_two")
    public static final SqlColumn<BigDecimal> expectedProfitLossTwo = vehicleDisposalDetail.expectedProfitLossTwo;

    /**
     * Database Column Remarks:
     *   当月资产净值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.current_month_net_value")
    public static final SqlColumn<BigDecimal> currentMonthNetValue = vehicleDisposalDetail.currentMonthNetValue;

    /**
     * Database Column Remarks:
     *   二手车发票URL
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.secondhand_car_invoice_url")
    public static final SqlColumn<String> secondhandCarInvoiceUrl = vehicleDisposalDetail.secondhandCarInvoiceUrl;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleDisposalDetail.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_time")
    public static final SqlColumn<Date> createTime = vehicleDisposalDetail.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleDisposalDetail.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleDisposalDetail.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_time")
    public static final SqlColumn<Date> updateTime = vehicleDisposalDetail.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleDisposalDetail.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleDisposalDetail.updateOperName;

    /**
     * Database Column Remarks:
     *   附件url
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.file_url")
    public static final SqlColumn<String> fileUrl = vehicleDisposalDetail.fileUrl;

    /**
     * Database Column Remarks:
     *   实际售价附件
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_file_url")
    public static final SqlColumn<String> actualSaleFileUrl = vehicleDisposalDetail.actualSaleFileUrl;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    public static final class VehicleDisposalDetail extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> disposalId = column("disposal_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleAssetId = column("vehicle_asset_id", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<String> assetCompanyName = column("asset_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> ownOrganizationId = column("own_organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> ownOrganizationName = column("own_organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> usageOrganizationId = column("usage_organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> usageOrganizationName = column("usage_organization_name", JDBCType.VARCHAR);

        public final SqlColumn<String> belongingTeam = column("belonging_team", JDBCType.VARCHAR);

        public final SqlColumn<Date> startDate = column("start_date", JDBCType.DATE);

        public final SqlColumn<Date> realRetirementDate = column("real_retirement_date", JDBCType.DATE);

        public final SqlColumn<Date> licenseDate = column("license_date", JDBCType.DATE);

        public final SqlColumn<Integer> usageMonthLimit = column("usage_month_limit", JDBCType.INTEGER);

        public final SqlColumn<Integer> usageIdRegistrationCard = column("usage_id_registration_card", JDBCType.INTEGER);

        public final SqlColumn<Integer> usedMonths = column("used_months", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> estimatedMarketPrice = column("estimated_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> startingPrice = column("starting_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> estimatedNetSaleValue = column("estimated_net_sale_value", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> minimumAcceptablePrice = column("minimum_acceptable_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> handlingFee = column("handling_fee", JDBCType.DECIMAL);

        public final SqlColumn<String> saleReason = column("sale_reason", JDBCType.VARCHAR);

        public final SqlColumn<String> actualSaleReason = column("actual_sale_reason", JDBCType.VARCHAR);

        public final SqlColumn<String> usageNature = column("usage_nature", JDBCType.VARCHAR);

        public final SqlColumn<Integer> disposalType = column("disposal_type", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> estimatedVehicleLoss = column("estimated_vehicle_loss", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> governmentSubsidyAmount = column("government_subsidy_amount", JDBCType.DECIMAL);

        public final SqlColumn<String> disposalReason = column("disposal_reason", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> originalValue = column("original_value", JDBCType.DECIMAL);

        public final SqlColumn<String> accumulatedDepreciation = column("accumulated_depreciation", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> netValue = column("net_value", JDBCType.DECIMAL);

        public final SqlColumn<String> financialEvaluation = column("financial_evaluation", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> actualSellingPrice = column("actual_selling_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> actualNetPrice = column("actual_net_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> netPriceDiff = column("net_price_diff", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> saleGainLoss = column("sale_gain_loss", JDBCType.DECIMAL);

        public final SqlColumn<Integer> actualSoldQuantity = column("actual_sold_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> quantityDiff = column("quantity_diff", JDBCType.INTEGER);

        public final SqlColumn<String> actualSaleDesc = column("actual_sale_desc", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> estimatedScrapLossAmount = column("estimated_scrap_loss_amount", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isInsuranceAuction = column("is_insurance_auction", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> actualAuctionAmount = column("actual_auction_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> actualScrapProfitLoss = column("actual_scrap_profit_loss", JDBCType.DECIMAL);

        public final SqlColumn<String> licenseTypeManual = column("license_type_manual", JDBCType.VARCHAR);

        public final SqlColumn<Integer> quotaType = column("quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleAbbreviationId = column("vehicle_abbreviation_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleColorId = column("vehicle_color_id", JDBCType.INTEGER);

        public final SqlColumn<String> msrp = column("msrp", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> mileage = column("mileage", JDBCType.DECIMAL);

        public final SqlColumn<String> vehicleCondition = column("vehicle_condition", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> minimumSellingPrice = column("minimum_selling_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> auctionReservePrice = column("auction_reserve_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> expectedProfitLossOne = column("expected_profit_loss_one", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> expectedProfitLossTwo = column("expected_profit_loss_two", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> currentMonthNetValue = column("current_month_net_value", JDBCType.DECIMAL);

        public final SqlColumn<String> secondhandCarInvoiceUrl = column("secondhand_car_invoice_url", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<String> fileUrl = column("file_url", JDBCType.LONGVARCHAR);

        public final SqlColumn<String> actualSaleFileUrl = column("actual_sale_file_url", JDBCType.LONGVARCHAR);

        public VehicleDisposalDetail() {
            super("t_vehicle_disposal_detail");
        }
    }
}