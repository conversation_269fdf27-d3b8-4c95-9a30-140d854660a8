package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleTransferFixedDetailsDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    public static final VehicleTransferFixedDetails vehicleTransferFixedDetails = new VehicleTransferFixedDetails();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.id")
    public static final SqlColumn<Long> id = vehicleTransferFixedDetails.id;

    /**
     * Database Column Remarks:
     *   转固申请id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.transfer_fixed__apply_id")
    public static final SqlColumn<Long> transferFixedApplyId = vehicleTransferFixedDetails.transferFixedApplyId;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.vin")
    public static final SqlColumn<String> vin = vehicleTransferFixedDetails.vin;

    /**
     * Database Column Remarks:
     *   产品线 1-巡网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.product_line")
    public static final SqlColumn<Integer> productLine = vehicleTransferFixedDetails.productLine;

    /**
     * Database Column Remarks:
     *   业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.business_line")
    public static final SqlColumn<Integer> businessLine = vehicleTransferFixedDetails.businessLine;

    /**
     * Database Column Remarks:
     *   对应转固审批状态 1 审批中 2 审批完成 3 审批取消
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.approval_status")
    public static final SqlColumn<Integer> approvalStatus = vehicleTransferFixedDetails.approvalStatus;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleTransferFixedDetails.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.create_time")
    public static final SqlColumn<Date> createTime = vehicleTransferFixedDetails.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleTransferFixedDetails.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleTransferFixedDetails.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.update_time")
    public static final SqlColumn<Date> updateTime = vehicleTransferFixedDetails.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleTransferFixedDetails.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_transfer_fixed_details.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleTransferFixedDetails.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_transfer_fixed_details")
    public static final class VehicleTransferFixedDetails extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> transferFixedApplyId = column("transfer_fixed__apply_id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> businessLine = column("business_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> approvalStatus = column("approval_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleTransferFixedDetails() {
            super("t_vehicle_transfer_fixed_details");
        }
    }
}