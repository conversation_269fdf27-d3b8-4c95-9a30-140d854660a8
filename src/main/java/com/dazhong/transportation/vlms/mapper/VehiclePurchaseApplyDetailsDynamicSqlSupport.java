package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehiclePurchaseApplyDetailsDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    public static final VehiclePurchaseApplyDetails vehiclePurchaseApplyDetails = new VehiclePurchaseApplyDetails();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.id")
    public static final SqlColumn<Long> id = vehiclePurchaseApplyDetails.id;

    /**
     * Database Column Remarks:
     *   采购申id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_apply_id")
    public static final SqlColumn<Long> purchaseApplyId = vehiclePurchaseApplyDetails.purchaseApplyId;

    /**
     * Database Column Remarks:
     *   采购申请明细编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.apply_details_no")
    public static final SqlColumn<String> applyDetailsNo = vehiclePurchaseApplyDetails.applyDetailsNo;

    /**
     * Database Column Remarks:
     *   预算情况枚举值 1-预算内 2-预算外
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.budget_status")
    public static final SqlColumn<Integer> budgetStatus = vehiclePurchaseApplyDetails.budgetStatus;

    /**
     * Database Column Remarks:
     *   采购类型枚举值 1-新车 2-二手车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_type")
    public static final SqlColumn<Integer> purchaseType = vehiclePurchaseApplyDetails.purchaseType;

    /**
     * Database Column Remarks:
     *   采购车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_model_id")
    public static final SqlColumn<Long> purchaseModelId = vehiclePurchaseApplyDetails.purchaseModelId;

    /**
     * Database Column Remarks:
     *   单价（巡网）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.unit_price")
    public static final SqlColumn<BigDecimal> unitPrice = vehiclePurchaseApplyDetails.unitPrice;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quantity")
    public static final SqlColumn<Integer> quantity = vehiclePurchaseApplyDetails.quantity;

    /**
     * Database Column Remarks:
     *   已收货车辆数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.received_quantity")
    public static final SqlColumn<Integer> receivedQuantity = vehiclePurchaseApplyDetails.receivedQuantity;

    /**
     * Database Column Remarks:
     *   其他费用（巡网）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.other_costs")
    public static final SqlColumn<BigDecimal> otherCosts = vehiclePurchaseApplyDetails.otherCosts;

    /**
     * Database Column Remarks:
     *   总价（巡网）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.total_price")
    public static final SqlColumn<BigDecimal> totalPrice = vehiclePurchaseApplyDetails.totalPrice;

    /**
     * Database Column Remarks:
     *   业务类型枚举值 1-巡讯 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.business_type")
    public static final SqlColumn<Integer> businessType = vehiclePurchaseApplyDetails.businessType;

    /**
     * Database Column Remarks:
     *   收货所属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.owner_id")
    public static final SqlColumn<Integer> ownerId = vehiclePurchaseApplyDetails.ownerId;

    /**
     * Database Column Remarks:
     *   制造商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.manufacturer_id")
    public static final SqlColumn<Integer> manufacturerId = vehiclePurchaseApplyDetails.manufacturerId;

    /**
     * Database Column Remarks:
     *   供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.supplier_id")
    public static final SqlColumn<Integer> supplierId = vehiclePurchaseApplyDetails.supplierId;

    /**
     * Database Column Remarks:
     *   已使用年限（二手车）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_years")
    public static final SqlColumn<String> usedYears = vehiclePurchaseApplyDetails.usedYears;

    /**
     * Database Column Remarks:
     *   已使用公里数（二手车）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_km")
    public static final SqlColumn<String> usedKm = vehiclePurchaseApplyDetails.usedKm;

    /**
     * Database Column Remarks:
     *   车辆预计退运时间（商务）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_return_date")
    public static final SqlColumn<Date> expectedReturnDate = vehiclePurchaseApplyDetails.expectedReturnDate;

    /**
     * Database Column Remarks:
     *   期望到货日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_delivery_date")
    public static final SqlColumn<Date> expectedDeliveryDate = vehiclePurchaseApplyDetails.expectedDeliveryDate;

    /**
     * Database Column Remarks:
     *   是否占用管控额度 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_quota_occupied")
    public static final SqlColumn<Integer> isQuotaOccupied = vehiclePurchaseApplyDetails.isQuotaOccupied;

    /**
     * Database Column Remarks:
     *   额度类型枚举值  1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_type")
    public static final SqlColumn<Integer> quotaType = vehiclePurchaseApplyDetails.quotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_asset_ownership")
    public static final SqlColumn<Integer> quotaAssetOwnership = vehiclePurchaseApplyDetails.quotaAssetOwnership;

    /**
     * Database Column Remarks:
     *   预占用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.pre_occupied")
    public static final SqlColumn<Integer> preOccupied = vehiclePurchaseApplyDetails.preOccupied;

    /**
     * Database Column Remarks:
     *   投资回报率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.return_on_investment")
    public static final SqlColumn<String> returnOnInvestment = vehiclePurchaseApplyDetails.returnOnInvestment;

    /**
     * Database Column Remarks:
     *   月单车收益
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.monthly_bicycle_revenue")
    public static final SqlColumn<String> monthlyBicycleRevenue = vehiclePurchaseApplyDetails.monthlyBicycleRevenue;

    /**
     * Database Column Remarks:
     *   车辆残值率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.vehicle_residual_value_rate")
    public static final SqlColumn<String> vehicleResidualValueRate = vehiclePurchaseApplyDetails.vehicleResidualValueRate;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehiclePurchaseApplyDetails.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_time")
    public static final SqlColumn<Date> createTime = vehiclePurchaseApplyDetails.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehiclePurchaseApplyDetails.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_name")
    public static final SqlColumn<String> createOperName = vehiclePurchaseApplyDetails.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_time")
    public static final SqlColumn<Date> updateTime = vehiclePurchaseApplyDetails.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehiclePurchaseApplyDetails.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehiclePurchaseApplyDetails.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    public static final class VehiclePurchaseApplyDetails extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> purchaseApplyId = column("purchase_apply_id", JDBCType.BIGINT);

        public final SqlColumn<String> applyDetailsNo = column("apply_details_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> budgetStatus = column("budget_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> purchaseType = column("purchase_type", JDBCType.INTEGER);

        public final SqlColumn<Long> purchaseModelId = column("purchase_model_id", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> unitPrice = column("unit_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> quantity = column("quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> receivedQuantity = column("received_quantity", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> otherCosts = column("other_costs", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> totalPrice = column("total_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> businessType = column("business_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> manufacturerId = column("manufacturer_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> supplierId = column("supplier_id", JDBCType.INTEGER);

        public final SqlColumn<String> usedYears = column("used_years", JDBCType.VARCHAR);

        public final SqlColumn<String> usedKm = column("used_km", JDBCType.VARCHAR);

        public final SqlColumn<Date> expectedReturnDate = column("expected_return_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> expectedDeliveryDate = column("expected_delivery_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> isQuotaOccupied = column("is_quota_occupied", JDBCType.INTEGER);

        public final SqlColumn<Integer> quotaType = column("quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> quotaAssetOwnership = column("quota_asset_ownership", JDBCType.INTEGER);

        public final SqlColumn<Integer> preOccupied = column("pre_occupied", JDBCType.INTEGER);

        public final SqlColumn<String> returnOnInvestment = column("return_on_investment", JDBCType.VARCHAR);

        public final SqlColumn<String> monthlyBicycleRevenue = column("monthly_bicycle_revenue", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleResidualValueRate = column("vehicle_residual_value_rate", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehiclePurchaseApplyDetails() {
            super("t_vehicle_purchase_apply_details");
        }
    }
}