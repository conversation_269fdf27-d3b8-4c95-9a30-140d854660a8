package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleSyncExternalBusinessInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.*;

@Mapper
public interface VehicleSyncExternalBusinessInfoMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, CommonInsertMapper<VehicleSyncExternalBusinessInfo> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, licensePlate, businessType, businessNo, businessTime, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName, businessInfo);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleSyncExternalBusinessInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleSyncExternalBusinessInfoResult")
    Optional<VehicleSyncExternalBusinessInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleSyncExternalBusinessInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_type", property="businessType", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_no", property="businessNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_time", property="businessTime", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_info", property="businessInfo", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<VehicleSyncExternalBusinessInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleSyncExternalBusinessInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleSyncExternalBusinessInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default int insert(VehicleSyncExternalBusinessInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleSyncExternalBusinessInfo, c ->
            c.map(vin).toProperty("vin")
            .map(licensePlate).toProperty("licensePlate")
            .map(businessType).toProperty("businessType")
            .map(businessNo).toProperty("businessNo")
            .map(businessTime).toProperty("businessTime")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
            .map(businessInfo).toProperty("businessInfo")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default int insertSelective(VehicleSyncExternalBusinessInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleSyncExternalBusinessInfo, c ->
            c.map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(businessType).toPropertyWhenPresent("businessType", record::getBusinessType)
            .map(businessNo).toPropertyWhenPresent("businessNo", record::getBusinessNo)
            .map(businessTime).toPropertyWhenPresent("businessTime", record::getBusinessTime)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
            .map(businessInfo).toPropertyWhenPresent("businessInfo", record::getBusinessInfo)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default Optional<VehicleSyncExternalBusinessInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleSyncExternalBusinessInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default List<VehicleSyncExternalBusinessInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleSyncExternalBusinessInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default List<VehicleSyncExternalBusinessInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleSyncExternalBusinessInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default Optional<VehicleSyncExternalBusinessInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleSyncExternalBusinessInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleSyncExternalBusinessInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(record::getVin)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(businessType).equalTo(record::getBusinessType)
                .set(businessNo).equalTo(record::getBusinessNo)
                .set(businessTime).equalTo(record::getBusinessTime)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName)
                .set(businessInfo).equalTo(record::getBusinessInfo);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleSyncExternalBusinessInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(record::getVin)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(businessType).equalToWhenPresent(record::getBusinessType)
                .set(businessNo).equalToWhenPresent(record::getBusinessNo)
                .set(businessTime).equalToWhenPresent(record::getBusinessTime)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
                .set(businessInfo).equalToWhenPresent(record::getBusinessInfo);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default int updateByPrimaryKey(VehicleSyncExternalBusinessInfo record) {
        return update(c ->
            c.set(vin).equalTo(record::getVin)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(businessType).equalTo(record::getBusinessType)
            .set(businessNo).equalTo(record::getBusinessNo)
            .set(businessTime).equalTo(record::getBusinessTime)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .set(businessInfo).equalTo(record::getBusinessInfo)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_sync_external_business_info")
    default int updateByPrimaryKeySelective(VehicleSyncExternalBusinessInfo record) {
        return update(c ->
            c.set(vin).equalToWhenPresent(record::getVin)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(businessType).equalToWhenPresent(record::getBusinessType)
            .set(businessNo).equalToWhenPresent(record::getBusinessNo)
            .set(businessTime).equalToWhenPresent(record::getBusinessTime)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .set(businessInfo).equalToWhenPresent(record::getBusinessInfo)
            .where(id, isEqualTo(record::getId))
        );
    }
}