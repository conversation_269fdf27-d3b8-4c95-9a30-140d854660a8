package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleContractDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleContract;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleContractMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, contractNo, contractStartDate, contractEndDate, deliverDate, collectDate, deliverTaskNo, collectTaskNo, bareCarPrice, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleContract> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleContractResult")
    Optional<VehicleContract> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleContractResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="contract_no", property="contractNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="contract_start_date", property="contractStartDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="contract_end_date", property="contractEndDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deliver_date", property="deliverDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="collect_date", property="collectDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deliver_task_no", property="deliverTaskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="collect_task_no", property="collectTaskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="bare_car_price", property="bareCarPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleContract> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleContract, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleContract, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default int insert(VehicleContract record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleContract, c ->
            c.map(vin).toProperty("vin")
            .map(contractNo).toProperty("contractNo")
            .map(contractStartDate).toProperty("contractStartDate")
            .map(contractEndDate).toProperty("contractEndDate")
            .map(deliverDate).toProperty("deliverDate")
            .map(collectDate).toProperty("collectDate")
            .map(deliverTaskNo).toProperty("deliverTaskNo")
            .map(collectTaskNo).toProperty("collectTaskNo")
            .map(bareCarPrice).toProperty("bareCarPrice")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default int insertSelective(VehicleContract record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleContract, c ->
            c.map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(contractNo).toPropertyWhenPresent("contractNo", record::getContractNo)
            .map(contractStartDate).toPropertyWhenPresent("contractStartDate", record::getContractStartDate)
            .map(contractEndDate).toPropertyWhenPresent("contractEndDate", record::getContractEndDate)
            .map(deliverDate).toPropertyWhenPresent("deliverDate", record::getDeliverDate)
            .map(collectDate).toPropertyWhenPresent("collectDate", record::getCollectDate)
            .map(deliverTaskNo).toPropertyWhenPresent("deliverTaskNo", record::getDeliverTaskNo)
            .map(collectTaskNo).toPropertyWhenPresent("collectTaskNo", record::getCollectTaskNo)
            .map(bareCarPrice).toPropertyWhenPresent("bareCarPrice", record::getBareCarPrice)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default Optional<VehicleContract> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleContract, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default List<VehicleContract> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleContract, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default List<VehicleContract> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleContract, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default Optional<VehicleContract> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleContract, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleContract record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(record::getVin)
                .set(contractNo).equalTo(record::getContractNo)
                .set(contractStartDate).equalTo(record::getContractStartDate)
                .set(contractEndDate).equalTo(record::getContractEndDate)
                .set(deliverDate).equalTo(record::getDeliverDate)
                .set(collectDate).equalTo(record::getCollectDate)
                .set(deliverTaskNo).equalTo(record::getDeliverTaskNo)
                .set(collectTaskNo).equalTo(record::getCollectTaskNo)
                .set(bareCarPrice).equalTo(record::getBareCarPrice)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleContract record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(record::getVin)
                .set(contractNo).equalToWhenPresent(record::getContractNo)
                .set(contractStartDate).equalToWhenPresent(record::getContractStartDate)
                .set(contractEndDate).equalToWhenPresent(record::getContractEndDate)
                .set(deliverDate).equalToWhenPresent(record::getDeliverDate)
                .set(collectDate).equalToWhenPresent(record::getCollectDate)
                .set(deliverTaskNo).equalToWhenPresent(record::getDeliverTaskNo)
                .set(collectTaskNo).equalToWhenPresent(record::getCollectTaskNo)
                .set(bareCarPrice).equalToWhenPresent(record::getBareCarPrice)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default int updateByPrimaryKey(VehicleContract record) {
        return update(c ->
            c.set(vin).equalTo(record::getVin)
            .set(contractNo).equalTo(record::getContractNo)
            .set(contractStartDate).equalTo(record::getContractStartDate)
            .set(contractEndDate).equalTo(record::getContractEndDate)
            .set(deliverDate).equalTo(record::getDeliverDate)
            .set(collectDate).equalTo(record::getCollectDate)
            .set(deliverTaskNo).equalTo(record::getDeliverTaskNo)
            .set(collectTaskNo).equalTo(record::getCollectTaskNo)
            .set(bareCarPrice).equalTo(record::getBareCarPrice)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    default int updateByPrimaryKeySelective(VehicleContract record) {
        return update(c ->
            c.set(vin).equalToWhenPresent(record::getVin)
            .set(contractNo).equalToWhenPresent(record::getContractNo)
            .set(contractStartDate).equalToWhenPresent(record::getContractStartDate)
            .set(contractEndDate).equalToWhenPresent(record::getContractEndDate)
            .set(deliverDate).equalToWhenPresent(record::getDeliverDate)
            .set(collectDate).equalToWhenPresent(record::getCollectDate)
            .set(deliverTaskNo).equalToWhenPresent(record::getDeliverTaskNo)
            .set(collectTaskNo).equalToWhenPresent(record::getCollectTaskNo)
            .set(bareCarPrice).equalToWhenPresent(record::getBareCarPrice)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}