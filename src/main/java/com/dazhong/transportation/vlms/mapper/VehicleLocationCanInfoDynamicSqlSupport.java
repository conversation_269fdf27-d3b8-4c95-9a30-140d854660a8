package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleLocationCanInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    public static final VehicleLocationCanInfo vehicleLocationCanInfo = new VehicleLocationCanInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.id")
    public static final SqlColumn<Long> id = vehicleLocationCanInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.vin")
    public static final SqlColumn<String> vin = vehicleLocationCanInfo.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.license_plate")
    public static final SqlColumn<String> licensePlate = vehicleLocationCanInfo.licensePlate;

    /**
     * Database Column Remarks:
     *   终端号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.terminal_no")
    public static final SqlColumn<String> terminalNo = vehicleLocationCanInfo.terminalNo;

    /**
     * Database Column Remarks:
     *   纬度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.latitude")
    public static final SqlColumn<String> latitude = vehicleLocationCanInfo.latitude;

    /**
     * Database Column Remarks:
     *   经度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.longitude")
    public static final SqlColumn<String> longitude = vehicleLocationCanInfo.longitude;

    /**
     * Database Column Remarks:
     *   里程数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.mileage")
    public static final SqlColumn<BigDecimal> mileage = vehicleLocationCanInfo.mileage;

    /**
     * Database Column Remarks:
     *   油量百分比
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.oil_percent")
    public static final SqlColumn<BigDecimal> oilPercent = vehicleLocationCanInfo.oilPercent;

    /**
     * Database Column Remarks:
     *   电量百分比
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.electric_percent")
    public static final SqlColumn<BigDecimal> electricPercent = vehicleLocationCanInfo.electricPercent;

    /**
     * Database Column Remarks:
     *   采集时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.collection_time")
    public static final SqlColumn<String> collectionTime = vehicleLocationCanInfo.collectionTime;

    /**
     * Database Column Remarks:
     *   终端渠道
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.terminal_channel")
    public static final SqlColumn<String> terminalChannel = vehicleLocationCanInfo.terminalChannel;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleLocationCanInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.create_time")
    public static final SqlColumn<Date> createTime = vehicleLocationCanInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleLocationCanInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleLocationCanInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.update_time")
    public static final SqlColumn<Date> updateTime = vehicleLocationCanInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleLocationCanInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_location_can_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleLocationCanInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_location_can_info")
    public static final class VehicleLocationCanInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<String> terminalNo = column("terminal_no", JDBCType.VARCHAR);

        public final SqlColumn<String> latitude = column("latitude", JDBCType.VARCHAR);

        public final SqlColumn<String> longitude = column("longitude", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> mileage = column("mileage", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> oilPercent = column("oil_percent", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> electricPercent = column("electric_percent", JDBCType.DECIMAL);

        public final SqlColumn<String> collectionTime = column("collection_time", JDBCType.VARCHAR);

        public final SqlColumn<String> terminalChannel = column("terminal_channel", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleLocationCanInfo() {
            super("t_vehicle_location_can_info");
        }
    }
}