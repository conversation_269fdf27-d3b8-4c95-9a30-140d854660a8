package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VinModelInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VinModelInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VinModelInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, vin, modelName, modelJson);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VinModelInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VinModelInfoResult")
    Optional<VinModelInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VinModelInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="model_name", property="modelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="model_json", property="modelJson", jdbcType=JdbcType.VARCHAR)
    })
    List<VinModelInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vinModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vinModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default int insert(VinModelInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vinModelInfo, c ->
            c.map(vin).toProperty("vin")
            .map(modelName).toProperty("modelName")
            .map(modelJson).toProperty("modelJson")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default int insertSelective(VinModelInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vinModelInfo, c ->
            c.map(vin).toPropertyWhenPresent("vin", record::getVin)
            .map(modelName).toPropertyWhenPresent("modelName", record::getModelName)
            .map(modelJson).toPropertyWhenPresent("modelJson", record::getModelJson)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default Optional<VinModelInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vinModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default List<VinModelInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vinModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default List<VinModelInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vinModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default Optional<VinModelInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vinModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VinModelInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalTo(record::getVin)
                .set(modelName).equalTo(record::getModelName)
                .set(modelJson).equalTo(record::getModelJson);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VinModelInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(vin).equalToWhenPresent(record::getVin)
                .set(modelName).equalToWhenPresent(record::getModelName)
                .set(modelJson).equalToWhenPresent(record::getModelJson);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default int updateByPrimaryKey(VinModelInfo record) {
        return update(c ->
            c.set(vin).equalTo(record::getVin)
            .set(modelName).equalTo(record::getModelName)
            .set(modelJson).equalTo(record::getModelJson)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    default int updateByPrimaryKeySelective(VinModelInfo record) {
        return update(c ->
            c.set(vin).equalToWhenPresent(record::getVin)
            .set(modelName).equalToWhenPresent(record::getModelName)
            .set(modelJson).equalToWhenPresent(record::getModelJson)
            .where(id, isEqualTo(record::getId))
        );
    }
}