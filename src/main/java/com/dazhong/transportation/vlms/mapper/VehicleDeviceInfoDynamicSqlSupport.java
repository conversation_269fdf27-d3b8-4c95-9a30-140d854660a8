package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleDeviceInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    public static final VehicleDeviceInfo vehicleDeviceInfo = new VehicleDeviceInfo();

    /**
     * Database Column Remarks:
     *   设备ID，系统自动生成的唯一编码流水号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.id")
    public static final SqlColumn<Long> id = vehicleDeviceInfo.id;

    /**
     * Database Column Remarks:
     *   设备类型 设备类型 有源  无源  原厂ETC TBOX
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_type")
    public static final SqlColumn<String> deviceType = vehicleDeviceInfo.deviceType;

    /**
     * Database Column Remarks:
     *   设备的编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_seq")
    public static final SqlColumn<String> deviceSeq = vehicleDeviceInfo.deviceSeq;

    /**
     * Database Column Remarks:
     *   设备的品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_brand")
    public static final SqlColumn<String> deviceBrand = vehicleDeviceInfo.deviceBrand;

    /**
     * Database Column Remarks:
     *   设备的型号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_model")
    public static final SqlColumn<String> deviceModel = vehicleDeviceInfo.deviceModel;

    /**
     * Database Column Remarks:
     *   设备关联的SIM卡号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.sim_card_number")
    public static final SqlColumn<String> simCardNumber = vehicleDeviceInfo.simCardNumber;

    /**
     * Database Column Remarks:
     *   设备的安装时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_time")
    public static final SqlColumn<Date> installTime = vehicleDeviceInfo.installTime;

    /**
     * Database Column Remarks:
     *   设备的安装地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_location")
    public static final SqlColumn<String> installLocation = vehicleDeviceInfo.installLocation;

    /**
     * Database Column Remarks:
     *   设备关联的车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.vin")
    public static final SqlColumn<String> vin = vehicleDeviceInfo.vin;

    /**
     * Database Column Remarks:
     *   设备的激活时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.activation_time")
    public static final SqlColumn<Date> activationTime = vehicleDeviceInfo.activationTime;

    /**
     * Database Column Remarks:
     *   设备的到期时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.expiration_time")
    public static final SqlColumn<Date> expirationTime = vehicleDeviceInfo.expirationTime;

    /**
     * Database Column Remarks:
     *   GPS最新的定位时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location_time")
    public static final SqlColumn<Date> latestLocationTime = vehicleDeviceInfo.latestLocationTime;

    /**
     * Database Column Remarks:
     *   GPS最新的定位经度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.longitude")
    public static final SqlColumn<BigDecimal> longitude = vehicleDeviceInfo.longitude;

    /**
     * Database Column Remarks:
     *   GPS最新的定位纬度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latitude")
    public static final SqlColumn<BigDecimal> latitude = vehicleDeviceInfo.latitude;

    /**
     * Database Column Remarks:
     *   GPS最新的定位地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location")
    public static final SqlColumn<String> latestLocation = vehicleDeviceInfo.latestLocation;

    /**
     * Database Column Remarks:
     *   设备读取到最新里程数据的时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_mileage_update_time")
    public static final SqlColumn<Date> latestMileageUpdateTime = vehicleDeviceInfo.latestMileageUpdateTime;

    /**
     * Database Column Remarks:
     *   设备读取到最新里程数据
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.total_mileage")
    public static final SqlColumn<BigDecimal> totalMileage = vehicleDeviceInfo.totalMileage;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleDeviceInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_time")
    public static final SqlColumn<Date> createTime = vehicleDeviceInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleDeviceInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleDeviceInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_time")
    public static final SqlColumn<Date> updateTime = vehicleDeviceInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleDeviceInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleDeviceInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    public static final class VehicleDeviceInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> deviceType = column("device_type", JDBCType.VARCHAR);

        public final SqlColumn<String> deviceSeq = column("device_seq", JDBCType.VARCHAR);

        public final SqlColumn<String> deviceBrand = column("device_brand", JDBCType.VARCHAR);

        public final SqlColumn<String> deviceModel = column("device_model", JDBCType.VARCHAR);

        public final SqlColumn<String> simCardNumber = column("sim_card_number", JDBCType.VARCHAR);

        public final SqlColumn<Date> installTime = column("install_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> installLocation = column("install_location", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<Date> activationTime = column("activation_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> expirationTime = column("expiration_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> latestLocationTime = column("latest_location_time", JDBCType.TIMESTAMP);

        public final SqlColumn<BigDecimal> longitude = column("longitude", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> latitude = column("latitude", JDBCType.DECIMAL);

        public final SqlColumn<String> latestLocation = column("latest_location", JDBCType.VARCHAR);

        public final SqlColumn<Date> latestMileageUpdateTime = column("latest_mileage_update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<BigDecimal> totalMileage = column("total_mileage", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleDeviceInfo() {
            super("t_vehicle_device_info");
        }
    }
}