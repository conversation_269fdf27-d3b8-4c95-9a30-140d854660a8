package com.dazhong.transportation.vlms.mapper.extend;

import com.dazhong.transportation.vlms.mapper.DataSupplierInfoMapper;
import com.dazhong.transportation.vlms.model.DataSupplierInfo;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import javax.annotation.Generated;

import static com.dazhong.transportation.vlms.mapper.DataSupplierInfoDynamicSqlSupport.*;

@Mapper
public interface DataSupplierInfoExtendMapper extends DataSupplierInfoMapper {

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_supplier_info")
    default int insertSelectiveWithId(DataSupplierInfo record) {
        return MyBatis3Utils.insert(this::insert, record, dataSupplierInfo, c ->
                c.map(id).toPropertyWhenPresent("id", record::getId)
                        .map(name).toPropertyWhenPresent("name", record::getName)
                        .map(address).toPropertyWhenPresent("address", record::getAddress)
                        .map(phone).toPropertyWhenPresent("phone", record::getPhone)
                        .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
                        .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
                        .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
                        .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
                        .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
                        .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
                        .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }
}
