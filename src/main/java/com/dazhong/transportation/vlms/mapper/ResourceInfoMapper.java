package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.ResourceInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.ResourceInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ResourceInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, resourceKey, resourceName, resourceCode, resourceUrl, resourceType, resourceIconUrl, parentResourceId, systemCode, sort, hide, hideTitle, miscDesc, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<ResourceInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ResourceInfoResult")
    Optional<ResourceInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ResourceInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="resource_key", property="resourceKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="resource_name", property="resourceName", jdbcType=JdbcType.VARCHAR),
        @Result(column="resource_code", property="resourceCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="resource_url", property="resourceUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="resource_type", property="resourceType", jdbcType=JdbcType.INTEGER),
        @Result(column="resource_icon_url", property="resourceIconUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="parent_resource_id", property="parentResourceId", jdbcType=JdbcType.BIGINT),
        @Result(column="system_code", property="systemCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="hide", property="hide", jdbcType=JdbcType.INTEGER),
        @Result(column="hide_title", property="hideTitle", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<ResourceInfo> selectMany(SelectStatementProvider selectStatement);


    @SelectProvider(type= SqlProviderAdapter.class, method="select")
    @Results(id="SearchRoleResourceResult", value = {
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="resource_key", property="resourceKey", jdbcType=JdbcType.VARCHAR),
            @Result(column="resource_name", property="resourceName", jdbcType=JdbcType.VARCHAR),
            @Result(column="resource_url", property="resourceUrl", jdbcType=JdbcType.VARCHAR),
            @Result(column="resource_type", property="resourceType", jdbcType=JdbcType.INTEGER),
            @Result(column="resource_icon_url", property="resourceIconUrl", jdbcType=JdbcType.VARCHAR),
            @Result(column="resource_code", property="resourceCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="parent_resource_id", property="parentResourceId", jdbcType=JdbcType.BIGINT),
            @Result(column="system_code", property="systemCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
            @Result(column="hide", property="hide", jdbcType=JdbcType.INTEGER),
            @Result(column="hide_title", property="hideTitle", jdbcType=JdbcType.INTEGER),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
            @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
    })
    List<ResourceInfo> queryhRoleResourceList(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, resourceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, resourceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default int insert(ResourceInfo record) {
        return MyBatis3Utils.insert(this::insert, record, resourceInfo, c ->
            c.map(resourceKey).toProperty("resourceKey")
            .map(resourceName).toProperty("resourceName")
            .map(resourceCode).toProperty("resourceCode")
            .map(resourceUrl).toProperty("resourceUrl")
            .map(resourceType).toProperty("resourceType")
            .map(resourceIconUrl).toProperty("resourceIconUrl")
            .map(parentResourceId).toProperty("parentResourceId")
            .map(systemCode).toProperty("systemCode")
            .map(sort).toProperty("sort")
            .map(hide).toProperty("hide")
            .map(hideTitle).toProperty("hideTitle")
            .map(miscDesc).toProperty("miscDesc")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default int insertSelective(ResourceInfo record) {
        return MyBatis3Utils.insert(this::insert, record, resourceInfo, c ->
            c.map(resourceKey).toPropertyWhenPresent("resourceKey", record::getResourceKey)
            .map(resourceName).toPropertyWhenPresent("resourceName", record::getResourceName)
            .map(resourceCode).toPropertyWhenPresent("resourceCode", record::getResourceCode)
            .map(resourceUrl).toPropertyWhenPresent("resourceUrl", record::getResourceUrl)
            .map(resourceType).toPropertyWhenPresent("resourceType", record::getResourceType)
            .map(resourceIconUrl).toPropertyWhenPresent("resourceIconUrl", record::getResourceIconUrl)
            .map(parentResourceId).toPropertyWhenPresent("parentResourceId", record::getParentResourceId)
            .map(systemCode).toPropertyWhenPresent("systemCode", record::getSystemCode)
            .map(sort).toPropertyWhenPresent("sort", record::getSort)
            .map(hide).toPropertyWhenPresent("hide", record::getHide)
            .map(hideTitle).toPropertyWhenPresent("hideTitle", record::getHideTitle)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", record::getMiscDesc)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default Optional<ResourceInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, resourceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default List<ResourceInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, resourceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default List<ResourceInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, resourceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default Optional<ResourceInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, resourceInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    static UpdateDSL<UpdateModel> updateAllColumns(ResourceInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(resourceKey).equalTo(record::getResourceKey)
                .set(resourceName).equalTo(record::getResourceName)
                .set(resourceCode).equalTo(record::getResourceCode)
                .set(resourceUrl).equalTo(record::getResourceUrl)
                .set(resourceType).equalTo(record::getResourceType)
                .set(resourceIconUrl).equalTo(record::getResourceIconUrl)
                .set(parentResourceId).equalTo(record::getParentResourceId)
                .set(systemCode).equalTo(record::getSystemCode)
                .set(sort).equalTo(record::getSort)
                .set(hide).equalTo(record::getHide)
                .set(hideTitle).equalTo(record::getHideTitle)
                .set(miscDesc).equalTo(record::getMiscDesc)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ResourceInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(resourceKey).equalToWhenPresent(record::getResourceKey)
                .set(resourceName).equalToWhenPresent(record::getResourceName)
                .set(resourceCode).equalToWhenPresent(record::getResourceCode)
                .set(resourceUrl).equalToWhenPresent(record::getResourceUrl)
                .set(resourceType).equalToWhenPresent(record::getResourceType)
                .set(resourceIconUrl).equalToWhenPresent(record::getResourceIconUrl)
                .set(parentResourceId).equalToWhenPresent(record::getParentResourceId)
                .set(systemCode).equalToWhenPresent(record::getSystemCode)
                .set(sort).equalToWhenPresent(record::getSort)
                .set(hide).equalToWhenPresent(record::getHide)
                .set(hideTitle).equalToWhenPresent(record::getHideTitle)
                .set(miscDesc).equalToWhenPresent(record::getMiscDesc)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default int updateByPrimaryKey(ResourceInfo record) {
        return update(c ->
            c.set(resourceKey).equalTo(record::getResourceKey)
            .set(resourceName).equalTo(record::getResourceName)
            .set(resourceCode).equalTo(record::getResourceCode)
            .set(resourceUrl).equalTo(record::getResourceUrl)
            .set(resourceType).equalTo(record::getResourceType)
            .set(resourceIconUrl).equalTo(record::getResourceIconUrl)
            .set(parentResourceId).equalTo(record::getParentResourceId)
            .set(systemCode).equalTo(record::getSystemCode)
            .set(sort).equalTo(record::getSort)
            .set(hide).equalTo(record::getHide)
            .set(hideTitle).equalTo(record::getHideTitle)
            .set(miscDesc).equalTo(record::getMiscDesc)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    default int updateByPrimaryKeySelective(ResourceInfo record) {
        return update(c ->
            c.set(resourceKey).equalToWhenPresent(record::getResourceKey)
            .set(resourceName).equalToWhenPresent(record::getResourceName)
            .set(resourceCode).equalToWhenPresent(record::getResourceCode)
            .set(resourceUrl).equalToWhenPresent(record::getResourceUrl)
            .set(resourceType).equalToWhenPresent(record::getResourceType)
            .set(resourceIconUrl).equalToWhenPresent(record::getResourceIconUrl)
            .set(parentResourceId).equalToWhenPresent(record::getParentResourceId)
            .set(systemCode).equalToWhenPresent(record::getSystemCode)
            .set(sort).equalToWhenPresent(record::getSort)
            .set(hide).equalToWhenPresent(record::getHide)
            .set(hideTitle).equalToWhenPresent(record::getHideTitle)
            .set(miscDesc).equalToWhenPresent(record::getMiscDesc)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}