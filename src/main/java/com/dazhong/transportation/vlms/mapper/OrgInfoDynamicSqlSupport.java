package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class OrgInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    public static final OrgInfo orgInfo = new OrgInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.id")
    public static final SqlColumn<Long> id = orgInfo.id;

    /**
     * Database Column Remarks:
     *   组织机构code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_code")
    public static final SqlColumn<String> companyCode = orgInfo.companyCode;

    /**
     * Database Column Remarks:
     *   组织机构名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_name")
    public static final SqlColumn<String> companyName = orgInfo.companyName;

    /**
     * Database Column Remarks:
     *   是否选中 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.checked_state")
    public static final SqlColumn<Integer> checkedState = orgInfo.checkedState;

    /**
     * Database Column Remarks:
     *   父资源id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.parent_id")
    public static final SqlColumn<Long> parentId = orgInfo.parentId;

    /**
     * Database Column Remarks:
     *   位置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.position")
    public static final SqlColumn<Integer> position = orgInfo.position;

    /**
     * Database Column Remarks:
     *   类型（公司、部门）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.org_type")
    public static final SqlColumn<String> orgType = orgInfo.orgType;

    /**
     * Database Column Remarks:
     *   唯一标识
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.vg_uid")
    public static final SqlColumn<String> vgUid = orgInfo.vgUid;

    /**
     * Database Column Remarks:
     *   父节点标识
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.pg_uid")
    public static final SqlColumn<String> pgUid = orgInfo.pgUid;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = orgInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_time")
    public static final SqlColumn<Date> createTime = orgInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = orgInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_name")
    public static final SqlColumn<String> createOperName = orgInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_time")
    public static final SqlColumn<Date> updateTime = orgInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = orgInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = orgInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    public static final class OrgInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> companyCode = column("company_code", JDBCType.VARCHAR);

        public final SqlColumn<String> companyName = column("company_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> checkedState = column("checked_state", JDBCType.INTEGER);

        public final SqlColumn<Long> parentId = column("parent_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> position = column("position", JDBCType.INTEGER);

        public final SqlColumn<String> orgType = column("org_type", JDBCType.VARCHAR);

        public final SqlColumn<String> vgUid = column("vg_uid", JDBCType.VARCHAR);

        public final SqlColumn<String> pgUid = column("pg_uid", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public OrgInfo() {
            super("t_org_info");
        }
    }
}