package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleFileRecordDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_file_record")
    public static final VehicleFileRecord vehicleFileRecord = new VehicleFileRecord();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.id")
    public static final SqlColumn<Long> id = vehicleFileRecord.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.vin")
    public static final SqlColumn<String> vin = vehicleFileRecord.vin;

    /**
     * Database Column Remarks:
     *   业务类型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.file_type_name")
    public static final SqlColumn<String> fileTypeName = vehicleFileRecord.fileTypeName;

    /**
     * Database Column Remarks:
     *   文件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.file_name")
    public static final SqlColumn<String> fileName = vehicleFileRecord.fileName;

    /**
     * Database Column Remarks:
     *   文件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.file_url")
    public static final SqlColumn<String> fileUrl = vehicleFileRecord.fileUrl;

    /**
     * Database Column Remarks:
     *   文件描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.file_desc")
    public static final SqlColumn<String> fileDesc = vehicleFileRecord.fileDesc;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleFileRecord.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.create_time")
    public static final SqlColumn<Date> createTime = vehicleFileRecord.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleFileRecord.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleFileRecord.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.update_time")
    public static final SqlColumn<Date> updateTime = vehicleFileRecord.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleFileRecord.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_file_record.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleFileRecord.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_file_record")
    public static final class VehicleFileRecord extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> fileTypeName = column("file_type_name", JDBCType.VARCHAR);

        public final SqlColumn<String> fileName = column("file_name", JDBCType.VARCHAR);

        public final SqlColumn<String> fileUrl = column("file_url", JDBCType.VARCHAR);

        public final SqlColumn<String> fileDesc = column("file_desc", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleFileRecord() {
            super("t_vehicle_file_record");
        }
    }
}