package com.dazhong.transportation.vlms.mapper;

import com.dazhong.transportation.vlms.model.LicensePlateQuotaTransactionRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import javax.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaTransactionRecordDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface LicensePlateQuotaTransactionRecordMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    BasicColumn[] selectList = BasicColumn.columnList(id, licensePlateWithdrawalDate, quotaPrintDate, quotaNumber, quotaType, assetCompanyId, assetCompanyName, taskNumber, licensePlate, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<LicensePlateQuotaTransactionRecord> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LicensePlateQuotaTransactionRecordResult")
    Optional<LicensePlateQuotaTransactionRecord> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LicensePlateQuotaTransactionRecordResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="license_plate_withdrawal_date", property="licensePlateWithdrawalDate", jdbcType=JdbcType.DATE),
        @Result(column="quota_print_date", property="quotaPrintDate", jdbcType=JdbcType.DATE),
        @Result(column="quota_number", property="quotaNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="quota_type", property="quotaType", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_id", property="assetCompanyId", jdbcType=JdbcType.INTEGER),
        @Result(column="asset_company_name", property="assetCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="task_number", property="taskNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="license_plate", property="licensePlate", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<LicensePlateQuotaTransactionRecord> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, licensePlateQuotaTransactionRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, licensePlateQuotaTransactionRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default int insert(LicensePlateQuotaTransactionRecord record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateQuotaTransactionRecord, c ->
            c.map(licensePlateWithdrawalDate).toProperty("licensePlateWithdrawalDate")
            .map(quotaPrintDate).toProperty("quotaPrintDate")
            .map(quotaNumber).toProperty("quotaNumber")
            .map(quotaType).toProperty("quotaType")
            .map(assetCompanyId).toProperty("assetCompanyId")
            .map(assetCompanyName).toProperty("assetCompanyName")
            .map(taskNumber).toProperty("taskNumber")
            .map(licensePlate).toProperty("licensePlate")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default int insertSelective(LicensePlateQuotaTransactionRecord record) {
        return MyBatis3Utils.insert(this::insert, record, licensePlateQuotaTransactionRecord, c ->
            c.map(licensePlateWithdrawalDate).toPropertyWhenPresent("licensePlateWithdrawalDate", record::getLicensePlateWithdrawalDate)
            .map(quotaPrintDate).toPropertyWhenPresent("quotaPrintDate", record::getQuotaPrintDate)
            .map(quotaNumber).toPropertyWhenPresent("quotaNumber", record::getQuotaNumber)
            .map(quotaType).toPropertyWhenPresent("quotaType", record::getQuotaType)
            .map(assetCompanyId).toPropertyWhenPresent("assetCompanyId", record::getAssetCompanyId)
            .map(assetCompanyName).toPropertyWhenPresent("assetCompanyName", record::getAssetCompanyName)
            .map(taskNumber).toPropertyWhenPresent("taskNumber", record::getTaskNumber)
            .map(licensePlate).toPropertyWhenPresent("licensePlate", record::getLicensePlate)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default Optional<LicensePlateQuotaTransactionRecord> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, licensePlateQuotaTransactionRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default List<LicensePlateQuotaTransactionRecord> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, licensePlateQuotaTransactionRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default List<LicensePlateQuotaTransactionRecord> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, licensePlateQuotaTransactionRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default Optional<LicensePlateQuotaTransactionRecord> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, licensePlateQuotaTransactionRecord, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    static UpdateDSL<UpdateModel> updateAllColumns(LicensePlateQuotaTransactionRecord record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(licensePlateWithdrawalDate).equalTo(record::getLicensePlateWithdrawalDate)
                .set(quotaPrintDate).equalTo(record::getQuotaPrintDate)
                .set(quotaNumber).equalTo(record::getQuotaNumber)
                .set(quotaType).equalTo(record::getQuotaType)
                .set(assetCompanyId).equalTo(record::getAssetCompanyId)
                .set(assetCompanyName).equalTo(record::getAssetCompanyName)
                .set(taskNumber).equalTo(record::getTaskNumber)
                .set(licensePlate).equalTo(record::getLicensePlate)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LicensePlateQuotaTransactionRecord record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(licensePlateWithdrawalDate).equalToWhenPresent(record::getLicensePlateWithdrawalDate)
                .set(quotaPrintDate).equalToWhenPresent(record::getQuotaPrintDate)
                .set(quotaNumber).equalToWhenPresent(record::getQuotaNumber)
                .set(quotaType).equalToWhenPresent(record::getQuotaType)
                .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
                .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
                .set(taskNumber).equalToWhenPresent(record::getTaskNumber)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default int updateByPrimaryKey(LicensePlateQuotaTransactionRecord record) {
        return update(c ->
            c.set(licensePlateWithdrawalDate).equalTo(record::getLicensePlateWithdrawalDate)
            .set(quotaPrintDate).equalTo(record::getQuotaPrintDate)
            .set(quotaNumber).equalTo(record::getQuotaNumber)
            .set(quotaType).equalTo(record::getQuotaType)
            .set(assetCompanyId).equalTo(record::getAssetCompanyId)
            .set(assetCompanyName).equalTo(record::getAssetCompanyName)
            .set(taskNumber).equalTo(record::getTaskNumber)
            .set(licensePlate).equalTo(record::getLicensePlate)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    default int updateByPrimaryKeySelective(LicensePlateQuotaTransactionRecord record) {
        return update(c ->
            c.set(licensePlateWithdrawalDate).equalToWhenPresent(record::getLicensePlateWithdrawalDate)
            .set(quotaPrintDate).equalToWhenPresent(record::getQuotaPrintDate)
            .set(quotaNumber).equalToWhenPresent(record::getQuotaNumber)
            .set(quotaType).equalToWhenPresent(record::getQuotaType)
            .set(assetCompanyId).equalToWhenPresent(record::getAssetCompanyId)
            .set(assetCompanyName).equalToWhenPresent(record::getAssetCompanyName)
            .set(taskNumber).equalToWhenPresent(record::getTaskNumber)
            .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: t_license_plate_quota_transaction_record")
    default int updateTaskNumber(LicensePlateQuotaTransactionRecord record) {
        return update(c -> c
                .set(taskNumber).equalToWhenPresent(record::getTaskNumber)
                .set(licensePlate).equalToWhenPresent(record::getLicensePlate)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
                .where(taskNumber, isEqualTo(StringUtils.EMPTY))
                .and(quotaPrintDate, isEqualTo(record::getQuotaPrintDate))
                .and(quotaNumber, isEqualTo(record::getQuotaNumber))
        );
    }
}