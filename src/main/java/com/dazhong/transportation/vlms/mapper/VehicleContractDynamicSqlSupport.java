package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleContractDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    public static final VehicleContract vehicleContract = new VehicleContract();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.id")
    public static final SqlColumn<Long> id = vehicleContract.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.vin")
    public static final SqlColumn<String> vin = vehicleContract.vin;

    /**
     * Database Column Remarks:
     *   合同号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.contract_no")
    public static final SqlColumn<String> contractNo = vehicleContract.contractNo;

    /**
     * Database Column Remarks:
     *   合同开始日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.contract_start_date")
    public static final SqlColumn<Date> contractStartDate = vehicleContract.contractStartDate;

    /**
     * Database Column Remarks:
     *   合同结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.contract_end_date")
    public static final SqlColumn<Date> contractEndDate = vehicleContract.contractEndDate;

    /**
     * Database Column Remarks:
     *   发车日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.deliver_date")
    public static final SqlColumn<Date> deliverDate = vehicleContract.deliverDate;

    /**
     * Database Column Remarks:
     *   收车日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.collect_date")
    public static final SqlColumn<Date> collectDate = vehicleContract.collectDate;

    /**
     * Database Column Remarks:
     *   发车任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.deliver_task_no")
    public static final SqlColumn<String> deliverTaskNo = vehicleContract.deliverTaskNo;

    /**
     * Database Column Remarks:
     *   收车任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.collect_task_no")
    public static final SqlColumn<String> collectTaskNo = vehicleContract.collectTaskNo;

    /**
     * Database Column Remarks:
     *   裸车价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.bare_car_price")
    public static final SqlColumn<BigDecimal> bareCarPrice = vehicleContract.bareCarPrice;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleContract.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.create_time")
    public static final SqlColumn<Date> createTime = vehicleContract.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleContract.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleContract.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.update_time")
    public static final SqlColumn<Date> updateTime = vehicleContract.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleContract.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_contract.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleContract.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_contract")
    public static final class VehicleContract extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> contractNo = column("contract_no", JDBCType.VARCHAR);

        public final SqlColumn<Date> contractStartDate = column("contract_start_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> contractEndDate = column("contract_end_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deliverDate = column("deliver_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> collectDate = column("collect_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> deliverTaskNo = column("deliver_task_no", JDBCType.VARCHAR);

        public final SqlColumn<String> collectTaskNo = column("collect_task_no", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> bareCarPrice = column("bare_car_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleContract() {
            super("t_vehicle_contract");
        }
    }
}