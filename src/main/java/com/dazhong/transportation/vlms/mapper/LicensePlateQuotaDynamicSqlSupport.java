package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class LicensePlateQuotaDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    public static final LicensePlateQuota licensePlateQuota = new LicensePlateQuota();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.id")
    public static final SqlColumn<Long> id = licensePlateQuota.id;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = licensePlateQuota.assetCompanyId;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.quota_type")
    public static final SqlColumn<Integer> quotaType = licensePlateQuota.quotaType;

    /**
     * Database Column Remarks:
     *   额度总数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.quota")
    public static final SqlColumn<Integer> quota = licensePlateQuota.quota;

    /**
     * Database Column Remarks:
     *   预占用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.pre_occupied")
    public static final SqlColumn<Integer> preOccupied = licensePlateQuota.preOccupied;

    /**
     * Database Column Remarks:
     *   占用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.occupied")
    public static final SqlColumn<Integer> occupied = licensePlateQuota.occupied;

    /**
     * Database Column Remarks:
     *   剩余可用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.remaining")
    public static final SqlColumn<Integer> remaining = licensePlateQuota.remaining;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.is_deleted")
    public static final SqlColumn<Integer> isDeleted = licensePlateQuota.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.create_time")
    public static final SqlColumn<Date> createTime = licensePlateQuota.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.create_oper_id")
    public static final SqlColumn<Long> createOperId = licensePlateQuota.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.create_oper_name")
    public static final SqlColumn<String> createOperName = licensePlateQuota.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.update_time")
    public static final SqlColumn<Date> updateTime = licensePlateQuota.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.update_oper_id")
    public static final SqlColumn<Long> updateOperId = licensePlateQuota.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota.update_oper_name")
    public static final SqlColumn<String> updateOperName = licensePlateQuota.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota")
    public static final class LicensePlateQuota extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> quotaType = column("quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> quota = column("quota", JDBCType.INTEGER);

        public final SqlColumn<Integer> preOccupied = column("pre_occupied", JDBCType.INTEGER);

        public final SqlColumn<Integer> occupied = column("occupied", JDBCType.INTEGER);

        public final SqlColumn<Integer> remaining = column("remaining", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public LicensePlateQuota() {
            super("t_license_plate_quota");
        }
    }
}