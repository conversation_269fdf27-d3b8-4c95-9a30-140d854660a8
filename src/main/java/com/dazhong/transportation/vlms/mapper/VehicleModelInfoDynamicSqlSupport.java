package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleModelInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    public static final VehicleModelInfo vehicleModelInfo = new VehicleModelInfo();

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.id")
    public static final SqlColumn<Long> id = vehicleModelInfo.id;

    /**
     * Database Column Remarks:
     *   汽车之家车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.autohome_vehicle_model_id")
    public static final SqlColumn<Long> autohomeVehicleModelId = vehicleModelInfo.autohomeVehicleModelId;

    /**
     * Database Column Remarks:
     *   品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_name")
    public static final SqlColumn<String> vehicleBrandName = vehicleModelInfo.vehicleBrandName;

    /**
     * Database Column Remarks:
     *   车系
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_series_name")
    public static final SqlColumn<String> vehicleSeriesName = vehicleModelInfo.vehicleSeriesName;

    /**
     * Database Column Remarks:
     *   车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_name")
    public static final SqlColumn<String> vehicleModelName = vehicleModelInfo.vehicleModelName;

    /**
     * Database Column Remarks:
     *   财务车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.financial_model_name")
    public static final SqlColumn<String> financialModelName = vehicleModelInfo.financialModelName;

    /**
     * Database Column Remarks:
     *   商品车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_abbreviation_id")
    public static final SqlColumn<Integer> vehicleAbbreviationId = vehicleModelInfo.vehicleAbbreviationId;

    /**
     * Database Column Remarks:
     *   座位数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_passenger")
    public static final SqlColumn<Integer> assessPassenger = vehicleModelInfo.assessPassenger;

    /**
     * Database Column Remarks:
     *   大巴实际座位数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.bus_assess_passenger")
    public static final SqlColumn<Integer> busAssessPassenger = vehicleModelInfo.busAssessPassenger;

    /**
     * Database Column Remarks:
     *   发动机型号（车型）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_model_no")
    public static final SqlColumn<String> engineModelNo = vehicleModelInfo.engineModelNo;

    /**
     * Database Column Remarks:
     *   能源类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gas_type_id")
    public static final SqlColumn<Integer> gasTypeId = vehicleModelInfo.gasTypeId;

    /**
     * Database Column Remarks:
     *   车辆级别 (1-经济轿车, 2-舒适轿车, 3-豪华轿车, 4-SUV, 5-商务车, 6-轻客, 7-跑车, 8-皮卡, 9-大巴)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_level")
    public static final SqlColumn<Integer> vehicleLevel = vehicleModelInfo.vehicleLevel;

    /**
     * Database Column Remarks:
     *   轮胎规格
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_param")
    public static final SqlColumn<String> wheelParam = vehicleModelInfo.wheelParam;

    /**
     * Database Column Remarks:
     *   环保标准
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.exhaust_id")
    public static final SqlColumn<Integer> exhaustId = vehicleModelInfo.exhaustId;

    /**
     * Database Column Remarks:
     *   上市时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.ttm_month")
    public static final SqlColumn<String> ttmMonth = vehicleModelInfo.ttmMonth;

    /**
     * Database Column Remarks:
     *   车身-长度（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_length")
    public static final SqlColumn<Integer> outLength = vehicleModelInfo.outLength;

    /**
     * Database Column Remarks:
     *   车身-宽度（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_width")
    public static final SqlColumn<Integer> outWidth = vehicleModelInfo.outWidth;

    /**
     * Database Column Remarks:
     *   车身-高度（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_height")
    public static final SqlColumn<Integer> outHeight = vehicleModelInfo.outHeight;

    /**
     * Database Column Remarks:
     *   车身-轴距（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base1")
    public static final SqlColumn<Integer> wheelBase1 = vehicleModelInfo.wheelBase1;

    /**
     * Database Column Remarks:
     *   整备质量(kg)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.total_mass")
    public static final SqlColumn<Integer> totalMass = vehicleModelInfo.totalMass;

    /**
     * Database Column Remarks:
     *   油箱容积（升）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_tank_capacity")
    public static final SqlColumn<Integer> fuelTankCapacity = vehicleModelInfo.fuelTankCapacity;

    /**
     * Database Column Remarks:
     *   燃油标号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_label_name")
    public static final SqlColumn<String> fuelLabelName = vehicleModelInfo.fuelLabelName;

    /**
     * Database Column Remarks:
     *   发动机-排量(mL)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.capacity")
    public static final SqlColumn<BigDecimal> capacity = vehicleModelInfo.capacity;

    /**
     * Database Column Remarks:
     *   电池容量（kWh）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.battery_capacity")
    public static final SqlColumn<BigDecimal> batteryCapacity = vehicleModelInfo.batteryCapacity;

    /**
     * Database Column Remarks:
     *   车辆续航里程（km）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_range")
    public static final SqlColumn<Integer> vehicleRange = vehicleModelInfo.vehicleRange;

    /**
     * Database Column Remarks:
     *   快速充能时间（h）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fast_charging_time")
    public static final SqlColumn<BigDecimal> fastChargingTime = vehicleModelInfo.fastChargingTime;

    /**
     * Database Column Remarks:
     *   慢充能时间（h）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.slow_charging_time")
    public static final SqlColumn<BigDecimal> slowChargingTime = vehicleModelInfo.slowChargingTime;

    /**
     * Database Column Remarks:
     *   百公里油耗（L/100km）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy_miit")
    public static final SqlColumn<BigDecimal> fuelEconomyMiit = vehicleModelInfo.fuelEconomyMiit;

    /**
     * Database Column Remarks:
     *   售价（汽车之家）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.price_on_autohome")
    public static final SqlColumn<Integer> priceOnAutohome = vehicleModelInfo.priceOnAutohome;

    /**
     * Database Column Remarks:
     *   车辆型号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_no")
    public static final SqlColumn<String> vehicleModelNo = vehicleModelInfo.vehicleModelNo;

    /**
     * Database Column Remarks:
     *   功率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.power")
    public static final SqlColumn<Integer> power = vehicleModelInfo.power;

    /**
     * Database Column Remarks:
     *   前轮距
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_front")
    public static final SqlColumn<Integer> treadFront = vehicleModelInfo.treadFront;

    /**
     * Database Column Remarks:
     *   后轮距
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_rear")
    public static final SqlColumn<Integer> treadRear = vehicleModelInfo.treadRear;

    /**
     * Database Column Remarks:
     *   轮胎数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_quantity")
    public static final SqlColumn<Integer> wheelQuantity = vehicleModelInfo.wheelQuantity;

    /**
     * Database Column Remarks:
     *   后轴钢板弹簧数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.spring_lamination")
    public static final SqlColumn<Integer> springLamination = vehicleModelInfo.springLamination;

    /**
     * Database Column Remarks:
     *   轴距2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base2")
    public static final SqlColumn<Integer> wheelBase2 = vehicleModelInfo.wheelBase2;

    /**
     * Database Column Remarks:
     *   轴距3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base3")
    public static final SqlColumn<Integer> wheelBase3 = vehicleModelInfo.wheelBase3;

    /**
     * Database Column Remarks:
     *   轴数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.axle_quantity")
    public static final SqlColumn<Integer> axleQuantity = vehicleModelInfo.axleQuantity;

    /**
     * Database Column Remarks:
     *   货箱内部尺寸长
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_length")
    public static final SqlColumn<Integer> containerLength = vehicleModelInfo.containerLength;

    /**
     * Database Column Remarks:
     *   货箱内部尺寸宽
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_width")
    public static final SqlColumn<Integer> containerWidth = vehicleModelInfo.containerWidth;

    /**
     * Database Column Remarks:
     *   货箱内部尺寸高
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_height")
    public static final SqlColumn<Integer> containerHeight = vehicleModelInfo.containerHeight;

    /**
     * Database Column Remarks:
     *   核定载质量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_mass")
    public static final SqlColumn<Integer> assessMass = vehicleModelInfo.assessMass;

    /**
     * Database Column Remarks:
     *   准牵引总质量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.traction_mass")
    public static final SqlColumn<Integer> tractionMass = vehicleModelInfo.tractionMass;

    /**
     * Database Column Remarks:
     *   驾驶室载客
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.cab_passenger")
    public static final SqlColumn<Integer> cabPassenger = vehicleModelInfo.cabPassenger;

    /**
     * Database Column Remarks:
     *   国产/进口 (0: 国产, 1: 进口)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacture_location")
    public static final SqlColumn<Integer> manufactureLocation = vehicleModelInfo.manufactureLocation;

    /**
     * Database Column Remarks:
     *   车门数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.door_quantity")
    public static final SqlColumn<Integer> doorQuantity = vehicleModelInfo.doorQuantity;

    /**
     * Database Column Remarks:
     *   档位数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_quantity")
    public static final SqlColumn<Integer> gearQuantity = vehicleModelInfo.gearQuantity;

    /**
     * Database Column Remarks:
     *   0-100加速
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.acceleration")
    public static final SqlColumn<BigDecimal> acceleration = vehicleModelInfo.acceleration;

    /**
     * Database Column Remarks:
     *   最高车速
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.speed")
    public static final SqlColumn<Integer> speed = vehicleModelInfo.speed;

    /**
     * Database Column Remarks:
     *   最小转弯半径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turning_radius")
    public static final SqlColumn<BigDecimal> turningRadius = vehicleModelInfo.turningRadius;

    /**
     * Database Column Remarks:
     *   最小离地间隙
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.road_clearance")
    public static final SqlColumn<Integer> roadClearance = vehicleModelInfo.roadClearance;

    /**
     * Database Column Remarks:
     *   最大爬坡度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gradient")
    public static final SqlColumn<Integer> gradient = vehicleModelInfo.gradient;

    /**
     * Database Column Remarks:
     *   等速油耗
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy")
    public static final SqlColumn<BigDecimal> fuelEconomy = vehicleModelInfo.fuelEconomy;

    /**
     * Database Column Remarks:
     *   扭矩
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.torque")
    public static final SqlColumn<Integer> torque = vehicleModelInfo.torque;

    /**
     * Database Column Remarks:
     *   压缩比
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.compression_ratio")
    public static final SqlColumn<String> compressionRatio = vehicleModelInfo.compressionRatio;

    /**
     * Database Column Remarks:
     *   制造商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacturer_id")
    public static final SqlColumn<Integer> manufacturerId = vehicleModelInfo.manufacturerId;

    /**
     * Database Column Remarks:
     *   车辆品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_id")
    public static final SqlColumn<Integer> vehicleBrandId = vehicleModelInfo.vehicleBrandId;

    /**
     * Database Column Remarks:
     *   车辆类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_type_id")
    public static final SqlColumn<Integer> vehicleTypeId = vehicleModelInfo.vehicleTypeId;

    /**
     * Database Column Remarks:
     *   驱动类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_drive_id")
    public static final SqlColumn<Integer> wheelDriveId = vehicleModelInfo.wheelDriveId;

    /**
     * Database Column Remarks:
     *   制动形式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.break_mode_id")
    public static final SqlColumn<Integer> breakModeId = vehicleModelInfo.breakModeId;

    /**
     * Database Column Remarks:
     *   转向方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turn_mode_id")
    public static final SqlColumn<Integer> turnModeId = vehicleModelInfo.turnModeId;

    /**
     * Database Column Remarks:
     *   驾驶位置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.drive_position_id")
    public static final SqlColumn<Integer> drivePositionId = vehicleModelInfo.drivePositionId;

    /**
     * Database Column Remarks:
     *   发动机位置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_position_id")
    public static final SqlColumn<Integer> enginePositionId = vehicleModelInfo.enginePositionId;

    /**
     * Database Column Remarks:
     *   变速箱形式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_box_type_id")
    public static final SqlColumn<Integer> gearBoxTypeId = vehicleModelInfo.gearBoxTypeId;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleModelInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_time")
    public static final SqlColumn<Date> createTime = vehicleModelInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleModelInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleModelInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_time")
    public static final SqlColumn<Date> updateTime = vehicleModelInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleModelInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleModelInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    public static final class VehicleModelInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> autohomeVehicleModelId = column("autohome_vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleBrandName = column("vehicle_brand_name", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleSeriesName = column("vehicle_series_name", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleModelName = column("vehicle_model_name", JDBCType.VARCHAR);

        public final SqlColumn<String> financialModelName = column("financial_model_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> vehicleAbbreviationId = column("vehicle_abbreviation_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> assessPassenger = column("assess_passenger", JDBCType.INTEGER);

        public final SqlColumn<Integer> busAssessPassenger = column("bus_assess_passenger", JDBCType.INTEGER);

        public final SqlColumn<String> engineModelNo = column("engine_model_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> gasTypeId = column("gas_type_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleLevel = column("vehicle_level", JDBCType.INTEGER);

        public final SqlColumn<String> wheelParam = column("wheel_param", JDBCType.VARCHAR);

        public final SqlColumn<Integer> exhaustId = column("exhaust_id", JDBCType.INTEGER);

        public final SqlColumn<String> ttmMonth = column("ttm_month", JDBCType.VARCHAR);

        public final SqlColumn<Integer> outLength = column("out_length", JDBCType.INTEGER);

        public final SqlColumn<Integer> outWidth = column("out_width", JDBCType.INTEGER);

        public final SqlColumn<Integer> outHeight = column("out_height", JDBCType.INTEGER);

        public final SqlColumn<Integer> wheelBase1 = column("wheel_base1", JDBCType.INTEGER);

        public final SqlColumn<Integer> totalMass = column("total_mass", JDBCType.INTEGER);

        public final SqlColumn<Integer> fuelTankCapacity = column("fuel_tank_capacity", JDBCType.INTEGER);

        public final SqlColumn<String> fuelLabelName = column("fuel_label_name", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> capacity = column("capacity", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> batteryCapacity = column("battery_capacity", JDBCType.DECIMAL);

        public final SqlColumn<Integer> vehicleRange = column("vehicle_range", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> fastChargingTime = column("fast_charging_time", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> slowChargingTime = column("slow_charging_time", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> fuelEconomyMiit = column("fuel_economy_miit", JDBCType.DECIMAL);

        public final SqlColumn<Integer> priceOnAutohome = column("price_on_autohome", JDBCType.INTEGER);

        public final SqlColumn<String> vehicleModelNo = column("vehicle_model_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> power = column("power", JDBCType.INTEGER);

        public final SqlColumn<Integer> treadFront = column("tread_front", JDBCType.INTEGER);

        public final SqlColumn<Integer> treadRear = column("tread_rear", JDBCType.INTEGER);

        public final SqlColumn<Integer> wheelQuantity = column("wheel_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> springLamination = column("spring_lamination", JDBCType.INTEGER);

        public final SqlColumn<Integer> wheelBase2 = column("wheel_base2", JDBCType.INTEGER);

        public final SqlColumn<Integer> wheelBase3 = column("wheel_base3", JDBCType.INTEGER);

        public final SqlColumn<Integer> axleQuantity = column("axle_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> containerLength = column("container_length", JDBCType.INTEGER);

        public final SqlColumn<Integer> containerWidth = column("container_width", JDBCType.INTEGER);

        public final SqlColumn<Integer> containerHeight = column("container_height", JDBCType.INTEGER);

        public final SqlColumn<Integer> assessMass = column("assess_mass", JDBCType.INTEGER);

        public final SqlColumn<Integer> tractionMass = column("traction_mass", JDBCType.INTEGER);

        public final SqlColumn<Integer> cabPassenger = column("cab_passenger", JDBCType.INTEGER);

        public final SqlColumn<Integer> manufactureLocation = column("manufacture_location", JDBCType.INTEGER);

        public final SqlColumn<Integer> doorQuantity = column("door_quantity", JDBCType.INTEGER);

        public final SqlColumn<Integer> gearQuantity = column("gear_quantity", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> acceleration = column("acceleration", JDBCType.DECIMAL);

        public final SqlColumn<Integer> speed = column("speed", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> turningRadius = column("turning_radius", JDBCType.DECIMAL);

        public final SqlColumn<Integer> roadClearance = column("road_clearance", JDBCType.INTEGER);

        public final SqlColumn<Integer> gradient = column("gradient", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> fuelEconomy = column("fuel_economy", JDBCType.DECIMAL);

        public final SqlColumn<Integer> torque = column("torque", JDBCType.INTEGER);

        public final SqlColumn<String> compressionRatio = column("compression_ratio", JDBCType.VARCHAR);

        public final SqlColumn<Integer> manufacturerId = column("manufacturer_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleBrandId = column("vehicle_brand_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleTypeId = column("vehicle_type_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> wheelDriveId = column("wheel_drive_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> breakModeId = column("break_mode_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> turnModeId = column("turn_mode_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> drivePositionId = column("drive_position_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> enginePositionId = column("engine_position_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> gearBoxTypeId = column("gear_box_type_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleModelInfo() {
            super("t_vehicle_model_info");
        }
    }
}