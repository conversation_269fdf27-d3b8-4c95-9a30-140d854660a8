package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class DataOwnerInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_owner_info")
    public static final DataOwnerInfo dataOwnerInfo = new DataOwnerInfo();

    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.id")
    public static final SqlColumn<Long> id = dataOwnerInfo.id;

    /**
     * Database Column Remarks:
     *   所有人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.name")
    public static final SqlColumn<String> name = dataOwnerInfo.name;

    /**
     * Database Column Remarks:
     *   所有人地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.address")
    public static final SqlColumn<String> address = dataOwnerInfo.address;

    /**
     * Database Column Remarks:
     *   所有人手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.phone")
    public static final SqlColumn<String> phone = dataOwnerInfo.phone;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = dataOwnerInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.create_time")
    public static final SqlColumn<Date> createTime = dataOwnerInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = dataOwnerInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.create_oper_name")
    public static final SqlColumn<String> createOperName = dataOwnerInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.update_time")
    public static final SqlColumn<Date> updateTime = dataOwnerInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = dataOwnerInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_owner_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = dataOwnerInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_owner_info")
    public static final class DataOwnerInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> address = column("address", JDBCType.VARCHAR);

        public final SqlColumn<String> phone = column("phone", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public DataOwnerInfo() {
            super("t_data_owner_info");
        }
    }
}