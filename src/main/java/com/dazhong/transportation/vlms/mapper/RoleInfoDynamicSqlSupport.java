package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class RoleInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_role_info")
    public static final RoleInfo roleInfo = new RoleInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.id")
    public static final SqlColumn<Long> id = roleInfo.id;

    /**
     * Database Column Remarks:
     *   系统编码 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.system_code")
    public static final SqlColumn<String> systemCode = roleInfo.systemCode;

    /**
     * Database Column Remarks:
     *   角色名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.role_name")
    public static final SqlColumn<String> roleName = roleInfo.roleName;

    /**
     * Database Column Remarks:
     *   角色状态 1-启用 2-禁用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.role_status")
    public static final SqlColumn<Integer> roleStatus = roleInfo.roleStatus;

    /**
     * Database Column Remarks:
     *   状态（0=正常 1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = roleInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.create_time")
    public static final SqlColumn<Date> createTime = roleInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = roleInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.create_oper_name")
    public static final SqlColumn<String> createOperName = roleInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.update_time")
    public static final SqlColumn<Date> updateTime = roleInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = roleInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_role_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = roleInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_role_info")
    public static final class RoleInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> systemCode = column("system_code", JDBCType.VARCHAR);

        public final SqlColumn<String> roleName = column("role_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> roleStatus = column("role_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public RoleInfo() {
            super("t_role_info");
        }
    }
}