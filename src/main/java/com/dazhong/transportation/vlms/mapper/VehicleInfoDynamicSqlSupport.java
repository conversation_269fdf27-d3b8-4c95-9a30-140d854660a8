package com.dazhong.transportation.vlms.mapper;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    public static final VehicleInfo vehicleInfo = new VehicleInfo();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.id")
    public static final SqlColumn<Long> id = vehicleInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vin")
    public static final SqlColumn<String> vin = vehicleInfo.vin;

    /**
     * Database Column Remarks:
     *   资产编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_asset_id")
    public static final SqlColumn<String> vehicleAssetId = vehicleInfo.vehicleAssetId;

    /**
     * Database Column Remarks:
     *   关联资产ID（仅用于历史数据同步）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.relate_asset_id")
    public static final SqlColumn<Long> relateAssetId = vehicleInfo.relateAssetId;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_no")
    public static final SqlColumn<String> engineNo = vehicleInfo.engineNo;

    /**
     * Database Column Remarks:
     *   发动机型号(车辆)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_model")
    public static final SqlColumn<String> engineModel = vehicleInfo.engineModel;

    /**
     * Database Column Remarks:
     *   车型id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = vehicleInfo.vehicleModelId;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate")
    public static final SqlColumn<String> licensePlate = vehicleInfo.licensePlate;

    /**
     * Database Column Remarks:
     *   是否使用额度（上牌） 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.use_quota_type")
    public static final SqlColumn<Integer> useQuotaType = vehicleInfo.useQuotaType;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_type")
    public static final SqlColumn<Integer> quotaType = vehicleInfo.quotaType;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_color_id")
    public static final SqlColumn<Integer> vehicleColorId = vehicleInfo.vehicleColorId;

    /**
     * Database Column Remarks:
     *   内饰颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.interior_color")
    public static final SqlColumn<String> interiorColor = vehicleInfo.interiorColor;

    /**
     * Database Column Remarks:
     *   供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.supplier_id")
    public static final SqlColumn<Integer> supplierId = vehicleInfo.supplierId;

    /**
     * Database Column Remarks:
     *   是否回购 1-是 2-否 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_repurchase")
    public static final SqlColumn<Integer> isRepurchase = vehicleInfo.isRepurchase;

    /**
     * Database Column Remarks:
     *   回购时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_date")
    public static final SqlColumn<Date> repurchaseDate = vehicleInfo.repurchaseDate;

    /**
     * Database Column Remarks:
     *   回购要求
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_requirements")
    public static final SqlColumn<String> repurchaseRequirements = vehicleInfo.repurchaseRequirements;

    /**
     * Database Column Remarks:
     *   使用年限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_age_limit")
    public static final SqlColumn<Integer> usageAgeLimit = vehicleInfo.usageAgeLimit;

    /**
     * Database Column Remarks:
     *   折旧年限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_age_limit")
    public static final SqlColumn<Integer> depreciationAgeLimit = vehicleInfo.depreciationAgeLimit;

    /**
     * Database Column Remarks:
     *   实际报废日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.real_retirement_date")
    public static final SqlColumn<Date> realRetirementDate = vehicleInfo.realRetirementDate;

    /**
     * Database Column Remarks:
     *   裸车价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_price")
    public static final SqlColumn<BigDecimal> purchasePrice = vehicleInfo.purchasePrice;

    /**
     * Database Column Remarks:
     *   购置税
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax")
    public static final SqlColumn<BigDecimal> purchaseTax = vehicleInfo.purchaseTax;

    /**
     * Database Column Remarks:
     *   牌照费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_price")
    public static final SqlColumn<BigDecimal> licensePlatePrice = vehicleInfo.licensePlatePrice;

    /**
     * Database Column Remarks:
     *   上牌杂费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_other_price")
    public static final SqlColumn<BigDecimal> licensePlateOtherPrice = vehicleInfo.licensePlateOtherPrice;

    /**
     * Database Column Remarks:
     *   装潢费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.upholster_price")
    public static final SqlColumn<BigDecimal> upholsterPrice = vehicleInfo.upholsterPrice;

    /**
     * Database Column Remarks:
     *   购置总价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.total_price")
    public static final SqlColumn<BigDecimal> totalPrice = vehicleInfo.totalPrice;

    /**
     * Database Column Remarks:
     *   账面净值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.remain_price")
    public static final SqlColumn<BigDecimal> remainPrice = vehicleInfo.remainPrice;

    /**
     * Database Column Remarks:
     *   旧车销售价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.second_hand_price")
    public static final SqlColumn<BigDecimal> secondHandPrice = vehicleInfo.secondHandPrice;

    /**
     * Database Column Remarks:
     *   资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.property_status")
    public static final SqlColumn<Integer> propertyStatus = vehicleInfo.propertyStatus;

    /**
     * Database Column Remarks:
     *   产品线 1-巡网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_line")
    public static final SqlColumn<Integer> productLine = vehicleInfo.productLine;

    /**
     * Database Column Remarks:
     *   业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.business_line")
    public static final SqlColumn<Integer> businessLine = vehicleInfo.businessLine;

    /**
     * Database Column Remarks:
     *   运营状态 1-待运 2-租出
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_status")
    public static final SqlColumn<Integer> operatingStatus = vehicleInfo.operatingStatus;

    /**
     * Database Column Remarks:
     *   车辆申购公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_code")
    public static final SqlColumn<String> subscriptionCompanyCode = vehicleInfo.subscriptionCompanyCode;

    /**
     * Database Column Remarks:
     *   申购公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_name")
    public static final SqlColumn<String> subscriptionCompanyName = vehicleInfo.subscriptionCompanyName;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_asset_company_id")
    public static final SqlColumn<Integer> quotaAssetCompanyId = vehicleInfo.quotaAssetCompanyId;

    /**
     * Database Column Remarks:
     *   资产所属公司ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.asset_company_id")
    public static final SqlColumn<Integer> assetCompanyId = vehicleInfo.assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产机构
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.own_organization_id")
    public static final SqlColumn<Long> ownOrganizationId = vehicleInfo.ownOrganizationId;

    /**
     * Database Column Remarks:
     *   使用机构
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_organization_id")
    public static final SqlColumn<Long> usageOrganizationId = vehicleInfo.usageOrganizationId;

    /**
     * Database Column Remarks:
     *   所属车队
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.belonging_team")
    public static final SqlColumn<String> belongingTeam = vehicleInfo.belongingTeam;

    /**
     * Database Column Remarks:
     *   获得方式 数据字典详细描叙
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.obtain_way_id")
    public static final SqlColumn<Integer> obtainWayId = vehicleInfo.obtainWayId;

    /**
     * Database Column Remarks:
     *   管辖区县
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.area_id")
    public static final SqlColumn<Integer> areaId = vehicleInfo.areaId;

    /**
     * Database Column Remarks:
     *   折旧数据ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_data_id")
    public static final SqlColumn<Long> depreciationDataId = vehicleInfo.depreciationDataId;

    /**
     * Database Column Remarks:
     *   最新定位地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_position")
    public static final SqlColumn<String> latestPosition = vehicleInfo.latestPosition;

    /**
     * Database Column Remarks:
     *   最新总里程 (车机)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_total_mileage")
    public static final SqlColumn<String> latestTotalMileage = vehicleInfo.latestTotalMileage;

    /**
     * Database Column Remarks:
     *   使用性质 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_id_registration_card")
    public static final SqlColumn<Integer> usageIdRegistrationCard = vehicleInfo.usageIdRegistrationCard;

    /**
     * Database Column Remarks:
     *   车辆类型 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_type_registration_card")
    public static final SqlColumn<Integer> vehicleTypeRegistrationCard = vehicleInfo.vehicleTypeRegistrationCard;

    /**
     * Database Column Remarks:
     *   注册日期(行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.registration_date_registration_card")
    public static final SqlColumn<Date> registrationDateRegistrationCard = vehicleInfo.registrationDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   发证日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date_registration_card")
    public static final SqlColumn<Date> issuanceDateRegistrationCard = vehicleInfo.issuanceDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   强制报废日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.retirement_date_registration_card")
    public static final SqlColumn<Date> retirementDateRegistrationCard = vehicleInfo.retirementDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   年检到期日 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.annual_inspection_due_date_registration_card")
    public static final SqlColumn<Date> annualInspectionDueDateRegistrationCard = vehicleInfo.annualInspectionDueDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   档案编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.file_number")
    public static final SqlColumn<String> fileNumber = vehicleInfo.fileNumber;

    /**
     * Database Column Remarks:
     *   车辆出厂日期 (产证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_date")
    public static final SqlColumn<Date> productDate = vehicleInfo.productDate;

    /**
     * Database Column Remarks:
     *   发证日期 (产证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date")
    public static final SqlColumn<Date> issuanceDate = vehicleInfo.issuanceDate;

    /**
     * Database Column Remarks:
     *   产证编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_number")
    public static final SqlColumn<String> certificateNumber = vehicleInfo.certificateNumber;

    /**
     * Database Column Remarks:
     *   行驶证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_license_url")
    public static final SqlColumn<String> vehicleLicenseUrl = vehicleInfo.vehicleLicenseUrl;

    /**
     * Database Column Remarks:
     *   产证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_ownership_url")
    public static final SqlColumn<String> certificateOwnershipUrl = vehicleInfo.certificateOwnershipUrl;

    /**
     * Database Column Remarks:
     *   合格证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_conformity_url")
    public static final SqlColumn<String> certificateConformityUrl = vehicleInfo.certificateConformityUrl;

    /**
     * Database Column Remarks:
     *   车辆发票文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_invoice_url")
    public static final SqlColumn<String> vehicleInvoiceUrl = vehicleInfo.vehicleInvoiceUrl;

    /**
     * Database Column Remarks:
     *   购置税文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax_url")
    public static final SqlColumn<String> purchaseTaxUrl = vehicleInfo.purchaseTaxUrl;

    /**
     * Database Column Remarks:
     *   营运证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_permit_url")
    public static final SqlColumn<String> operatingPermitUrl = vehicleInfo.operatingPermitUrl;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleInfo.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_time")
    public static final SqlColumn<Date> createTime = vehicleInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleInfo.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleInfo.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_time")
    public static final SqlColumn<Date> updateTime = vehicleInfo.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleInfo.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleInfo.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    public static final class VehicleInfo extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleAssetId = column("vehicle_asset_id", JDBCType.VARCHAR);

        public final SqlColumn<Long> relateAssetId = column("relate_asset_id", JDBCType.BIGINT);

        public final SqlColumn<String> engineNo = column("engine_no", JDBCType.VARCHAR);

        public final SqlColumn<String> engineModel = column("engine_model", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<String> licensePlate = column("license_plate", JDBCType.VARCHAR);

        public final SqlColumn<Integer> useQuotaType = column("use_quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> quotaType = column("quota_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleColorId = column("vehicle_color_id", JDBCType.INTEGER);

        public final SqlColumn<String> interiorColor = column("interior_color", JDBCType.VARCHAR);

        public final SqlColumn<Integer> supplierId = column("supplier_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> isRepurchase = column("is_repurchase", JDBCType.INTEGER);

        public final SqlColumn<Date> repurchaseDate = column("repurchase_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> repurchaseRequirements = column("repurchase_requirements", JDBCType.VARCHAR);

        public final SqlColumn<Integer> usageAgeLimit = column("usage_age_limit", JDBCType.INTEGER);

        public final SqlColumn<Integer> depreciationAgeLimit = column("depreciation_age_limit", JDBCType.INTEGER);

        public final SqlColumn<Date> realRetirementDate = column("real_retirement_date", JDBCType.TIMESTAMP);

        public final SqlColumn<BigDecimal> purchasePrice = column("purchase_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> purchaseTax = column("purchase_tax", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> licensePlatePrice = column("license_plate_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> licensePlateOtherPrice = column("license_plate_other_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> upholsterPrice = column("upholster_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> totalPrice = column("total_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> remainPrice = column("remain_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> secondHandPrice = column("second_hand_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> propertyStatus = column("property_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> businessLine = column("business_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> operatingStatus = column("operating_status", JDBCType.INTEGER);

        public final SqlColumn<String> subscriptionCompanyCode = column("subscription_company_code", JDBCType.VARCHAR);

        public final SqlColumn<String> subscriptionCompanyName = column("subscription_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> quotaAssetCompanyId = column("quota_asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> assetCompanyId = column("asset_company_id", JDBCType.INTEGER);

        public final SqlColumn<Long> ownOrganizationId = column("own_organization_id", JDBCType.BIGINT);

        public final SqlColumn<Long> usageOrganizationId = column("usage_organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> belongingTeam = column("belonging_team", JDBCType.VARCHAR);

        public final SqlColumn<Integer> obtainWayId = column("obtain_way_id", JDBCType.INTEGER);

        public final SqlColumn<Integer> areaId = column("area_id", JDBCType.INTEGER);

        public final SqlColumn<Long> depreciationDataId = column("depreciation_data_id", JDBCType.BIGINT);

        public final SqlColumn<String> latestPosition = column("latest_position", JDBCType.VARCHAR);

        public final SqlColumn<String> latestTotalMileage = column("latest_total_mileage", JDBCType.VARCHAR);

        public final SqlColumn<Integer> usageIdRegistrationCard = column("usage_id_registration_card", JDBCType.INTEGER);

        public final SqlColumn<Integer> vehicleTypeRegistrationCard = column("vehicle_type_registration_card", JDBCType.INTEGER);

        public final SqlColumn<Date> registrationDateRegistrationCard = column("registration_date_registration_card", JDBCType.DATE);

        public final SqlColumn<Date> issuanceDateRegistrationCard = column("issuance_date_registration_card", JDBCType.DATE);

        public final SqlColumn<Date> retirementDateRegistrationCard = column("retirement_date_registration_card", JDBCType.DATE);

        public final SqlColumn<Date> annualInspectionDueDateRegistrationCard = column("annual_inspection_due_date_registration_card", JDBCType.DATE);

        public final SqlColumn<String> fileNumber = column("file_number", JDBCType.VARCHAR);

        public final SqlColumn<Date> productDate = column("product_date", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> issuanceDate = column("issuance_date", JDBCType.TIMESTAMP);

        public final SqlColumn<String> certificateNumber = column("certificate_number", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleLicenseUrl = column("vehicle_license_url", JDBCType.VARCHAR);

        public final SqlColumn<String> certificateOwnershipUrl = column("certificate_ownership_url", JDBCType.VARCHAR);

        public final SqlColumn<String> certificateConformityUrl = column("certificate_conformity_url", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleInvoiceUrl = column("vehicle_invoice_url", JDBCType.VARCHAR);

        public final SqlColumn<String> purchaseTaxUrl = column("purchase_tax_url", JDBCType.VARCHAR);

        public final SqlColumn<String> operatingPermitUrl = column("operating_permit_url", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleInfo() {
            super("t_vehicle_info");
        }
    }
}