package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehiclePurchaseApply;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehiclePurchaseApplyMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    BasicColumn[] selectList = BasicColumn.columnList(id, purchaseApplyNo, approvalNumber, purchaseApplyStatus, applyProductLine, applyName, applyRemark, purchaseQuantity, takeDeliveryQuantity, preOccupied, vehicleOccupied, ownerId, applyOrgId, applyOrgCode, applyUserNo, applyUser, subscriptionCompanyCode, subscriptionCompanyName, supplierType, intentionNo, submitDate, applicantDepartmentCode, applicantDepartmentName, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehiclePurchaseApply> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehiclePurchaseApplyResult")
    Optional<VehiclePurchaseApply> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehiclePurchaseApplyResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="purchase_apply_no", property="purchaseApplyNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="approval_number", property="approvalNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="purchase_apply_status", property="purchaseApplyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="apply_product_line", property="applyProductLine", jdbcType=JdbcType.INTEGER),
        @Result(column="apply_name", property="applyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="apply_remark", property="applyRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="purchase_quantity", property="purchaseQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="take_delivery_quantity", property="takeDeliveryQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="pre_occupied", property="preOccupied", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_occupied", property="vehicleOccupied", jdbcType=JdbcType.INTEGER),
        @Result(column="owner_id", property="ownerId", jdbcType=JdbcType.INTEGER),
        @Result(column="apply_org_id", property="applyOrgId", jdbcType=JdbcType.BIGINT),
        @Result(column="apply_org_code", property="applyOrgCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="apply_user_no", property="applyUserNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="apply_user", property="applyUser", jdbcType=JdbcType.VARCHAR),
        @Result(column="subscription_company_code", property="subscriptionCompanyCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="subscription_company_name", property="subscriptionCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="supplier_type", property="supplierType", jdbcType=JdbcType.INTEGER),
        @Result(column="intention_no", property="intentionNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="submit_date", property="submitDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="applicant_department_code", property="applicantDepartmentCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="applicant_department_name", property="applicantDepartmentName", jdbcType=JdbcType.VARCHAR),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehiclePurchaseApply> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehiclePurchaseApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehiclePurchaseApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default int insert(VehiclePurchaseApply record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseApply, c ->
            c.map(purchaseApplyNo).toProperty("purchaseApplyNo")
            .map(approvalNumber).toProperty("approvalNumber")
            .map(purchaseApplyStatus).toProperty("purchaseApplyStatus")
            .map(applyProductLine).toProperty("applyProductLine")
            .map(applyName).toProperty("applyName")
            .map(applyRemark).toProperty("applyRemark")
            .map(purchaseQuantity).toProperty("purchaseQuantity")
            .map(takeDeliveryQuantity).toProperty("takeDeliveryQuantity")
            .map(preOccupied).toProperty("preOccupied")
            .map(vehicleOccupied).toProperty("vehicleOccupied")
            .map(ownerId).toProperty("ownerId")
            .map(applyOrgId).toProperty("applyOrgId")
            .map(applyOrgCode).toProperty("applyOrgCode")
            .map(applyUserNo).toProperty("applyUserNo")
            .map(applyUser).toProperty("applyUser")
            .map(subscriptionCompanyCode).toProperty("subscriptionCompanyCode")
            .map(subscriptionCompanyName).toProperty("subscriptionCompanyName")
            .map(supplierType).toProperty("supplierType")
            .map(intentionNo).toProperty("intentionNo")
            .map(submitDate).toProperty("submitDate")
            .map(applicantDepartmentCode).toProperty("applicantDepartmentCode")
            .map(applicantDepartmentName).toProperty("applicantDepartmentName")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default int insertSelective(VehiclePurchaseApply record) {
        return MyBatis3Utils.insert(this::insert, record, vehiclePurchaseApply, c ->
            c.map(purchaseApplyNo).toPropertyWhenPresent("purchaseApplyNo", record::getPurchaseApplyNo)
            .map(approvalNumber).toPropertyWhenPresent("approvalNumber", record::getApprovalNumber)
            .map(purchaseApplyStatus).toPropertyWhenPresent("purchaseApplyStatus", record::getPurchaseApplyStatus)
            .map(applyProductLine).toPropertyWhenPresent("applyProductLine", record::getApplyProductLine)
            .map(applyName).toPropertyWhenPresent("applyName", record::getApplyName)
            .map(applyRemark).toPropertyWhenPresent("applyRemark", record::getApplyRemark)
            .map(purchaseQuantity).toPropertyWhenPresent("purchaseQuantity", record::getPurchaseQuantity)
            .map(takeDeliveryQuantity).toPropertyWhenPresent("takeDeliveryQuantity", record::getTakeDeliveryQuantity)
            .map(preOccupied).toPropertyWhenPresent("preOccupied", record::getPreOccupied)
            .map(vehicleOccupied).toPropertyWhenPresent("vehicleOccupied", record::getVehicleOccupied)
            .map(ownerId).toPropertyWhenPresent("ownerId", record::getOwnerId)
            .map(applyOrgId).toPropertyWhenPresent("applyOrgId", record::getApplyOrgId)
            .map(applyOrgCode).toPropertyWhenPresent("applyOrgCode", record::getApplyOrgCode)
            .map(applyUserNo).toPropertyWhenPresent("applyUserNo", record::getApplyUserNo)
            .map(applyUser).toPropertyWhenPresent("applyUser", record::getApplyUser)
            .map(subscriptionCompanyCode).toPropertyWhenPresent("subscriptionCompanyCode", record::getSubscriptionCompanyCode)
            .map(subscriptionCompanyName).toPropertyWhenPresent("subscriptionCompanyName", record::getSubscriptionCompanyName)
            .map(supplierType).toPropertyWhenPresent("supplierType", record::getSupplierType)
            .map(intentionNo).toPropertyWhenPresent("intentionNo", record::getIntentionNo)
            .map(submitDate).toPropertyWhenPresent("submitDate", record::getSubmitDate)
            .map(applicantDepartmentCode).toPropertyWhenPresent("applicantDepartmentCode", record::getApplicantDepartmentCode)
            .map(applicantDepartmentName).toPropertyWhenPresent("applicantDepartmentName", record::getApplicantDepartmentName)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default Optional<VehiclePurchaseApply> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehiclePurchaseApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default List<VehiclePurchaseApply> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehiclePurchaseApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default List<VehiclePurchaseApply> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehiclePurchaseApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default Optional<VehiclePurchaseApply> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehiclePurchaseApply, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    static UpdateDSL<UpdateModel> updateAllColumns(VehiclePurchaseApply record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyNo).equalTo(record::getPurchaseApplyNo)
                .set(approvalNumber).equalTo(record::getApprovalNumber)
                .set(purchaseApplyStatus).equalTo(record::getPurchaseApplyStatus)
                .set(applyProductLine).equalTo(record::getApplyProductLine)
                .set(applyName).equalTo(record::getApplyName)
                .set(applyRemark).equalTo(record::getApplyRemark)
                .set(purchaseQuantity).equalTo(record::getPurchaseQuantity)
                .set(takeDeliveryQuantity).equalTo(record::getTakeDeliveryQuantity)
                .set(preOccupied).equalTo(record::getPreOccupied)
                .set(vehicleOccupied).equalTo(record::getVehicleOccupied)
                .set(ownerId).equalTo(record::getOwnerId)
                .set(applyOrgId).equalTo(record::getApplyOrgId)
                .set(applyOrgCode).equalTo(record::getApplyOrgCode)
                .set(applyUserNo).equalTo(record::getApplyUserNo)
                .set(applyUser).equalTo(record::getApplyUser)
                .set(subscriptionCompanyCode).equalTo(record::getSubscriptionCompanyCode)
                .set(subscriptionCompanyName).equalTo(record::getSubscriptionCompanyName)
                .set(supplierType).equalTo(record::getSupplierType)
                .set(intentionNo).equalTo(record::getIntentionNo)
                .set(submitDate).equalTo(record::getSubmitDate)
                .set(applicantDepartmentCode).equalTo(record::getApplicantDepartmentCode)
                .set(applicantDepartmentName).equalTo(record::getApplicantDepartmentName)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehiclePurchaseApply record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(purchaseApplyNo).equalToWhenPresent(record::getPurchaseApplyNo)
                .set(approvalNumber).equalToWhenPresent(record::getApprovalNumber)
                .set(purchaseApplyStatus).equalToWhenPresent(record::getPurchaseApplyStatus)
                .set(applyProductLine).equalToWhenPresent(record::getApplyProductLine)
                .set(applyName).equalToWhenPresent(record::getApplyName)
                .set(applyRemark).equalToWhenPresent(record::getApplyRemark)
                .set(purchaseQuantity).equalToWhenPresent(record::getPurchaseQuantity)
                .set(takeDeliveryQuantity).equalToWhenPresent(record::getTakeDeliveryQuantity)
                .set(preOccupied).equalToWhenPresent(record::getPreOccupied)
                .set(vehicleOccupied).equalToWhenPresent(record::getVehicleOccupied)
                .set(ownerId).equalToWhenPresent(record::getOwnerId)
                .set(applyOrgId).equalToWhenPresent(record::getApplyOrgId)
                .set(applyOrgCode).equalToWhenPresent(record::getApplyOrgCode)
                .set(applyUserNo).equalToWhenPresent(record::getApplyUserNo)
                .set(applyUser).equalToWhenPresent(record::getApplyUser)
                .set(subscriptionCompanyCode).equalToWhenPresent(record::getSubscriptionCompanyCode)
                .set(subscriptionCompanyName).equalToWhenPresent(record::getSubscriptionCompanyName)
                .set(supplierType).equalToWhenPresent(record::getSupplierType)
                .set(intentionNo).equalToWhenPresent(record::getIntentionNo)
                .set(submitDate).equalToWhenPresent(record::getSubmitDate)
                .set(applicantDepartmentCode).equalToWhenPresent(record::getApplicantDepartmentCode)
                .set(applicantDepartmentName).equalToWhenPresent(record::getApplicantDepartmentName)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default int updateByPrimaryKey(VehiclePurchaseApply record) {
        return update(c ->
            c.set(purchaseApplyNo).equalTo(record::getPurchaseApplyNo)
            .set(approvalNumber).equalTo(record::getApprovalNumber)
            .set(purchaseApplyStatus).equalTo(record::getPurchaseApplyStatus)
            .set(applyProductLine).equalTo(record::getApplyProductLine)
            .set(applyName).equalTo(record::getApplyName)
            .set(applyRemark).equalTo(record::getApplyRemark)
            .set(purchaseQuantity).equalTo(record::getPurchaseQuantity)
            .set(takeDeliveryQuantity).equalTo(record::getTakeDeliveryQuantity)
            .set(preOccupied).equalTo(record::getPreOccupied)
            .set(vehicleOccupied).equalTo(record::getVehicleOccupied)
            .set(ownerId).equalTo(record::getOwnerId)
            .set(applyOrgId).equalTo(record::getApplyOrgId)
            .set(applyOrgCode).equalTo(record::getApplyOrgCode)
            .set(applyUserNo).equalTo(record::getApplyUserNo)
            .set(applyUser).equalTo(record::getApplyUser)
            .set(subscriptionCompanyCode).equalTo(record::getSubscriptionCompanyCode)
            .set(subscriptionCompanyName).equalTo(record::getSubscriptionCompanyName)
            .set(supplierType).equalTo(record::getSupplierType)
            .set(intentionNo).equalTo(record::getIntentionNo)
            .set(submitDate).equalTo(record::getSubmitDate)
            .set(applicantDepartmentCode).equalTo(record::getApplicantDepartmentCode)
            .set(applicantDepartmentName).equalTo(record::getApplicantDepartmentName)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    default int updateByPrimaryKeySelective(VehiclePurchaseApply record) {
        return update(c ->
            c.set(purchaseApplyNo).equalToWhenPresent(record::getPurchaseApplyNo)
            .set(approvalNumber).equalToWhenPresent(record::getApprovalNumber)
            .set(purchaseApplyStatus).equalToWhenPresent(record::getPurchaseApplyStatus)
            .set(applyProductLine).equalToWhenPresent(record::getApplyProductLine)
            .set(applyName).equalToWhenPresent(record::getApplyName)
            .set(applyRemark).equalToWhenPresent(record::getApplyRemark)
            .set(purchaseQuantity).equalToWhenPresent(record::getPurchaseQuantity)
            .set(takeDeliveryQuantity).equalToWhenPresent(record::getTakeDeliveryQuantity)
            .set(preOccupied).equalToWhenPresent(record::getPreOccupied)
            .set(vehicleOccupied).equalToWhenPresent(record::getVehicleOccupied)
            .set(ownerId).equalToWhenPresent(record::getOwnerId)
            .set(applyOrgId).equalToWhenPresent(record::getApplyOrgId)
            .set(applyOrgCode).equalToWhenPresent(record::getApplyOrgCode)
            .set(applyUserNo).equalToWhenPresent(record::getApplyUserNo)
            .set(applyUser).equalToWhenPresent(record::getApplyUser)
            .set(subscriptionCompanyCode).equalToWhenPresent(record::getSubscriptionCompanyCode)
            .set(subscriptionCompanyName).equalToWhenPresent(record::getSubscriptionCompanyName)
            .set(supplierType).equalToWhenPresent(record::getSupplierType)
            .set(intentionNo).equalToWhenPresent(record::getIntentionNo)
            .set(submitDate).equalToWhenPresent(record::getSubmitDate)
            .set(applicantDepartmentCode).equalToWhenPresent(record::getApplicantDepartmentCode)
            .set(applicantDepartmentName).equalToWhenPresent(record::getApplicantDepartmentName)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}