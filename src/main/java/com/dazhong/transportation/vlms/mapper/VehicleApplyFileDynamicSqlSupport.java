package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleApplyFileDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_apply_file")
    public static final VehicleApplyFile vehicleApplyFile = new VehicleApplyFile();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.id")
    public static final SqlColumn<Long> id = vehicleApplyFile.id;

    /**
     * Database Column Remarks:
     *   外键ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.foreign_id")
    public static final SqlColumn<Long> foreignId = vehicleApplyFile.foreignId;

    /**
     * Database Column Remarks:
     *   业务类型 1-采购申请 2-转固申请 3-转固申请 4-车辆调拨 5-车辆转籍 6-切换业务类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.business_type")
    public static final SqlColumn<Integer> businessType = vehicleApplyFile.businessType;

    /**
     * Database Column Remarks:
     *   文件类型 数据字段
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.file_type")
    public static final SqlColumn<Integer> fileType = vehicleApplyFile.fileType;

    /**
     * Database Column Remarks:
     *   文件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.file_name")
    public static final SqlColumn<String> fileName = vehicleApplyFile.fileName;

    /**
     * Database Column Remarks:
     *   文件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.file_url")
    public static final SqlColumn<String> fileUrl = vehicleApplyFile.fileUrl;

    /**
     * Database Column Remarks:
     *   文件描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.file_desc")
    public static final SqlColumn<String> fileDesc = vehicleApplyFile.fileDesc;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleApplyFile.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.create_time")
    public static final SqlColumn<Date> createTime = vehicleApplyFile.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleApplyFile.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleApplyFile.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.update_time")
    public static final SqlColumn<Date> updateTime = vehicleApplyFile.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleApplyFile.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_apply_file.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleApplyFile.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_apply_file")
    public static final class VehicleApplyFile extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> foreignId = column("foreign_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> businessType = column("business_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> fileType = column("file_type", JDBCType.INTEGER);

        public final SqlColumn<String> fileName = column("file_name", JDBCType.VARCHAR);

        public final SqlColumn<String> fileUrl = column("file_url", JDBCType.VARCHAR);

        public final SqlColumn<String> fileDesc = column("file_desc", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleApplyFile() {
            super("t_vehicle_apply_file");
        }
    }
}