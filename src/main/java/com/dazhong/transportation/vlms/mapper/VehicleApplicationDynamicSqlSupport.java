package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleApplicationDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    public static final VehicleApplication vehicleApplication = new VehicleApplication();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.id")
    public static final SqlColumn<Long> id = vehicleApplication.id;

    /**
     * Database Column Remarks:
     *   钉钉审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.ding_talk_no")
    public static final SqlColumn<String> dingTalkNo = vehicleApplication.dingTalkNo;

    /**
     * Database Column Remarks:
     *   单据类型 1-车辆调拨 2-车辆转籍 3-切换业务类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.application_type")
    public static final SqlColumn<Integer> applicationType = vehicleApplication.applicationType;

    /**
     * Database Column Remarks:
     *   单据标题
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.document_title")
    public static final SqlColumn<String> documentTitle = vehicleApplication.documentTitle;

    /**
     * Database Column Remarks:
     *   单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.document_status")
    public static final SqlColumn<Integer> documentStatus = vehicleApplication.documentStatus;

    /**
     * Database Column Remarks:
     *   申请人审批部门id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.originator_dept_id")
    public static final SqlColumn<Long> originatorDeptId = vehicleApplication.originatorDeptId;

    /**
     * Database Column Remarks:
     *   申请人审批部门名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.originator_dept_name")
    public static final SqlColumn<String> originatorDeptName = vehicleApplication.originatorDeptName;

    /**
     * Database Column Remarks:
     *   调出部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.transfer_from_department_code")
    public static final SqlColumn<String> transferFromDepartmentCode = vehicleApplication.transferFromDepartmentCode;

    /**
     * Database Column Remarks:
     *   调出部门名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.transfer_from_department_name")
    public static final SqlColumn<String> transferFromDepartmentName = vehicleApplication.transferFromDepartmentName;

    /**
     * Database Column Remarks:
     *   调入部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.transfer_to_department_code")
    public static final SqlColumn<String> transferToDepartmentCode = vehicleApplication.transferToDepartmentCode;

    /**
     * Database Column Remarks:
     *   调入部门名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.transfer_to_department_name")
    public static final SqlColumn<String> transferToDepartmentName = vehicleApplication.transferToDepartmentName;

    /**
     * Database Column Remarks:
     *   所在部门id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.organization_id")
    public static final SqlColumn<Long> organizationId = vehicleApplication.organizationId;

    /**
     * Database Column Remarks:
     *   所在部门
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.organization_name")
    public static final SqlColumn<String> organizationName = vehicleApplication.organizationName;

    /**
     * Database Column Remarks:
     *   单据所属资产公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.owner_id")
    public static final SqlColumn<Integer> ownerId = vehicleApplication.ownerId;

    /**
     * Database Column Remarks:
     *   单据所属资产公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.owner_name")
    public static final SqlColumn<String> ownerName = vehicleApplication.ownerName;

    /**
     * Database Column Remarks:
     *   提交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.submit_date")
    public static final SqlColumn<Date> submitDate = vehicleApplication.submitDate;

    /**
     * Database Column Remarks:
     *   产品线 1-巡网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.product_line")
    public static final SqlColumn<Integer> productLine = vehicleApplication.productLine;

    /**
     * Database Column Remarks:
     *   承担方
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.cost_bearer")
    public static final SqlColumn<String> costBearer = vehicleApplication.costBearer;

    /**
     * Database Column Remarks:
     *   合同号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.contract_number")
    public static final SqlColumn<String> contractNumber = vehicleApplication.contractNumber;

    /**
     * Database Column Remarks:
     *   变动原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.update_reason")
    public static final SqlColumn<String> updateReason = vehicleApplication.updateReason;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.remark")
    public static final SqlColumn<String> remark = vehicleApplication.remark;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleApplication.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.create_time")
    public static final SqlColumn<Date> createTime = vehicleApplication.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleApplication.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleApplication.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.update_time")
    public static final SqlColumn<Date> updateTime = vehicleApplication.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleApplication.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleApplication.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application")
    public static final class VehicleApplication extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> dingTalkNo = column("ding_talk_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> applicationType = column("application_type", JDBCType.INTEGER);

        public final SqlColumn<String> documentTitle = column("document_title", JDBCType.VARCHAR);

        public final SqlColumn<Integer> documentStatus = column("document_status", JDBCType.INTEGER);

        public final SqlColumn<Long> originatorDeptId = column("originator_dept_id", JDBCType.BIGINT);

        public final SqlColumn<String> originatorDeptName = column("originator_dept_name", JDBCType.VARCHAR);

        public final SqlColumn<String> transferFromDepartmentCode = column("transfer_from_department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> transferFromDepartmentName = column("transfer_from_department_name", JDBCType.VARCHAR);

        public final SqlColumn<String> transferToDepartmentCode = column("transfer_to_department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> transferToDepartmentName = column("transfer_to_department_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> organizationId = column("organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> organizationName = column("organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<String> ownerName = column("owner_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> submitDate = column("submit_date", JDBCType.DATE);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<String> costBearer = column("cost_bearer", JDBCType.VARCHAR);

        public final SqlColumn<String> contractNumber = column("contract_number", JDBCType.VARCHAR);

        public final SqlColumn<String> updateReason = column("update_reason", JDBCType.VARCHAR);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleApplication() {
            super("t_vehicle_application");
        }
    }
}