package com.dazhong.transportation.vlms.mapper;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

public final class VehicleReverseDisposalDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    public static final VehicleReverseDisposal vehicleReverseDisposal = new VehicleReverseDisposal();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.id")
    public static final SqlColumn<Long> id = vehicleReverseDisposal.id;

    /**
     * Database Column Remarks:
     *   单据号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.document_no")
    public static final SqlColumn<String> documentNo = vehicleReverseDisposal.documentNo;

    /**
     * Database Column Remarks:
     *   单据标题
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.document_title")
    public static final SqlColumn<String> documentTitle = vehicleReverseDisposal.documentTitle;

    /**
     * Database Column Remarks:
     *   单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.document_status")
    public static final SqlColumn<Integer> documentStatus = vehicleReverseDisposal.documentStatus;

    /**
     * Database Column Remarks:
     *   条线 1-巡网业务线 2-商务业务线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.product_line")
    public static final SqlColumn<Integer> productLine = vehicleReverseDisposal.productLine;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.originator_dept_id")
    public static final SqlColumn<Long> originatorDeptId = vehicleReverseDisposal.originatorDeptId;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.originator_dept_name")
    public static final SqlColumn<String> originatorDeptName = vehicleReverseDisposal.originatorDeptName;

    /**
     * Database Column Remarks:
     *   所在部门id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.organization_id")
    public static final SqlColumn<Long> organizationId = vehicleReverseDisposal.organizationId;

    /**
     * Database Column Remarks:
     *   所在部门
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.organization_name")
    public static final SqlColumn<String> organizationName = vehicleReverseDisposal.organizationName;

    /**
     * Database Column Remarks:
     *   单据所属资产公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.owner_id")
    public static final SqlColumn<Integer> ownerId = vehicleReverseDisposal.ownerId;

    /**
     * Database Column Remarks:
     *   单据所属资产公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.owner_name")
    public static final SqlColumn<String> ownerName = vehicleReverseDisposal.ownerName;

    /**
     * Database Column Remarks:
     *   资产公司所属部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.department_code")
    public static final SqlColumn<String> departmentCode = vehicleReverseDisposal.departmentCode;

    /**
     * Database Column Remarks:
     *   资产公司所属部门名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.department_name")
    public static final SqlColumn<String> departmentName = vehicleReverseDisposal.departmentName;

    /**
     * Database Column Remarks:
     *   钉钉审批单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.ding_talk_no")
    public static final SqlColumn<String> dingTalkNo = vehicleReverseDisposal.dingTalkNo;

    /**
     * Database Column Remarks:
     *   提交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.submit_date")
    public static final SqlColumn<Date> submitDate = vehicleReverseDisposal.submitDate;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.remark")
    public static final SqlColumn<String> remark = vehicleReverseDisposal.remark;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.is_deleted")
    public static final SqlColumn<Integer> isDeleted = vehicleReverseDisposal.isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.create_time")
    public static final SqlColumn<Date> createTime = vehicleReverseDisposal.createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.create_oper_id")
    public static final SqlColumn<Long> createOperId = vehicleReverseDisposal.createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.create_oper_name")
    public static final SqlColumn<String> createOperName = vehicleReverseDisposal.createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.update_time")
    public static final SqlColumn<Date> updateTime = vehicleReverseDisposal.updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.update_oper_id")
    public static final SqlColumn<Long> updateOperId = vehicleReverseDisposal.updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_reverse_disposal.update_oper_name")
    public static final SqlColumn<String> updateOperName = vehicleReverseDisposal.updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_reverse_disposal")
    public static final class VehicleReverseDisposal extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> documentNo = column("document_no", JDBCType.VARCHAR);

        public final SqlColumn<String> documentTitle = column("document_title", JDBCType.VARCHAR);

        public final SqlColumn<Integer> documentStatus = column("document_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Long> originatorDeptId = column("originator_dept_id", JDBCType.BIGINT);

        public final SqlColumn<String> originatorDeptName = column("originator_dept_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> organizationId = column("organization_id", JDBCType.BIGINT);

        public final SqlColumn<String> organizationName = column("organization_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> ownerId = column("owner_id", JDBCType.INTEGER);

        public final SqlColumn<String> ownerName = column("owner_name", JDBCType.VARCHAR);

        public final SqlColumn<String> departmentCode = column("department_code", JDBCType.VARCHAR);

        public final SqlColumn<String> departmentName = column("department_name", JDBCType.VARCHAR);

        public final SqlColumn<String> dingTalkNo = column("ding_talk_no", JDBCType.VARCHAR);

        public final SqlColumn<Date> submitDate = column("submit_date", JDBCType.DATE);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> isDeleted = column("is_deleted", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> createOperId = column("create_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> createOperName = column("create_oper_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateOperId = column("update_oper_id", JDBCType.BIGINT);

        public final SqlColumn<String> updateOperName = column("update_oper_name", JDBCType.VARCHAR);

        public VehicleReverseDisposal() {
            super("t_vehicle_reverse_disposal");
        }
    }
}