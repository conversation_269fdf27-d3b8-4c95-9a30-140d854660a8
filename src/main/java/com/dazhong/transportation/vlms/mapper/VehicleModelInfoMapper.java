package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.VehicleModelInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.VehicleModelInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface VehicleModelInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, autohomeVehicleModelId, vehicleBrandName, vehicleSeriesName, vehicleModelName, financialModelName, vehicleAbbreviationId, assessPassenger, busAssessPassenger, engineModelNo, gasTypeId, vehicleLevel, wheelParam, exhaustId, ttmMonth, outLength, outWidth, outHeight, wheelBase1, totalMass, fuelTankCapacity, fuelLabelName, capacity, batteryCapacity, vehicleRange, fastChargingTime, slowChargingTime, fuelEconomyMiit, priceOnAutohome, vehicleModelNo, power, treadFront, treadRear, wheelQuantity, springLamination, wheelBase2, wheelBase3, axleQuantity, containerLength, containerWidth, containerHeight, assessMass, tractionMass, cabPassenger, manufactureLocation, doorQuantity, gearQuantity, acceleration, speed, turningRadius, roadClearance, gradient, fuelEconomy, torque, compressionRatio, manufacturerId, vehicleBrandId, vehicleTypeId, wheelDriveId, breakModeId, turnModeId, drivePositionId, enginePositionId, gearBoxTypeId, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<VehicleModelInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("VehicleModelInfoResult")
    Optional<VehicleModelInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="VehicleModelInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="autohome_vehicle_model_id", property="autohomeVehicleModelId", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_brand_name", property="vehicleBrandName", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_series_name", property="vehicleSeriesName", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_name", property="vehicleModelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="financial_model_name", property="financialModelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_abbreviation_id", property="vehicleAbbreviationId", jdbcType=JdbcType.INTEGER),
        @Result(column="assess_passenger", property="assessPassenger", jdbcType=JdbcType.INTEGER),
        @Result(column="bus_assess_passenger", property="busAssessPassenger", jdbcType=JdbcType.INTEGER),
        @Result(column="engine_model_no", property="engineModelNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="gas_type_id", property="gasTypeId", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_level", property="vehicleLevel", jdbcType=JdbcType.INTEGER),
        @Result(column="wheel_param", property="wheelParam", jdbcType=JdbcType.VARCHAR),
        @Result(column="exhaust_id", property="exhaustId", jdbcType=JdbcType.INTEGER),
        @Result(column="ttm_month", property="ttmMonth", jdbcType=JdbcType.VARCHAR),
        @Result(column="out_length", property="outLength", jdbcType=JdbcType.INTEGER),
        @Result(column="out_width", property="outWidth", jdbcType=JdbcType.INTEGER),
        @Result(column="out_height", property="outHeight", jdbcType=JdbcType.INTEGER),
        @Result(column="wheel_base1", property="wheelBase1", jdbcType=JdbcType.INTEGER),
        @Result(column="total_mass", property="totalMass", jdbcType=JdbcType.INTEGER),
        @Result(column="fuel_tank_capacity", property="fuelTankCapacity", jdbcType=JdbcType.INTEGER),
        @Result(column="fuel_label_name", property="fuelLabelName", jdbcType=JdbcType.VARCHAR),
        @Result(column="capacity", property="capacity", jdbcType=JdbcType.DECIMAL),
        @Result(column="battery_capacity", property="batteryCapacity", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_range", property="vehicleRange", jdbcType=JdbcType.INTEGER),
        @Result(column="fast_charging_time", property="fastChargingTime", jdbcType=JdbcType.DECIMAL),
        @Result(column="slow_charging_time", property="slowChargingTime", jdbcType=JdbcType.DECIMAL),
        @Result(column="fuel_economy_miit", property="fuelEconomyMiit", jdbcType=JdbcType.DECIMAL),
        @Result(column="price_on_autohome", property="priceOnAutohome", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_model_no", property="vehicleModelNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="power", property="power", jdbcType=JdbcType.INTEGER),
        @Result(column="tread_front", property="treadFront", jdbcType=JdbcType.INTEGER),
        @Result(column="tread_rear", property="treadRear", jdbcType=JdbcType.INTEGER),
        @Result(column="wheel_quantity", property="wheelQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="spring_lamination", property="springLamination", jdbcType=JdbcType.INTEGER),
        @Result(column="wheel_base2", property="wheelBase2", jdbcType=JdbcType.INTEGER),
        @Result(column="wheel_base3", property="wheelBase3", jdbcType=JdbcType.INTEGER),
        @Result(column="axle_quantity", property="axleQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="container_length", property="containerLength", jdbcType=JdbcType.INTEGER),
        @Result(column="container_width", property="containerWidth", jdbcType=JdbcType.INTEGER),
        @Result(column="container_height", property="containerHeight", jdbcType=JdbcType.INTEGER),
        @Result(column="assess_mass", property="assessMass", jdbcType=JdbcType.INTEGER),
        @Result(column="traction_mass", property="tractionMass", jdbcType=JdbcType.INTEGER),
        @Result(column="cab_passenger", property="cabPassenger", jdbcType=JdbcType.INTEGER),
        @Result(column="manufacture_location", property="manufactureLocation", jdbcType=JdbcType.INTEGER),
        @Result(column="door_quantity", property="doorQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="gear_quantity", property="gearQuantity", jdbcType=JdbcType.INTEGER),
        @Result(column="acceleration", property="acceleration", jdbcType=JdbcType.DECIMAL),
        @Result(column="speed", property="speed", jdbcType=JdbcType.INTEGER),
        @Result(column="turning_radius", property="turningRadius", jdbcType=JdbcType.DECIMAL),
        @Result(column="road_clearance", property="roadClearance", jdbcType=JdbcType.INTEGER),
        @Result(column="gradient", property="gradient", jdbcType=JdbcType.INTEGER),
        @Result(column="fuel_economy", property="fuelEconomy", jdbcType=JdbcType.DECIMAL),
        @Result(column="torque", property="torque", jdbcType=JdbcType.INTEGER),
        @Result(column="compression_ratio", property="compressionRatio", jdbcType=JdbcType.VARCHAR),
        @Result(column="manufacturer_id", property="manufacturerId", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_brand_id", property="vehicleBrandId", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_type_id", property="vehicleTypeId", jdbcType=JdbcType.INTEGER),
        @Result(column="wheel_drive_id", property="wheelDriveId", jdbcType=JdbcType.INTEGER),
        @Result(column="break_mode_id", property="breakModeId", jdbcType=JdbcType.INTEGER),
        @Result(column="turn_mode_id", property="turnModeId", jdbcType=JdbcType.INTEGER),
        @Result(column="drive_position_id", property="drivePositionId", jdbcType=JdbcType.INTEGER),
        @Result(column="engine_position_id", property="enginePositionId", jdbcType=JdbcType.INTEGER),
        @Result(column="gear_box_type_id", property="gearBoxTypeId", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<VehicleModelInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, vehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, vehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default int insert(VehicleModelInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleModelInfo, c ->
            c.map(autohomeVehicleModelId).toProperty("autohomeVehicleModelId")
            .map(vehicleBrandName).toProperty("vehicleBrandName")
            .map(vehicleSeriesName).toProperty("vehicleSeriesName")
            .map(vehicleModelName).toProperty("vehicleModelName")
            .map(financialModelName).toProperty("financialModelName")
            .map(vehicleAbbreviationId).toProperty("vehicleAbbreviationId")
            .map(assessPassenger).toProperty("assessPassenger")
            .map(busAssessPassenger).toProperty("busAssessPassenger")
            .map(engineModelNo).toProperty("engineModelNo")
            .map(gasTypeId).toProperty("gasTypeId")
            .map(vehicleLevel).toProperty("vehicleLevel")
            .map(wheelParam).toProperty("wheelParam")
            .map(exhaustId).toProperty("exhaustId")
            .map(ttmMonth).toProperty("ttmMonth")
            .map(outLength).toProperty("outLength")
            .map(outWidth).toProperty("outWidth")
            .map(outHeight).toProperty("outHeight")
            .map(wheelBase1).toProperty("wheelBase1")
            .map(totalMass).toProperty("totalMass")
            .map(fuelTankCapacity).toProperty("fuelTankCapacity")
            .map(fuelLabelName).toProperty("fuelLabelName")
            .map(capacity).toProperty("capacity")
            .map(batteryCapacity).toProperty("batteryCapacity")
            .map(vehicleRange).toProperty("vehicleRange")
            .map(fastChargingTime).toProperty("fastChargingTime")
            .map(slowChargingTime).toProperty("slowChargingTime")
            .map(fuelEconomyMiit).toProperty("fuelEconomyMiit")
            .map(priceOnAutohome).toProperty("priceOnAutohome")
            .map(vehicleModelNo).toProperty("vehicleModelNo")
            .map(power).toProperty("power")
            .map(treadFront).toProperty("treadFront")
            .map(treadRear).toProperty("treadRear")
            .map(wheelQuantity).toProperty("wheelQuantity")
            .map(springLamination).toProperty("springLamination")
            .map(wheelBase2).toProperty("wheelBase2")
            .map(wheelBase3).toProperty("wheelBase3")
            .map(axleQuantity).toProperty("axleQuantity")
            .map(containerLength).toProperty("containerLength")
            .map(containerWidth).toProperty("containerWidth")
            .map(containerHeight).toProperty("containerHeight")
            .map(assessMass).toProperty("assessMass")
            .map(tractionMass).toProperty("tractionMass")
            .map(cabPassenger).toProperty("cabPassenger")
            .map(manufactureLocation).toProperty("manufactureLocation")
            .map(doorQuantity).toProperty("doorQuantity")
            .map(gearQuantity).toProperty("gearQuantity")
            .map(acceleration).toProperty("acceleration")
            .map(speed).toProperty("speed")
            .map(turningRadius).toProperty("turningRadius")
            .map(roadClearance).toProperty("roadClearance")
            .map(gradient).toProperty("gradient")
            .map(fuelEconomy).toProperty("fuelEconomy")
            .map(torque).toProperty("torque")
            .map(compressionRatio).toProperty("compressionRatio")
            .map(manufacturerId).toProperty("manufacturerId")
            .map(vehicleBrandId).toProperty("vehicleBrandId")
            .map(vehicleTypeId).toProperty("vehicleTypeId")
            .map(wheelDriveId).toProperty("wheelDriveId")
            .map(breakModeId).toProperty("breakModeId")
            .map(turnModeId).toProperty("turnModeId")
            .map(drivePositionId).toProperty("drivePositionId")
            .map(enginePositionId).toProperty("enginePositionId")
            .map(gearBoxTypeId).toProperty("gearBoxTypeId")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default int insertSelective(VehicleModelInfo record) {
        return MyBatis3Utils.insert(this::insert, record, vehicleModelInfo, c ->
            c.map(autohomeVehicleModelId).toPropertyWhenPresent("autohomeVehicleModelId", record::getAutohomeVehicleModelId)
            .map(vehicleBrandName).toPropertyWhenPresent("vehicleBrandName", record::getVehicleBrandName)
            .map(vehicleSeriesName).toPropertyWhenPresent("vehicleSeriesName", record::getVehicleSeriesName)
            .map(vehicleModelName).toPropertyWhenPresent("vehicleModelName", record::getVehicleModelName)
            .map(financialModelName).toPropertyWhenPresent("financialModelName", record::getFinancialModelName)
            .map(vehicleAbbreviationId).toPropertyWhenPresent("vehicleAbbreviationId", record::getVehicleAbbreviationId)
            .map(assessPassenger).toPropertyWhenPresent("assessPassenger", record::getAssessPassenger)
            .map(busAssessPassenger).toPropertyWhenPresent("busAssessPassenger", record::getBusAssessPassenger)
            .map(engineModelNo).toPropertyWhenPresent("engineModelNo", record::getEngineModelNo)
            .map(gasTypeId).toPropertyWhenPresent("gasTypeId", record::getGasTypeId)
            .map(vehicleLevel).toPropertyWhenPresent("vehicleLevel", record::getVehicleLevel)
            .map(wheelParam).toPropertyWhenPresent("wheelParam", record::getWheelParam)
            .map(exhaustId).toPropertyWhenPresent("exhaustId", record::getExhaustId)
            .map(ttmMonth).toPropertyWhenPresent("ttmMonth", record::getTtmMonth)
            .map(outLength).toPropertyWhenPresent("outLength", record::getOutLength)
            .map(outWidth).toPropertyWhenPresent("outWidth", record::getOutWidth)
            .map(outHeight).toPropertyWhenPresent("outHeight", record::getOutHeight)
            .map(wheelBase1).toPropertyWhenPresent("wheelBase1", record::getWheelBase1)
            .map(totalMass).toPropertyWhenPresent("totalMass", record::getTotalMass)
            .map(fuelTankCapacity).toPropertyWhenPresent("fuelTankCapacity", record::getFuelTankCapacity)
            .map(fuelLabelName).toPropertyWhenPresent("fuelLabelName", record::getFuelLabelName)
            .map(capacity).toPropertyWhenPresent("capacity", record::getCapacity)
            .map(batteryCapacity).toPropertyWhenPresent("batteryCapacity", record::getBatteryCapacity)
            .map(vehicleRange).toPropertyWhenPresent("vehicleRange", record::getVehicleRange)
            .map(fastChargingTime).toPropertyWhenPresent("fastChargingTime", record::getFastChargingTime)
            .map(slowChargingTime).toPropertyWhenPresent("slowChargingTime", record::getSlowChargingTime)
            .map(fuelEconomyMiit).toPropertyWhenPresent("fuelEconomyMiit", record::getFuelEconomyMiit)
            .map(priceOnAutohome).toPropertyWhenPresent("priceOnAutohome", record::getPriceOnAutohome)
            .map(vehicleModelNo).toPropertyWhenPresent("vehicleModelNo", record::getVehicleModelNo)
            .map(power).toPropertyWhenPresent("power", record::getPower)
            .map(treadFront).toPropertyWhenPresent("treadFront", record::getTreadFront)
            .map(treadRear).toPropertyWhenPresent("treadRear", record::getTreadRear)
            .map(wheelQuantity).toPropertyWhenPresent("wheelQuantity", record::getWheelQuantity)
            .map(springLamination).toPropertyWhenPresent("springLamination", record::getSpringLamination)
            .map(wheelBase2).toPropertyWhenPresent("wheelBase2", record::getWheelBase2)
            .map(wheelBase3).toPropertyWhenPresent("wheelBase3", record::getWheelBase3)
            .map(axleQuantity).toPropertyWhenPresent("axleQuantity", record::getAxleQuantity)
            .map(containerLength).toPropertyWhenPresent("containerLength", record::getContainerLength)
            .map(containerWidth).toPropertyWhenPresent("containerWidth", record::getContainerWidth)
            .map(containerHeight).toPropertyWhenPresent("containerHeight", record::getContainerHeight)
            .map(assessMass).toPropertyWhenPresent("assessMass", record::getAssessMass)
            .map(tractionMass).toPropertyWhenPresent("tractionMass", record::getTractionMass)
            .map(cabPassenger).toPropertyWhenPresent("cabPassenger", record::getCabPassenger)
            .map(manufactureLocation).toPropertyWhenPresent("manufactureLocation", record::getManufactureLocation)
            .map(doorQuantity).toPropertyWhenPresent("doorQuantity", record::getDoorQuantity)
            .map(gearQuantity).toPropertyWhenPresent("gearQuantity", record::getGearQuantity)
            .map(acceleration).toPropertyWhenPresent("acceleration", record::getAcceleration)
            .map(speed).toPropertyWhenPresent("speed", record::getSpeed)
            .map(turningRadius).toPropertyWhenPresent("turningRadius", record::getTurningRadius)
            .map(roadClearance).toPropertyWhenPresent("roadClearance", record::getRoadClearance)
            .map(gradient).toPropertyWhenPresent("gradient", record::getGradient)
            .map(fuelEconomy).toPropertyWhenPresent("fuelEconomy", record::getFuelEconomy)
            .map(torque).toPropertyWhenPresent("torque", record::getTorque)
            .map(compressionRatio).toPropertyWhenPresent("compressionRatio", record::getCompressionRatio)
            .map(manufacturerId).toPropertyWhenPresent("manufacturerId", record::getManufacturerId)
            .map(vehicleBrandId).toPropertyWhenPresent("vehicleBrandId", record::getVehicleBrandId)
            .map(vehicleTypeId).toPropertyWhenPresent("vehicleTypeId", record::getVehicleTypeId)
            .map(wheelDriveId).toPropertyWhenPresent("wheelDriveId", record::getWheelDriveId)
            .map(breakModeId).toPropertyWhenPresent("breakModeId", record::getBreakModeId)
            .map(turnModeId).toPropertyWhenPresent("turnModeId", record::getTurnModeId)
            .map(drivePositionId).toPropertyWhenPresent("drivePositionId", record::getDrivePositionId)
            .map(enginePositionId).toPropertyWhenPresent("enginePositionId", record::getEnginePositionId)
            .map(gearBoxTypeId).toPropertyWhenPresent("gearBoxTypeId", record::getGearBoxTypeId)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default Optional<VehicleModelInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, vehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default List<VehicleModelInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, vehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default List<VehicleModelInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, vehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default Optional<VehicleModelInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, vehicleModelInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    static UpdateDSL<UpdateModel> updateAllColumns(VehicleModelInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(autohomeVehicleModelId).equalTo(record::getAutohomeVehicleModelId)
                .set(vehicleBrandName).equalTo(record::getVehicleBrandName)
                .set(vehicleSeriesName).equalTo(record::getVehicleSeriesName)
                .set(vehicleModelName).equalTo(record::getVehicleModelName)
                .set(financialModelName).equalTo(record::getFinancialModelName)
                .set(vehicleAbbreviationId).equalTo(record::getVehicleAbbreviationId)
                .set(assessPassenger).equalTo(record::getAssessPassenger)
                .set(busAssessPassenger).equalTo(record::getBusAssessPassenger)
                .set(engineModelNo).equalTo(record::getEngineModelNo)
                .set(gasTypeId).equalTo(record::getGasTypeId)
                .set(vehicleLevel).equalTo(record::getVehicleLevel)
                .set(wheelParam).equalTo(record::getWheelParam)
                .set(exhaustId).equalTo(record::getExhaustId)
                .set(ttmMonth).equalTo(record::getTtmMonth)
                .set(outLength).equalTo(record::getOutLength)
                .set(outWidth).equalTo(record::getOutWidth)
                .set(outHeight).equalTo(record::getOutHeight)
                .set(wheelBase1).equalTo(record::getWheelBase1)
                .set(totalMass).equalTo(record::getTotalMass)
                .set(fuelTankCapacity).equalTo(record::getFuelTankCapacity)
                .set(fuelLabelName).equalTo(record::getFuelLabelName)
                .set(capacity).equalTo(record::getCapacity)
                .set(batteryCapacity).equalTo(record::getBatteryCapacity)
                .set(vehicleRange).equalTo(record::getVehicleRange)
                .set(fastChargingTime).equalTo(record::getFastChargingTime)
                .set(slowChargingTime).equalTo(record::getSlowChargingTime)
                .set(fuelEconomyMiit).equalTo(record::getFuelEconomyMiit)
                .set(priceOnAutohome).equalTo(record::getPriceOnAutohome)
                .set(vehicleModelNo).equalTo(record::getVehicleModelNo)
                .set(power).equalTo(record::getPower)
                .set(treadFront).equalTo(record::getTreadFront)
                .set(treadRear).equalTo(record::getTreadRear)
                .set(wheelQuantity).equalTo(record::getWheelQuantity)
                .set(springLamination).equalTo(record::getSpringLamination)
                .set(wheelBase2).equalTo(record::getWheelBase2)
                .set(wheelBase3).equalTo(record::getWheelBase3)
                .set(axleQuantity).equalTo(record::getAxleQuantity)
                .set(containerLength).equalTo(record::getContainerLength)
                .set(containerWidth).equalTo(record::getContainerWidth)
                .set(containerHeight).equalTo(record::getContainerHeight)
                .set(assessMass).equalTo(record::getAssessMass)
                .set(tractionMass).equalTo(record::getTractionMass)
                .set(cabPassenger).equalTo(record::getCabPassenger)
                .set(manufactureLocation).equalTo(record::getManufactureLocation)
                .set(doorQuantity).equalTo(record::getDoorQuantity)
                .set(gearQuantity).equalTo(record::getGearQuantity)
                .set(acceleration).equalTo(record::getAcceleration)
                .set(speed).equalTo(record::getSpeed)
                .set(turningRadius).equalTo(record::getTurningRadius)
                .set(roadClearance).equalTo(record::getRoadClearance)
                .set(gradient).equalTo(record::getGradient)
                .set(fuelEconomy).equalTo(record::getFuelEconomy)
                .set(torque).equalTo(record::getTorque)
                .set(compressionRatio).equalTo(record::getCompressionRatio)
                .set(manufacturerId).equalTo(record::getManufacturerId)
                .set(vehicleBrandId).equalTo(record::getVehicleBrandId)
                .set(vehicleTypeId).equalTo(record::getVehicleTypeId)
                .set(wheelDriveId).equalTo(record::getWheelDriveId)
                .set(breakModeId).equalTo(record::getBreakModeId)
                .set(turnModeId).equalTo(record::getTurnModeId)
                .set(drivePositionId).equalTo(record::getDrivePositionId)
                .set(enginePositionId).equalTo(record::getEnginePositionId)
                .set(gearBoxTypeId).equalTo(record::getGearBoxTypeId)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(VehicleModelInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(autohomeVehicleModelId).equalToWhenPresent(record::getAutohomeVehicleModelId)
                .set(vehicleBrandName).equalToWhenPresent(record::getVehicleBrandName)
                .set(vehicleSeriesName).equalToWhenPresent(record::getVehicleSeriesName)
                .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
                .set(financialModelName).equalToWhenPresent(record::getFinancialModelName)
                .set(vehicleAbbreviationId).equalToWhenPresent(record::getVehicleAbbreviationId)
                .set(assessPassenger).equalToWhenPresent(record::getAssessPassenger)
                .set(busAssessPassenger).equalToWhenPresent(record::getBusAssessPassenger)
                .set(engineModelNo).equalToWhenPresent(record::getEngineModelNo)
                .set(gasTypeId).equalToWhenPresent(record::getGasTypeId)
                .set(vehicleLevel).equalToWhenPresent(record::getVehicleLevel)
                .set(wheelParam).equalToWhenPresent(record::getWheelParam)
                .set(exhaustId).equalToWhenPresent(record::getExhaustId)
                .set(ttmMonth).equalToWhenPresent(record::getTtmMonth)
                .set(outLength).equalToWhenPresent(record::getOutLength)
                .set(outWidth).equalToWhenPresent(record::getOutWidth)
                .set(outHeight).equalToWhenPresent(record::getOutHeight)
                .set(wheelBase1).equalToWhenPresent(record::getWheelBase1)
                .set(totalMass).equalToWhenPresent(record::getTotalMass)
                .set(fuelTankCapacity).equalToWhenPresent(record::getFuelTankCapacity)
                .set(fuelLabelName).equalToWhenPresent(record::getFuelLabelName)
                .set(capacity).equalToWhenPresent(record::getCapacity)
                .set(batteryCapacity).equalToWhenPresent(record::getBatteryCapacity)
                .set(vehicleRange).equalToWhenPresent(record::getVehicleRange)
                .set(fastChargingTime).equalToWhenPresent(record::getFastChargingTime)
                .set(slowChargingTime).equalToWhenPresent(record::getSlowChargingTime)
                .set(fuelEconomyMiit).equalToWhenPresent(record::getFuelEconomyMiit)
                .set(priceOnAutohome).equalToWhenPresent(record::getPriceOnAutohome)
                .set(vehicleModelNo).equalToWhenPresent(record::getVehicleModelNo)
                .set(power).equalToWhenPresent(record::getPower)
                .set(treadFront).equalToWhenPresent(record::getTreadFront)
                .set(treadRear).equalToWhenPresent(record::getTreadRear)
                .set(wheelQuantity).equalToWhenPresent(record::getWheelQuantity)
                .set(springLamination).equalToWhenPresent(record::getSpringLamination)
                .set(wheelBase2).equalToWhenPresent(record::getWheelBase2)
                .set(wheelBase3).equalToWhenPresent(record::getWheelBase3)
                .set(axleQuantity).equalToWhenPresent(record::getAxleQuantity)
                .set(containerLength).equalToWhenPresent(record::getContainerLength)
                .set(containerWidth).equalToWhenPresent(record::getContainerWidth)
                .set(containerHeight).equalToWhenPresent(record::getContainerHeight)
                .set(assessMass).equalToWhenPresent(record::getAssessMass)
                .set(tractionMass).equalToWhenPresent(record::getTractionMass)
                .set(cabPassenger).equalToWhenPresent(record::getCabPassenger)
                .set(manufactureLocation).equalToWhenPresent(record::getManufactureLocation)
                .set(doorQuantity).equalToWhenPresent(record::getDoorQuantity)
                .set(gearQuantity).equalToWhenPresent(record::getGearQuantity)
                .set(acceleration).equalToWhenPresent(record::getAcceleration)
                .set(speed).equalToWhenPresent(record::getSpeed)
                .set(turningRadius).equalToWhenPresent(record::getTurningRadius)
                .set(roadClearance).equalToWhenPresent(record::getRoadClearance)
                .set(gradient).equalToWhenPresent(record::getGradient)
                .set(fuelEconomy).equalToWhenPresent(record::getFuelEconomy)
                .set(torque).equalToWhenPresent(record::getTorque)
                .set(compressionRatio).equalToWhenPresent(record::getCompressionRatio)
                .set(manufacturerId).equalToWhenPresent(record::getManufacturerId)
                .set(vehicleBrandId).equalToWhenPresent(record::getVehicleBrandId)
                .set(vehicleTypeId).equalToWhenPresent(record::getVehicleTypeId)
                .set(wheelDriveId).equalToWhenPresent(record::getWheelDriveId)
                .set(breakModeId).equalToWhenPresent(record::getBreakModeId)
                .set(turnModeId).equalToWhenPresent(record::getTurnModeId)
                .set(drivePositionId).equalToWhenPresent(record::getDrivePositionId)
                .set(enginePositionId).equalToWhenPresent(record::getEnginePositionId)
                .set(gearBoxTypeId).equalToWhenPresent(record::getGearBoxTypeId)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default int updateByPrimaryKey(VehicleModelInfo record) {
        return update(c ->
            c.set(autohomeVehicleModelId).equalTo(record::getAutohomeVehicleModelId)
            .set(vehicleBrandName).equalTo(record::getVehicleBrandName)
            .set(vehicleSeriesName).equalTo(record::getVehicleSeriesName)
            .set(vehicleModelName).equalTo(record::getVehicleModelName)
            .set(financialModelName).equalTo(record::getFinancialModelName)
            .set(vehicleAbbreviationId).equalTo(record::getVehicleAbbreviationId)
            .set(assessPassenger).equalTo(record::getAssessPassenger)
            .set(busAssessPassenger).equalTo(record::getBusAssessPassenger)
            .set(engineModelNo).equalTo(record::getEngineModelNo)
            .set(gasTypeId).equalTo(record::getGasTypeId)
            .set(vehicleLevel).equalTo(record::getVehicleLevel)
            .set(wheelParam).equalTo(record::getWheelParam)
            .set(exhaustId).equalTo(record::getExhaustId)
            .set(ttmMonth).equalTo(record::getTtmMonth)
            .set(outLength).equalTo(record::getOutLength)
            .set(outWidth).equalTo(record::getOutWidth)
            .set(outHeight).equalTo(record::getOutHeight)
            .set(wheelBase1).equalTo(record::getWheelBase1)
            .set(totalMass).equalTo(record::getTotalMass)
            .set(fuelTankCapacity).equalTo(record::getFuelTankCapacity)
            .set(fuelLabelName).equalTo(record::getFuelLabelName)
            .set(capacity).equalTo(record::getCapacity)
            .set(batteryCapacity).equalTo(record::getBatteryCapacity)
            .set(vehicleRange).equalTo(record::getVehicleRange)
            .set(fastChargingTime).equalTo(record::getFastChargingTime)
            .set(slowChargingTime).equalTo(record::getSlowChargingTime)
            .set(fuelEconomyMiit).equalTo(record::getFuelEconomyMiit)
            .set(priceOnAutohome).equalTo(record::getPriceOnAutohome)
            .set(vehicleModelNo).equalTo(record::getVehicleModelNo)
            .set(power).equalTo(record::getPower)
            .set(treadFront).equalTo(record::getTreadFront)
            .set(treadRear).equalTo(record::getTreadRear)
            .set(wheelQuantity).equalTo(record::getWheelQuantity)
            .set(springLamination).equalTo(record::getSpringLamination)
            .set(wheelBase2).equalTo(record::getWheelBase2)
            .set(wheelBase3).equalTo(record::getWheelBase3)
            .set(axleQuantity).equalTo(record::getAxleQuantity)
            .set(containerLength).equalTo(record::getContainerLength)
            .set(containerWidth).equalTo(record::getContainerWidth)
            .set(containerHeight).equalTo(record::getContainerHeight)
            .set(assessMass).equalTo(record::getAssessMass)
            .set(tractionMass).equalTo(record::getTractionMass)
            .set(cabPassenger).equalTo(record::getCabPassenger)
            .set(manufactureLocation).equalTo(record::getManufactureLocation)
            .set(doorQuantity).equalTo(record::getDoorQuantity)
            .set(gearQuantity).equalTo(record::getGearQuantity)
            .set(acceleration).equalTo(record::getAcceleration)
            .set(speed).equalTo(record::getSpeed)
            .set(turningRadius).equalTo(record::getTurningRadius)
            .set(roadClearance).equalTo(record::getRoadClearance)
            .set(gradient).equalTo(record::getGradient)
            .set(fuelEconomy).equalTo(record::getFuelEconomy)
            .set(torque).equalTo(record::getTorque)
            .set(compressionRatio).equalTo(record::getCompressionRatio)
            .set(manufacturerId).equalTo(record::getManufacturerId)
            .set(vehicleBrandId).equalTo(record::getVehicleBrandId)
            .set(vehicleTypeId).equalTo(record::getVehicleTypeId)
            .set(wheelDriveId).equalTo(record::getWheelDriveId)
            .set(breakModeId).equalTo(record::getBreakModeId)
            .set(turnModeId).equalTo(record::getTurnModeId)
            .set(drivePositionId).equalTo(record::getDrivePositionId)
            .set(enginePositionId).equalTo(record::getEnginePositionId)
            .set(gearBoxTypeId).equalTo(record::getGearBoxTypeId)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    default int updateByPrimaryKeySelective(VehicleModelInfo record) {
        return update(c ->
            c.set(autohomeVehicleModelId).equalToWhenPresent(record::getAutohomeVehicleModelId)
            .set(vehicleBrandName).equalToWhenPresent(record::getVehicleBrandName)
            .set(vehicleSeriesName).equalToWhenPresent(record::getVehicleSeriesName)
            .set(vehicleModelName).equalToWhenPresent(record::getVehicleModelName)
            .set(financialModelName).equalToWhenPresent(record::getFinancialModelName)
            .set(vehicleAbbreviationId).equalToWhenPresent(record::getVehicleAbbreviationId)
            .set(assessPassenger).equalToWhenPresent(record::getAssessPassenger)
            .set(busAssessPassenger).equalToWhenPresent(record::getBusAssessPassenger)
            .set(engineModelNo).equalToWhenPresent(record::getEngineModelNo)
            .set(gasTypeId).equalToWhenPresent(record::getGasTypeId)
            .set(vehicleLevel).equalToWhenPresent(record::getVehicleLevel)
            .set(wheelParam).equalToWhenPresent(record::getWheelParam)
            .set(exhaustId).equalToWhenPresent(record::getExhaustId)
            .set(ttmMonth).equalToWhenPresent(record::getTtmMonth)
            .set(outLength).equalToWhenPresent(record::getOutLength)
            .set(outWidth).equalToWhenPresent(record::getOutWidth)
            .set(outHeight).equalToWhenPresent(record::getOutHeight)
            .set(wheelBase1).equalToWhenPresent(record::getWheelBase1)
            .set(totalMass).equalToWhenPresent(record::getTotalMass)
            .set(fuelTankCapacity).equalToWhenPresent(record::getFuelTankCapacity)
            .set(fuelLabelName).equalToWhenPresent(record::getFuelLabelName)
            .set(capacity).equalToWhenPresent(record::getCapacity)
            .set(batteryCapacity).equalToWhenPresent(record::getBatteryCapacity)
            .set(vehicleRange).equalToWhenPresent(record::getVehicleRange)
            .set(fastChargingTime).equalToWhenPresent(record::getFastChargingTime)
            .set(slowChargingTime).equalToWhenPresent(record::getSlowChargingTime)
            .set(fuelEconomyMiit).equalToWhenPresent(record::getFuelEconomyMiit)
            .set(priceOnAutohome).equalToWhenPresent(record::getPriceOnAutohome)
            .set(vehicleModelNo).equalToWhenPresent(record::getVehicleModelNo)
            .set(power).equalToWhenPresent(record::getPower)
            .set(treadFront).equalToWhenPresent(record::getTreadFront)
            .set(treadRear).equalToWhenPresent(record::getTreadRear)
            .set(wheelQuantity).equalToWhenPresent(record::getWheelQuantity)
            .set(springLamination).equalToWhenPresent(record::getSpringLamination)
            .set(wheelBase2).equalToWhenPresent(record::getWheelBase2)
            .set(wheelBase3).equalToWhenPresent(record::getWheelBase3)
            .set(axleQuantity).equalToWhenPresent(record::getAxleQuantity)
            .set(containerLength).equalToWhenPresent(record::getContainerLength)
            .set(containerWidth).equalToWhenPresent(record::getContainerWidth)
            .set(containerHeight).equalToWhenPresent(record::getContainerHeight)
            .set(assessMass).equalToWhenPresent(record::getAssessMass)
            .set(tractionMass).equalToWhenPresent(record::getTractionMass)
            .set(cabPassenger).equalToWhenPresent(record::getCabPassenger)
            .set(manufactureLocation).equalToWhenPresent(record::getManufactureLocation)
            .set(doorQuantity).equalToWhenPresent(record::getDoorQuantity)
            .set(gearQuantity).equalToWhenPresent(record::getGearQuantity)
            .set(acceleration).equalToWhenPresent(record::getAcceleration)
            .set(speed).equalToWhenPresent(record::getSpeed)
            .set(turningRadius).equalToWhenPresent(record::getTurningRadius)
            .set(roadClearance).equalToWhenPresent(record::getRoadClearance)
            .set(gradient).equalToWhenPresent(record::getGradient)
            .set(fuelEconomy).equalToWhenPresent(record::getFuelEconomy)
            .set(torque).equalToWhenPresent(record::getTorque)
            .set(compressionRatio).equalToWhenPresent(record::getCompressionRatio)
            .set(manufacturerId).equalToWhenPresent(record::getManufacturerId)
            .set(vehicleBrandId).equalToWhenPresent(record::getVehicleBrandId)
            .set(vehicleTypeId).equalToWhenPresent(record::getVehicleTypeId)
            .set(wheelDriveId).equalToWhenPresent(record::getWheelDriveId)
            .set(breakModeId).equalToWhenPresent(record::getBreakModeId)
            .set(turnModeId).equalToWhenPresent(record::getTurnModeId)
            .set(drivePositionId).equalToWhenPresent(record::getDrivePositionId)
            .set(enginePositionId).equalToWhenPresent(record::getEnginePositionId)
            .set(gearBoxTypeId).equalToWhenPresent(record::getGearBoxTypeId)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}