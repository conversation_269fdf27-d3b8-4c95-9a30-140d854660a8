package com.dazhong.transportation.vlms.mapper;

import static com.dazhong.transportation.vlms.mapper.JdyDataInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import com.dazhong.transportation.vlms.model.JdyDataInfo;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface JdyDataInfoMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, dataId, entryId, widget1732281602495, widget1732271235702, widget1732271235713, widget1732271235722, widget1736926051627, widget1736926051635, widget1732271235709, widget1732271235742, widget1732271235741, widget1732271235750, widget1743895605231, widget1732271235734, widget1744687012604, widget1737338544071, widget1732281602514, widget1737338544074, widget1732271235755, widget1745227149952, widget1747774937598, widget1743581186765, widget1743579518411, widget1750208093989, widget1747775605137, widget1732583760832, widget1732583760833, widget1732583760834, widget1732583760838, widget1732583760842, widget1747775605138, widget1732280180068, widget1732285199512, widget1747775605139, widget1744027642987, widget1744027642988, widget1743579339407, widget1732271235785, widget1732271235772, widget1732281602529, widget1732271235786, widget1732271235787, widget1732271235788, widget1732271235789, widget1743641684293, widget1743602799865, widget1743562445365, widget1743387862109, widget1743562445367, widget1743562445368, widget1743387862100, widget1743602799863, widget1732285199519, widget1743983365624, widget1743983365636, widget1743641684294, widget1732271235810, widget1743602799866, widget1743562445378, widget1743562445379, widget1743562445380, widget1743602799867, widget1743562445383, widget1743562445384, widget1743983365631, widget1743983365635, widget1743641684295, widget1743562445375, widget1743387862103, widget1743562445366, widget1743387862104, widget1743387862105, widget1743562445369, widget1732271235811, widget1743562445372, widget1743983365633, widget1743983365637, flowState, isDeleted, createTime, createOperId, createOperName, updateTime, updateOperId, updateOperName);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="record.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<JdyDataInfo> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("JdyDataInfoResult")
    Optional<JdyDataInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="JdyDataInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="data_id", property="dataId", jdbcType=JdbcType.VARCHAR),
        @Result(column="entry_id", property="entryId", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732281602495", property="widget1732281602495", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1732271235702", property="widget1732271235702", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1732271235713", property="widget1732271235713", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235722", property="widget1732271235722", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1736926051627", property="widget1736926051627", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1736926051635", property="widget1736926051635", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235709", property="widget1732271235709", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235742", property="widget1732271235742", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235741", property="widget1732271235741", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235750", property="widget1732271235750", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1743895605231", property="widget1743895605231", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235734", property="widget1732271235734", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1744687012604", property="widget1744687012604", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1737338544071", property="widget1737338544071", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732281602514", property="widget1732281602514", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1737338544074", property="widget1737338544074", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1732271235755", property="widget1732271235755", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1745227149952", property="widget1745227149952", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1747774937598", property="widget1747774937598", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743581186765", property="widget1743581186765", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1743579518411", property="widget1743579518411", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1750208093989", property="widget1750208093989", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1747775605137", property="widget1747775605137", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732583760832", property="widget1732583760832", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732583760833", property="widget1732583760833", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732583760834", property="widget1732583760834", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732583760838", property="widget1732583760838", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732583760842", property="widget1732583760842", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1747775605138", property="widget1747775605138", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732280180068", property="widget1732280180068", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732285199512", property="widget1732285199512", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1747775605139", property="widget1747775605139", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1744027642987", property="widget1744027642987", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1744027642988", property="widget1744027642988", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743579339407", property="widget1743579339407", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235785", property="widget1732271235785", jdbcType=JdbcType.INTEGER),
        @Result(column="_widget_1732271235772", property="widget1732271235772", jdbcType=JdbcType.INTEGER),
        @Result(column="_widget_1732281602529", property="widget1732281602529", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1732271235786", property="widget1732271235786", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732271235787", property="widget1732271235787", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732271235788", property="widget1732271235788", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732271235789", property="widget1732271235789", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743641684293", property="widget1743641684293", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1743602799865", property="widget1743602799865", jdbcType=JdbcType.INTEGER),
        @Result(column="_widget_1743562445365", property="widget1743562445365", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743387862109", property="widget1743387862109", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445367", property="widget1743562445367", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445368", property="widget1743562445368", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743387862100", property="widget1743387862100", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743602799863", property="widget1743602799863", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732285199519", property="widget1732285199519", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743983365624", property="widget1743983365624", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1743983365636", property="widget1743983365636", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1743641684294", property="widget1743641684294", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1732271235810", property="widget1732271235810", jdbcType=JdbcType.INTEGER),
        @Result(column="_widget_1743602799866", property="widget1743602799866", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445378", property="widget1743562445378", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445379", property="widget1743562445379", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445380", property="widget1743562445380", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743602799867", property="widget1743602799867", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445383", property="widget1743562445383", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445384", property="widget1743562445384", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743983365631", property="widget1743983365631", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1743983365635", property="widget1743983365635", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1743641684295", property="widget1743641684295", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="_widget_1743562445375", property="widget1743562445375", jdbcType=JdbcType.INTEGER),
        @Result(column="_widget_1743387862103", property="widget1743387862103", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445366", property="widget1743562445366", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743387862104", property="widget1743387862104", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743387862105", property="widget1743387862105", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445369", property="widget1743562445369", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1732271235811", property="widget1732271235811", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743562445372", property="widget1743562445372", jdbcType=JdbcType.DECIMAL),
        @Result(column="_widget_1743983365633", property="widget1743983365633", jdbcType=JdbcType.VARCHAR),
        @Result(column="_widget_1743983365637", property="widget1743983365637", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="flow_state", property="flowState", jdbcType=JdbcType.INTEGER),
        @Result(column="is_deleted", property="isDeleted", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_oper_id", property="createOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_oper_name", property="createOperName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_oper_id", property="updateOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_oper_name", property="updateOperName", jdbcType=JdbcType.VARCHAR)
    })
    List<JdyDataInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, jdyDataInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, jdyDataInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default int insert(JdyDataInfo record) {
        return MyBatis3Utils.insert(this::insert, record, jdyDataInfo, c ->
            c.map(dataId).toProperty("dataId")
            .map(entryId).toProperty("entryId")
            .map(widget1732281602495).toProperty("widget1732281602495")
            .map(widget1732271235702).toProperty("widget1732271235702")
            .map(widget1732271235713).toProperty("widget1732271235713")
            .map(widget1732271235722).toProperty("widget1732271235722")
            .map(widget1736926051627).toProperty("widget1736926051627")
            .map(widget1736926051635).toProperty("widget1736926051635")
            .map(widget1732271235709).toProperty("widget1732271235709")
            .map(widget1732271235742).toProperty("widget1732271235742")
            .map(widget1732271235741).toProperty("widget1732271235741")
            .map(widget1732271235750).toProperty("widget1732271235750")
            .map(widget1743895605231).toProperty("widget1743895605231")
            .map(widget1732271235734).toProperty("widget1732271235734")
            .map(widget1744687012604).toProperty("widget1744687012604")
            .map(widget1737338544071).toProperty("widget1737338544071")
            .map(widget1732281602514).toProperty("widget1732281602514")
            .map(widget1737338544074).toProperty("widget1737338544074")
            .map(widget1732271235755).toProperty("widget1732271235755")
            .map(widget1745227149952).toProperty("widget1745227149952")
            .map(widget1747774937598).toProperty("widget1747774937598")
            .map(widget1743581186765).toProperty("widget1743581186765")
            .map(widget1743579518411).toProperty("widget1743579518411")
            .map(widget1750208093989).toProperty("widget1750208093989")
            .map(widget1747775605137).toProperty("widget1747775605137")
            .map(widget1732583760832).toProperty("widget1732583760832")
            .map(widget1732583760833).toProperty("widget1732583760833")
            .map(widget1732583760834).toProperty("widget1732583760834")
            .map(widget1732583760838).toProperty("widget1732583760838")
            .map(widget1732583760842).toProperty("widget1732583760842")
            .map(widget1747775605138).toProperty("widget1747775605138")
            .map(widget1732280180068).toProperty("widget1732280180068")
            .map(widget1732285199512).toProperty("widget1732285199512")
            .map(widget1747775605139).toProperty("widget1747775605139")
            .map(widget1744027642987).toProperty("widget1744027642987")
            .map(widget1744027642988).toProperty("widget1744027642988")
            .map(widget1743579339407).toProperty("widget1743579339407")
            .map(widget1732271235785).toProperty("widget1732271235785")
            .map(widget1732271235772).toProperty("widget1732271235772")
            .map(widget1732281602529).toProperty("widget1732281602529")
            .map(widget1732271235786).toProperty("widget1732271235786")
            .map(widget1732271235787).toProperty("widget1732271235787")
            .map(widget1732271235788).toProperty("widget1732271235788")
            .map(widget1732271235789).toProperty("widget1732271235789")
            .map(widget1743641684293).toProperty("widget1743641684293")
            .map(widget1743602799865).toProperty("widget1743602799865")
            .map(widget1743562445365).toProperty("widget1743562445365")
            .map(widget1743387862109).toProperty("widget1743387862109")
            .map(widget1743562445367).toProperty("widget1743562445367")
            .map(widget1743562445368).toProperty("widget1743562445368")
            .map(widget1743387862100).toProperty("widget1743387862100")
            .map(widget1743602799863).toProperty("widget1743602799863")
            .map(widget1732285199519).toProperty("widget1732285199519")
            .map(widget1743983365624).toProperty("widget1743983365624")
            .map(widget1743983365636).toProperty("widget1743983365636")
            .map(widget1743641684294).toProperty("widget1743641684294")
            .map(widget1732271235810).toProperty("widget1732271235810")
            .map(widget1743602799866).toProperty("widget1743602799866")
            .map(widget1743562445378).toProperty("widget1743562445378")
            .map(widget1743562445379).toProperty("widget1743562445379")
            .map(widget1743562445380).toProperty("widget1743562445380")
            .map(widget1743602799867).toProperty("widget1743602799867")
            .map(widget1743562445383).toProperty("widget1743562445383")
            .map(widget1743562445384).toProperty("widget1743562445384")
            .map(widget1743983365631).toProperty("widget1743983365631")
            .map(widget1743983365635).toProperty("widget1743983365635")
            .map(widget1743641684295).toProperty("widget1743641684295")
            .map(widget1743562445375).toProperty("widget1743562445375")
            .map(widget1743387862103).toProperty("widget1743387862103")
            .map(widget1743562445366).toProperty("widget1743562445366")
            .map(widget1743387862104).toProperty("widget1743387862104")
            .map(widget1743387862105).toProperty("widget1743387862105")
            .map(widget1743562445369).toProperty("widget1743562445369")
            .map(widget1732271235811).toProperty("widget1732271235811")
            .map(widget1743562445372).toProperty("widget1743562445372")
            .map(widget1743983365633).toProperty("widget1743983365633")
            .map(widget1743983365637).toProperty("widget1743983365637")
            .map(flowState).toProperty("flowState")
            .map(isDeleted).toProperty("isDeleted")
            .map(createTime).toProperty("createTime")
            .map(createOperId).toProperty("createOperId")
            .map(createOperName).toProperty("createOperName")
            .map(updateTime).toProperty("updateTime")
            .map(updateOperId).toProperty("updateOperId")
            .map(updateOperName).toProperty("updateOperName")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default int insertSelective(JdyDataInfo record) {
        return MyBatis3Utils.insert(this::insert, record, jdyDataInfo, c ->
            c.map(dataId).toPropertyWhenPresent("dataId", record::getDataId)
            .map(entryId).toPropertyWhenPresent("entryId", record::getEntryId)
            .map(widget1732281602495).toPropertyWhenPresent("widget1732281602495", record::getWidget1732281602495)
            .map(widget1732271235702).toPropertyWhenPresent("widget1732271235702", record::getWidget1732271235702)
            .map(widget1732271235713).toPropertyWhenPresent("widget1732271235713", record::getWidget1732271235713)
            .map(widget1732271235722).toPropertyWhenPresent("widget1732271235722", record::getWidget1732271235722)
            .map(widget1736926051627).toPropertyWhenPresent("widget1736926051627", record::getWidget1736926051627)
            .map(widget1736926051635).toPropertyWhenPresent("widget1736926051635", record::getWidget1736926051635)
            .map(widget1732271235709).toPropertyWhenPresent("widget1732271235709", record::getWidget1732271235709)
            .map(widget1732271235742).toPropertyWhenPresent("widget1732271235742", record::getWidget1732271235742)
            .map(widget1732271235741).toPropertyWhenPresent("widget1732271235741", record::getWidget1732271235741)
            .map(widget1732271235750).toPropertyWhenPresent("widget1732271235750", record::getWidget1732271235750)
            .map(widget1743895605231).toPropertyWhenPresent("widget1743895605231", record::getWidget1743895605231)
            .map(widget1732271235734).toPropertyWhenPresent("widget1732271235734", record::getWidget1732271235734)
            .map(widget1744687012604).toPropertyWhenPresent("widget1744687012604", record::getWidget1744687012604)
            .map(widget1737338544071).toPropertyWhenPresent("widget1737338544071", record::getWidget1737338544071)
            .map(widget1732281602514).toPropertyWhenPresent("widget1732281602514", record::getWidget1732281602514)
            .map(widget1737338544074).toPropertyWhenPresent("widget1737338544074", record::getWidget1737338544074)
            .map(widget1732271235755).toPropertyWhenPresent("widget1732271235755", record::getWidget1732271235755)
            .map(widget1745227149952).toPropertyWhenPresent("widget1745227149952", record::getWidget1745227149952)
            .map(widget1747774937598).toPropertyWhenPresent("widget1747774937598", record::getWidget1747774937598)
            .map(widget1743581186765).toPropertyWhenPresent("widget1743581186765", record::getWidget1743581186765)
            .map(widget1743579518411).toPropertyWhenPresent("widget1743579518411", record::getWidget1743579518411)
            .map(widget1750208093989).toPropertyWhenPresent("widget1750208093989", record::getWidget1750208093989)
            .map(widget1747775605137).toPropertyWhenPresent("widget1747775605137", record::getWidget1747775605137)
            .map(widget1732583760832).toPropertyWhenPresent("widget1732583760832", record::getWidget1732583760832)
            .map(widget1732583760833).toPropertyWhenPresent("widget1732583760833", record::getWidget1732583760833)
            .map(widget1732583760834).toPropertyWhenPresent("widget1732583760834", record::getWidget1732583760834)
            .map(widget1732583760838).toPropertyWhenPresent("widget1732583760838", record::getWidget1732583760838)
            .map(widget1732583760842).toPropertyWhenPresent("widget1732583760842", record::getWidget1732583760842)
            .map(widget1747775605138).toPropertyWhenPresent("widget1747775605138", record::getWidget1747775605138)
            .map(widget1732280180068).toPropertyWhenPresent("widget1732280180068", record::getWidget1732280180068)
            .map(widget1732285199512).toPropertyWhenPresent("widget1732285199512", record::getWidget1732285199512)
            .map(widget1747775605139).toPropertyWhenPresent("widget1747775605139", record::getWidget1747775605139)
            .map(widget1744027642987).toPropertyWhenPresent("widget1744027642987", record::getWidget1744027642987)
            .map(widget1744027642988).toPropertyWhenPresent("widget1744027642988", record::getWidget1744027642988)
            .map(widget1743579339407).toPropertyWhenPresent("widget1743579339407", record::getWidget1743579339407)
            .map(widget1732271235785).toPropertyWhenPresent("widget1732271235785", record::getWidget1732271235785)
            .map(widget1732271235772).toPropertyWhenPresent("widget1732271235772", record::getWidget1732271235772)
            .map(widget1732281602529).toPropertyWhenPresent("widget1732281602529", record::getWidget1732281602529)
            .map(widget1732271235786).toPropertyWhenPresent("widget1732271235786", record::getWidget1732271235786)
            .map(widget1732271235787).toPropertyWhenPresent("widget1732271235787", record::getWidget1732271235787)
            .map(widget1732271235788).toPropertyWhenPresent("widget1732271235788", record::getWidget1732271235788)
            .map(widget1732271235789).toPropertyWhenPresent("widget1732271235789", record::getWidget1732271235789)
            .map(widget1743641684293).toPropertyWhenPresent("widget1743641684293", record::getWidget1743641684293)
            .map(widget1743602799865).toPropertyWhenPresent("widget1743602799865", record::getWidget1743602799865)
            .map(widget1743562445365).toPropertyWhenPresent("widget1743562445365", record::getWidget1743562445365)
            .map(widget1743387862109).toPropertyWhenPresent("widget1743387862109", record::getWidget1743387862109)
            .map(widget1743562445367).toPropertyWhenPresent("widget1743562445367", record::getWidget1743562445367)
            .map(widget1743562445368).toPropertyWhenPresent("widget1743562445368", record::getWidget1743562445368)
            .map(widget1743387862100).toPropertyWhenPresent("widget1743387862100", record::getWidget1743387862100)
            .map(widget1743602799863).toPropertyWhenPresent("widget1743602799863", record::getWidget1743602799863)
            .map(widget1732285199519).toPropertyWhenPresent("widget1732285199519", record::getWidget1732285199519)
            .map(widget1743983365624).toPropertyWhenPresent("widget1743983365624", record::getWidget1743983365624)
            .map(widget1743983365636).toPropertyWhenPresent("widget1743983365636", record::getWidget1743983365636)
            .map(widget1743641684294).toPropertyWhenPresent("widget1743641684294", record::getWidget1743641684294)
            .map(widget1732271235810).toPropertyWhenPresent("widget1732271235810", record::getWidget1732271235810)
            .map(widget1743602799866).toPropertyWhenPresent("widget1743602799866", record::getWidget1743602799866)
            .map(widget1743562445378).toPropertyWhenPresent("widget1743562445378", record::getWidget1743562445378)
            .map(widget1743562445379).toPropertyWhenPresent("widget1743562445379", record::getWidget1743562445379)
            .map(widget1743562445380).toPropertyWhenPresent("widget1743562445380", record::getWidget1743562445380)
            .map(widget1743602799867).toPropertyWhenPresent("widget1743602799867", record::getWidget1743602799867)
            .map(widget1743562445383).toPropertyWhenPresent("widget1743562445383", record::getWidget1743562445383)
            .map(widget1743562445384).toPropertyWhenPresent("widget1743562445384", record::getWidget1743562445384)
            .map(widget1743983365631).toPropertyWhenPresent("widget1743983365631", record::getWidget1743983365631)
            .map(widget1743983365635).toPropertyWhenPresent("widget1743983365635", record::getWidget1743983365635)
            .map(widget1743641684295).toPropertyWhenPresent("widget1743641684295", record::getWidget1743641684295)
            .map(widget1743562445375).toPropertyWhenPresent("widget1743562445375", record::getWidget1743562445375)
            .map(widget1743387862103).toPropertyWhenPresent("widget1743387862103", record::getWidget1743387862103)
            .map(widget1743562445366).toPropertyWhenPresent("widget1743562445366", record::getWidget1743562445366)
            .map(widget1743387862104).toPropertyWhenPresent("widget1743387862104", record::getWidget1743387862104)
            .map(widget1743387862105).toPropertyWhenPresent("widget1743387862105", record::getWidget1743387862105)
            .map(widget1743562445369).toPropertyWhenPresent("widget1743562445369", record::getWidget1743562445369)
            .map(widget1732271235811).toPropertyWhenPresent("widget1732271235811", record::getWidget1732271235811)
            .map(widget1743562445372).toPropertyWhenPresent("widget1743562445372", record::getWidget1743562445372)
            .map(widget1743983365633).toPropertyWhenPresent("widget1743983365633", record::getWidget1743983365633)
            .map(widget1743983365637).toPropertyWhenPresent("widget1743983365637", record::getWidget1743983365637)
            .map(flowState).toPropertyWhenPresent("flowState", record::getFlowState)
            .map(isDeleted).toPropertyWhenPresent("isDeleted", record::getIsDeleted)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(createOperId).toPropertyWhenPresent("createOperId", record::getCreateOperId)
            .map(createOperName).toPropertyWhenPresent("createOperName", record::getCreateOperName)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(updateOperId).toPropertyWhenPresent("updateOperId", record::getUpdateOperId)
            .map(updateOperName).toPropertyWhenPresent("updateOperName", record::getUpdateOperName)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default Optional<JdyDataInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, jdyDataInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default List<JdyDataInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, jdyDataInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default List<JdyDataInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, jdyDataInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default Optional<JdyDataInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, jdyDataInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    static UpdateDSL<UpdateModel> updateAllColumns(JdyDataInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dataId).equalTo(record::getDataId)
                .set(entryId).equalTo(record::getEntryId)
                .set(widget1732281602495).equalTo(record::getWidget1732281602495)
                .set(widget1732271235702).equalTo(record::getWidget1732271235702)
                .set(widget1732271235713).equalTo(record::getWidget1732271235713)
                .set(widget1732271235722).equalTo(record::getWidget1732271235722)
                .set(widget1736926051627).equalTo(record::getWidget1736926051627)
                .set(widget1736926051635).equalTo(record::getWidget1736926051635)
                .set(widget1732271235709).equalTo(record::getWidget1732271235709)
                .set(widget1732271235742).equalTo(record::getWidget1732271235742)
                .set(widget1732271235741).equalTo(record::getWidget1732271235741)
                .set(widget1732271235750).equalTo(record::getWidget1732271235750)
                .set(widget1743895605231).equalTo(record::getWidget1743895605231)
                .set(widget1732271235734).equalTo(record::getWidget1732271235734)
                .set(widget1744687012604).equalTo(record::getWidget1744687012604)
                .set(widget1737338544071).equalTo(record::getWidget1737338544071)
                .set(widget1732281602514).equalTo(record::getWidget1732281602514)
                .set(widget1737338544074).equalTo(record::getWidget1737338544074)
                .set(widget1732271235755).equalTo(record::getWidget1732271235755)
                .set(widget1745227149952).equalTo(record::getWidget1745227149952)
                .set(widget1747774937598).equalTo(record::getWidget1747774937598)
                .set(widget1743581186765).equalTo(record::getWidget1743581186765)
                .set(widget1743579518411).equalTo(record::getWidget1743579518411)
                .set(widget1750208093989).equalTo(record::getWidget1750208093989)
                .set(widget1747775605137).equalTo(record::getWidget1747775605137)
                .set(widget1732583760832).equalTo(record::getWidget1732583760832)
                .set(widget1732583760833).equalTo(record::getWidget1732583760833)
                .set(widget1732583760834).equalTo(record::getWidget1732583760834)
                .set(widget1732583760838).equalTo(record::getWidget1732583760838)
                .set(widget1732583760842).equalTo(record::getWidget1732583760842)
                .set(widget1747775605138).equalTo(record::getWidget1747775605138)
                .set(widget1732280180068).equalTo(record::getWidget1732280180068)
                .set(widget1732285199512).equalTo(record::getWidget1732285199512)
                .set(widget1747775605139).equalTo(record::getWidget1747775605139)
                .set(widget1744027642987).equalTo(record::getWidget1744027642987)
                .set(widget1744027642988).equalTo(record::getWidget1744027642988)
                .set(widget1743579339407).equalTo(record::getWidget1743579339407)
                .set(widget1732271235785).equalTo(record::getWidget1732271235785)
                .set(widget1732271235772).equalTo(record::getWidget1732271235772)
                .set(widget1732281602529).equalTo(record::getWidget1732281602529)
                .set(widget1732271235786).equalTo(record::getWidget1732271235786)
                .set(widget1732271235787).equalTo(record::getWidget1732271235787)
                .set(widget1732271235788).equalTo(record::getWidget1732271235788)
                .set(widget1732271235789).equalTo(record::getWidget1732271235789)
                .set(widget1743641684293).equalTo(record::getWidget1743641684293)
                .set(widget1743602799865).equalTo(record::getWidget1743602799865)
                .set(widget1743562445365).equalTo(record::getWidget1743562445365)
                .set(widget1743387862109).equalTo(record::getWidget1743387862109)
                .set(widget1743562445367).equalTo(record::getWidget1743562445367)
                .set(widget1743562445368).equalTo(record::getWidget1743562445368)
                .set(widget1743387862100).equalTo(record::getWidget1743387862100)
                .set(widget1743602799863).equalTo(record::getWidget1743602799863)
                .set(widget1732285199519).equalTo(record::getWidget1732285199519)
                .set(widget1743983365624).equalTo(record::getWidget1743983365624)
                .set(widget1743983365636).equalTo(record::getWidget1743983365636)
                .set(widget1743641684294).equalTo(record::getWidget1743641684294)
                .set(widget1732271235810).equalTo(record::getWidget1732271235810)
                .set(widget1743602799866).equalTo(record::getWidget1743602799866)
                .set(widget1743562445378).equalTo(record::getWidget1743562445378)
                .set(widget1743562445379).equalTo(record::getWidget1743562445379)
                .set(widget1743562445380).equalTo(record::getWidget1743562445380)
                .set(widget1743602799867).equalTo(record::getWidget1743602799867)
                .set(widget1743562445383).equalTo(record::getWidget1743562445383)
                .set(widget1743562445384).equalTo(record::getWidget1743562445384)
                .set(widget1743983365631).equalTo(record::getWidget1743983365631)
                .set(widget1743983365635).equalTo(record::getWidget1743983365635)
                .set(widget1743641684295).equalTo(record::getWidget1743641684295)
                .set(widget1743562445375).equalTo(record::getWidget1743562445375)
                .set(widget1743387862103).equalTo(record::getWidget1743387862103)
                .set(widget1743562445366).equalTo(record::getWidget1743562445366)
                .set(widget1743387862104).equalTo(record::getWidget1743387862104)
                .set(widget1743387862105).equalTo(record::getWidget1743387862105)
                .set(widget1743562445369).equalTo(record::getWidget1743562445369)
                .set(widget1732271235811).equalTo(record::getWidget1732271235811)
                .set(widget1743562445372).equalTo(record::getWidget1743562445372)
                .set(widget1743983365633).equalTo(record::getWidget1743983365633)
                .set(widget1743983365637).equalTo(record::getWidget1743983365637)
                .set(flowState).equalTo(record::getFlowState)
                .set(isDeleted).equalTo(record::getIsDeleted)
                .set(createTime).equalTo(record::getCreateTime)
                .set(createOperId).equalTo(record::getCreateOperId)
                .set(createOperName).equalTo(record::getCreateOperName)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(updateOperId).equalTo(record::getUpdateOperId)
                .set(updateOperName).equalTo(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(JdyDataInfo record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dataId).equalToWhenPresent(record::getDataId)
                .set(entryId).equalToWhenPresent(record::getEntryId)
                .set(widget1732281602495).equalToWhenPresent(record::getWidget1732281602495)
                .set(widget1732271235702).equalToWhenPresent(record::getWidget1732271235702)
                .set(widget1732271235713).equalToWhenPresent(record::getWidget1732271235713)
                .set(widget1732271235722).equalToWhenPresent(record::getWidget1732271235722)
                .set(widget1736926051627).equalToWhenPresent(record::getWidget1736926051627)
                .set(widget1736926051635).equalToWhenPresent(record::getWidget1736926051635)
                .set(widget1732271235709).equalToWhenPresent(record::getWidget1732271235709)
                .set(widget1732271235742).equalToWhenPresent(record::getWidget1732271235742)
                .set(widget1732271235741).equalToWhenPresent(record::getWidget1732271235741)
                .set(widget1732271235750).equalToWhenPresent(record::getWidget1732271235750)
                .set(widget1743895605231).equalToWhenPresent(record::getWidget1743895605231)
                .set(widget1732271235734).equalToWhenPresent(record::getWidget1732271235734)
                .set(widget1744687012604).equalToWhenPresent(record::getWidget1744687012604)
                .set(widget1737338544071).equalToWhenPresent(record::getWidget1737338544071)
                .set(widget1732281602514).equalToWhenPresent(record::getWidget1732281602514)
                .set(widget1737338544074).equalToWhenPresent(record::getWidget1737338544074)
                .set(widget1732271235755).equalToWhenPresent(record::getWidget1732271235755)
                .set(widget1745227149952).equalToWhenPresent(record::getWidget1745227149952)
                .set(widget1747774937598).equalToWhenPresent(record::getWidget1747774937598)
                .set(widget1743581186765).equalToWhenPresent(record::getWidget1743581186765)
                .set(widget1743579518411).equalToWhenPresent(record::getWidget1743579518411)
                .set(widget1750208093989).equalToWhenPresent(record::getWidget1750208093989)
                .set(widget1747775605137).equalToWhenPresent(record::getWidget1747775605137)
                .set(widget1732583760832).equalToWhenPresent(record::getWidget1732583760832)
                .set(widget1732583760833).equalToWhenPresent(record::getWidget1732583760833)
                .set(widget1732583760834).equalToWhenPresent(record::getWidget1732583760834)
                .set(widget1732583760838).equalToWhenPresent(record::getWidget1732583760838)
                .set(widget1732583760842).equalToWhenPresent(record::getWidget1732583760842)
                .set(widget1747775605138).equalToWhenPresent(record::getWidget1747775605138)
                .set(widget1732280180068).equalToWhenPresent(record::getWidget1732280180068)
                .set(widget1732285199512).equalToWhenPresent(record::getWidget1732285199512)
                .set(widget1747775605139).equalToWhenPresent(record::getWidget1747775605139)
                .set(widget1744027642987).equalToWhenPresent(record::getWidget1744027642987)
                .set(widget1744027642988).equalToWhenPresent(record::getWidget1744027642988)
                .set(widget1743579339407).equalToWhenPresent(record::getWidget1743579339407)
                .set(widget1732271235785).equalToWhenPresent(record::getWidget1732271235785)
                .set(widget1732271235772).equalToWhenPresent(record::getWidget1732271235772)
                .set(widget1732281602529).equalToWhenPresent(record::getWidget1732281602529)
                .set(widget1732271235786).equalToWhenPresent(record::getWidget1732271235786)
                .set(widget1732271235787).equalToWhenPresent(record::getWidget1732271235787)
                .set(widget1732271235788).equalToWhenPresent(record::getWidget1732271235788)
                .set(widget1732271235789).equalToWhenPresent(record::getWidget1732271235789)
                .set(widget1743641684293).equalToWhenPresent(record::getWidget1743641684293)
                .set(widget1743602799865).equalToWhenPresent(record::getWidget1743602799865)
                .set(widget1743562445365).equalToWhenPresent(record::getWidget1743562445365)
                .set(widget1743387862109).equalToWhenPresent(record::getWidget1743387862109)
                .set(widget1743562445367).equalToWhenPresent(record::getWidget1743562445367)
                .set(widget1743562445368).equalToWhenPresent(record::getWidget1743562445368)
                .set(widget1743387862100).equalToWhenPresent(record::getWidget1743387862100)
                .set(widget1743602799863).equalToWhenPresent(record::getWidget1743602799863)
                .set(widget1732285199519).equalToWhenPresent(record::getWidget1732285199519)
                .set(widget1743983365624).equalToWhenPresent(record::getWidget1743983365624)
                .set(widget1743983365636).equalToWhenPresent(record::getWidget1743983365636)
                .set(widget1743641684294).equalToWhenPresent(record::getWidget1743641684294)
                .set(widget1732271235810).equalToWhenPresent(record::getWidget1732271235810)
                .set(widget1743602799866).equalToWhenPresent(record::getWidget1743602799866)
                .set(widget1743562445378).equalToWhenPresent(record::getWidget1743562445378)
                .set(widget1743562445379).equalToWhenPresent(record::getWidget1743562445379)
                .set(widget1743562445380).equalToWhenPresent(record::getWidget1743562445380)
                .set(widget1743602799867).equalToWhenPresent(record::getWidget1743602799867)
                .set(widget1743562445383).equalToWhenPresent(record::getWidget1743562445383)
                .set(widget1743562445384).equalToWhenPresent(record::getWidget1743562445384)
                .set(widget1743983365631).equalToWhenPresent(record::getWidget1743983365631)
                .set(widget1743983365635).equalToWhenPresent(record::getWidget1743983365635)
                .set(widget1743641684295).equalToWhenPresent(record::getWidget1743641684295)
                .set(widget1743562445375).equalToWhenPresent(record::getWidget1743562445375)
                .set(widget1743387862103).equalToWhenPresent(record::getWidget1743387862103)
                .set(widget1743562445366).equalToWhenPresent(record::getWidget1743562445366)
                .set(widget1743387862104).equalToWhenPresent(record::getWidget1743387862104)
                .set(widget1743387862105).equalToWhenPresent(record::getWidget1743387862105)
                .set(widget1743562445369).equalToWhenPresent(record::getWidget1743562445369)
                .set(widget1732271235811).equalToWhenPresent(record::getWidget1732271235811)
                .set(widget1743562445372).equalToWhenPresent(record::getWidget1743562445372)
                .set(widget1743983365633).equalToWhenPresent(record::getWidget1743983365633)
                .set(widget1743983365637).equalToWhenPresent(record::getWidget1743983365637)
                .set(flowState).equalToWhenPresent(record::getFlowState)
                .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(createOperId).equalToWhenPresent(record::getCreateOperId)
                .set(createOperName).equalToWhenPresent(record::getCreateOperName)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
                .set(updateOperName).equalToWhenPresent(record::getUpdateOperName);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default int updateByPrimaryKey(JdyDataInfo record) {
        return update(c ->
            c.set(dataId).equalTo(record::getDataId)
            .set(entryId).equalTo(record::getEntryId)
            .set(widget1732281602495).equalTo(record::getWidget1732281602495)
            .set(widget1732271235702).equalTo(record::getWidget1732271235702)
            .set(widget1732271235713).equalTo(record::getWidget1732271235713)
            .set(widget1732271235722).equalTo(record::getWidget1732271235722)
            .set(widget1736926051627).equalTo(record::getWidget1736926051627)
            .set(widget1736926051635).equalTo(record::getWidget1736926051635)
            .set(widget1732271235709).equalTo(record::getWidget1732271235709)
            .set(widget1732271235742).equalTo(record::getWidget1732271235742)
            .set(widget1732271235741).equalTo(record::getWidget1732271235741)
            .set(widget1732271235750).equalTo(record::getWidget1732271235750)
            .set(widget1743895605231).equalTo(record::getWidget1743895605231)
            .set(widget1732271235734).equalTo(record::getWidget1732271235734)
            .set(widget1744687012604).equalTo(record::getWidget1744687012604)
            .set(widget1737338544071).equalTo(record::getWidget1737338544071)
            .set(widget1732281602514).equalTo(record::getWidget1732281602514)
            .set(widget1737338544074).equalTo(record::getWidget1737338544074)
            .set(widget1732271235755).equalTo(record::getWidget1732271235755)
            .set(widget1745227149952).equalTo(record::getWidget1745227149952)
            .set(widget1747774937598).equalTo(record::getWidget1747774937598)
            .set(widget1743581186765).equalTo(record::getWidget1743581186765)
            .set(widget1743579518411).equalTo(record::getWidget1743579518411)
            .set(widget1750208093989).equalTo(record::getWidget1750208093989)
            .set(widget1747775605137).equalTo(record::getWidget1747775605137)
            .set(widget1732583760832).equalTo(record::getWidget1732583760832)
            .set(widget1732583760833).equalTo(record::getWidget1732583760833)
            .set(widget1732583760834).equalTo(record::getWidget1732583760834)
            .set(widget1732583760838).equalTo(record::getWidget1732583760838)
            .set(widget1732583760842).equalTo(record::getWidget1732583760842)
            .set(widget1747775605138).equalTo(record::getWidget1747775605138)
            .set(widget1732280180068).equalTo(record::getWidget1732280180068)
            .set(widget1732285199512).equalTo(record::getWidget1732285199512)
            .set(widget1747775605139).equalTo(record::getWidget1747775605139)
            .set(widget1744027642987).equalTo(record::getWidget1744027642987)
            .set(widget1744027642988).equalTo(record::getWidget1744027642988)
            .set(widget1743579339407).equalTo(record::getWidget1743579339407)
            .set(widget1732271235785).equalTo(record::getWidget1732271235785)
            .set(widget1732271235772).equalTo(record::getWidget1732271235772)
            .set(widget1732281602529).equalTo(record::getWidget1732281602529)
            .set(widget1732271235786).equalTo(record::getWidget1732271235786)
            .set(widget1732271235787).equalTo(record::getWidget1732271235787)
            .set(widget1732271235788).equalTo(record::getWidget1732271235788)
            .set(widget1732271235789).equalTo(record::getWidget1732271235789)
            .set(widget1743641684293).equalTo(record::getWidget1743641684293)
            .set(widget1743602799865).equalTo(record::getWidget1743602799865)
            .set(widget1743562445365).equalTo(record::getWidget1743562445365)
            .set(widget1743387862109).equalTo(record::getWidget1743387862109)
            .set(widget1743562445367).equalTo(record::getWidget1743562445367)
            .set(widget1743562445368).equalTo(record::getWidget1743562445368)
            .set(widget1743387862100).equalTo(record::getWidget1743387862100)
            .set(widget1743602799863).equalTo(record::getWidget1743602799863)
            .set(widget1732285199519).equalTo(record::getWidget1732285199519)
            .set(widget1743983365624).equalTo(record::getWidget1743983365624)
            .set(widget1743983365636).equalTo(record::getWidget1743983365636)
            .set(widget1743641684294).equalTo(record::getWidget1743641684294)
            .set(widget1732271235810).equalTo(record::getWidget1732271235810)
            .set(widget1743602799866).equalTo(record::getWidget1743602799866)
            .set(widget1743562445378).equalTo(record::getWidget1743562445378)
            .set(widget1743562445379).equalTo(record::getWidget1743562445379)
            .set(widget1743562445380).equalTo(record::getWidget1743562445380)
            .set(widget1743602799867).equalTo(record::getWidget1743602799867)
            .set(widget1743562445383).equalTo(record::getWidget1743562445383)
            .set(widget1743562445384).equalTo(record::getWidget1743562445384)
            .set(widget1743983365631).equalTo(record::getWidget1743983365631)
            .set(widget1743983365635).equalTo(record::getWidget1743983365635)
            .set(widget1743641684295).equalTo(record::getWidget1743641684295)
            .set(widget1743562445375).equalTo(record::getWidget1743562445375)
            .set(widget1743387862103).equalTo(record::getWidget1743387862103)
            .set(widget1743562445366).equalTo(record::getWidget1743562445366)
            .set(widget1743387862104).equalTo(record::getWidget1743387862104)
            .set(widget1743387862105).equalTo(record::getWidget1743387862105)
            .set(widget1743562445369).equalTo(record::getWidget1743562445369)
            .set(widget1732271235811).equalTo(record::getWidget1732271235811)
            .set(widget1743562445372).equalTo(record::getWidget1743562445372)
            .set(widget1743983365633).equalTo(record::getWidget1743983365633)
            .set(widget1743983365637).equalTo(record::getWidget1743983365637)
            .set(flowState).equalTo(record::getFlowState)
            .set(isDeleted).equalTo(record::getIsDeleted)
            .set(createTime).equalTo(record::getCreateTime)
            .set(createOperId).equalTo(record::getCreateOperId)
            .set(createOperName).equalTo(record::getCreateOperName)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(updateOperId).equalTo(record::getUpdateOperId)
            .set(updateOperName).equalTo(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    default int updateByPrimaryKeySelective(JdyDataInfo record) {
        return update(c ->
            c.set(dataId).equalToWhenPresent(record::getDataId)
            .set(entryId).equalToWhenPresent(record::getEntryId)
            .set(widget1732281602495).equalToWhenPresent(record::getWidget1732281602495)
            .set(widget1732271235702).equalToWhenPresent(record::getWidget1732271235702)
            .set(widget1732271235713).equalToWhenPresent(record::getWidget1732271235713)
            .set(widget1732271235722).equalToWhenPresent(record::getWidget1732271235722)
            .set(widget1736926051627).equalToWhenPresent(record::getWidget1736926051627)
            .set(widget1736926051635).equalToWhenPresent(record::getWidget1736926051635)
            .set(widget1732271235709).equalToWhenPresent(record::getWidget1732271235709)
            .set(widget1732271235742).equalToWhenPresent(record::getWidget1732271235742)
            .set(widget1732271235741).equalToWhenPresent(record::getWidget1732271235741)
            .set(widget1732271235750).equalToWhenPresent(record::getWidget1732271235750)
            .set(widget1743895605231).equalToWhenPresent(record::getWidget1743895605231)
            .set(widget1732271235734).equalToWhenPresent(record::getWidget1732271235734)
            .set(widget1744687012604).equalToWhenPresent(record::getWidget1744687012604)
            .set(widget1737338544071).equalToWhenPresent(record::getWidget1737338544071)
            .set(widget1732281602514).equalToWhenPresent(record::getWidget1732281602514)
            .set(widget1737338544074).equalToWhenPresent(record::getWidget1737338544074)
            .set(widget1732271235755).equalToWhenPresent(record::getWidget1732271235755)
            .set(widget1745227149952).equalToWhenPresent(record::getWidget1745227149952)
            .set(widget1747774937598).equalToWhenPresent(record::getWidget1747774937598)
            .set(widget1743581186765).equalToWhenPresent(record::getWidget1743581186765)
            .set(widget1743579518411).equalToWhenPresent(record::getWidget1743579518411)
            .set(widget1750208093989).equalToWhenPresent(record::getWidget1750208093989)
            .set(widget1747775605137).equalToWhenPresent(record::getWidget1747775605137)
            .set(widget1732583760832).equalToWhenPresent(record::getWidget1732583760832)
            .set(widget1732583760833).equalToWhenPresent(record::getWidget1732583760833)
            .set(widget1732583760834).equalToWhenPresent(record::getWidget1732583760834)
            .set(widget1732583760838).equalToWhenPresent(record::getWidget1732583760838)
            .set(widget1732583760842).equalToWhenPresent(record::getWidget1732583760842)
            .set(widget1747775605138).equalToWhenPresent(record::getWidget1747775605138)
            .set(widget1732280180068).equalToWhenPresent(record::getWidget1732280180068)
            .set(widget1732285199512).equalToWhenPresent(record::getWidget1732285199512)
            .set(widget1747775605139).equalToWhenPresent(record::getWidget1747775605139)
            .set(widget1744027642987).equalToWhenPresent(record::getWidget1744027642987)
            .set(widget1744027642988).equalToWhenPresent(record::getWidget1744027642988)
            .set(widget1743579339407).equalToWhenPresent(record::getWidget1743579339407)
            .set(widget1732271235785).equalToWhenPresent(record::getWidget1732271235785)
            .set(widget1732271235772).equalToWhenPresent(record::getWidget1732271235772)
            .set(widget1732281602529).equalToWhenPresent(record::getWidget1732281602529)
            .set(widget1732271235786).equalToWhenPresent(record::getWidget1732271235786)
            .set(widget1732271235787).equalToWhenPresent(record::getWidget1732271235787)
            .set(widget1732271235788).equalToWhenPresent(record::getWidget1732271235788)
            .set(widget1732271235789).equalToWhenPresent(record::getWidget1732271235789)
            .set(widget1743641684293).equalToWhenPresent(record::getWidget1743641684293)
            .set(widget1743602799865).equalToWhenPresent(record::getWidget1743602799865)
            .set(widget1743562445365).equalToWhenPresent(record::getWidget1743562445365)
            .set(widget1743387862109).equalToWhenPresent(record::getWidget1743387862109)
            .set(widget1743562445367).equalToWhenPresent(record::getWidget1743562445367)
            .set(widget1743562445368).equalToWhenPresent(record::getWidget1743562445368)
            .set(widget1743387862100).equalToWhenPresent(record::getWidget1743387862100)
            .set(widget1743602799863).equalToWhenPresent(record::getWidget1743602799863)
            .set(widget1732285199519).equalToWhenPresent(record::getWidget1732285199519)
            .set(widget1743983365624).equalToWhenPresent(record::getWidget1743983365624)
            .set(widget1743983365636).equalToWhenPresent(record::getWidget1743983365636)
            .set(widget1743641684294).equalToWhenPresent(record::getWidget1743641684294)
            .set(widget1732271235810).equalToWhenPresent(record::getWidget1732271235810)
            .set(widget1743602799866).equalToWhenPresent(record::getWidget1743602799866)
            .set(widget1743562445378).equalToWhenPresent(record::getWidget1743562445378)
            .set(widget1743562445379).equalToWhenPresent(record::getWidget1743562445379)
            .set(widget1743562445380).equalToWhenPresent(record::getWidget1743562445380)
            .set(widget1743602799867).equalToWhenPresent(record::getWidget1743602799867)
            .set(widget1743562445383).equalToWhenPresent(record::getWidget1743562445383)
            .set(widget1743562445384).equalToWhenPresent(record::getWidget1743562445384)
            .set(widget1743983365631).equalToWhenPresent(record::getWidget1743983365631)
            .set(widget1743983365635).equalToWhenPresent(record::getWidget1743983365635)
            .set(widget1743641684295).equalToWhenPresent(record::getWidget1743641684295)
            .set(widget1743562445375).equalToWhenPresent(record::getWidget1743562445375)
            .set(widget1743387862103).equalToWhenPresent(record::getWidget1743387862103)
            .set(widget1743562445366).equalToWhenPresent(record::getWidget1743562445366)
            .set(widget1743387862104).equalToWhenPresent(record::getWidget1743387862104)
            .set(widget1743387862105).equalToWhenPresent(record::getWidget1743387862105)
            .set(widget1743562445369).equalToWhenPresent(record::getWidget1743562445369)
            .set(widget1732271235811).equalToWhenPresent(record::getWidget1732271235811)
            .set(widget1743562445372).equalToWhenPresent(record::getWidget1743562445372)
            .set(widget1743983365633).equalToWhenPresent(record::getWidget1743983365633)
            .set(widget1743983365637).equalToWhenPresent(record::getWidget1743983365637)
            .set(flowState).equalToWhenPresent(record::getFlowState)
            .set(isDeleted).equalToWhenPresent(record::getIsDeleted)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(createOperId).equalToWhenPresent(record::getCreateOperId)
            .set(createOperName).equalToWhenPresent(record::getCreateOperName)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(updateOperId).equalToWhenPresent(record::getUpdateOperId)
            .set(updateOperName).equalToWhenPresent(record::getUpdateOperName)
            .where(id, isEqualTo(record::getId))
        );
    }
}