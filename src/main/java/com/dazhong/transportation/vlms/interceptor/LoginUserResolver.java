package com.dazhong.transportation.vlms.interceptor;

import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.exception.PermissionsException;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;


/**
 * 根据注解获取当前登录用户信息
 *
 * <AUTHOR>
 * @date 2024-12-25 19:01
 */
@Slf4j
@Component
public class LoginUserResolver implements HandlerMethodArgumentResolver {

    @Autowired
    public RedisUtils redisUtils;

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        if (methodParameter.hasParameterAnnotation(TokenUserAnnotation.class)) {
            return true;
        }
        return false;
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) nativeWebRequest.getNativeRequest();
        String tokenUserId = (String) request.getAttribute("tokenUserId");
        String user = redisUtils.getStr(BizConstant.LOGIN_USER_KEY + tokenUserId);
        if (StringUtils.isBlank(user)) {
            throw new PermissionsException(ExceptionEnum.LOGIN_FAIL);
        }
        redisUtils.set(BizConstant.LOGIN_USER_KEY + tokenUserId, user, BizConstant.LOGIN_EXPIRES_IN);
        TokenUserInfo tokenUserInfo = JSONUtil.toBean(user, TokenUserInfo.class);
        return tokenUserInfo;
    }
}
