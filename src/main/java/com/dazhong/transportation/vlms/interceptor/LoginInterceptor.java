package com.dazhong.transportation.vlms.interceptor;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.exception.PermissionsException;
import com.dazhong.transportation.vlms.service.IUserService;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 校验用户登录状态
 *
 * <AUTHOR>
 * @date 2024-12-28 13:03
 */
@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {

    @Autowired
    private IUserService userService;

    @Autowired
    public RedisUtils redisUtils;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            //获取函数上登录注解
            LoginRequiredAnnotation loginRequired = handlerMethod.getMethod().getAnnotation(LoginRequiredAnnotation.class);
            //获取类上登录注解
            LoginRequiredAnnotation classLoginRequired = handlerMethod.getMethod().getDeclaringClass().getAnnotation(LoginRequiredAnnotation.class);

            boolean needLogin = false;
            //函数上的注解优先级更高
            if (loginRequired != null) {
                needLogin = loginRequired.required();
            }
            //如果函数上没有注解，则采用类上的注解
            else if (classLoginRequired != null) {
                needLogin = classLoginRequired.required();
            }

            // 验证用户是否登录
            if (needLogin) {
                // 从请求头中获取token
                String headToken = request.getHeader("token");
                if (StringUtils.isBlank(headToken)) {
                    throw new PermissionsException(ExceptionEnum.LOGIN_FAIL);
                }
                String userId = redisUtils.getStr(BizConstant.LOGIN_TOKEN_KEY + headToken);
                if (StringUtils.isBlank(userId)) {
                    throw new PermissionsException(ExceptionEnum.LOGIN_FAIL);
                }
                redisUtils.set(BizConstant.LOGIN_TOKEN_KEY + headToken, userId, BizConstant.LOGIN_EXPIRES_IN);
                request.setAttribute("tokenUserId", userId);
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
