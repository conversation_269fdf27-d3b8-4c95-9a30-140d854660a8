package com.dazhong.transportation.vlms.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import com.dazhong.transportation.vlms.config.ApiSignatureAnnotation;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.exception.PermissionsException;

import lombok.extern.slf4j.Slf4j;

/**
 * 校验接口前面
 * <AUTHOR>
 * @date 2024-12-30 13:03
 */
@Slf4j
@Component
public class ApiSignatureInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            ApiSignatureAnnotation apiValidAnnotation = handlerMethod.getMethod().getAnnotation(ApiSignatureAnnotation.class);
            if (apiValidAnnotation != null){
                String appKey = request.getHeader("appKey");
                String sign = request.getHeader("sign");
                String signType = request.getHeader("signType");
                String timestamp = request.getHeader("timestamp");


                // todo 验签校验
                if (false){
                    throw new PermissionsException(ExceptionEnum.SIGN_FAIL);
                }
            }
        }
        return true;
    }


    /**
     * 校验签名通过
     * @return
     */
    private boolean validSignPass(){
        //TODO
        return true;
    }



    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
