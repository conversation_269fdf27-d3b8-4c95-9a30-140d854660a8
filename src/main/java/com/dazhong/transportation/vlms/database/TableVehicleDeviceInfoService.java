package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDeviceListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.model.VehicleDeviceInfo;

import java.util.List;

public interface TableVehicleDeviceInfoService extends BaseTableService<VehicleDeviceInfo, Long> {

    /**
     * 根据请求参数查询车辆设备信息列表
     * @param request 查询条件
     * @return 车辆设备信息列表
     */
    List<VehicleDeviceInfoResponse> searchVehicleDeviceList(SearchVehicleDeviceListRequest request);

    /**
     * 根据车架号查询设备信息
     * @param vin 车架号
     * @return 设备信息
     */
    VehicleDeviceInfo selectByVin(String vin);


    /**
     * 根据设备编号查询设备信息
     * @param deviceSeq 设备编号
     * @return 设备信息
     */
    VehicleDeviceInfo selectByDeviceSeq(String deviceSeq);
}
