package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dazhong.transportation.vlms.database.TableLicensePlateQuotaOperateLogService;
import com.dazhong.transportation.vlms.dto.LicensePlateQuotaOperateLogDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaOperateLogRequest;
import com.dazhong.transportation.vlms.mapper.LicensePlateQuotaOperateLogMapper;
import com.dazhong.transportation.vlms.model.LicensePlateQuotaOperateLog;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaOperateLogDynamicSqlSupport.licensePlateQuotaOperateLog;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableLicensePlateQuotaOperateLogServiceImpl implements TableLicensePlateQuotaOperateLogService {

    @Autowired
    private LicensePlateQuotaOperateLogMapper licensePlateQuotaOperateLogMapper;

    @Override
    public List<LicensePlateQuotaOperateLog> searchOperateLog(SearchLicensePlateQuotaOperateLogRequest request) {
        SelectStatementProvider selectStatement = select(licensePlateQuotaOperateLog.allColumns())
                .from(licensePlateQuotaOperateLog)
                .where()
                .and(licensePlateQuotaOperateLog.adjustType, isEqualToWhenPresent(request.getAdjustType()))
                .and(licensePlateQuotaOperateLog.createOperName, isLikeWhenPresent(transFuzzyQueryParam(request.getCreateOperName())).filter(StringUtils::isNotBlank))
                .and(licensePlateQuotaOperateLog.createTime, isBetweenWhenPresent(request.getCreateTimeStart()).and(request.getCreateTimeEnd()))
                .orderBy(licensePlateQuotaOperateLog.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateQuotaOperateLogMapper.selectMany(selectStatement);
    }

    @Override
    public void insertLog(LicensePlateQuotaOperateLogDto licensePlateQuotaOperateLogDTO, TokenUserInfo tokenUserInfo) {
        LicensePlateQuotaOperateLog newLog = BeanUtil.copyProperties(licensePlateQuotaOperateLogDTO, LicensePlateQuotaOperateLog.class);
        newLog.setAdjustType(Integer.parseInt(licensePlateQuotaOperateLogDTO.getAdjustType()));
        newLog.setCreateOperId(tokenUserInfo.getUserId());
        newLog.setCreateOperName(tokenUserInfo.getName());
        licensePlateQuotaOperateLogMapper.insertSelective(newLog);
    }
}
