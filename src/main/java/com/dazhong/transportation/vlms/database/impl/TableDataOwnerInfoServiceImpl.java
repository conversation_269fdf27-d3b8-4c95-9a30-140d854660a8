package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.util.ObjUtil;
import com.dazhong.transportation.vlms.database.TableDataOwnerInfoService;
import com.dazhong.transportation.vlms.dto.DataOwnerDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.DataOwnerInfoMapper;
import com.dazhong.transportation.vlms.mapper.extend.DataOwnerInfoExtendMapper;
import com.dazhong.transportation.vlms.model.DataOwnerInfo;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.CompanyExtraInfoDynamicSqlSupport.companyExtraInfo;
import static com.dazhong.transportation.vlms.mapper.DataOwnerInfoDynamicSqlSupport.dataOwnerInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableDataOwnerInfoServiceImpl implements TableDataOwnerInfoService {

    @Autowired
    private DataOwnerInfoMapper dataOwnerInfoMapper;

    @Autowired
    private DataOwnerInfoExtendMapper dataOwnerInfoExtendMapper;

    @Override
    public DataOwnerInfo insert(DataOwnerInfo dataOwnerInfo) {
        Date now = new Date();
        dataOwnerInfo.setCreateTime(now);
        dataOwnerInfo.setUpdateTime(now);
        dataOwnerInfoMapper.insertSelective(dataOwnerInfo);
        return dataOwnerInfo;
    }

    @Override
    public DataOwnerInfo insert(DataOwnerInfo dataOwnerInfo, String operator) {
        dataOwnerInfo.setCreateOperId(-1L);
        dataOwnerInfo.setCreateOperName(operator);
        dataOwnerInfo.setUpdateOperId(-1L);
        dataOwnerInfo.setUpdateOperName(operator);
        return this.insert(dataOwnerInfo);
    }

    @Override
    public DataOwnerInfo insert(DataOwnerInfo dataOwnerInfo, TokenUserInfo tokenUserInfo) {
        dataOwnerInfo.setCreateOperId(tokenUserInfo.getUserId());
        dataOwnerInfo.setCreateOperName(tokenUserInfo.getName());
        dataOwnerInfo.setUpdateOperId(tokenUserInfo.getUserId());
        dataOwnerInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(dataOwnerInfo);
    }

    @Override
    public int updateSelectiveById(DataOwnerInfo dataOwnerInfo) {
        dataOwnerInfo.setUpdateTime(new Date());
        return dataOwnerInfoMapper.updateByPrimaryKeySelective(dataOwnerInfo);
    }

    @Override
    public int updateSelectiveById(DataOwnerInfo dataOwnerInfo, String operator) {
        dataOwnerInfo.setUpdateOperId(-1L);
        dataOwnerInfo.setUpdateOperName(operator);
        return this.updateSelectiveById(dataOwnerInfo);
    }

    @Override
    public int updateSelectiveById(DataOwnerInfo dataOwnerInfo, TokenUserInfo tokenUserInfo) {
        dataOwnerInfo.setUpdateOperId(tokenUserInfo.getUserId());
        dataOwnerInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.updateSelectiveById(dataOwnerInfo);
    }

    @Override
    public List<DataOwnerInfo> queryAllOwner() {
        SelectStatementProvider selectStatement = select(dataOwnerInfo.allColumns())
                .from(dataOwnerInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataOwnerInfoMapper.selectMany(selectStatement);
    }

    @Override
    public List<DataOwnerDto> queryAllOwnerList() {
        SelectStatementProvider provider = select(
                dataOwnerInfo.id,
                dataOwnerInfo.address,
                dataOwnerInfo.name,
                dataOwnerInfo.phone,
                companyExtraInfo.ceoName,
                companyExtraInfo.ceoPhone,
                companyExtraInfo.dingTalkNo
        )
                .from(dataOwnerInfo)
                .leftJoin(companyExtraInfo).on(dataOwnerInfo.id, equalTo(companyExtraInfo.foreignId),
                        SqlBuilder.and(companyExtraInfo.businessType,equalTo(SqlBuilder.constant("2"))))
                .where()
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataOwnerInfoMapper.selectDataOwnerList(provider);
    }

    @Override
    public boolean hasDuplicateOwnerName(String ownerName, Long excludeId) {
        if (StringUtils.isBlank(ownerName)) {
            return true;
        }

        SelectStatementProvider selectStatement = select(dataOwnerInfo.allColumns())
                .from(dataOwnerInfo)
                .where(dataOwnerInfo.id, isNotEqualToWhenPresent(excludeId))
                .and(dataOwnerInfo.name, isEqualTo(ownerName))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        DataOwnerInfo duplicateOwnerInfo = dataOwnerInfoMapper.selectOne(selectStatement).orElse(null);
        return duplicateOwnerInfo != null;
    }

    @Override
    public DataOwnerInfo queryOwnerInfoById(Integer ownerId) {
        Optional<DataOwnerInfo> optional = dataOwnerInfoMapper.selectByPrimaryKey(ownerId.longValue());
        return optional.orElse(null);
    }

    @Override
    public DataOwnerInfo queryOwnerInfoByName(String ownerName) {
        if (StringUtils.isBlank(ownerName)) {
            return null;
        }
        SelectStatementProvider provider = select(dataOwnerInfo.allColumns())
                .from(dataOwnerInfo)
                .where()
                .and(dataOwnerInfo.name, isEqualTo(ownerName))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<DataOwnerInfo> optional = dataOwnerInfoMapper.selectOne(provider);
        return optional.orElse(null);
    }

    @Override
    public DataOwnerInfo queryOwnerInfoById(Long ownerId) {
        if (ownerId == null) {
            return null;
        }
        Optional<DataOwnerInfo> optional = dataOwnerInfoMapper.selectByPrimaryKey(ownerId);
        return optional.orElse(null);
    }

    @Override
    public List<DataOwnerInfo> queryOwnerInfoList(List<Long> idList) {
        SelectStatementProvider provider = select(dataOwnerInfo.allColumns())
                .from(dataOwnerInfo)
                .where()
                .and(dataOwnerInfo.id, isInWhenPresent(idList).filter(ObjUtil::isNotEmpty))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataOwnerInfoMapper.selectMany(provider);
    }

    @Override
    public DataOwnerInfo insertById(DataOwnerInfo dataOwnerInfo) {
        Date now = new Date();
        dataOwnerInfo.setCreateTime(now);
        dataOwnerInfo.setUpdateTime(now);
        // 使用扩展Mapper的方法，支持保存用户指定的ID
        dataOwnerInfoExtendMapper.insertSelectiveWithId(dataOwnerInfo);
        return dataOwnerInfo;
    }

    @Override
    public DataOwnerInfo insertById(DataOwnerInfo dataOwnerInfo, String operator) {
        dataOwnerInfo.setCreateOperId(-1L);
        dataOwnerInfo.setCreateOperName(operator);
        dataOwnerInfo.setUpdateOperId(-1L);
        dataOwnerInfo.setUpdateOperName(operator);
        return this.insertById(dataOwnerInfo);
    }

    @Override
    public DataOwnerInfo insertById(DataOwnerInfo dataOwnerInfo, TokenUserInfo tokenUserInfo) {
        dataOwnerInfo.setCreateOperId(tokenUserInfo.getUserId());
        dataOwnerInfo.setCreateOperName(tokenUserInfo.getName());
        dataOwnerInfo.setUpdateOperId(tokenUserInfo.getUserId());
        dataOwnerInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insertById(dataOwnerInfo);
    }
}
