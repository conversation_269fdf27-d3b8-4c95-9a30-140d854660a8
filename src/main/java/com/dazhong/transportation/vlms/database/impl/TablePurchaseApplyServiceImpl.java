package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TablePurchaseApplyService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchPurchaseApplyRequest;
import com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyMapper;
import com.dazhong.transportation.vlms.model.VehiclePurchaseApply;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyDynamicSqlSupport.vehiclePurchaseApply;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2025-01-06 10:38
 */
@Service
public class TablePurchaseApplyServiceImpl implements TablePurchaseApplyService {

    @Autowired
    private VehiclePurchaseApplyMapper vehiclePurchaseApplyMapper;

    @Override
    public List<VehiclePurchaseApply> searchPurchaseList(SearchPurchaseApplyRequest request,TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();
        SelectStatementProvider selectStatement = select(vehiclePurchaseApply.allColumns())
                .from(vehiclePurchaseApply)
                .where()
                .and(vehiclePurchaseApply.isDeleted, isEqualTo(0))
                .and(vehiclePurchaseApply.purchaseApplyNo, isLikeWhenPresent(transFuzzyQueryParam(request.getPurchaseApplyNo())))
                .and(vehiclePurchaseApply.approvalNumber, isLikeWhenPresent(transFuzzyQueryParam(request.getApprovalNumber())))
                .and(vehiclePurchaseApply.purchaseApplyStatus, isInWhenPresent(request.getPurchaseApplyStatus()).filter(ObjectValidUtil::isValid))
                .and(vehiclePurchaseApply.applyOrgId, isEqualToWhenPresent(request.getApplyOrgId()).filter(ObjectValidUtil::isValid))
                .and(vehiclePurchaseApply.subscriptionCompanyCode, isEqualToWhenPresent(request.getSubscriptionCompanyCode()).filter(StringUtils::isNotBlank))
                .and(vehiclePurchaseApply.createOperName, isLikeWhenPresent(transFuzzyQueryParam(request.getCreateUser())))
                .and(vehiclePurchaseApply.applyOrgId, isInWhenPresent(orgIdList),or(vehiclePurchaseApply.ownerId, isInWhenPresent(ownerIdList)))
                .orderBy(vehiclePurchaseApply.updateTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return vehiclePurchaseApplyMapper.selectMany(selectStatement);
    }

    @Override
    public VehiclePurchaseApply queryPurchaseApply(String purchaseApplyNo) {
        SelectStatementProvider selectStatement = select(vehiclePurchaseApply.allColumns())
                .from(vehiclePurchaseApply)
                .where()
                .and(vehiclePurchaseApply.purchaseApplyNo, isEqualTo(purchaseApplyNo))
                .and(vehiclePurchaseApply.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehiclePurchaseApply> optional = vehiclePurchaseApplyMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public VehiclePurchaseApply queryPurchaseApplyApprovalNumber(String approvalNumber) {
        SelectStatementProvider selectStatement = select(vehiclePurchaseApply.allColumns())
                .from(vehiclePurchaseApply)
                .where()
                .and(vehiclePurchaseApply.approvalNumber, isEqualTo(approvalNumber))
                .and(vehiclePurchaseApply.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehiclePurchaseApply> optional = vehiclePurchaseApplyMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public VehiclePurchaseApply selectById(Long id) {
        SelectStatementProvider selectStatement = select(vehiclePurchaseApply.allColumns())
                .from(vehiclePurchaseApply)
                .where()
                .and(vehiclePurchaseApply.id, isEqualTo(id))
                .and(vehiclePurchaseApply.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehiclePurchaseApply> optional = vehiclePurchaseApplyMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int savePurchaseApply(VehiclePurchaseApply purchaseApply, TokenUserInfo tokenUserInfo) {
        purchaseApply.setApplyUser(tokenUserInfo.getName());
        purchaseApply.setApplyUserNo(tokenUserInfo.getDingTalkNum());
        purchaseApply.setCreateOperId(tokenUserInfo.getUserId());
        purchaseApply.setCreateOperName(tokenUserInfo.getName());
        return vehiclePurchaseApplyMapper.insertSelective(purchaseApply);
    }

    @Override
    public int updatePurchaseApply(VehiclePurchaseApply purchaseApply, TokenUserInfo tokenUserInfo) {
        purchaseApply.setUpdateOperId(purchaseApply.getCreateOperId());
        purchaseApply.setUpdateOperName(purchaseApply.getCreateOperName());
        purchaseApply.setUpdateTime(new Date());
        return vehiclePurchaseApplyMapper.updateByPrimaryKeySelective(purchaseApply);
    }
}
