package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.DataDictInfo;

import java.util.List;

/**
 * 数据字典表服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableDataDictService extends BaseTableService<DataDictInfo, Long> {


    /**
     * 查询数据字典信息
     * @return
     */
    List<DataDictInfo> searchDataDictList();

    /**
     * 保存数据字典信息
     * @param dataDictInfo
     * @return
     */
    int saveDataDict(DataDictInfo dataDictInfo);

    /**
     * 修改数据字典信息
     * @param dataDictInfo
     * @return
     */
    int updateDataDict(DataDictInfo dataDictInfo);

    /**
     * 删除数据字典信息
     * @param id
     * @return
     */
    int deleteDataDict(Long id);

    /**
     * 查询数据字典明细
     * @param dataCode
     * @return
     */
    DataDictInfo queryDataDictByCode(String dataCode);
}
