package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableSsoUserService;
import com.dazhong.transportation.vlms.mapper.SsoUserInfoMapper;
import com.dazhong.transportation.vlms.model.SsoUserInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.SsoUserInfoDynamicSqlSupport.ssoUserInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableSsoUserServiceImpl implements TableSsoUserService {

    @Autowired
    private SsoUserInfoMapper ssoUserInfoMapper;

    @Override
    public List<SsoUserInfo> querySsoUserList() {
        SelectStatementProvider provider = select(ssoUserInfo.allColumns())
                .from(ssoUserInfo)
                .where()
                .and(ssoUserInfo.isDeleted, isEqualTo(0))
                .orderBy(ssoUserInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ssoUserInfoMapper.selectMany(provider);
    }

    @Override
    public SsoUserInfo querySsoUserByUserId(String userId) {
        SelectStatementProvider provider = select(ssoUserInfo.allColumns())
                .from(ssoUserInfo)
                .where()
                .and(ssoUserInfo.userId, isEqualTo(userId))
                .and(ssoUserInfo.isDeleted, isEqualTo(0))
                .orderBy(ssoUserInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<SsoUserInfo> optional = ssoUserInfoMapper.selectOne(provider);
        return optional.orElse(null);
    }

    @Override
    public int saveSsoUser(SsoUserInfo ssoUserInfo) {
        return ssoUserInfoMapper.insertSelective(ssoUserInfo);
    }

    @Override
    public int updateSsoUser(SsoUserInfo ssoUserInfo) {
        return ssoUserInfoMapper.updateByPrimaryKeySelective(ssoUserInfo);
    }

    @Override
    public int deleteSsoUser(Long id) {
        return ssoUserInfoMapper.deleteByPrimaryKey(id);
    }
}
