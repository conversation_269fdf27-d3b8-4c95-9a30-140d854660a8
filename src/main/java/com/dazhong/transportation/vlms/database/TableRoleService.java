package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.RoleInfo;

import java.util.List;

/**
 * 系统角色服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableRoleService extends BaseTableService<RoleInfo, Long> {

    /**
     * 根据名称查询
     * @param roleName
     * @return
     */
    RoleInfo queryRoleInfoByName(String roleName);
    /**
     * 新增角色
     * @param info
     * @return
     */
    int addRoleInfo(RoleInfo info);

    /**
     * 修改角色
     * @param info
     * @return
     */
    int updateRoleInfo(RoleInfo info);

    /**
     * 删除角色
     * @param id
     * @return
     */
    int deleteRoleInfo(Long id);

    /**
     * 查询角色列表
     * @return
     */
    List<RoleInfo> queryAllRoleList();

    /**
     * 查询用户角色列表
     * @param userId
     * @return
     */
    List<RoleInfo> queryAllRoleListByUserId(Long userId);

}
