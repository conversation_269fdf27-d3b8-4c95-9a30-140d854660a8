package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TablePurchaseEquipmentService;
import com.dazhong.transportation.vlms.mapper.VehiclePurchaseEquipmentMapper;
import com.dazhong.transportation.vlms.model.VehiclePurchaseEquipment;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseEquipmentDynamicSqlSupport.vehiclePurchaseEquipment;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2025-01-06 10:38
 */
@Service
public class TablePurchaseEquipmentServiceImpl implements TablePurchaseEquipmentService {

    @Autowired
    private VehiclePurchaseEquipmentMapper vehiclePurchaseEquipmentMapper;


    @Override
    public List<VehiclePurchaseEquipment> queryPurchaseEquipmentList(Long purchaseApplyId) {
        SelectStatementProvider selectStatement = select(vehiclePurchaseEquipment.allColumns())
                .from(vehiclePurchaseEquipment)
                .where()
                .and(vehiclePurchaseEquipment.purchaseApplyId, isEqualTo(purchaseApplyId))
                .and(vehiclePurchaseEquipment.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehiclePurchaseEquipmentMapper.selectMany(selectStatement);
    }

    @Override
    public int savePurchaseEquipment(VehiclePurchaseEquipment purchaseEquipment) {
        return vehiclePurchaseEquipmentMapper.insertSelective(purchaseEquipment);
    }

    @Override
    public int deletePurchaseEquipment(Long purchaseApplyId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehiclePurchaseEquipment).where()
                .and(vehiclePurchaseEquipment.purchaseApplyId, isEqualTo(purchaseApplyId))
                .and(vehiclePurchaseEquipment.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehiclePurchaseEquipmentMapper.delete(deleteStatementProvider);
    }
}
