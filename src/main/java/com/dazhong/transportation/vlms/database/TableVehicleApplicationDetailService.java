package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.model.VehicleApplicationDetail;

import java.util.List;

public interface TableVehicleApplicationDetailService extends BaseTableService<VehicleApplicationDetail, Long> {

    /**
     * 批量插入
     *
     * @param vehicleApplicationDetailList 车辆处置详情列表
     * @param tokenUserInfo                用户登录信息
     */
    void batchInsert(List<VehicleApplicationDetail> vehicleApplicationDetailList, TokenUserInfo tokenUserInfo);

    /**
     * 批量删除
     * @param applicationId 申请单id
     */
    void deleteByApplicationId(Long applicationId);

    /**
     * 根据单据号查询车辆处置详情列表
     *
     * @param applicationId 申请单id
     * @return 车辆处置详情列表
     */
    List<VehicleApplicationDetail> getVehicleApplicationDetailListByApplicationId(Long applicationId);
}
