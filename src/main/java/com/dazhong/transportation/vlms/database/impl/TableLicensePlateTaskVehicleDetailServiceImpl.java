package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableLicensePlateTaskVehicleDetailService;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.LicensePlateTaskVehicleDetailMapper;
import com.dazhong.transportation.vlms.model.LicensePlateTaskVehicleDetail;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskVehicleDetailDynamicSqlSupport.licensePlateTaskVehicleDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class TableLicensePlateTaskVehicleDetailServiceImpl implements TableLicensePlateTaskVehicleDetailService {

    @Autowired
    private LicensePlateTaskVehicleDetailMapper licensePlateTaskVehicleDetailMapper;

    @Override
    public int insertMultiple(List<LicensePlateTaskVehicleDetailDto> info, TokenUserInfo tokenUserInfo) {
        List<LicensePlateTaskVehicleDetail> rows = new ArrayList<>();
        for (LicensePlateTaskVehicleDetailDto licensePlateTaskVehicleDetailDTO : info) {
            LicensePlateTaskVehicleDetail row = new LicensePlateTaskVehicleDetail();
            BeanUtils.copyProperties(licensePlateTaskVehicleDetailDTO, row);
            row.setCreateOperId(tokenUserInfo.getUserId());
            row.setCreateOperName(tokenUserInfo.getName());
            rows.add(row);
        }
        return licensePlateTaskVehicleDetailMapper.insertMultiple(rows);
    }

    @Override
    public List<LicensePlateTaskVehicleDetail> selectByTaskNumber(String taskNumber) {
        SelectStatementProvider provider = select(licensePlateTaskVehicleDetail.allColumns())
                .from(licensePlateTaskVehicleDetail)
                .where()
                .and(licensePlateTaskVehicleDetail.taskNumber, isEqualTo(taskNumber))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateTaskVehicleDetailMapper.selectMany(provider);
    }
}
