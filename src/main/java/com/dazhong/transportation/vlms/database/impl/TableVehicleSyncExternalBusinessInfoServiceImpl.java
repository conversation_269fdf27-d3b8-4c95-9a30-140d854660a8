package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableVehicleSyncExternalBusinessInfoService;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalBusinessInfoDto;
import com.dazhong.transportation.vlms.mapper.extend.VehicleSyncExternalBusinessInfoExtendMapper;
import com.dazhong.transportation.vlms.model.VehicleSyncExternalBusinessInfo;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.Date;
import java.util.List;
import static com.dazhong.transportation.vlms.mapper.VehicleSyncExternalBusinessInfoDynamicSqlSupport.vehicleSyncExternalBusinessInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleSyncExternalBusinessInfoServiceImpl implements TableVehicleSyncExternalBusinessInfoService {

    @Autowired
    private VehicleSyncExternalBusinessInfoExtendMapper vehicleSyncExternalBusinessInfoExtendMapper;

    @Override
    public int batchInsert(List<VehicleSyncExternalBusinessInfoDto> request) {
        if (CollectionUtils.isEmpty(request)){
            return 0;
        }
        List<VehicleSyncExternalBusinessInfo> rows = Lists.newArrayList();
        request.forEach(detail ->{
            VehicleSyncExternalBusinessInfo row = new VehicleSyncExternalBusinessInfo();
            row.setVin(StringUtils.defaultString(detail.getVin(),""));
            row.setLicensePlate(StringUtils.defaultString(detail.getLicensePlate(),""));
            row.setBusinessNo(StringUtils.defaultString(detail.getBusinessNo(),""));
            row.setBusinessTime(StringUtils.defaultString(detail.getBusinessTime(),""));
            row.setBusinessType(StringUtils.defaultString(detail.getBusinessType(),""));
            row.setBusinessInfo(StringUtils.defaultString(detail.getBusinessInfo(),""));
            row.setIsDeleted(0);
            row.setCreateTime(new Date());
            row.setUpdateTime(new Date());
            row.setCreateOperId(-1L);
            row.setCreateOperName(BizConstant.SYSTEM_NAME);
            row.setUpdateOperId(-1L);
            row.setUpdateOperName(BizConstant.SYSTEM_NAME);
            rows.add(row);
        });
        return vehicleSyncExternalBusinessInfoExtendMapper.insertMultiple(rows);
    }

    @Override
    public List<VehicleSyncExternalBusinessInfo> selectByLicensePlateAndType(String licensePlate, String businessType) {
        SelectStatementProvider provider = select(
                vehicleSyncExternalBusinessInfo.id,
                vehicleSyncExternalBusinessInfo.vin,
                vehicleSyncExternalBusinessInfo.licensePlate,
                vehicleSyncExternalBusinessInfo.businessType,
                vehicleSyncExternalBusinessInfo.businessTime,
                vehicleSyncExternalBusinessInfo.businessInfo,
                vehicleSyncExternalBusinessInfo.createTime
                )
                .from(vehicleSyncExternalBusinessInfo)
                .where()
                .and(vehicleSyncExternalBusinessInfo.licensePlate, isEqualToWhenPresent(licensePlate).filter(StringUtils::isNotBlank))
                .and(vehicleSyncExternalBusinessInfo.businessType, isEqualToWhenPresent(businessType).filter(StringUtils::isNotBlank))
                .orderBy(vehicleSyncExternalBusinessInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleSyncExternalBusinessInfoExtendMapper.selectMany(provider);
    }
}
