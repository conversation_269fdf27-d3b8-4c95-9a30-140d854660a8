package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleApplicationListDto;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleApplicationListRequest;
import com.dazhong.transportation.vlms.model.VehicleApplication;

import java.util.List;

public interface TableVehicleApplicationService extends BaseTableService<VehicleApplication, Long> {

    /**
     * 新增车辆申请单
     *
     * @param vehicleApplication 插入对象
     * @param tokenUserInfo      操作人信息
     * @return 返回车辆申请单
     */
    VehicleApplication insert(VehicleApplication vehicleApplication, TokenUserInfo tokenUserInfo);

    /**
     * 查询车辆处置列表
     *
     * @param searchVehicleApplicationListRequest 查询车辆处置列表入参
     * @param tokenUserInfo                       用户登录信息
     * @return 返回车辆处置列表
     */
    List<VehicleApplicationListDto> queryVehicleApplicationList(SearchVehicleApplicationListRequest searchVehicleApplicationListRequest, TokenUserInfo tokenUserInfo);

    /**
     * 根据钉钉审批号查询车辆申请单
     *
     * @param dingTalkNo 钉钉审批号
     * @return 返回车辆申请单
     */
    VehicleApplication selectByDingTalkNo(String dingTalkNo);
}
