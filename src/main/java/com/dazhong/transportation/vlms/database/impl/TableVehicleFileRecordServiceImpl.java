package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleFileRecordService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleFileRecordMapper;
import com.dazhong.transportation.vlms.model.VehicleFileRecord;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.DataDictInfoDynamicSqlSupport.dataDictInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleFileRecordDynamicSqlSupport.vehicleFileRecord;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * <AUTHOR>
 * @date 2025-02-08 10:39
 */
@Service
public class TableVehicleFileRecordServiceImpl implements TableVehicleFileRecordService {

    @Autowired
    private VehicleFileRecordMapper vehicleFileRecordMapper;

    @Override
    public List<VehicleFileRecord> queryVehicleFileRecord(String vin) {
        SelectStatementProvider provider = select(vehicleFileRecord.allColumns())
                .from(vehicleFileRecord)
                .where()
                .and(vehicleFileRecord.vin,isEqualTo(vin))
                .and(vehicleFileRecord.isDeleted, isEqualTo(0))
                .orderBy(dataDictInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleFileRecordMapper.selectMany(provider);
    }

    @Override
    public VehicleFileRecord insert(VehicleFileRecord vehicleFileRecord, TokenUserInfo tokenUserInfo) {
        vehicleFileRecord.setCreateOperId(tokenUserInfo.getUserId());
        vehicleFileRecord.setCreateOperName(tokenUserInfo.getName());
        vehicleFileRecord.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleFileRecord.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(vehicleFileRecord);
    }

    @Override
    public VehicleFileRecord insert(VehicleFileRecord vehicleFileRecord) {
        Date now = new Date();
        vehicleFileRecord.setCreateTime(now);
        vehicleFileRecord.setUpdateTime(now);
        vehicleFileRecordMapper.insertSelective(vehicleFileRecord);
        return vehicleFileRecord;
    }
}
