package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.model.VehicleDisposalDetail;

import java.util.List;

public interface TableVehicleDisposalDetailService extends BaseTableService<VehicleDisposalDetail, Long> {

    /**
     * 批量插入
     *
     * @param vehicleDisposalDetailList 车辆处置详情列表
     * @param tokenUserInfo             用户登录信息
     */
    void batchInsert(List<VehicleDisposalDetail> vehicleDisposalDetailList, TokenUserInfo tokenUserInfo);

    /**
     * 批量删除
     *
     * @param disposalId 申请单id
     */
    void deleteByDisposalId(Long disposalId);

    /**
     * 根据单据号查询车辆处置详情列表
     *
     * @param disposalId 处置单id
     * @return 车辆处置详情列表
     */
    List<VehicleDisposalDetail> getVehicleDisposalDetailListByDisposalId(Long disposalId);

    /**
     * 新增车辆处置
     *
     * @param vehicleDisposalDetail 插入对象
     * @return
     */
    void updateDingTalkInfo(VehicleDisposalDetail vehicleDisposalDetail);
}
