package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleDeviceInfoService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDeviceListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.mapper.extend.VehicleDeviceInfoExtendMapper;
import com.dazhong.transportation.vlms.model.VehicleDeviceInfo;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleDeviceInfoDynamicSqlSupport.vehicleDeviceInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleInfoDynamicSqlSupport.vehicleInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleDeviceInfoServiceImpl implements TableVehicleDeviceInfoService {

    @Autowired
    private VehicleDeviceInfoExtendMapper vehicleDeviceInfoMapper;

    @Override
    public VehicleDeviceInfo insert(VehicleDeviceInfo vehicleDeviceInfo) {
        Date now = new Date();
        vehicleDeviceInfo.setCreateTime(now);
        vehicleDeviceInfo.setUpdateTime(now);
        vehicleDeviceInfoMapper.insertSelective(vehicleDeviceInfo);
        return vehicleDeviceInfo;
    }

    @Override
    public VehicleDeviceInfo insert(VehicleDeviceInfo vehicleDeviceInfo, String operator) {
        vehicleDeviceInfo.setCreateOperId(-1L);
        vehicleDeviceInfo.setCreateOperName(operator);
        vehicleDeviceInfo.setUpdateOperId(-1L);
        vehicleDeviceInfo.setUpdateOperName(operator);
        return this.insert(vehicleDeviceInfo);
    }

    @Override
    public VehicleDeviceInfo insert(VehicleDeviceInfo vehicleDeviceInfo, TokenUserInfo tokenUserInfo) {
        vehicleDeviceInfo.setCreateOperId(tokenUserInfo.getUserId());
        vehicleDeviceInfo.setCreateOperName(tokenUserInfo.getName());
        vehicleDeviceInfo.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleDeviceInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(vehicleDeviceInfo);
    }

    @Override
    public int updateSelectiveById(VehicleDeviceInfo vehicleDeviceInfo) {
        vehicleDeviceInfo.setUpdateTime(new Date());
        return vehicleDeviceInfoMapper.updateByPrimaryKeySelective(vehicleDeviceInfo);
    }

    @Override
    public int updateSelectiveById(VehicleDeviceInfo vehicleDeviceInfo, String operator) {
        vehicleDeviceInfo.setUpdateOperId(-1L);
        vehicleDeviceInfo.setUpdateOperName(operator);
        return this.updateSelectiveById(vehicleDeviceInfo);
    }

    @Override
    public int updateSelectiveById(VehicleDeviceInfo vehicleDeviceInfo, TokenUserInfo tokenUserInfo) {
        vehicleDeviceInfo.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleDeviceInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.updateSelectiveById(vehicleDeviceInfo);
    }

    @Override
    public List<VehicleDeviceInfoResponse> searchVehicleDeviceList(SearchVehicleDeviceListRequest request) {
        SelectStatementProvider selectStatement = select(
                vehicleDeviceInfo.allColumns(),
                vehicleInfo.licensePlate
                ).from(vehicleDeviceInfo)
                .leftJoin(vehicleInfo).on(vehicleDeviceInfo.vin, equalTo(vehicleInfo.vin))
                .where(vehicleDeviceInfo.vin, isLikeWhenPresent(transFuzzyQueryParam(request.getVin())))
                .and(vehicleInfo.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(request.getLicensePlate())))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDeviceInfoMapper.searchVehicleDeviceList(selectStatement);
    }

    @Override
    public VehicleDeviceInfo selectByVin(String vin) {
        if(StringUtils.isBlank(vin)){
            return null;
        }

        SelectStatementProvider selectStatement = select(vehicleDeviceInfo.allColumns())
                .from(vehicleDeviceInfo)
                .where(vehicleDeviceInfo.vin, isEqualTo(vin))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDeviceInfoMapper.selectOne(selectStatement).orElse(null);
    }

    @Override
    public VehicleDeviceInfo selectByDeviceSeq(String deviceSeq) {
        if(StringUtils.isBlank(deviceSeq)){
            return null;
        }

        SelectStatementProvider selectStatement = select(vehicleDeviceInfo.allColumns())
                .from(vehicleDeviceInfo)
                .where(vehicleDeviceInfo.deviceSeq, isEqualTo(deviceSeq))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDeviceInfoMapper.selectOne(selectStatement).orElse(null);
    }
}
