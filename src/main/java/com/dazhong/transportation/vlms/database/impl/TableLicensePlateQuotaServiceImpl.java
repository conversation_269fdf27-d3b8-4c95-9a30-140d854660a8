package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableLicensePlateQuotaService;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaRequest;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaResponse;
import com.dazhong.transportation.vlms.mapper.LicensePlateQuotaMapper;
import com.dazhong.transportation.vlms.model.LicensePlateQuota;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaDynamicSqlSupport.licensePlateQuota;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableLicensePlateQuotaServiceImpl implements TableLicensePlateQuotaService {

    @Autowired
    private LicensePlateQuotaMapper licensePlateQuotaMapper;

    @Override
    public List<LicensePlateQuota> queryList(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest) {
        SelectStatementProvider provider = select(licensePlateQuota.allColumns())
                .from(licensePlateQuota)
                .where()
                .and(licensePlateQuota.quotaType, isEqualToWhenPresent(searchLicensePlateQuotaRequest.getQuotaType()))
                .and(licensePlateQuota.assetCompanyId, isInWhenPresent(searchLicensePlateQuotaRequest.getAssetCompanyIdList()))
                .orderBy(licensePlateQuota.quotaType)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateQuotaMapper.selectMany(provider);
    }

    @Override
    public List<LicensePlateQuota> queryListGroupByQuotaType(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest) {
        SelectStatementProvider provider = select(
                licensePlateQuota.quotaType,
                sum(licensePlateQuota.quota).as("quota"),
                sum(licensePlateQuota.preOccupied).as("pre_occupied"),
                sum(licensePlateQuota.occupied).as("occupied"),
                sum(licensePlateQuota.remaining).as("remaining")
        )
                .from(licensePlateQuota)
                .where()
                .and(licensePlateQuota.quotaType, isEqualToWhenPresent(searchLicensePlateQuotaRequest.getQuotaType()))
                .and(licensePlateQuota.assetCompanyId, isInWhenPresent(searchLicensePlateQuotaRequest.getAssetCompanyIdList()))
                .groupBy(licensePlateQuota.quotaType)
                .orderBy(licensePlateQuota.quotaType)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateQuotaMapper.selectMany(provider);
    }

    @Override
    public int updateByPrimaryKeySelective(LicensePlateQuota licensePlateQuota) {
        return licensePlateQuotaMapper.updateByPrimaryKeySelective(licensePlateQuota);
    }

    @Override
    public int saveLicensePlateQuota(LicensePlateQuota licensePlateQuota) {
        return licensePlateQuotaMapper.insertSelective(licensePlateQuota);
    }

    @Override
    public LicensePlateQuota selectByQuotaTypeAndAssetCompanyCode(Integer quotaType, Integer assetCompanyId) {
        SelectStatementProvider selectStatement = select(licensePlateQuota.allColumns())
                .from(licensePlateQuota)
                .where()
                .and(licensePlateQuota.quotaType, isEqualTo(quotaType))
                .and(licensePlateQuota.assetCompanyId, isEqualTo(assetCompanyId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<LicensePlateQuota> optional = licensePlateQuotaMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public LicensePlateQuotaResponse selectLicensePlateQuotaResponse(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest) {
        SelectStatementProvider provider = select(
                sum(licensePlateQuota.quota).as("quota"),
                sum(licensePlateQuota.preOccupied).as("preOccupied"),
                sum(licensePlateQuota.occupied).as("occupied"),
                sum(licensePlateQuota.remaining).as("remaining"))
                .from(licensePlateQuota)
                .where()
                .and(licensePlateQuota.quotaType, isEqualToWhenPresent(searchLicensePlateQuotaRequest.getQuotaType()))
                .and(licensePlateQuota.assetCompanyId, isInWhenPresent(searchLicensePlateQuotaRequest.getAssetCompanyIdList()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateQuotaMapper.selectLicensePlateQuotaResponse(provider);
    }

    @Override
    public List<LicensePlateQuota> selectByQuotaType(Integer quotaType) {
        SelectStatementProvider selectStatement = select(licensePlateQuota.allColumns())
                .from(licensePlateQuota)
                .where()
                .and(licensePlateQuota.quotaType, isEqualTo(quotaType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateQuotaMapper.selectMany(selectStatement);
    }
}
