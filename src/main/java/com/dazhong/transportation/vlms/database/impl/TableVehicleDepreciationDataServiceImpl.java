package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleDepreciationDataService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleDepreciationDataMapper;
import com.dazhong.transportation.vlms.model.VehicleDepreciationData;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleDepreciationDataDynamicSqlSupport.vehicleDepreciationData;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleDepreciationDataServiceImpl implements TableVehicleDepreciationDataService {

    @Autowired
    private VehicleDepreciationDataMapper vehicleDepreciationDataMapper;

    @Override
    public List<VehicleDepreciationData> queryVehicleDepreciationDataList(String vin) {
        SelectStatementProvider provider = select(vehicleDepreciationData.allColumns())
                .from(vehicleDepreciationData)
                .where()
                .and(vehicleDepreciationData.vin, isIn(vin))
                .and(vehicleDepreciationData.isDeleted, isEqualTo(0))
                .orderBy(vehicleDepreciationData.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDepreciationDataMapper.selectMany(provider);
    }

    @Override
    public VehicleDepreciationData insert(VehicleDepreciationData vehicleDepreciationData, TokenUserInfo tokenUserInfo) {
        vehicleDepreciationData.setCreateOperId(tokenUserInfo.getUserId());
        vehicleDepreciationData.setCreateOperName(tokenUserInfo.getName());
        vehicleDepreciationDataMapper.insertSelective(vehicleDepreciationData);
        return vehicleDepreciationData;
    }
}
