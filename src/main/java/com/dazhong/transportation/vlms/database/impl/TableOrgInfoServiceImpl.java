package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableOrgInfoService;
import com.dazhong.transportation.vlms.mapper.OrgInfoMapper;
import com.dazhong.transportation.vlms.model.OrgInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.OrgInfoDynamicSqlSupport.orgInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableOrgInfoServiceImpl implements TableOrgInfoService {

    @Autowired
    private OrgInfoMapper orgInfoMapper;

    @Override
    public List<OrgInfo> queryAllOrgInfo() {
        SelectStatementProvider provider = select(orgInfo.allColumns())
                .from(orgInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return orgInfoMapper.selectMany(provider);
    }

    @Override
    public List<OrgInfo> queryOrgListByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        SelectStatementProvider provider = select(orgInfo.allColumns())
                .from(orgInfo)
                .where()
                .and(orgInfo.id, isInWhenPresent(idList))
                .orderBy(orgInfo.position)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return orgInfoMapper.selectMany(provider);
    }


    @Override
    public List<OrgInfo> queryOrgInfoByIsDeleted(Integer isDeleted) {
        SelectStatementProvider provider = select(orgInfo.allColumns())
                .from(orgInfo)
                .where()
                .and(orgInfo.isDeleted, isEqualToWhenPresent(isDeleted))
                .orderBy(orgInfo.position)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return orgInfoMapper.selectMany(provider);
    }

    @Override
    public List<OrgInfo> queryOrgInfoByCheckedState(Integer checkedState) {
        SelectStatementProvider provider = select(orgInfo.allColumns())
                .from(orgInfo)
                .where()
                .and(orgInfo.checkedState, isEqualToWhenPresent(checkedState))
                .orderBy(orgInfo.position)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return orgInfoMapper.selectMany(provider);
    }

    @Override
    public OrgInfo queryOrgInfoById(Long orgId) {
        Optional<OrgInfo> optional = orgInfoMapper.selectByPrimaryKey(orgId);
        return optional.orElse(null);
    }

    @Override
    public OrgInfo queryOrgInfoByName(String companyName) {
        if (StringUtils.isBlank(companyName)) {
            return null;
        }
        SelectStatementProvider provider = select(orgInfo.allColumns())
                .from(orgInfo)
                .where()
                .and(orgInfo.companyName, isEqualToWhenPresent(companyName))
                .and(orgInfo.checkedState, isEqualToWhenPresent(1))
                .orderBy(orgInfo.position)
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<OrgInfo> optional = orgInfoMapper.selectOne(provider);
        return optional.orElse(null);
    }

    @Override
    public int updateOrgInfo(OrgInfo orgInfo) {
        orgInfo.setUpdateTime(new Date());
        return orgInfoMapper.updateByPrimaryKeySelective(orgInfo);
    }

    @Override
    public int batchInsertOrgInfo(List<OrgInfo> orgInfoList) {
        for (OrgInfo info : orgInfoList) {
            orgInfoMapper.insertSelective(info);
        }
        return 1;
    }

    @Override
    public int deleteOrgInfo() {
        return orgInfoMapper.delete(deleteFrom(orgInfo).build().render(RenderingStrategies.MYBATIS3));
    }

    @Override
    public OrgInfo insert(OrgInfo orgInfo) {
        orgInfoMapper.insertSelective(orgInfo);
        return orgInfo;
    }
}
