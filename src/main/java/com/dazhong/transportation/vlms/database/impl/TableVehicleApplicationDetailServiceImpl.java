package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleApplicationDetailService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleApplicationDetailMapper;
import com.dazhong.transportation.vlms.model.VehicleApplicationDetail;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleApplicationDetailDynamicSqlSupport.vehicleApplicationDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleApplicationDetailServiceImpl implements TableVehicleApplicationDetailService {

    @Autowired
    private VehicleApplicationDetailMapper vehicleApplicationDetailMapper;

    @Override
    public void batchInsert(List<VehicleApplicationDetail> vehicleApplicationDetailList, TokenUserInfo tokenUserInfo) {
        for (VehicleApplicationDetail vehicleApplicationDetail : vehicleApplicationDetailList) {
            vehicleApplicationDetail.setCreateTime(new Date());
            vehicleApplicationDetail.setCreateOperId(tokenUserInfo.getUserId());
            vehicleApplicationDetail.setCreateOperName(tokenUserInfo.getName());
        }
        vehicleApplicationDetailMapper.insertMultiple(vehicleApplicationDetailList);
    }

    @Override
    public void deleteByApplicationId(Long applicationId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehicleApplicationDetail)
                .where()
                .and(vehicleApplicationDetail.applicationId, isEqualTo(applicationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        vehicleApplicationDetailMapper.delete(deleteStatementProvider);
    }

    @Override
    public List<VehicleApplicationDetail> getVehicleApplicationDetailListByApplicationId(Long applicationId) {
        SelectStatementProvider selectStatement = select(vehicleApplicationDetail.allColumns())
                .from(vehicleApplicationDetail)
                .where()
                .and(vehicleApplicationDetail.applicationId, isEqualTo(applicationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleApplicationDetailMapper.selectMany(selectStatement);
    }
}
