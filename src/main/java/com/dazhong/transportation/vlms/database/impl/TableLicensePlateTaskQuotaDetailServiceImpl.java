package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableLicensePlateTaskQuotaDetailService;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskQuotaDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.LicensePlateTaskQuotaDetailMapper;
import com.dazhong.transportation.vlms.model.LicensePlateTaskQuotaDetail;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskQuotaDetailDynamicSqlSupport.licensePlateTaskQuotaDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class TableLicensePlateTaskQuotaDetailServiceImpl implements TableLicensePlateTaskQuotaDetailService {

    @Autowired
    private LicensePlateTaskQuotaDetailMapper licensePlateTaskQuotaDetailMapper;

    @Override
    public int insertMultiple(List<LicensePlateTaskQuotaDetailDto> info, TokenUserInfo tokenUserInfo) {
        List<LicensePlateTaskQuotaDetail> rows = new ArrayList<>();
        for (LicensePlateTaskQuotaDetailDto LicensePlateTaskQuotaDetailDTO : info) {
            LicensePlateTaskQuotaDetail row = new LicensePlateTaskQuotaDetail();
            BeanUtils.copyProperties(LicensePlateTaskQuotaDetailDTO, row);
            row.setCreateOperId(tokenUserInfo.getUserId());
            row.setCreateOperName(tokenUserInfo.getName());
            row.setCreateTime(new Date());
            rows.add(row);
        }
        return licensePlateTaskQuotaDetailMapper.insertMultiple(rows);
    }

    @Override
    public List<LicensePlateTaskQuotaDetail> selectByTaskNumber(String taskNumber) {
        SelectStatementProvider provider = select(licensePlateTaskQuotaDetail.allColumns())
                .from(licensePlateTaskQuotaDetail)
                .where()
                .and(licensePlateTaskQuotaDetail.taskNumber, isEqualTo(taskNumber))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateTaskQuotaDetailMapper.selectMany(provider);
    }
}
