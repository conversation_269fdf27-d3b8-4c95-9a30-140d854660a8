package com.dazhong.transportation.vlms.database.impl;

import static com.dazhong.transportation.vlms.mapper.VehicleModelInfoDynamicSqlSupport.vehicleModelInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.where.WhereApplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dazhong.transportation.vlms.database.TableVehicleModelInfoService;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleModelDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleModelListRequest;
import com.dazhong.transportation.vlms.mapper.extend.VehicleModelInfoExtendMapper;
import com.dazhong.transportation.vlms.model.VehicleModelInfo;

@Service
public class TableVehicleModelInfoServiceImpl implements TableVehicleModelInfoService {

    @Autowired
    private VehicleModelInfoExtendMapper vehicleModelInfoMapper;

    @Override
    public VehicleModelInfo insert(VehicleModelInfo vehicleModelInfo) {
        Date now = new Date();
        vehicleModelInfo.setCreateTime(now);
        vehicleModelInfo.setUpdateTime(now);
        vehicleModelInfoMapper.insertSelective(vehicleModelInfo);
        return vehicleModelInfo;
    }

    @Override
    public VehicleModelInfo insert(VehicleModelInfo vehicleModelInfo, TokenUserInfo tokenUserInfo) {
        vehicleModelInfo.setCreateOperId(tokenUserInfo.getUserId());
        vehicleModelInfo.setCreateOperName(tokenUserInfo.getName());
        vehicleModelInfo.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleModelInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(vehicleModelInfo);
    }

    @Override
    public int updateSelectiveById(VehicleModelInfo vehicleModelInfo) {
        vehicleModelInfo.setUpdateTime(new Date());
        return vehicleModelInfoMapper.updateByPrimaryKeySelective(vehicleModelInfo);
    }

    @Override
    public int updateSelectiveById(VehicleModelInfo vehicleModelInfo, TokenUserInfo tokenUserInfo) {
        vehicleModelInfo.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleModelInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.updateSelectiveById(vehicleModelInfo);
    }


    

    @Override
    public VehicleModelInfo selectById(Long id) {
        if(id == null){
            return null;
        }
        return vehicleModelInfoMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public List<VehicleModelInfo> searchVehicleModelList(SearchVehicleModelListRequest request) {
        // 构建动态查询条件
        // 车型名称：支持模糊查询（LIKE），用于按名称搜索车型
        // 车型编号：支持精确查询（等值匹配），确保编号查询的准确性
        SelectStatementProvider selectStatement = select(vehicleModelInfo.allColumns())
                .from(vehicleModelInfo)
                // 车型名称模糊查询：当用户输入车型名称时，支持部分匹配搜索
                .where(vehicleModelInfo.vehicleModelName, isLikeWhenPresent(transFuzzyQueryParam(request.getVehicleModelName())))
                // 车型编号精确查询：当用户输入车型编号时，进行精确匹配，避免误匹配
                .and(vehicleModelInfo.vehicleModelNo, isEqualToWhenPresent(request.getVehicleModelNo()).filter(StringUtils::isNotBlank))
                .orderBy(vehicleModelInfo.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleModelInfoMapper.selectMany(selectStatement);
    }

    @Override
    public List<VehicleModelInfo> getAllVehicleModel() {
        SelectStatementProvider selectStatement = select(vehicleModelInfo.allColumns())
                .from(vehicleModelInfo)
                .orderBy(vehicleModelInfo.vehicleModelName.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleModelInfoMapper.selectMany(selectStatement);
    }

    @Override
    public Map<Long, VehicleModelInfo> getAllVehicleModelMap() {
        List<VehicleModelInfo> vehicleModelInfoList = this.getAllVehicleModel();
        if(vehicleModelInfoList == null || vehicleModelInfoList.isEmpty()){
            return new HashMap<>();
        }
        Map<Long, VehicleModelInfo> result = new HashMap<>();
        for (VehicleModelInfo modelInfo : vehicleModelInfoList) {
            result.put(modelInfo.getId(), modelInfo);
        }
        return result;
    }

    @Override
    public Map<String, VehicleModelInfo> getAllVehicleModelMapByName() {
        List<VehicleModelInfo> vehicleModelInfoList = this.getAllVehicleModel();
        if(vehicleModelInfoList == null || vehicleModelInfoList.isEmpty()){
            return new HashMap<>();
        }
        Map<String, VehicleModelInfo> result = new HashMap<>();
        for (VehicleModelInfo modelInfo : vehicleModelInfoList) {
            result.put(modelInfo.getVehicleModelName(), modelInfo);
        }
        return result;
    }

    @Override
    public boolean isAutohomeDuplicate(Long autohomeId, Long excludeId) {
        boolean result = false;
        if(autohomeId == null){
            return result;
        }

        SelectStatementProvider selectStatement = select(vehicleModelInfo.allColumns())
                .from(vehicleModelInfo)
                .where(vehicleModelInfo.autohomeVehicleModelId, isEqualTo(autohomeId))
                .and(vehicleModelInfo.id, isNotEqualToWhenPresent(excludeId))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        VehicleModelInfo duplicateModelInfo = vehicleModelInfoMapper.selectOne(selectStatement).orElse(null);
        return duplicateModelInfo != null;
    }

    @Override
    public boolean isAutohomeDuplicate(Long autohomeId) {
        return isAutohomeDuplicate(autohomeId, null);
    }

    @Override
    public boolean isModelNameDuplicate(String modelName, Long excludeId) {
        boolean result = false;
        if(StringUtils.isBlank(modelName)){
            return result;
        }
        SelectStatementProvider selectStatement = select(vehicleModelInfo.allColumns())
                .from(vehicleModelInfo)
                .where(vehicleModelInfo.vehicleModelName, isEqualTo(modelName))
                .and(vehicleModelInfo.id, isNotEqualToWhenPresent(excludeId))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        VehicleModelInfo duplicateModelInfo = vehicleModelInfoMapper.selectOne(selectStatement).orElse(null);
        return duplicateModelInfo != null;
    }

    @Override
    public boolean isModelNameDuplicate(String modelName) {
        return isModelNameDuplicate(modelName, null);
    }

    @Override
    public List<SyncDatabaseVehicleModelDto> searchVehicleModelDatabaseSyncInfo(SearchDatabaseTableSyncDataRequest request) {

        SelectStatementProvider selectStatement = select(vehicleModelInfo.allColumns())
                .from(vehicleModelInfo)
                .where(vehicleModelInfo.id, isGreaterThanOrEqualTo(request.getIndex()))
                .and(vehicleModelInfo.id, isInWhenPresent(request.getVehicleModelIdList()))
                .orderBy(vehicleModelInfo.id)
                .limit(request.getPageSize())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleModelInfoMapper.searchVehicleModelDatabaseSyncData(selectStatement);
    }

    @Override
    public int updateCoverById(VehicleModelInfo vehicleModelInfo, TokenUserInfo tokenUserInfo) {
        vehicleModelInfo.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleModelInfo.setUpdateOperName(tokenUserInfo.getName());
        vehicleModelInfo.setUpdateTime(new Date());
        vehicleModelInfo.setIsDeleted(0);
        return vehicleModelInfoMapper.updateByPrimaryKey(vehicleModelInfo);
    }

    @Override
    public List<VehicleModelInfo> selectSyncSaasVehicleModelInfo(Long lastId, Integer pageSize, boolean isFullSync) {
        // 获取当天0点的时间
        LocalDateTime todayStart = LocalDate.now().atStartOfDay();
        Date todayStartDate = Date.from(todayStart.atZone(ZoneId.systemDefault()).toInstant());
        
        // 构建查询条件
        WhereApplier whereApplier = isFullSync 
            ? c -> c.where(vehicleModelInfo.id, isGreaterThan(lastId))
            : c -> c.where(vehicleModelInfo.id, isGreaterThan(lastId))
                .and(vehicleModelInfo.updateTime, isGreaterThanOrEqualTo(todayStartDate));
        
        SelectStatementProvider selectStatement = select(
                vehicleModelInfo.id,
                vehicleModelInfo.vehicleModelNo,
                vehicleModelInfo.vehicleModelName,
                vehicleModelInfo.vehicleBrandName,
                vehicleModelInfo.vehicleSeriesName)
                .from(vehicleModelInfo)
                .applyWhere(whereApplier)
                .orderBy(vehicleModelInfo.id)
                .limit(pageSize)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleModelInfoMapper.selectMany(selectStatement);
    }
}
