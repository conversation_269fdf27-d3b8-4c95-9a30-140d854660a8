package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.*;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleRequest;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchInboundVehicleRequest;
import com.dazhong.transportation.vlms.dto.response.InboundVehicleResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleListResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.model.VehicleInfo;

import java.util.List;

/**
 * 车辆服务
 * <AUTHOR>
 * @date 2025-01-10 13:57
 */
public interface TableVehicleService extends BaseTableService<VehicleInfo, Long> {

    /**
     * 查询在库车辆列表
     * @return
     */
    List<InboundVehicleResponse> searchInboundVehicleList(SearchInboundVehicleRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询车辆列表
     * @return
     */
    List<VehicleListResponse> searchAssetVehicleList(SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 根据车架号查询车辆列表
     * @param vinList
     * @return
     */
    List<VehicleListResponse> queryAssetVehicleList(List<String> vinList);

    /**
     * 根据车架号查询车辆列表
     * @param vinList
     * @return
     */
    List<VehicleListResponse> queryVehicleList(List<String> vinList);

    /**
     * 查询车辆转固信息
     * @param vinList
     * @return
     */
    List<VehicleTransferFixedResponse> searchVehicleTransferFixed(List<String> vinList);

    /**
     * 根据vin查询车辆
     * @param vin
     * @return
     */
    VehicleInfo queryVehicleByVin(String vin);

    /**
     * 根据车牌号查询车辆
     * @param licensePlate
     * @return
     */
    VehicleInfo queryVehicleByLicensePlate(String licensePlate);

    /**
     * 根据vin模糊查询车辆
     * @param vin
     * @param licensePlate
     * @return
     */
    List<VehicleInfo> queryVehicleListByVin(String vin,String licensePlate);

    /**
     * 根据vin查询车辆
     * @param vinList
     * @return
     */
    List<VehicleInfo> queryVehicleByVinList(List<String> vinList);

    /**
     * 根据车牌号查询车辆
     * @param licensePlateList
     * @return
     */
    List<VehicleInfo> queryVehicleByLicensePlateList(List<String> licensePlateList);

    /**
     * 根据资产编号查询车辆
     * @param vehicleAssetIdList
     * @return
     */
    List<VehicleInfo> queryVehicleByAssetIdList(List<String> vehicleAssetIdList);

    /**
     * 保存车辆
     * @param vehicleInfo
     * @return
     */
    int saveVehicle(VehicleInfo vehicleInfo, TokenUserInfo tokenUserInfo);

    /**
     * 更新车辆
     * @param vehicleInfo
     * @return
     */
    int updateVehicle(VehicleInfo vehicleInfo, TokenUserInfo tokenUserInfo);

    /**
     * 更新车牌相关信息
     *
     * @param vehicleInfo
     * @return
     */
    int updateLicensePlateByVin(VehicleInfo vehicleInfo, TokenUserInfo tokenUserInfo);

    /**
     * 更新车辆所属车队、运营状态
     *
     * @param vinList
     * @param belongingTeam
     * @return
     */
    int batchUpdateVehicleBelongingTeam(List<String> vinList, String belongingTeam);

    /**
     * 更新车辆所属车队、运营状态
     *
     * @param vinList
     * @param operatingStatus
     * @return
     */
    int batchUpdateVehicleOperatingStatus(List<String> vinList, Integer operatingStatus);

    /**
     * 更新车辆
     * @param vehicleInfo
     * @return
     */
    int updateVehicleByVinSelective(VehicleInfo vehicleInfo);

    /**
     * 同步车辆信息
     * @param request 查询条件
     * @return 同步数据列表
     */
    List<SyncAssetVehicleInfoDto> searchAssetVehicleSyncInfo(SearchAssetVehicleSyncDataRequest request);


    /**
     * 同步车辆信息
     * @param request
     * @return 同步数据列表
     */
    List<SyncDatabaseVehicleDto> searchVehicleDatabaseSyncInfo(SearchDatabaseTableSyncDataRequest request);


    /**
     * 同步车辆资产信息
     * @param request
     * @return 同步数据列表
     */
    List<SyncDatabaseVehicleAssetDto> searchVehicleAssetDatabaseSyncInfo(SearchDatabaseTableSyncDataRequest request);

    /**
     * 导出车辆信息
     * @param index 索引标识位（用于分段导出）
     * @param request 查询条件
     * @param tokenUserInfo 用户信息
     * @return 导出数据列表
     */
    List<ExportAssetVehicleInfoDto> exportAssetVehicleInfo(Long index, SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询所有车辆信息
     * @return 车辆信息列表
     */
    List<VehicleInfo> selectAllUsageOrganizationId();

    /**
     * 分页查询需要同步到SAAS系统的车辆信息
     * @param lastId 上次查询的最后ID
     * @param pageSize 每页大小
     * @param isFullSync 是否全量同步（true:全量同步，false:增量同步）
     * @return 车辆信息列表
     */
    List<VehicleInfo> selectSyncSaasVehicleInfo(Long lastId, Integer pageSize, boolean isFullSync);

    /**
     * 更新车辆运营状态（Transactional(propagation = Propagation.REQUIRES_NEW)）
     * 
     * @param vin
     * @param operatingStatus
     * @return
     */
    public int updateVehicleOperatingStatusInNewTransaction(String vin, Integer operatingStatus);
}
