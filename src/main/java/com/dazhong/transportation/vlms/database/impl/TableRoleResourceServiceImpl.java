package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableRoleResourceService;
import com.dazhong.transportation.vlms.mapper.RoleResourceInfoMapper;
import com.dazhong.transportation.vlms.model.RoleResourceInfo;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.RoleResourceInfoDynamicSqlSupport.roleResourceInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.deleteFrom;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableRoleResourceServiceImpl implements TableRoleResourceService {

    @Autowired
    private RoleResourceInfoMapper roleResourceInfoMapper;


    @Override
    public int batchInsert(List<RoleResourceInfo> list) {
        return roleResourceInfoMapper.insertMultiple(list);
    }

    @Override
    public int deleteByRoleId(Long roleId) {
        DeleteStatementProvider render = deleteFrom(roleResourceInfo).where()
                .and(roleResourceInfo.roleId, isEqualTo(roleId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return roleResourceInfoMapper.delete(render);
    }
}
