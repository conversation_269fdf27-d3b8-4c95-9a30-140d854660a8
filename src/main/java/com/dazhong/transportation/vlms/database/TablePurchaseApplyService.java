package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchPurchaseApplyRequest;
import com.dazhong.transportation.vlms.model.VehiclePurchaseApply;

import java.util.List;

/**
 * 车辆采购表服务
 * <AUTHOR>
 * @date 2025-01-06 10:37
 */
public interface TablePurchaseApplyService extends BaseTableService<VehiclePurchaseApply, Long> {

    /**
     * 查询采购列表
     * @return
     */
    List<VehiclePurchaseApply> searchPurchaseList(SearchPurchaseApplyRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 查询采购详情
     * @param applyNo
     * @return
     */
    VehiclePurchaseApply queryPurchaseApply(String applyNo);

    /**
     * 查询采购详情
     * @param approvalNumber
     * @return
     */
    VehiclePurchaseApply queryPurchaseApplyApprovalNumber(String approvalNumber);

    /**
     * 新增采购信息
     * @param purchaseApply
     * @param tokenUserInfo
     * @return
     */
    int savePurchaseApply(VehiclePurchaseApply purchaseApply, TokenUserInfo tokenUserInfo);

    /**
     * 更新采购信息
     * @param purchaseApply
     * @param tokenUserInfo
     * @return
     */
    int updatePurchaseApply(VehiclePurchaseApply purchaseApply,TokenUserInfo tokenUserInfo);
}
