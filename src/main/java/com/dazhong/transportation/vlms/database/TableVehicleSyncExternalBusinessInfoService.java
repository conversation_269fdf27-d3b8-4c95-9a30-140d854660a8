package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.dto.VehicleSyncExternalBusinessInfoDto;
import com.dazhong.transportation.vlms.model.VehicleSyncExternalBusinessInfo;

import java.util.List;

public interface TableVehicleSyncExternalBusinessInfoService {

    /**
     * 批量插入外部车辆业务数据
     * @param request
     * @return
     */
    int batchInsert(List<VehicleSyncExternalBusinessInfoDto> request);


    /**
     * 根据车牌号和数据类型查
     * @param licensePlate 车牌号
     * @param businessType 数据类型
     * @return
     */
    List<VehicleSyncExternalBusinessInfo> selectByLicensePlateAndType(String licensePlate,String businessType);

}
