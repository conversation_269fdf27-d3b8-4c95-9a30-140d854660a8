package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleDisposalDetailService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleDisposalDetailMapper;
import com.dazhong.transportation.vlms.model.VehicleDisposalDetail;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleDisposalDetailDynamicSqlSupport.vehicleDisposalDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.delete.DeleteDSL.deleteFrom;

@Service
public class TableVehicleDisposalDetailServiceImpl implements TableVehicleDisposalDetailService {

    @Autowired
    private VehicleDisposalDetailMapper vehicleDisposalDetailMapper;

    @Override
    public void batchInsert(List<VehicleDisposalDetail> vehicleDisposalDetailList, TokenUserInfo tokenUserInfo) {
        for (VehicleDisposalDetail vehicleDisposalDetail : vehicleDisposalDetailList) {
            vehicleDisposalDetail.setCreateTime(new Date());
            vehicleDisposalDetail.setCreateOperId(tokenUserInfo.getUserId());
            vehicleDisposalDetail.setCreateOperName(tokenUserInfo.getName());
        }
        vehicleDisposalDetailMapper.insertMultiple(vehicleDisposalDetailList);
    }

    @Override
    public void deleteByDisposalId(Long disposalId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehicleDisposalDetail)
                .where()
                .and(vehicleDisposalDetail.disposalId, isEqualTo(disposalId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        vehicleDisposalDetailMapper.delete(deleteStatementProvider);
    }

    @Override
    public List<VehicleDisposalDetail> getVehicleDisposalDetailListByDisposalId(Long disposalId) {
        SelectStatementProvider selectStatement = select(vehicleDisposalDetail.allColumns())
                .from(vehicleDisposalDetail)
                .where()
                .and(vehicleDisposalDetail.disposalId, isEqualTo(disposalId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDisposalDetailMapper.selectMany(selectStatement);
    }

    @Override
    public void updateDingTalkInfo(VehicleDisposalDetail disposalDetail) {
        UpdateStatementProvider updateStatementProvider = SqlBuilder.update(vehicleDisposalDetail)
                .set(vehicleDisposalDetail.originalValue).equalToWhenPresent(disposalDetail.getOriginalValue())
                .set(vehicleDisposalDetail.accumulatedDepreciation).equalToWhenPresent(disposalDetail.getAccumulatedDepreciation())
                .set(vehicleDisposalDetail.netValue).equalToWhenPresent(disposalDetail.getNetValue())
                .set(vehicleDisposalDetail.financialEvaluation).equalToWhenPresent(disposalDetail.getFinancialEvaluation())
                .set(vehicleDisposalDetail.actualSellingPrice).equalToWhenPresent(disposalDetail.getActualSellingPrice())
                .set(vehicleDisposalDetail.actualNetPrice).equalToWhenPresent(disposalDetail.getActualNetPrice())
                .set(vehicleDisposalDetail.netPriceDiff).equalToWhenPresent(disposalDetail.getNetPriceDiff())
                .set(vehicleDisposalDetail.saleGainLoss).equalToWhenPresent(disposalDetail.getSaleGainLoss())
                .set(vehicleDisposalDetail.actualSoldQuantity).equalToWhenPresent(disposalDetail.getActualSoldQuantity())
                .set(vehicleDisposalDetail.quantityDiff).equalToWhenPresent(disposalDetail.getQuantityDiff())
                .set(vehicleDisposalDetail.actualSaleDesc).equalToWhenPresent(disposalDetail.getActualSaleDesc())
                .set(vehicleDisposalDetail.fileUrl).equalToWhenPresent(disposalDetail.getFileUrl())
                .set(vehicleDisposalDetail.actualSaleFileUrl).equalToWhenPresent(disposalDetail.getActualSaleFileUrl())
                .set(vehicleDisposalDetail.estimatedScrapLossAmount).equalToWhenPresent(disposalDetail.getEstimatedScrapLossAmount())
                .set(vehicleDisposalDetail.isInsuranceAuction).equalToWhenPresent(disposalDetail.getIsInsuranceAuction())
                .set(vehicleDisposalDetail.actualAuctionAmount).equalToWhenPresent(disposalDetail.getActualAuctionAmount())
                .set(vehicleDisposalDetail.actualScrapProfitLoss).equalToWhenPresent(disposalDetail.getActualScrapProfitLoss())
                .set(vehicleDisposalDetail.realRetirementDate).equalToWhenPresent(disposalDetail::getRealRetirementDate)
                .where(vehicleDisposalDetail.vin, isEqualTo(disposalDetail.getVin()))
                .and(vehicleDisposalDetail.disposalId, isEqualTo(disposalDetail.getDisposalId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        vehicleDisposalDetailMapper.update(updateStatementProvider);
    }

    @Override
    public int updateSelectiveById(VehicleDisposalDetail disposalDetail, TokenUserInfo tokenUserInfo) {
        disposalDetail.setUpdateTime(new Date());
        disposalDetail.setUpdateOperId(tokenUserInfo.getUserId());
        disposalDetail.setUpdateOperName(tokenUserInfo.getName());
        return vehicleDisposalDetailMapper.updateByPrimaryKeySelective(disposalDetail);
    }
}
