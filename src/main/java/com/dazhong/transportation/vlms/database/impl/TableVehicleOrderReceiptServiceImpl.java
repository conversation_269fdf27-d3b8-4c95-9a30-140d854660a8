package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleOrderReceiptService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehiclePurchaseReceiptDto;
import com.dazhong.transportation.vlms.dto.VehicleReceivingDto;
import com.dazhong.transportation.vlms.mapper.VehicleOrderReceiptExtendMapper;
import com.dazhong.transportation.vlms.mapper.VehicleOrderReceiptMapper;
import com.dazhong.transportation.vlms.model.VehicleOrderReceipt;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleInfoDynamicSqlSupport.vehicleInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleModelInfoDynamicSqlSupport.vehicleModelInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleOrderReceiptDynamicSqlSupport.vehicleOrderReceipt;
import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyDynamicSqlSupport.vehiclePurchaseApply;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2025-01-06 10:38
 */
@Service
public class TableVehicleOrderReceiptServiceImpl implements TableVehicleOrderReceiptService {

    @Autowired
    private VehicleOrderReceiptMapper vehicleOrderReceiptMapper;

    @Autowired
    private VehicleOrderReceiptExtendMapper vehicleOrderReceiptExtendMapper;


    @Override
    public List<VehicleOrderReceipt> queryListByVin(List<String> vinList) {
        SelectStatementProvider selectStatement = select(vehicleOrderReceipt.allColumns())
                .from(vehicleOrderReceipt)
                .where()
                .and(vehicleOrderReceipt.vin, isIn(vinList))
                .and(vehicleOrderReceipt.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleOrderReceiptMapper.selectMany(selectStatement);
    }

    @Override
    public int saveVehicleOrderReceipt(VehicleOrderReceipt vehicleOrderReceipt, TokenUserInfo tokenUserInfo) {
        vehicleOrderReceipt.setCreateOperId(tokenUserInfo.getUserId());
        vehicleOrderReceipt.setCreateOperName(tokenUserInfo.getName());
        return vehicleOrderReceiptMapper.insertSelective(vehicleOrderReceipt);
    }



    @Override
    public List<VehicleReceivingDto> qeuryVehicleOrderReceiptList(Long purchaseApplyId) {
        SelectStatementProvider selectStatement = select(
                vehicleOrderReceipt.vin,
                vehicleOrderReceipt.orderDate,
                vehicleOrderReceipt.receiptDate,
                vehicleOrderReceipt.supplierId,
                vehicleOrderReceipt.isRepurchase,
                vehicleOrderReceipt.repurchaseDate,
                vehicleOrderReceipt.repurchaseRequirements,
                vehicleOrderReceipt.createOperName,
                vehicleOrderReceipt.createTime,
                vehicleOrderReceipt.vehicleColorId,
                vehicleOrderReceipt.interiorColor,
                vehicleOrderReceipt.engineNo,
                vehicleOrderReceipt.applyDetailsNo,
                vehicleOrderReceipt.ownerId,
                vehicleInfo.vehicleModelId,
                vehicleInfo.vehicleAssetId,
                vehicleInfo.assetCompanyId,
                vehicleModelInfo.vehicleModelName,
                vehicleModelInfo.vehicleModelNo,
                vehiclePurchaseApply.approvalNumber)
                .from(vehicleOrderReceipt)
                .leftJoin(vehiclePurchaseApply).on(vehicleOrderReceipt.purchaseApplyId, equalTo(vehiclePurchaseApply.id))
                .leftJoin(vehicleInfo).on(vehicleInfo.vin, equalTo(vehicleOrderReceipt.vin))
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .where()
                .and(vehicleOrderReceipt.purchaseApplyId, isEqualTo(purchaseApplyId))
                .and(vehicleOrderReceipt.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleOrderReceiptExtendMapper.qeuryVehicleOrderReceiptList(selectStatement);
    }

    @Override
    public VehiclePurchaseReceiptDto queryVehiclePurchaseReceipt(String vin) {
        SelectStatementProvider selectStatement = select(
                vehicleOrderReceipt.vin,
                vehicleOrderReceipt.orderDate,
                vehicleOrderReceipt.receiptDate,
                vehicleOrderReceipt.supplierId,
                vehicleOrderReceipt.isRepurchase,
                vehicleOrderReceipt.repurchaseDate,
                vehicleOrderReceipt.repurchaseRequirements,
                vehicleOrderReceipt.vehicleColorId,
                vehicleOrderReceipt.interiorColor,
                vehicleOrderReceipt.engineNo,
                vehicleOrderReceipt.businessLine,
                vehicleOrderReceipt.ownerId,
                vehicleInfo.vehicleModelId,
                vehicleInfo.vehicleAssetId,
                vehicleInfo.usageAgeLimit,
                vehicleInfo.depreciationAgeLimit,
                vehicleInfo.purchasePrice,
                vehicleInfo.purchaseTax,
                vehicleInfo.licensePlatePrice,
                vehicleInfo.licensePlateOtherPrice,
                vehicleInfo.upholsterPrice,
                vehicleInfo.totalPrice,
                vehicleModelInfo.vehicleModelName,
                vehicleModelInfo.vehicleModelNo,
                vehiclePurchaseApply.approvalNumber)
                .from(vehicleOrderReceipt)
                .leftJoin(vehiclePurchaseApply).on(vehicleOrderReceipt.purchaseApplyId, equalTo(vehiclePurchaseApply.id))
                .leftJoin(vehicleInfo).on(vehicleInfo.vin, equalTo(vehicleOrderReceipt.vin))
                .leftJoin(vehicleModelInfo).on(vehicleInfo.vehicleModelId, equalTo(vehicleModelInfo.id))
                .where()
                .and(vehicleOrderReceipt.vin, isEqualTo(vin))
                .and(vehicleOrderReceipt.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleOrderReceiptExtendMapper.queryVehiclePurchaseReceipt(selectStatement);
    }

    @Override
    public VehicleOrderReceipt queryVehicleOrderReceipt(String vin) {
        SelectStatementProvider selectStatement = select(vehicleOrderReceipt.allColumns())
                .from(vehicleOrderReceipt)
                .where()
                .and(vehicleOrderReceipt.vin, isEqualTo(vin))
                .and(vehicleOrderReceipt.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleOrderReceipt> optional = vehicleOrderReceiptMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int updateSelectiveById(VehicleOrderReceipt vehicleOrderReceipt, TokenUserInfo tokenUserInfo) {
        vehicleOrderReceipt.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleOrderReceipt.setUpdateOperName(tokenUserInfo.getName());
        return vehicleOrderReceiptMapper.updateByPrimaryKeySelective(vehicleOrderReceipt);
    }
}
