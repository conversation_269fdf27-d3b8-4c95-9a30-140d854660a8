package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaTransactionRecordRequest;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.model.LicensePlateQuotaTransactionRecord;

import java.util.Date;

import java.util.List;

/**
 * 额度单流水表服务
 *
 * <AUTHOR>
 * @date 2025/01/06
 */
public interface TableLicensePlateQuotaTransactionRecordService extends BaseTableService<LicensePlateQuotaTransactionRecord, Long> {

    /**
     * 根据id更新额度单流水表
     *
     * @param licensePlateQuotaTransactionRecord 更新数据
     * @param tokenUserInfo                      操作人信息
     * @return
     */
    int updateSelectiveById(LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord, TokenUserInfo tokenUserInfo);

    /**
     * 根据id更新额度单流水表
     *
     * @param licensePlateQuotaTransactionRecord 更新数据
     * @param tokenUserInfo                      操作人信息
     * @return
     */
    int updateTaskNumber(LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord, TokenUserInfo tokenUserInfo);

    /**
     * 查询额度单流水表
     *
     * @param searchLicensePlateQuotaTransactionRecordRequest 查询额度单流水表请求
     * @return {@link List}<{@link LicensePlateQuotaTransactionRecord}>
     */
    List<LicensePlateQuotaTransactionRecord> queryList(SearchLicensePlateQuotaTransactionRecordRequest searchLicensePlateQuotaTransactionRecordRequest);

    /**
     * 查询额度单流水表导出数据
     *
     * @param index 索引标识位（用于分段导出）
     * @param searchLicensePlateQuotaTransactionRecordRequest 查询额度单流水表请求
     * @return {@link List}<{@link LicensePlateQuotaTransactionRecord}>
     */
    List<LicensePlateQuotaTransactionRecord> exportList(Long index,SearchLicensePlateQuotaTransactionRecordRequest request);

    /**
     * 获取额度编号下拉列表
     *
     * @param quotaNumber
     * @return
     */
    ComboResponse<String, String> queryQuotaNumberCombo(String quotaNumber);

    /**
     * 根据额度单号和打印日期查询额度单流水记录
     *
     * @param quotaNumber    额度单号
     * @param quotaPrintDate 额度单打印日期
     * @return 额度单流水记录列表
     */
    LicensePlateQuotaTransactionRecord selectByQuotaNumberAndQuotaPrintDate(String quotaNumber, Date quotaPrintDate);
}
