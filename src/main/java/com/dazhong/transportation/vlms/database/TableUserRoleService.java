package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.UserRoleInfo;

import java.util.List;

/**
 * 用户角色表服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableUserRoleService extends BaseTableService<UserRoleInfo, Long> {

    /**
     * 批量新增
     * @param list
     * @return
     */
    void batchInsert(List<UserRoleInfo> list);

    /**
     * 查询用户角色
     * @param userId
     * @return
     */
    List<UserRoleInfo> queryUserRoleByUserId(Long userId);

    /**
     * 查询角色用户
     * @param roleId
     * @return
     */
    List<UserRoleInfo> queryUserRoleByRoleId(Long roleId);

    /**
     * 删除用户角色关联关系
     * @param userId
     * @return
     */
    int deleteByUserId(Long userId);

    /**
     * 删除角色用户关联关系
     * @param roleId
     * @return
     */
    int deleteByRoleId(Long roleId);
}
