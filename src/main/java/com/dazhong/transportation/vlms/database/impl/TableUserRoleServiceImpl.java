package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dazhong.transportation.vlms.database.TableUserRoleService;
import com.dazhong.transportation.vlms.mapper.UserRoleInfoMapper;
import com.dazhong.transportation.vlms.model.UserRoleInfo;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.RoleInfoDynamicSqlSupport.roleInfo;
import static com.dazhong.transportation.vlms.mapper.UserRoleInfoDynamicSqlSupport.userRoleInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableUserRoleServiceImpl implements TableUserRoleService {

    @Autowired
    private UserRoleInfoMapper userRoleInfoMapper;


    @Override
    public void batchInsert(List<UserRoleInfo> list) {
        if (CollectionUtil.isEmpty(list)){
            return;
        }
        userRoleInfoMapper.insertMultiple(list);
    }

    @Override
    public List<UserRoleInfo> queryUserRoleByUserId(Long userId) {
        SelectStatementProvider provider = select(userRoleInfo.allColumns())
                .from(userRoleInfo)
                .join(roleInfo).on(userRoleInfo.roleId,equalTo(roleInfo.id))
                .where()
                .and(userRoleInfo.userId, isEqualTo(userId))
                .and(roleInfo.roleStatus, isEqualTo(1))
                .and(userRoleInfo.isDeleted, isEqualTo(0))
                .orderBy(userRoleInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userRoleInfoMapper.selectMany(provider);
    }

    @Override
    public List<UserRoleInfo> queryUserRoleByRoleId(Long roleId) {
        SelectStatementProvider provider = select(userRoleInfo.allColumns())
                .from(userRoleInfo)
                .where()
                .and(userRoleInfo.roleId, isEqualTo(roleId))
                .and(userRoleInfo.isDeleted, isEqualTo(0))
                .orderBy(userRoleInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userRoleInfoMapper.selectMany(provider);
    }

    @Override
    public int deleteByUserId(Long userId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(userRoleInfo).where()
                .and(userRoleInfo.userId, isEqualTo(userId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userRoleInfoMapper.delete(deleteStatementProvider);
    }

    @Override
    public int deleteByRoleId(Long roleId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(userRoleInfo).where()
                .and(userRoleInfo.roleId, isEqualTo(roleId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userRoleInfoMapper.delete(deleteStatementProvider);
    }
}
