package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableTransferFixedDetailsService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.mapper.VehicleTransferFixedDetailsMapper;
import com.dazhong.transportation.vlms.model.VehicleTransferFixedDetails;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleTransferFixedApplyDynamicSqlSupport.vehicleTransferFixedApply;
import static com.dazhong.transportation.vlms.mapper.VehicleTransferFixedDetailsDynamicSqlSupport.vehicleTransferFixedDetails;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableTransferFixedDetailsServiceImpl implements TableTransferFixedDetailsService {

    @Autowired
    private VehicleTransferFixedDetailsMapper vehicleTransferFixedDetailsMapper;


    @Override
    public List<VehicleTransferFixedDetails> queryTransferFixedDetailsByApplyId(Long applyId) {
        SelectStatementProvider selectStatement = select(vehicleTransferFixedDetails.allColumns())
                .from(vehicleTransferFixedDetails)
                .where()
                .and(vehicleTransferFixedDetails.transferFixedApplyId, isEqualTo(applyId))
                .and(vehicleTransferFixedDetails.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleTransferFixedDetailsMapper.selectMany(selectStatement);
    }

    @Override
    public List<VehicleTransferFixedDetails> queryVehicleApprovalRecordList() {
        SelectStatementProvider selectStatement = select(vehicleTransferFixedDetails.allColumns())
                .from(vehicleTransferFixedDetails)
                .where()
                .and(vehicleTransferFixedDetails.approvalStatus, isEqualTo(1))
                .and(vehicleTransferFixedDetails.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleTransferFixedDetailsMapper.selectMany(selectStatement);
    }

    @Override
    public int deleteTransferFixedDetailsByApplyId(Long applyId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehicleTransferFixedDetails)
                .where()
                .and(vehicleTransferFixedDetails.transferFixedApplyId, isEqualTo(applyId))
                .and(vehicleTransferFixedDetails.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleTransferFixedDetailsMapper.delete(deleteStatementProvider);
    }

    @Override
    public int updateApprovalStatus(Long applyId, Integer approvalStatus) {
        UpdateStatementProvider updateStatementProvider = SqlBuilder.update(vehicleTransferFixedDetails)
                .set(vehicleTransferFixedDetails.approvalStatus).equalTo(approvalStatus)
                .where(vehicleTransferFixedDetails.transferFixedApplyId, isEqualTo(applyId))
                .and(vehicleTransferFixedDetails.isDeleted,isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleTransferFixedDetailsMapper.update(updateStatementProvider);
    }

    @Override
    public List<VehicleTransferFixedResponse> queryTransferFixedDetailsByVin(List<String> vinList) {
        SelectStatementProvider selectStatement = select(
                vehicleTransferFixedDetails.vin,
                vehicleTransferFixedApply.applyNo,
                vehicleTransferFixedApply.applyStatus,
                vehicleTransferFixedApply.createOperName,
                vehicleTransferFixedApply.createTime)
                .from(vehicleTransferFixedDetails)
                .join(vehicleTransferFixedApply).on(vehicleTransferFixedDetails.transferFixedApplyId, equalTo(vehicleTransferFixedApply.id))
                .where()
                .and(vehicleTransferFixedDetails.vin, isIn(vinList))
                .and(vehicleTransferFixedDetails.approvalStatus, isEqualTo(1))
                .and(vehicleTransferFixedDetails.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleTransferFixedDetailsMapper.queryTransferFixedDetailsByVin(selectStatement);
    }

    @Override
    public VehicleTransferFixedDetails insert(VehicleTransferFixedDetails vehicleTransferFixedDetails, TokenUserInfo tokenUserInfo) {
        vehicleTransferFixedDetails.setCreateOperId(tokenUserInfo.getUserId());
        vehicleTransferFixedDetails.setCreateOperName(tokenUserInfo.getName());
        vehicleTransferFixedDetailsMapper.insertSelective(vehicleTransferFixedDetails);
        return vehicleTransferFixedDetails;
    }

}
