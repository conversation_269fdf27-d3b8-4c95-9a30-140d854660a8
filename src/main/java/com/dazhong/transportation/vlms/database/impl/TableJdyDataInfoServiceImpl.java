package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableJdyDataInfoService;
import com.dazhong.transportation.vlms.mapper.JdyDataInfoMapper;
import com.dazhong.transportation.vlms.model.JdyDataInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.JdyDataInfoDynamicSqlSupport.jdyDataInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class TableJdyDataInfoServiceImpl implements TableJdyDataInfoService {

    @Autowired
    private JdyDataInfoMapper jdyDataInfoMapper;

    @Override
    public JdyDataInfo insert(JdyDataInfo jdyDataInfo) {
        jdyDataInfoMapper.insertSelective(jdyDataInfo);
        return jdyDataInfo;
    }


    @Override
    public JdyDataInfo queryLastOne() {
        SelectStatementProvider selectStatement = select(jdyDataInfo.allColumns())
                .from(jdyDataInfo)
                .where()
                .orderBy(jdyDataInfo.id.descending())
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<JdyDataInfo> optional = jdyDataInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }
}
