package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleDecorationService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleDecorationMapper;
import com.dazhong.transportation.vlms.model.VehicleDecoration;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleDecorationDynamicSqlSupport.vehicleDecoration;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleDecorationServiceImpl implements TableVehicleDecorationService {

    @Autowired
    private VehicleDecorationMapper vehicleDecorationMapper;

    @Override
    public List<VehicleDecoration> queryDecorationList(List<String> vinList) {
        SelectStatementProvider provider = select(vehicleDecoration.allColumns())
                .from(vehicleDecoration)
                .where()
                .and(vehicleDecoration.vin, isIn(vinList))
                .and(vehicleDecoration.isDeleted, isEqualTo(0))
                .orderBy(vehicleDecoration.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDecorationMapper.selectMany(provider);
    }

    @Override
    public void deleteByVin(String vin) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehicleDecoration)
                .where()
                .and(vehicleDecoration.vin, isEqualTo(vin))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        vehicleDecorationMapper.delete(deleteStatementProvider);
    }

    @Override
    public VehicleDecoration insert(VehicleDecoration vehicleDecoration, TokenUserInfo tokenUserInfo) {
        vehicleDecoration.setCreateOperId(tokenUserInfo.getUserId());
        vehicleDecoration.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleDecorationMapper.insertSelective(vehicleDecoration);
        return vehicleDecoration;
    }
}
