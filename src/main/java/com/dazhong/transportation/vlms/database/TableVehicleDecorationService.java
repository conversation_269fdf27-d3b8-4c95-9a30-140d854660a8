package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehicleDecoration;

import java.util.List;

/**
 * 车辆装潢表服务
 */
public interface TableVehicleDecorationService extends BaseTableService<VehicleDecoration, Long> {

    /**
     * 查询车辆装潢信息
     * @return
     */
    List<VehicleDecoration> queryDecorationList(List<String> vinList);

    /**
     * 根据车架号删除
     * @param vin
     */
    void deleteByVin(String vin);
}
