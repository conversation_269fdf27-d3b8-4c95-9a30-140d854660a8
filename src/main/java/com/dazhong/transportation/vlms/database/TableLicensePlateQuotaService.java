package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaRequest;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaResponse;
import com.dazhong.transportation.vlms.model.LicensePlateQuota;

import java.util.List;


/**
 * 车牌额度表服务接口
 *
 * <AUTHOR>
 * @date 2024/12/31
 */
public interface TableLicensePlateQuotaService extends BaseTableService<LicensePlateQuota, Long> {

    /**
     * 查询额度信息
     *
     * @param searchLicensePlateQuotaRequest 查询额度信息请求
     * @return 返回额度信息
     */
    List<LicensePlateQuota> queryList(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest);

    /**
     * 查询额度信息
     *
     * @param searchLicensePlateQuotaRequest 查询额度信息请求
     * @return 返回额度信息
     */
    List<LicensePlateQuota> queryListGroupByQuotaType(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest);

    /**
     * 更新额度信息
     *
     * @param licensePlateQuota 车牌额度
     * @return 返回影响行数
     */
    int updateByPrimaryKeySelective(LicensePlateQuota licensePlateQuota);

    /**
     * 保存额度信息
     *
     * @param licensePlateQuota 车牌额度
     * @return 返回影响行数
     */
    int saveLicensePlateQuota(LicensePlateQuota licensePlateQuota);

    /**
     * 根据额度类型和资产归属公司查询额度信息
     *
     * @param quotaType      额度类型
     * @param assetCompanyId 资产归属公司id
     * @return 返回额度信息
     */
    LicensePlateQuota selectByQuotaTypeAndAssetCompanyCode(Integer quotaType, Integer assetCompanyId);

    /**
     * 查询额度信息
     *
     * @param searchLicensePlateQuotaRequest 查询额度信息请求
     * @return 返回额度信息
     */
    LicensePlateQuotaResponse selectLicensePlateQuotaResponse(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest);

    /**
     * 根据额度类型和资产归属公司查询额度信息
     *
     * @param quotaType 额度类型
     * @return 返回额度信息
     */
    List<LicensePlateQuota> selectByQuotaType(Integer quotaType);

}
