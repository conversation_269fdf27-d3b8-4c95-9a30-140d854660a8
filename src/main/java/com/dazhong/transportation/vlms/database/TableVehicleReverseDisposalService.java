package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalDetailListDto;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.model.VehicleReverseDisposal;

import java.util.List;

public interface TableVehicleReverseDisposalService extends BaseTableService<VehicleReverseDisposal, Long> {

    /**
     * 新增车辆逆处置记录
     *
     * @param vehicleReverseDisposal 插入对象
     * @param tokenUserInfo          操作人信息
     * @return
     */
    VehicleReverseDisposal insert(VehicleReverseDisposal vehicleReverseDisposal, TokenUserInfo tokenUserInfo);

    /**
     * 查询车辆逆处置列表
     *
     * @param searchVehicleDisposalListRequest 查询车辆处置列表入参
     * @return 返回车辆处置列表
     */
    List<VehicleReverseDisposalListDto> queryVehicleReverseDisposalList(SearchVehicleDisposalListRequest searchVehicleDisposalListRequest, TokenUserInfo tokenUserInfo);

    /**
     * 根据钉钉流水号查询车辆逆处置
     *
     * @param dingTalkNo 钉钉审批单号
     * @return
     */
    VehicleReverseDisposal selectByDingTalkNo(String dingTalkNo);

    /**
     * 查询车辆处置列表
     *
     * @param vin                车架号
     * @param disposalDocumentNo 处置申请单号
     * @return 返回车辆处置列表
     */
    VehicleReverseDisposalDetailListDto queryVehicleReverseDisposalDetail(String vin, String disposalDocumentNo);

}
