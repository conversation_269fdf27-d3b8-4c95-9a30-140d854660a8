package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehiclePurchaseReceiptDto;
import com.dazhong.transportation.vlms.dto.VehicleReceivingDto;
import com.dazhong.transportation.vlms.model.VehicleOrderReceipt;

import java.util.List;

/**
 * 车辆订单收货表表服务
 * <AUTHOR>
 * @date 2025-01-07 11:27
 */
public interface TableVehicleOrderReceiptService extends BaseTableService<VehicleOrderReceipt, Long> {

    /**
     * 根据vin查询
     * @param vinList
     * @return
     */
    List<VehicleOrderReceipt> queryListByVin(List<String> vinList);

    /**
     * 保存车辆收货信息
     * @param vehicleOrderReceipt
     * @return
     */
    int saveVehicleOrderReceipt(VehicleOrderReceipt vehicleOrderReceipt, TokenUserInfo tokenUserInfo);

    /**
     * 查询采购收货车辆列表
     * @return
     */
    List<VehicleReceivingDto> qeuryVehicleOrderReceiptList(Long purchaseApplyId);

    /**
     * 查询车辆采购收货信息
     * 根据vin查询
     * @param vin
     * @return
     */
    VehiclePurchaseReceiptDto queryVehiclePurchaseReceipt(String vin);

    /**
     * 查询车辆采购收货信息
     * 根据vin查询
     * @param vin
     * @return
     */
    VehicleOrderReceipt queryVehicleOrderReceipt(String vin);
}
