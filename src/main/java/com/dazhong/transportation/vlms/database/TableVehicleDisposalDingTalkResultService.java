package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehicleDisposalDingTalkResult;

public interface TableVehicleDisposalDingTalkResultService extends BaseTableService<VehicleDisposalDingTalkResult, Long> {

    /**
     * 根据单据号查询车辆处置详情列表
     *
     * @param disposalId 处置单id
     * @return 车辆处置详情列表
     */
    VehicleDisposalDingTalkResult getVehicleDisposalDingTalkResultByDisposalId(Long disposalId);
}
