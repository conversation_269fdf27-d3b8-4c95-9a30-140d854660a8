package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dazhong.transportation.vlms.database.TableCompanyExtraInfoService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.CompanyExtraInfoMapper;
import com.dazhong.transportation.vlms.model.CompanyExtraInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.CompanyExtraInfoDynamicSqlSupport.companyExtraInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableCompanyExtraInfoServiceImpl implements TableCompanyExtraInfoService {

    @Autowired
    private CompanyExtraInfoMapper companyExtraInfoMapper;

    @Override
    public CompanyExtraInfo selectCompanyExtraInfoList(Long foreignId, Integer businessType) {
        SelectStatementProvider selectStatement = select(companyExtraInfo.allColumns())
                .from(companyExtraInfo)
                .where()
                .and(companyExtraInfo.foreignId, isEqualTo(foreignId))
                .and(companyExtraInfo.businessType,isEqualTo(businessType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<CompanyExtraInfo> optional = companyExtraInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public List<CompanyExtraInfo> selectCompanyExtraInfoList(List<Long> foreignIdList, Integer businessType) {
        if (CollectionUtil.isEmpty(foreignIdList)) {
            return null;
        }
        SelectStatementProvider selectStatement = select(companyExtraInfo.allColumns())
                .from(companyExtraInfo)
                .where()
                .and(companyExtraInfo.foreignId, isInWhenPresent(foreignIdList))
                .and(companyExtraInfo.businessType, isEqualTo(businessType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return companyExtraInfoMapper.selectMany(selectStatement);
    }

    @Override
    public CompanyExtraInfo insert(CompanyExtraInfo companyExtraInfo, TokenUserInfo tokenUserInfo) {
        companyExtraInfo.setCreateOperId(tokenUserInfo.getUserId());
        companyExtraInfo.setCreateOperName(tokenUserInfo.getName());
        companyExtraInfoMapper.insertSelective(companyExtraInfo);
        return companyExtraInfo;
    }

    @Override
    public int updateSelectiveById(CompanyExtraInfo companyExtraInfo, TokenUserInfo tokenUserInfo) {
        companyExtraInfo.setUpdateTime(new Date());
        companyExtraInfo.setUpdateOperId(tokenUserInfo.getUserId());
        companyExtraInfo.setUpdateOperName(tokenUserInfo.getName());
        return companyExtraInfoMapper.updateByPrimaryKeySelective(companyExtraInfo);
    }
}
