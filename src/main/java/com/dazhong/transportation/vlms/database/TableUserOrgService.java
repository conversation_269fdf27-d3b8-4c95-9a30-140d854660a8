package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.UserOrgInfo;

import java.util.List;

/**
 * 组织架构服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableUserOrgService extends BaseTableService<UserOrgInfo, Long> {

    /**
     * 根据用户ID查询公司编码列表
     * @param userId 用户ID
     * @param type 关联机构类型 1-Organization 2-owner
     * @return 公司编码列表
     */
    List<Long> queryOrgIdList(Long userId,Integer type);

    /**
     * 根据公司编码查询用户列表
     * @param orgId 公司编码ID
     * @param type 关联机构类型 1-Organization 2-owner
     * @return 公司信息
     */
    List<Long> queryUserIdList(Long orgId,Integer type);

    /**
     * 根据用户ID查询用户关联公司信息
     * @param userId 用户ID
     * @return 用户关联公司信息
     */
    List<UserOrgInfo> queryUserOrgInfoByUserId(Long userId);

    /**
     * 批量新增
     * @param list
     * @return
     */
    int batchInsert(List<UserOrgInfo> list);

    /**
     * 删除用户关联组织架构信息
     * @param userId
     * @return
     */
    int deleteByUserId(Long userId);
}
