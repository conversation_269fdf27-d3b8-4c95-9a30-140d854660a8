package com.dazhong.transportation.vlms.database;

import java.util.List;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.DataOwnerDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.model.DataOwnerInfo;

public interface TableDataOwnerInfoService extends BaseTableService<DataOwnerInfo, Long> {

    /**
     * 查询所有车辆拥有公司信息
     * @return 车辆拥有公司信息列表（全表）
     */
    List<DataOwnerInfo> queryAllOwner();

    /**
     * 查询所有车辆拥有公司信息
     * @return 车辆拥有公司信息列表（全表）
     */
    List<DataOwnerDto> queryAllOwnerList();

    /**
     * 是否存在重复车辆拥有公司名称
     * @param ownerName 车辆拥有公司名称
     * @param excludeId 需要排除的ID
     * @return true:已经存在  false:不存在
     */
    boolean hasDuplicateOwnerName(String ownerName, Long excludeId);

    /**
     * 查询公司信息
     * @param ownerId 主鍵id
     * @return
     */
    DataOwnerInfo queryOwnerInfoById(Integer ownerId);

    /**
     * 查询公司信息
     * @param ownerName 主鍵id
     * @return
     */
    DataOwnerInfo queryOwnerInfoByName(String ownerName);

    /**
     * 查询公司信息
     * @param ownerId 主鍵id (Long类型)
     * @return 公司信息，如果不存在返回null
     */
    DataOwnerInfo queryOwnerInfoById(Long ownerId);

    /**
     * 根据组织编码列表查询组织信息列表
     * @param idList 组织编码列表
     * @return 组织信息列表
     */
    List<DataOwnerInfo> queryOwnerInfoList(List<Long> idList);

    /**
     * 插入数据（支持自定义ID）
     * 当用户指定了自定义ID时使用此方法，能够保存用户指定的ID到数据库
     *
     * @param dataOwnerInfo 插入对象（包含用户指定的ID）
     * @return 插入后对象信息
     */
    DataOwnerInfo insertById(DataOwnerInfo dataOwnerInfo);

    /**
     * 插入数据（支持自定义ID）
     * 当用户指定了自定义ID时使用此方法，能够保存用户指定的ID到数据库
     *
     * @param dataOwnerInfo 插入对象（包含用户指定的ID）
     * @param operator 操作人名称
     * @return 插入后对象信息
     */
    DataOwnerInfo insertById(DataOwnerInfo dataOwnerInfo, String operator);

    /**
     * 插入数据（支持自定义ID）
     * 当用户指定了自定义ID时使用此方法，能够保存用户指定的ID到数据库
     *
     * @param dataOwnerInfo 插入对象（包含用户指定的ID）
     * @param tokenUserInfo 操作人信息
     * @return 插入后对象信息
     */
    DataOwnerInfo insertById(DataOwnerInfo dataOwnerInfo, TokenUserInfo tokenUserInfo);
}
