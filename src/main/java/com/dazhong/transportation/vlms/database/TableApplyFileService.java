package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehicleApplyFile;

import java.util.List;

/**
 * 车辆附件表服务
 * <AUTHOR>
 * @date 2025-01-06 10:37
 */
public interface TableApplyFileService extends BaseTableService<VehicleApplyFile, Long> {

    /**
     * 查询附件信息
     * @param foreignId 申请id
     * @param businessType 业务类型 1-采购申请 2-转固申请 3-转固申请 4-车辆调拨 5-车辆转籍 6-切换业务类型
     * @return
     */
    List<VehicleApplyFile> queryFileList(Long foreignId,Integer businessType);

    /**
     * 删除附件信息
     * @param foreignId
     * @return
     */
    int deleteApplyFile(Long foreignId,Integer businessType);
}
