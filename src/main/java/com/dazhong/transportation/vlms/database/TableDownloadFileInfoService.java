package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.model.DownloadFileInfo;

import java.util.List;

public interface TableDownloadFileInfoService extends BaseTableService<DownloadFileInfo, Long> {

    /**
     * 分页查询下载文件列表
     * @param request 查询条件
     * @param tokenUserInfo 分页信息
     * @return 下载文件列表
     */
    List<DownloadFileInfo> searchDownloadFileList(PageRequest request, TokenUserInfo tokenUserInfo);
}
