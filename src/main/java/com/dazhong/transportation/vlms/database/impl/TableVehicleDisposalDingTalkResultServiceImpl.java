package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleDisposalDingTalkResultService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleDisposalDingTalkResultMapper;
import com.dazhong.transportation.vlms.model.VehicleDisposalDingTalkResult;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.dazhong.transportation.vlms.mapper.VehicleDisposalDingTalkResultDynamicSqlSupport.vehicleDisposalDingTalkResult;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class TableVehicleDisposalDingTalkResultServiceImpl implements TableVehicleDisposalDingTalkResultService {

    @Autowired
    private VehicleDisposalDingTalkResultMapper vehicleDisposalDingTalkResultMapper;

    @Override
    public VehicleDisposalDingTalkResult selectById(Long id) {
        return vehicleDisposalDingTalkResultMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int updateSelectiveById(VehicleDisposalDingTalkResult vehicleDisposalDingTalkResult) {
        return vehicleDisposalDingTalkResultMapper.updateByPrimaryKeySelective(vehicleDisposalDingTalkResult);
    }

    @Override
    public int updateSelectiveById(VehicleDisposalDingTalkResult vehicleDisposalDingTalkResult, TokenUserInfo tokenUserInfo) {
        vehicleDisposalDingTalkResult.setUpdateTime(new Date());
        vehicleDisposalDingTalkResult.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleDisposalDingTalkResult.setUpdateOperName(tokenUserInfo.getName());
        return vehicleDisposalDingTalkResultMapper.updateByPrimaryKeySelective(vehicleDisposalDingTalkResult);
    }

    @Override
    public VehicleDisposalDingTalkResult insert(VehicleDisposalDingTalkResult vehicleDisposalDingTalkResult, TokenUserInfo tokenUserInfo) {
        vehicleDisposalDingTalkResult.setCreateOperId(tokenUserInfo.getUserId());
        vehicleDisposalDingTalkResult.setCreateOperName(tokenUserInfo.getName());
        vehicleDisposalDingTalkResult.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleDisposalDingTalkResult.setUpdateOperName(tokenUserInfo.getName());
        vehicleDisposalDingTalkResultMapper.insertSelective(vehicleDisposalDingTalkResult);
        return vehicleDisposalDingTalkResult;
    }

    @Override
    public VehicleDisposalDingTalkResult getVehicleDisposalDingTalkResultByDisposalId(Long disposalId) {
        SelectStatementProvider selectStatement = select(vehicleDisposalDingTalkResult.allColumns())
                .from(vehicleDisposalDingTalkResult)
                .where()
                .and(vehicleDisposalDingTalkResult.disposalId, isEqualTo(disposalId))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDisposalDingTalkResultMapper.selectOne(selectStatement).orElse(null);
    }
}
