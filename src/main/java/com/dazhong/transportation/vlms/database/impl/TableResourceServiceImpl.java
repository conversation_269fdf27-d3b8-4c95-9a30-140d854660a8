package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableResourceService;
import com.dazhong.transportation.vlms.mapper.ResourceInfoMapper;
import com.dazhong.transportation.vlms.model.ResourceInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.ResourceInfoDynamicSqlSupport.resourceInfo;
import static com.dazhong.transportation.vlms.mapper.RoleResourceInfoDynamicSqlSupport.roleResourceInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableResourceServiceImpl implements TableResourceService {

    @Autowired
    private ResourceInfoMapper resourceInfoMapper;


    @Override
    public ResourceInfo selectById(Long id) {
        SelectStatementProvider selectStatement = select(resourceInfo.allColumns())
                .from(resourceInfo)
                .where(resourceInfo.id, isEqualTo(id))
                .and(resourceInfo.isDeleted, isEqualTo(0))
                .and(resourceInfo.systemCode, isEqualTo(BizConstant.system_code))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<ResourceInfo> optional = resourceInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public ResourceInfo queryResourceByKey(String resourceKey) {
        SelectStatementProvider selectStatement = select(resourceInfo.allColumns())
                .from(resourceInfo)
                .where(resourceInfo.resourceKey, isEqualTo(resourceKey))
                .and(resourceInfo.systemCode, isEqualTo(BizConstant.system_code))
                .and(resourceInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<ResourceInfo> optional = resourceInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int saveResource(ResourceInfo resourceInfo) {
        return resourceInfoMapper.insertSelective(resourceInfo);
    }

    @Override
    public int deleteResource(long id) {
        return resourceInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateResource(ResourceInfo resourceInfo) {
        return resourceInfoMapper.updateByPrimaryKeySelective(resourceInfo);
    }

    @Override
    public List<ResourceInfo> queryResourceByParentId(Long parentResourceId) {
        SelectStatementProvider provider = select(resourceInfo.allColumns())
                .from(resourceInfo)
                .where()
                .and(resourceInfo.parentResourceId, isEqualTo(parentResourceId))
                .and(resourceInfo.systemCode, isEqualTo(BizConstant.system_code))
                .and(resourceInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return resourceInfoMapper.selectMany(provider);
    }

    @Override
    public List<ResourceInfo> queryhRoleResourceList(List<Long> roleIdList) {
        SelectStatementProvider selectStatement = select(
                resourceInfo.id,
                resourceInfo.resourceKey,
                resourceInfo.resourceName,
                resourceInfo.resourceUrl,
                resourceInfo.resourceType,
                resourceInfo.resourceIconUrl,
                resourceInfo.resourceCode,
                resourceInfo.parentResourceId,
                resourceInfo.sort,
                resourceInfo.hide,
                resourceInfo.hideTitle,
                resourceInfo.createTime
        )
                .from(resourceInfo)
                .join(roleResourceInfo).on(resourceInfo.id, equalTo(roleResourceInfo.resourceId))
                .where()
                .and(roleResourceInfo.roleId, isIn(roleIdList))
                .and(resourceInfo.systemCode, isEqualTo(BizConstant.system_code))
                .and(roleResourceInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return resourceInfoMapper.queryhRoleResourceList(selectStatement);
    }

    @Override
    public List<ResourceInfo> qeuryAllResourceList() {
        SelectStatementProvider provider = select(resourceInfo.allColumns())
                .from(resourceInfo)
                .where()
                .and(resourceInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return resourceInfoMapper.selectMany(provider);
    }
}
