package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehicleManagementLegacyInfo;

import java.util.List;

/**
 * 车辆服务
 * <AUTHOR>
 * @date 2025-01-10 13:57
 */
public interface TableVehicleManagementLegacyService extends BaseTableService<VehicleManagementLegacyInfo, Long> {


    /**
     * 根据vin查询车辆
     * @param vin
     * @return
     */
    VehicleManagementLegacyInfo queryVehicleByVin(String vin);


    /**
     * 根据vin查询车辆
     * @param vinList
     * @return
     */
    List<VehicleManagementLegacyInfo> queryVehicleByVinList(List<String> vinList);

}
