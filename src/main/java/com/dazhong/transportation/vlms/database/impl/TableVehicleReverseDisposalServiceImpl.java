package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.dazhong.transportation.vlms.database.TableVehicleReverseDisposalService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalDetailListDto;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.enums.DocumentStatusEnum;
import com.dazhong.transportation.vlms.mapper.VehicleReverseDisposalMapper;
import com.dazhong.transportation.vlms.model.VehicleReverseDisposal;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleReverseDisposalDetailDynamicSqlSupport.vehicleReverseDisposalDetail;
import static com.dazhong.transportation.vlms.mapper.VehicleReverseDisposalDynamicSqlSupport.vehicleReverseDisposal;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleReverseDisposalServiceImpl implements TableVehicleReverseDisposalService {

    @Autowired
    VehicleReverseDisposalMapper vehicleReverseDisposalMapper;

    @Override
    public VehicleReverseDisposal selectById(Long id) {
        return vehicleReverseDisposalMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int updateSelectiveById(VehicleReverseDisposal vehicleReverseDisposal) {
        return vehicleReverseDisposalMapper.updateByPrimaryKeySelective(vehicleReverseDisposal);
    }

    @Override
    public VehicleReverseDisposal insert(VehicleReverseDisposal vehicleReverseDisposal, TokenUserInfo tokenUserInfo) {
        vehicleReverseDisposal.setCreateTime(new Date());
        vehicleReverseDisposal.setCreateOperId(tokenUserInfo.getUserId());
        vehicleReverseDisposal.setCreateOperName(tokenUserInfo.getName());
        vehicleReverseDisposalMapper.insertSelective(vehicleReverseDisposal);
        return vehicleReverseDisposal;
    }

    @Override
    public int updateSelectiveById(VehicleReverseDisposal vehicleReverseDisposal, TokenUserInfo tokenUserInfo) {
        vehicleReverseDisposal.setUpdateTime(new Date());
        vehicleReverseDisposal.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleReverseDisposal.setUpdateOperName(tokenUserInfo.getName());
        return vehicleReverseDisposalMapper.updateByPrimaryKeySelective(vehicleReverseDisposal);
    }

    @Override
    public List<VehicleReverseDisposalListDto> queryVehicleReverseDisposalList(SearchVehicleDisposalListRequest searchVehicleDisposalListRequest, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();
        SelectStatementProvider provider = select(
                vehicleReverseDisposal.allColumns(),
                count(vehicleReverseDisposalDetail.id).as("vehicleNumber"))
                .from(vehicleReverseDisposal)
                .leftJoin(vehicleReverseDisposalDetail).on(vehicleReverseDisposal.id, equalTo(vehicleReverseDisposalDetail.reverseDisposalId))
                .where()
                .and(vehicleReverseDisposal.dingTalkNo, isLikeWhenPresent(transFuzzyQueryParam(searchVehicleDisposalListRequest.getDingTalkNo())).filter(StringUtils::isNotBlank))
                .and(vehicleReverseDisposal.createOperName, isLikeWhenPresent(transFuzzyQueryParam(searchVehicleDisposalListRequest.getCreateOperName())).filter(StringUtils::isNotBlank))
                .and(vehicleReverseDisposal.documentStatus, isEqualToWhenPresent(searchVehicleDisposalListRequest.getDocumentStatus()))
                .and(vehicleReverseDisposal.documentStatus, isInWhenPresent(searchVehicleDisposalListRequest.getDocumentStatusList()))
                .and(vehicleReverseDisposalDetail.vin, isEqualToWhenPresent(searchVehicleDisposalListRequest.getVin()).filter(StringUtils::isNotBlank))
                .and(vehicleReverseDisposalDetail.licensePlate, isInWhenPresent(searchVehicleDisposalListRequest.getLicensePlate()).filter(StringUtils::isNotBlank))
                .and(vehicleReverseDisposal.organizationId, isInWhenPresent(orgIdList), or(vehicleReverseDisposal.ownerId, isInWhenPresent(ownerIdList)))
                .groupBy(vehicleReverseDisposal.id)
                .orderBy(vehicleReverseDisposal.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return vehicleReverseDisposalMapper.selectVehicleReverseDisposalList(provider);
    }

    @Override
    public VehicleReverseDisposal selectByDingTalkNo(String dingTalkNo) {
        return vehicleReverseDisposalMapper.selectByDingTalkNo(dingTalkNo).orElse(null);
    }

    @Override
    public VehicleReverseDisposalDetailListDto queryVehicleReverseDisposalDetail(String vin, String disposalDocumentNo) {
        SelectStatementProvider provider = select(
                vehicleReverseDisposal.allColumns(),
                count(vehicleReverseDisposalDetail.id).as("vehicleNumber"))
                .from(vehicleReverseDisposal)
                .leftJoin(vehicleReverseDisposalDetail).on(vehicleReverseDisposal.id, equalTo(vehicleReverseDisposalDetail.reverseDisposalId))
                .where()
                .and(vehicleReverseDisposal.documentStatus, isEqualToWhenPresent(DocumentStatusEnum.REVIEW_APPROVED.getCode()))
                .and(vehicleReverseDisposalDetail.vin, isEqualToWhenPresent(vin))
                .and(vehicleReverseDisposalDetail.disposalDocumentNo, isEqualToWhenPresent(disposalDocumentNo).filter(StringUtils::isNotBlank))
                .groupBy(vehicleReverseDisposal.id)
                .orderBy(vehicleReverseDisposal.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<VehicleReverseDisposalListDto> vehicleReverseDisposalListDtoList = vehicleReverseDisposalMapper.selectVehicleReverseDisposalList(provider);
        if (CollectionUtil.isNotEmpty(vehicleReverseDisposalListDtoList)) {
            return BeanUtil.copyProperties(vehicleReverseDisposalListDtoList.get(0), VehicleReverseDisposalDetailListDto.class);
        }
        return null;
    }
}
