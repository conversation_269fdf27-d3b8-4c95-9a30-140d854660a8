package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.DataMaintainDictInfo;

import java.util.List;

public interface TableDataMaintainDictInfoService extends BaseTableService<DataMaintainDictInfo, Long> {

    /**
     * 根据系统编码查询字典信息
     * @param systemCode 系统编码
     * @return 字典信息
     */
    List<DataMaintainDictInfo> queryBySystemCode(String systemCode);

    /**
     * 根据系统编码列表查询字典信息
     * @param systemCodeList 系统编码列表
     * @return 字典信息
     */
    List<DataMaintainDictInfo> selectBySystemCode(List<String> systemCodeList);

    /**
     * 判断字典编码是否重复
     * @param systemCode 系统编码
     * @param excludeId 剔除掉的ID（新增数据判断时，该值传NULL）
     * @param dataCode 判断重复的字典编码
     * @return true:已经存在  false:不存在
     */
    boolean hasDuplicateMaintainDictDataCode(String systemCode, Long excludeId, String dataCode);
}
