package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.LicensePlateQuotaOperateLogDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaOperateLogRequest;
import com.dazhong.transportation.vlms.model.LicensePlateQuotaOperateLog;

import java.util.List;

public interface TableLicensePlateQuotaOperateLogService extends BaseTableService<LicensePlateQuotaOperateLog, Long> {

    /**
     * 查询车牌额度日志列表
     *
     * @param request
     * @return
     */
    List<LicensePlateQuotaOperateLog> searchOperateLog(SearchLicensePlateQuotaOperateLogRequest request);

    /**
     * 保存日志
     *
     * @param licensePlateQuotaOperateLogDTO 车牌额度操作日志
     * @param tokenUserInfo                  用户信息
     */
    void insertLog(LicensePlateQuotaOperateLogDto licensePlateQuotaOperateLogDTO, TokenUserInfo tokenUserInfo);
}
