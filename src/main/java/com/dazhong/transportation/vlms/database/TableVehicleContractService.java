package com.dazhong.transportation.vlms.database;

import java.util.List;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehicleContract;

/**
 * 车辆合同表服务
 * <AUTHOR>
 * @date 2025-01-16 10:37
 */
public interface TableVehicleContractService extends BaseTableService<VehicleContract, Long> {


    /**
     * 根据车架号查询车辆合同
     * @param vin 车架号
     * @return 车辆合同
     */
    List<VehicleContract> queryVehicleContractByVin(String vin);

    /**
     * 根据合同号查询
     * @param contractNo 合同号
     * @return 车辆合同
     */
    VehicleContract queryByContractNo(String contractNo);

}
