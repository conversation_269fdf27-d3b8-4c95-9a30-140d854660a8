package com.dazhong.transportation.vlms.database;

import java.util.List;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.RoleResourceInfo;

/**
 * 角色资源服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableRoleResourceService extends BaseTableService<RoleResourceInfo, Long> {

    /**
     * 批量新增
     * @param list
     * @return
     */
    int batchInsert(List<RoleResourceInfo> list);

    /**
     * 根据角色删除角色资源
     * @param roleId
     * @return
     */
    int deleteByRoleId(Long roleId);
}
