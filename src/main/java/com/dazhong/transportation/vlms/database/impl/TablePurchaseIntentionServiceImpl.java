package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TablePurchaseIntentionService;
import com.dazhong.transportation.vlms.dto.request.SearchPurchaseIntentionRequest;
import com.dazhong.transportation.vlms.mapper.VehiclePurchaseIntentionMapper;
import com.dazhong.transportation.vlms.model.VehiclePurchaseIntention;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseIntentionDynamicSqlSupport.vehiclePurchaseIntention;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-30 09:38
 */
@Service
public class TablePurchaseIntentionServiceImpl implements TablePurchaseIntentionService {

    @Autowired
    private VehiclePurchaseIntentionMapper vehiclePurchaseIntentionMapper;

    @Override
    public List<VehiclePurchaseIntention> queryPurchaseIntention(SearchPurchaseIntentionRequest request, List<Long> orgIdList) {
        String contractNo = request.getContractNo();
        if (StringUtils.isNotBlank(contractNo)){
            contractNo = "%" + contractNo + "%";
        }
        SelectStatementProvider selectStatement = select(vehiclePurchaseIntention.allColumns())
                .from(vehiclePurchaseIntention)
                .where()
                .and(vehiclePurchaseIntention.contractNo, isLikeWhenPresent(contractNo).filter(StringUtils::isNotBlank))
//                .and(vehiclePurchaseIntention.orgCode, isInWhenPresent(orgIdList).filter(ObjectValidUtil::isValid))
                .and(vehiclePurchaseIntention.applyStatus, isEqualToWhenPresent(request.getApplyNoStatus()).filter(ObjectValidUtil::isValid))
                .and(vehiclePurchaseIntention.isDeleted, isEqualTo(0))
                .orderBy(vehiclePurchaseIntention.dataSyncTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehiclePurchaseIntentionMapper.selectMany(selectStatement);
    }

    @Override
    public VehiclePurchaseIntention queryPurchaseIntention(String intentionNo) {
        if (StringUtils.isBlank(intentionNo)){
            return null;
        }
        SelectStatementProvider selectStatement = select(vehiclePurchaseIntention.allColumns())
                .from(vehiclePurchaseIntention)
                .where()
                .and(vehiclePurchaseIntention.intentionNo, isEqualTo(intentionNo))
                .and(vehiclePurchaseIntention.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehiclePurchaseIntention> optional = vehiclePurchaseIntentionMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public VehiclePurchaseIntention queryPurchaseIntention(Long purchaseApplyId) {
        SelectStatementProvider selectStatement = select(vehiclePurchaseIntention.allColumns())
                .from(vehiclePurchaseIntention)
                .where()
                .and(vehiclePurchaseIntention.purchaseApplyId, isEqualTo(purchaseApplyId))
                .and(vehiclePurchaseIntention.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehiclePurchaseIntention> optional = vehiclePurchaseIntentionMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int updatePurchaseIntention(VehiclePurchaseIntention vehiclePurchaseIntention) {
        return vehiclePurchaseIntentionMapper.updateByPrimaryKeySelective(vehiclePurchaseIntention);
    }

    @Override
    public int insertPurchaseIntention(VehiclePurchaseIntention vehiclePurchaseIntention) {
        return vehiclePurchaseIntentionMapper.insertSelective(vehiclePurchaseIntention);
    }

    @Override
    public VehiclePurchaseIntention queryByContractNo(String contractNo) {
        if (StringUtils.isBlank(contractNo)){
            return null;
        }
        SelectStatementProvider selectStatement = select(vehiclePurchaseIntention.allColumns())
                .from(vehiclePurchaseIntention)
                .where()
                .and(vehiclePurchaseIntention.contractNo, isEqualTo(contractNo))
                .and(vehiclePurchaseIntention.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehiclePurchaseIntention> optional = vehiclePurchaseIntentionMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }
}
