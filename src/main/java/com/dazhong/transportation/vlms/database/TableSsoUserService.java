package com.dazhong.transportation.vlms.database;

import java.util.List;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.SsoUserInfo;

/**
 * 大众出行sso用户服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableSsoUserService extends BaseTableService<SsoUserInfo, Long> {

    /**
     * 查询所有用户信息
     * @return
     */
    List<SsoUserInfo> querySsoUserList();

    /**
     * 根据用户id查询用户信息
     * @param userId
     * @return
     */
    SsoUserInfo querySsoUserByUserId(String userId);

    /**
     * 保存用户信息
     * @param userInfo
     * @return
     */
    int saveSsoUser(SsoUserInfo userInfo);

    /**
     * 修改用户信息
     * @param userInfo
     * @return
     */
    int updateSsoUser(SsoUserInfo userInfo);

    /**
     * 删除用户信息
     * @param id
     * @return
     */
    int deleteSsoUser(Long id);
}
