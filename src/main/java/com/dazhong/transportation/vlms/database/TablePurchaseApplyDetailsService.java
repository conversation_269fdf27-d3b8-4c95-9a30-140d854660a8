package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehiclePurchaseApplyDetails;

import java.util.List;

/**
 * 车辆采购详情表服务
 * <AUTHOR>
 * @date 2025-01-06 10:37
 */
public interface TablePurchaseApplyDetailsService extends BaseTableService<VehiclePurchaseApplyDetails, Long> {

    /**
     * 根据采购申请ID查询采购详情列表
     * @param purchaseApplyId
     * @return
     */
    List<VehiclePurchaseApplyDetails> queryPurchaseDetailsList(Long purchaseApplyId);

    /**
     * 根据采购详情编号查询采购详情信息
     * @param applyDetailsNo
     * @return
     */
    VehiclePurchaseApplyDetails queryPurchaseApplyDetails(String applyDetailsNo);

    /**
     * 新增采购详情信息
     * @param purchaseApply
     * @return
     */
    int savePurchaseApplyDetails(VehiclePurchaseApplyDetails purchaseApply);

    /**
     * 更新采购详情信息
     * @param purchaseApply
     * @return
     */
    int updatePurchaseApplyDetails(VehiclePurchaseApplyDetails purchaseApply);

    /**
     * 删除采购详情信息
     * @param purchaseApplyId
     * @return
     */
    int deletePurchaseApplyDetails(Long purchaseApplyId);
}
