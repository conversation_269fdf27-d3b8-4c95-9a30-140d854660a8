package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.OrgInfo;

import java.util.List;

/**
 * 组织架构服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableOrgInfoService extends BaseTableService<OrgInfo, Long> {

    /**
     * 查询所有组织机构信息
     * @return 组织机构列表
     */
    List<OrgInfo> queryAllOrgInfo();

    /**
     * 根据组织编码列表查询组织信息列表
     * @param idList 组织编码列表
     * @return 组织信息列表
     */
    List<OrgInfo> queryOrgListByIds(List<Long> idList);

    /**
     * 根据状态查询所有组织机构信息
     * @param isDeleted
     * @return
     */
    List<OrgInfo> queryOrgInfoByIsDeleted(Integer isDeleted);


    /**
     * 根据状态查询所有组织机构信息
     * @param checkedState 是否选中 1-是 2-否
     * @return
     */
    List<OrgInfo> queryOrgInfoByCheckedState(Integer checkedState);

    /**
     * 查询组织信息
     * @param orgId
     * @return
     */
    OrgInfo queryOrgInfoById(Long orgId);

    /**
     * 查询组织信息
     *
     * @param companyName
     * @return
     */
    OrgInfo queryOrgInfoByName(String companyName);

    /**
     * 更新组织信息
     * @param orgInfo
     * @return
     */
    int updateOrgInfo(OrgInfo orgInfo);

    /**
     * 批量插入组织信息
     * @param orgInfoList
     * @return
     */
    int batchInsertOrgInfo(List<OrgInfo> orgInfoList);


    /**
     * 删除组织信息
     * @return
     */
    int deleteOrgInfo();
}
