package com.dazhong.transportation.vlms.database;

import java.util.List;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehicleDepreciationData;

/**
 * 车辆折旧数据表服务
 */
public interface TableVehicleDepreciationDataService extends BaseTableService<VehicleDepreciationData, Long> {

    /**
     * 查询车辆装潢信息
     * @return
     */
    List<VehicleDepreciationData> queryVehicleDepreciationDataList(String vin);

}
