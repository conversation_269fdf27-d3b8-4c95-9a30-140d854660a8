package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableDataAreaInfoService;
import com.dazhong.transportation.vlms.mapper.DataAreaInfoMapper;
import com.dazhong.transportation.vlms.model.DataAreaInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.DataAreaInfoDynamicSqlSupport.dataAreaInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class TableDataAreaInfoServiceImpl implements TableDataAreaInfoService {

    @Autowired
    private DataAreaInfoMapper dataAreaInfoMapper;

    @Override
    public List<DataAreaInfo> queryAllArea() {
        SelectStatementProvider selectStatement = select(dataAreaInfo.allColumns())
                .from(dataAreaInfo)
                .where(dataAreaInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataAreaInfoMapper.selectMany(selectStatement);
    }
}
