package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableLicensePlateTaskInfoService;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.mapper.LicensePlateTaskInfoMapper;
import com.dazhong.transportation.vlms.model.LicensePlateTaskInfo;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskInfoDynamicSqlSupport.licensePlateTaskInfo;
import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskQuotaDetailDynamicSqlSupport.licensePlateTaskQuotaDetail;
import static com.dazhong.transportation.vlms.mapper.LicensePlateTaskVehicleDetailDynamicSqlSupport.licensePlateTaskVehicleDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableLicensePlateTaskInfoServiceImpl implements TableLicensePlateTaskInfoService {

    @Autowired
    private LicensePlateTaskInfoMapper licensePlateTaskInfoMapper;

    @Override
    public LicensePlateTaskInfo insert(LicensePlateTaskInfo licensePlateTaskInfo, TokenUserInfo tokenUserInfo) {
        licensePlateTaskInfo.setCreateOperId(tokenUserInfo.getUserId());
        licensePlateTaskInfo.setCreateOperName(tokenUserInfo.getName());
        licensePlateTaskInfoMapper.insertSelective(licensePlateTaskInfo);
        return licensePlateTaskInfo;
    }

    @Override
    public List<LicensePlateTaskVehicleDetailDto> queryList(SearchLicensePlateTaskInfoRequest searchLicensePlateTaskInfoRequest) {
        SelectStatementProvider provider = select(
                licensePlateTaskVehicleDetail.id,
                licensePlateTaskInfo.id.as("taskId"),
                licensePlateTaskInfo.taskNumber,
                licensePlateTaskInfo.taskType,
                licensePlateTaskInfo.createTime,
                licensePlateTaskInfo.createOperName,
                licensePlateTaskVehicleDetail.licensePlate,
                licensePlateTaskVehicleDetail.vin,
                licensePlateTaskVehicleDetail.vehicleModelId.as("vehicleModelId"),
                licensePlateTaskVehicleDetail.assetCompanyId.as("assetCompanyId"),
                licensePlateTaskVehicleDetail.ownOrganizationId.as("ownOrganizationId"),
                licensePlateTaskVehicleDetail.usageOrganizationId.as("usageOrganizationId"),
                licensePlateTaskVehicleDetail.returnQuotaType.as("returnQuotaType"),
                licensePlateTaskVehicleDetail.quotaType.as("quotaType"),
                licensePlateTaskVehicleDetail.quotaAssetCompanyId.as("quotaAssetCompanyId"),
                licensePlateTaskVehicleDetail.quotaAssetCompanyName.as("quotaAssetCompanyName"),
                licensePlateTaskVehicleDetail.quotaNumber.as("quotaNumber"),
                licensePlateTaskVehicleDetail.quotaPrintDate.as("quotaPrintDate"),
                licensePlateTaskVehicleDetail.usageIdRegistrationCard.as("usageIdRegistrationCard"),
                licensePlateTaskVehicleDetail.registrationDateRegistrationCard.as("registrationDateRegistrationCard"),
                licensePlateTaskVehicleDetail.issuanceDateRegistrationCard.as("issuanceDateRegistrationCard"),
                licensePlateTaskVehicleDetail.fileNumber.as("fileNumber"),
                licensePlateTaskVehicleDetail.retirementDateRegistrationCard.as("retirementDateRegistrationCard"),
                licensePlateTaskVehicleDetail.annualInspectionDueDateRegistrationCard.as("annualInspectionDueDateRegistrationCard"))
                //licensePlateTaskVehicleDetail.vehicleTypeLicensePlate.as("vehicleTypeLicensePlate"),
                .from(licensePlateTaskInfo)
                .leftJoin(licensePlateTaskVehicleDetail).on(licensePlateTaskInfo.taskNumber, equalTo(licensePlateTaskVehicleDetail.taskNumber))
                .leftJoin(licensePlateTaskQuotaDetail).on(licensePlateTaskQuotaDetail.taskNumber, equalTo(licensePlateTaskVehicleDetail.taskNumber))
                .where()
                .and(licensePlateTaskVehicleDetail.vin, isEqualToWhenPresent(searchLicensePlateTaskInfoRequest.getVin()).filter(StringUtils::isNotBlank))
                .and(licensePlateTaskVehicleDetail.licensePlate, isLikeWhenPresent(transFuzzyQueryParam(searchLicensePlateTaskInfoRequest.getLicensePlate())).filter(StringUtils::isNotBlank))
                .and(licensePlateTaskInfo.taskType, isEqualToWhenPresent(searchLicensePlateTaskInfoRequest.getTaskType()))
                .and(licensePlateTaskVehicleDetail.quotaAssetCompanyId, isInWhenPresent(searchLicensePlateTaskInfoRequest.getQuotaAssetCompanyIdList()))
                .and(licensePlateTaskVehicleDetail.quotaNumber, isLikeWhenPresent(transFuzzyQueryParam(searchLicensePlateTaskInfoRequest.getQuotaNumber())).filter(StringUtils::isNotBlank), or(licensePlateTaskQuotaDetail.quotaNumber, isLikeWhenPresent(transFuzzyQueryParam(searchLicensePlateTaskInfoRequest.getQuotaNumber())).filter(StringUtils::isNotBlank)))
                .groupBy(licensePlateTaskVehicleDetail.id,
                        licensePlateTaskInfo.id)
                .orderBy(licensePlateTaskVehicleDetail.createTime.descending(), licensePlateTaskVehicleDetail.id)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateTaskInfoMapper.selectTaskVehicleDetail(provider);
    }

    @Override
    public LicensePlateTaskInfo selectByTaskNumber(String taskNumber) {
        return licensePlateTaskInfoMapper.selectByTaskNumber(taskNumber).orElse(null);
    }

    @Override
    public LicensePlateTaskVehicleDetailDto selectLatestVehicleDetailByVin(String vin) {
        SelectStatementProvider provider = select(
                licensePlateTaskVehicleDetail.id,
                licensePlateTaskInfo.taskNumber,
                licensePlateTaskInfo.taskType,
                licensePlateTaskInfo.createTime,
                licensePlateTaskInfo.createOperName,
                licensePlateTaskVehicleDetail.licensePlate,
                licensePlateTaskVehicleDetail.vin,
                licensePlateTaskVehicleDetail.vehicleModelId.as("vehicleModelId"),
                licensePlateTaskVehicleDetail.assetCompanyId.as("assetCompanyId"),
                licensePlateTaskVehicleDetail.ownOrganizationId.as("ownOrganizationId"),
                licensePlateTaskVehicleDetail.usageOrganizationId.as("usageOrganizationId"),
                licensePlateTaskVehicleDetail.returnQuotaType.as("returnQuotaType"),
                licensePlateTaskVehicleDetail.quotaType.as("quotaType"),
                licensePlateTaskVehicleDetail.quotaAssetCompanyId.as("quotaAssetCompanyId"),
                licensePlateTaskVehicleDetail.quotaAssetCompanyName.as("quotaAssetCompanyName"),
                licensePlateTaskVehicleDetail.quotaNumber.as("quotaNumber"),
                licensePlateTaskVehicleDetail.quotaPrintDate.as("quotaPrintDate"),
                licensePlateTaskVehicleDetail.usageIdRegistrationCard.as("usageIdRegistrationCard"),
                licensePlateTaskVehicleDetail.registrationDateRegistrationCard.as("registrationDateRegistrationCard"),
                licensePlateTaskVehicleDetail.issuanceDateRegistrationCard.as("issuanceDateRegistrationCard"),
                licensePlateTaskVehicleDetail.fileNumber.as("fileNumber"),
                licensePlateTaskVehicleDetail.retirementDateRegistrationCard.as("retirementDateRegistrationCard"),
                licensePlateTaskVehicleDetail.annualInspectionDueDateRegistrationCard.as("annualInspectionDueDateRegistrationCard"))
                //licensePlateTaskVehicleDetail.vehicleTypeLicensePlate.as("vehicleTypeLicensePlate"),
                .from(licensePlateTaskInfo)
                .leftJoin(licensePlateTaskVehicleDetail).on(licensePlateTaskInfo.taskNumber, equalTo(licensePlateTaskVehicleDetail.taskNumber))
                .where()
                .and(licensePlateTaskVehicleDetail.vin, isEqualTo(vin).filter(StringUtils::isNotBlank))
                .and(licensePlateTaskInfo.taskType, isEqualTo(1))
                .orderBy(licensePlateTaskInfo.createTime.descending())
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateTaskInfoMapper.selectLatestVehicleDetailByVin(provider);
    }
}
