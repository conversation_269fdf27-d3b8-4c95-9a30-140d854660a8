package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskQuotaDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.model.LicensePlateTaskQuotaDetail;

import java.util.List;

/**
 * 车牌任务额度详情表服务
 *
 * <AUTHOR>
 * @date 2025/01/06
 */
public interface TableLicensePlateTaskQuotaDetailService extends BaseTableService<LicensePlateTaskQuotaDetail, Long> {

    /**
     * 批量插入
     *
     * @param info          插入数据
     * @param tokenUserInfo 登录用户信息
     * @return 返回插入条数
     */
    int insertMultiple(List<LicensePlateTaskQuotaDetailDto> info, TokenUserInfo tokenUserInfo);

    /**
     * 根据任务编号查询
     *
     * @param taskNumber 任务编号
     * @return 返回数据
     */
    List<LicensePlateTaskQuotaDetail> selectByTaskNumber(String taskNumber);
}
