package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.model.LicensePlateTaskVehicleDetail;

import java.util.List;

public interface TableLicensePlateTaskVehicleDetailService extends BaseTableService<LicensePlateTaskVehicleDetail, Long> {

    /**
     * 批量插入
     *
     * @param info          插入数据
     * @param tokenUserInfo 用户信息
     * @return 返回插入条数
     */
    int insertMultiple(List<LicensePlateTaskVehicleDetailDto> info, TokenUserInfo tokenUserInfo);

    /**
     * 根据任务编号查询
     *
     * @param taskNumber 任务编号
     * @return 返回数据
     */
    List<LicensePlateTaskVehicleDetail> selectByTaskNumber(String taskNumber);
}
