package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableDownloadFileInfoService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.mapper.DownloadFileInfoMapper;
import com.dazhong.transportation.vlms.model.DownloadFileInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.DownloadFileInfoDynamicSqlSupport.downloadFileInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableDownloadFileInfoServiceImpl implements TableDownloadFileInfoService {

    @Autowired
    private DownloadFileInfoMapper downloadFileInfoMapper;


    @Override
    public DownloadFileInfo insert(DownloadFileInfo downloadFileInfo) {
        Date now = new Date();
        downloadFileInfo.setCreateTime(now);
        downloadFileInfo.setUpdateTime(now);
        downloadFileInfoMapper.insertSelective(downloadFileInfo);
        return downloadFileInfo;
    }

    @Override
    public DownloadFileInfo insert(DownloadFileInfo downloadFileInfo, TokenUserInfo tokenUserInfo) {
        downloadFileInfo.setCreateOperId(tokenUserInfo.getUserId());
        downloadFileInfo.setCreateOperName(tokenUserInfo.getName());
        downloadFileInfo.setUpdateOperId(tokenUserInfo.getUserId());
        downloadFileInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(downloadFileInfo);
    }

    @Override
    public int updateSelectiveById(DownloadFileInfo downloadFileInfo) {
        downloadFileInfo.setUpdateTime(new Date());
        return downloadFileInfoMapper.updateByPrimaryKeySelective(downloadFileInfo);
    }


    @Override
    public int updateSelectiveById(DownloadFileInfo downloadFileInfo, TokenUserInfo tokenUserInfo) {
        downloadFileInfo.setUpdateOperId(tokenUserInfo.getUserId());
        downloadFileInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.updateSelectiveById(downloadFileInfo);
    }

    @Override
    public List<DownloadFileInfo> searchDownloadFileList(PageRequest request, TokenUserInfo tokenUserInfo) {
        if(tokenUserInfo == null){
            return new ArrayList<>();
        }
        SelectStatementProvider selectStatement = select(downloadFileInfo.allColumns())
                .from(downloadFileInfo)
                .where(downloadFileInfo.createOperId, isEqualTo(tokenUserInfo.getUserId()))
                .and(downloadFileInfo.expireTime, isGreaterThan(new Date()))
                //只查询最近7天的数据
                .and(downloadFileInfo.createTime, isGreaterThanWhenPresent(new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000)))
                .orderBy(downloadFileInfo.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return downloadFileInfoMapper.selectMany(selectStatement);
    }
}
