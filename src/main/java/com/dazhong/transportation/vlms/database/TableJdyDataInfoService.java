package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDeviceListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.model.JdyDataInfo;
import com.dazhong.transportation.vlms.model.VehicleDeviceInfo;

import java.util.List;

public interface TableJdyDataInfoService extends BaseTableService<JdyDataInfo, Long> {


    /**
     * 查询最后一条数据
     * @return
     */
    JdyDataInfo queryLastOne();

}
