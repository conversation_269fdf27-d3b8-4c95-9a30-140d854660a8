package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.model.VehicleReverseDisposalDetail;

import java.util.List;

public interface TableVehicleReverseDisposalDetailService extends BaseTableService<VehicleReverseDisposalDetail, Long> {

    /**
     * 批量插入
     *
     * @param VehicleReverseDisposalDetailList 车辆逆处置详情列表
     * @param tokenUserInfo                    用户登录信息
     */
    void batchInsert(List<VehicleReverseDisposalDetail> VehicleReverseDisposalDetailList, TokenUserInfo tokenUserInfo);

    /**
     * 批量删除
     *
     * @param reverseDisposalId 申请单id
     */
    void deleteByReverseDisposalId(Long reverseDisposalId);

    /**
     * 根据单据号查询车辆逆处置详情列表
     *
     * @param reverseDisposalId 处置单id
     * @return 车辆逆处置详情列表
     */
    List<VehicleReverseDisposalDetail> getVehicleReverseDisposalDetailListByReverseDisposalId(Long reverseDisposalId);
}
