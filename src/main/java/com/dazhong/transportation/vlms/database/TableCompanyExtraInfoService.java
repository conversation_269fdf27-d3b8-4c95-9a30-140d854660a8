package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.CompanyExtraInfo;

import java.util.List;

/**
 * 公司额外信息表服务
 *
 * <AUTHOR>
 * @date 2025-04-16 16:03
 */
public interface TableCompanyExtraInfoService extends BaseTableService<CompanyExtraInfo, Long> {

    /**
     * 根据外键id和业务类型查询
     *
     * @param foreignId    外键id
     * @param businessType 业务类型
     * @return
     */
    CompanyExtraInfo selectCompanyExtraInfoList(Long foreignId, Integer businessType);

    /**
     * 根据外键id和业务类型查询
     *
     * @param foreignIdList 外键id List
     * @param businessType  业务类型
     * @return
     */
    List<CompanyExtraInfo> selectCompanyExtraInfoList(List<Long> foreignIdList, Integer businessType);
}
