package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.ResourceInfo;

import java.util.List;

/**
 * 资源服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableResourceService extends BaseTableService<ResourceInfo, Long> {

    /**
     * 根据资源key查询
     * @param resourceKey
     * @return
     */
    ResourceInfo queryResourceByKey(String resourceKey);


    /**
     * 保存
     * @param resourceInfo
     * @return
     */
    int saveResource(ResourceInfo resourceInfo);

    /**
     * 删除资源
     * @param id
     * @return
     */
    int deleteResource(long id);

    /**
     * 更新
     * @param resourceInfo
     * @return
     */
    int updateResource(ResourceInfo resourceInfo);

    /**
     * 根据父节点ID查询
     * @param parentResourceId
     * @return
     */
    List<ResourceInfo> queryResourceByParentId(Long parentResourceId);
    /**
     * 根据角色ID查询资源树
     * @param roleIdList
     * @return
     */
    List<ResourceInfo> queryhRoleResourceList(List<Long> roleIdList);


    /**
     * 查询全部资源
     * @return
     */
    List<ResourceInfo> qeuryAllResourceList();


}
