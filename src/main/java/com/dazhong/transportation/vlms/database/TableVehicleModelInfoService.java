package com.dazhong.transportation.vlms.database;

import java.util.List;
import java.util.Map;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleModelDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleModelListRequest;
import com.dazhong.transportation.vlms.model.VehicleModelInfo;

public interface TableVehicleModelInfoService extends BaseTableService<VehicleModelInfo, Long> {

    /**
     * 根据条件查询车辆型号信息
     * @param request 查询条件
     * @return 查询结果
     */
    List<VehicleModelInfo> searchVehicleModelList(SearchVehicleModelListRequest request);


    /**
     * 根据主键更新
     *
     * @param t             更新数据
     * @param tokenUserInfo 操作人信息
     * @return 影响行数
     */
    int updateCoverById(VehicleModelInfo vehicleModelInfo, TokenUserInfo tokenUserInfo);



    /**
     * 获取所有车型信息
     * 建议不超过5000，若超过应采用其他方法实现
     * @return 车型列表
     */
    List<VehicleModelInfo> getAllVehicleModel();

    /**
     * 获取所有车型信息
     * @return 车型列表 key:主键ID， value:车型信息
     */
    Map<Long, VehicleModelInfo> getAllVehicleModelMap();

    /**
     * 根据车型名称获取车型信息
     * @return 车型列表 key:车型名称， value:车型信息
     */
    Map<String, VehicleModelInfo> getAllVehicleModelMapByName();

    /**
     * 判断autohomeId是否重复
     * @param autohomeId 汽车之家车型ID
     * @param excludeId 排除的ID
     * @return 是否存在重复车型 true:重复  false:无重复车型
     */
    boolean isAutohomeDuplicate(Long autohomeId, Long excludeId);

    /**
     * 判断autohomeId是否重复
     * @param autohomeId 汽车之家车型ID
     * @return 是否存在重复车型 true:重复  false:无重复车型
     */
    boolean isAutohomeDuplicate(Long autohomeId);

    /**
     * 判断车型名称是否重复
     * @param modelName 车型名称
     * @param excludeId 排除的ID
     * @return 是否存在重复车型 true:重复  false:无重复车型
     */
    boolean isModelNameDuplicate(String modelName, Long excludeId);

    /**
     * 判断车型名称是否重复
     * @param modelName 车型名称
     * @return 是否存在重复车型 true:重复  false:无重复车型
     */
    boolean isModelNameDuplicate(String modelName);

    /**
     * 获取车型表数据库同步数据
     * @param request 查询条件
     * @return 数据库同步数据
     */
    List<SyncDatabaseVehicleModelDto> searchVehicleModelDatabaseSyncInfo(SearchDatabaseTableSyncDataRequest request);

    /**
     * 分页查询需要同步到SAAS系统的车型信息
     * @param lastId 上次查询的最后ID
     * @param pageSize 每页大小
     * @param isFullSync 是否全量同步（true:全量同步，false:增量同步）
     * @return 车型信息列表
     */
    List<VehicleModelInfo> selectSyncSaasVehicleModelInfo(Long lastId, Integer pageSize, boolean isFullSync);
}
