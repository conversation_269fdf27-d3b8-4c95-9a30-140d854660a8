package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableDataDictService;
import com.dazhong.transportation.vlms.mapper.DataDictInfoMapper;
import com.dazhong.transportation.vlms.model.DataDictInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.DataDictInfoDynamicSqlSupport.dataDictInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableDataDictServiceImpl implements TableDataDictService {

    @Autowired
    private DataDictInfoMapper dataDictInfoMapper;

    @Override
    public List<DataDictInfo> searchDataDictList() {
        SelectStatementProvider provider = select(dataDictInfo.allColumns())
                .from(dataDictInfo)
                .where()
                .and(dataDictInfo.systemCode,isEqualTo(BizConstant.system_code))
                .and(dataDictInfo.isDeleted, isEqualTo(0))
                .orderBy(dataDictInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataDictInfoMapper.selectMany(provider);
    }

    @Override
    public int saveDataDict(DataDictInfo dataDictInfo) {
        return dataDictInfoMapper.insertSelective(dataDictInfo);
    }

    @Override
    public int updateDataDict(DataDictInfo userInfo) {
        return dataDictInfoMapper.updateByPrimaryKeySelective(userInfo);
    }

    @Override
    public int deleteDataDict(Long id) {
        return dataDictInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public DataDictInfo queryDataDictByCode(String dataCode) {
        SelectStatementProvider selectStatement = select(dataDictInfo.allColumns())
                .from(dataDictInfo)
                .where()
                .and(dataDictInfo.dataCode, isEqualTo(dataCode))
                .and(dataDictInfo.systemCode,isEqualTo(BizConstant.system_code))
                .and(dataDictInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<DataDictInfo> optional = dataDictInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }
}
