package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableTransferFixedApplyService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchTransferFixedRequest;
import com.dazhong.transportation.vlms.mapper.VehicleTransferFixedApplyMapper;
import com.dazhong.transportation.vlms.model.VehicleTransferFixedApply;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleTransferFixedApplyDynamicSqlSupport.vehicleTransferFixedApply;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableTransferFixedApplyServiceImpl implements TableTransferFixedApplyService {

    @Autowired
    private VehicleTransferFixedApplyMapper vehicleTransferFixedApplyMapper;

    @Override
    public List<VehicleTransferFixedApply> searchVehicleTransferFixedApply(SearchTransferFixedRequest request, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();
        SelectStatementProvider selectStatement = select(vehicleTransferFixedApply.allColumns())
                .from(vehicleTransferFixedApply)
                .where()
                .and(vehicleTransferFixedApply.isDeleted, isEqualTo(0))
                .and(vehicleTransferFixedApply.approvalNumber, isLikeWhenPresent(transFuzzyQueryParam(request.getApprovalNumber())))
                .and(vehicleTransferFixedApply.createOperName, isLikeWhenPresent(transFuzzyQueryParam(request.getCreateUser())))
                .and(vehicleTransferFixedApply.applyStatus, isInWhenPresent(request.getApplyStatus()).filter(ObjectValidUtil::isValid))
                .and(vehicleTransferFixedApply.applyOrgId, isInWhenPresent(orgIdList),or(vehicleTransferFixedApply.ownerId, isInWhenPresent(ownerIdList)))
                .orderBy(vehicleTransferFixedApply.updateTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleTransferFixedApplyMapper.selectMany(selectStatement);
    }

    @Override
    public VehicleTransferFixedApply selectById(Long id) {
        SelectStatementProvider selectStatement = select(vehicleTransferFixedApply.allColumns())
                .from(vehicleTransferFixedApply)
                .where()
                .and(vehicleTransferFixedApply.id, isEqualTo(id))
                .and(vehicleTransferFixedApply.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleTransferFixedApply> optional = vehicleTransferFixedApplyMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public VehicleTransferFixedApply queryVehicleTransferFixedApply(String applyNo) {
        SelectStatementProvider selectStatement = select(vehicleTransferFixedApply.allColumns())
                .from(vehicleTransferFixedApply)
                .where()
                .and(vehicleTransferFixedApply.applyNo, isEqualTo(applyNo))
                .and(vehicleTransferFixedApply.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleTransferFixedApply> optional = vehicleTransferFixedApplyMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public VehicleTransferFixedApply queryTransferFixedApplyByApprovalNumber(String approvalNumber) {
        SelectStatementProvider selectStatement = select(vehicleTransferFixedApply.allColumns())
                .from(vehicleTransferFixedApply)
                .where()
                .and(vehicleTransferFixedApply.approvalNumber, isEqualTo(approvalNumber))
                .and(vehicleTransferFixedApply.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleTransferFixedApply> optional = vehicleTransferFixedApplyMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public VehicleTransferFixedApply insert(VehicleTransferFixedApply vehicleTransferFixedApply, TokenUserInfo tokenUserInfo) {
        vehicleTransferFixedApply.setCreateOperId(tokenUserInfo.getUserId());
        vehicleTransferFixedApply.setCreateOperName(tokenUserInfo.getName());
        vehicleTransferFixedApply.setApplyUserNo(tokenUserInfo.getDingTalkNum());
        vehicleTransferFixedApply.setApplyUser(tokenUserInfo.getName());
        vehicleTransferFixedApplyMapper.insertSelective(vehicleTransferFixedApply);
        return vehicleTransferFixedApply;
    }

    @Override
    public int updateVehicleTransferFixedApply(VehicleTransferFixedApply vehicleTransferFixedApply,TokenUserInfo tokenUserInfo) {
        vehicleTransferFixedApply.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleTransferFixedApply.setUpdateOperName(tokenUserInfo.getName());
        vehicleTransferFixedApply.setUpdateTime(new Date());
        return vehicleTransferFixedApplyMapper.updateByPrimaryKeySelective(vehicleTransferFixedApply);
    }
}
