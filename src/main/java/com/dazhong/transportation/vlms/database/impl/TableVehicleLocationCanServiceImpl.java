package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableVehicleLocationCanService;
import com.dazhong.transportation.vlms.database.TableVehicleService;
import com.dazhong.transportation.vlms.dto.VehicleLocationCanInfoDto;
import com.dazhong.transportation.vlms.mapper.VehicleInfoMapper;
import com.dazhong.transportation.vlms.mapper.VehicleLocationCanInfoMapper;
import com.dazhong.transportation.vlms.model.LicensePlateTaskVehicleDetail;
import com.dazhong.transportation.vlms.model.VehicleInfo;
import com.dazhong.transportation.vlms.model.VehicleLocationCanInfo;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dazhong.transportation.vlms.mapper.VehicleLocationCanInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleLocationCanServiceImpl implements TableVehicleLocationCanService {

    @Autowired
    private VehicleLocationCanInfoMapper vehicleLocationCanInfoMapper;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<VehicleLocationCanInfoDto> request) {
        if(CollectionUtils.isEmpty(request)){
            return 0;
        }
        //统一走先删除再增加的逻辑
        Map<String,VehicleLocationCanInfoDto> vehicleLocationCanInfoDtoMap = request.stream()
                .collect(Collectors.toMap(m -> m.getVin(), m -> m));

        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehicleLocationCanInfo).where()
                .and(vehicleLocationCanInfo.vin, isIn(vehicleLocationCanInfoDtoMap.keySet()))
                .and(vehicleLocationCanInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        vehicleLocationCanInfoMapper.delete(deleteStatementProvider);

        List<String> vinList = new ArrayList<>();
        vehicleLocationCanInfoDtoMap.keySet().forEach(vin ->{
            vinList.add(vin);
        });
        List<VehicleInfo> vehicles = tableVehicleService.queryVehicleByVinList(vinList);

        Map<String,VehicleInfo> vehicleInfoMap = vehicles.stream().collect(Collectors.toMap(m -> m.getVin(),m-> m));

        List<VehicleLocationCanInfo> rows  = new ArrayList<>();
        for (VehicleLocationCanInfoDto canInfoDto : request) {
            VehicleLocationCanInfo row = new VehicleLocationCanInfo();
            row.setVin(canInfoDto.getVin());
            VehicleInfo vehicleInfo = vehicleInfoMap.get(canInfoDto.getVin());
            row.setLicensePlate(vehicleInfo != null ? vehicleInfo.getLicensePlate() :"");
            row.setTerminalNo(StringUtils.isNotBlank(canInfoDto.getTerminalNo()) ? canInfoDto.getTerminalNo() : "");
            row.setLatitude(StringUtils.isNotBlank(canInfoDto.getLatitude()) ? canInfoDto.getLatitude() : "");
            row.setLongitude(StringUtils.isNotBlank(canInfoDto.getLongitude()) ? canInfoDto.getLongitude() : "");
            row.setOilPercent(canInfoDto.getOilPercent() == null ? BigDecimal.ZERO : canInfoDto.getOilPercent());
            row.setElectricPercent(canInfoDto.getElectricPercent() == null ? BigDecimal.ZERO : canInfoDto.getElectricPercent());
            row.setMileage(canInfoDto.getMileage() == null ? BigDecimal.ZERO : canInfoDto.getMileage());
            row.setCollectionTime(StringUtils.isNotBlank(canInfoDto.getCollectionTime()) ? canInfoDto.getCollectionTime() : "");
            row.setTerminalChannel(StringUtils.isNotBlank(canInfoDto.getTerminalChannel()) ? canInfoDto.getTerminalChannel() : "");
            row.setIsDeleted(0);
            row.setCreateTime(new Date());
            row.setUpdateTime(new Date());
            row.setCreateOperId(-1L);
            row.setCreateOperName(BizConstant.SYSTEM_NAME);
            row.setUpdateOperId(-1L);
            row.setUpdateOperName(BizConstant.SYSTEM_NAME);
            rows.add(row);
        }
        return vehicleLocationCanInfoMapper.insertMultiple(rows);
    }
}
