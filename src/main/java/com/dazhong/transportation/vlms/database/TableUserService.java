package com.dazhong.transportation.vlms.database;

import java.util.List;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.request.SearchUserRequest;
import com.dazhong.transportation.vlms.model.UserInfo;

/**
 * 系统用户服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TableUserService extends BaseTableService<UserInfo, Long> {

    /**
     * 查询所有用户信息
     * @param request
     * @param userIdList
     * @return
     */
    List<UserInfo> queryUserList(SearchUserRequest request,List<Long> userIdList);

    /**
     * 保存用户信息
     * @param userInfo
     * @return
     */
    void saveUser(UserInfo userInfo);

    /**
     * 修改用户信息
     * @param userInfo
     * @return
     */
    int updateUser(UserInfo userInfo);

    /**
     * 删除用户信息
     * @param userId
     * @return
     */
    int deleteUser(Long userId);

    /**
     * 根据账号查询用户信息
     * @param userAccount
     * @return
     */
    UserInfo queryUserByUserAccount(String userAccount);

    /**
     * 基于钉钉账号获取用户列表
     * @param dingTalkNumList
     * @return
     */
    List<UserInfo> batchQueryByDingTalkNum(List<String> dingTalkNumList);
}
