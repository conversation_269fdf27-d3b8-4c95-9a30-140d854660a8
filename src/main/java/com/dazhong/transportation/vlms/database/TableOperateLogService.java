package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchLogRequest;
import com.dazhong.transportation.vlms.model.OperateLog;

import java.util.List;

public interface TableOperateLogService extends BaseTableService<OperateLog, Long> {

    List<OperateLog> searchOperateLog(SearchLogRequest request);
    /**
     * 批量保存日志
     * @param logList
     * @return
     */
    void batchInsertLog(List<OperateLog> logList, TokenUserInfo tokenUserInfo);


    /**
     * 保存日志
     * @param foreignId 外键
     * @param businessType 业务类型 业务类型 1-系统管理 2-组织架构 3-车辆采购
     * @param operateType 操作类型 -自定义
     * @param operateContent 操作内容
     * @param tokenUserInfo 操作人
     */
    void insertLog(Long foreignId, Integer businessType, Integer operateType, String operateContent, TokenUserInfo tokenUserInfo);

}
