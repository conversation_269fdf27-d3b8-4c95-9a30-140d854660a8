package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dazhong.transportation.vlms.database.TableUserOrgService;
import com.dazhong.transportation.vlms.mapper.UserOrgInfoMapper;
import com.dazhong.transportation.vlms.model.UserOrgInfo;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.UserOrgInfoDynamicSqlSupport.userOrgInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableUserOrgServiceImpl implements TableUserOrgService {

    @Autowired
    private UserOrgInfoMapper userOrgInfoMapper;

    @Override
    public List<Long> queryOrgIdList(Long userId,Integer type) {
        SelectStatementProvider provider = select(userOrgInfo.allColumns())
                .from(userOrgInfo)
                .where()
                .and(userOrgInfo.userId, isEqualTo(userId))
                .and(userOrgInfo.orgType, isEqualToWhenPresent(type))
                .and(userOrgInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<UserOrgInfo> userOrgInfos = userOrgInfoMapper.selectMany(provider);
        if (CollectionUtil.isEmpty(userOrgInfos)){
            return Collections.emptyList();
        }
        List<Long> list = new ArrayList<>();
        userOrgInfos.forEach(userOrgInfo -> {
            list.add(userOrgInfo.getOrgId());
        });
        return list;
    }

    @Override
    public List<Long> queryUserIdList(Long orgId,Integer type) {
        SelectStatementProvider provider = select(userOrgInfo.allColumns())
                .from(userOrgInfo)
                .where()
                .and(userOrgInfo.orgId, isEqualTo(orgId))
                .and(userOrgInfo.orgType, isEqualTo(type))
                .and(userOrgInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<UserOrgInfo> userOrgInfos = userOrgInfoMapper.selectMany(provider);
        if (CollectionUtil.isEmpty(userOrgInfos)){
            return Collections.emptyList();
        }
        List<Long> list = new ArrayList<>();
        userOrgInfos.forEach(userOrgInfo -> {
            list.add(userOrgInfo.getUserId());
        });
        return list;
    }

    @Override
    public List<UserOrgInfo> queryUserOrgInfoByUserId(Long userId) {
        SelectStatementProvider provider = select(userOrgInfo.allColumns())
                .from(userOrgInfo)
                .where()
                .and(userOrgInfo.userId, isEqualTo(userId))
                .and(userOrgInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userOrgInfoMapper.selectMany(provider);
    }

    @Override
    public int batchInsert(List<UserOrgInfo> list) {
        if (CollectionUtil.isNotEmpty(list)){
            return userOrgInfoMapper.insertMultiple(list);
        }
        return 1;
    }

    @Override
    public int deleteByUserId(Long userId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(userOrgInfo).where()
                .and(userOrgInfo.userId, isEqualTo(userId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userOrgInfoMapper.delete(deleteStatementProvider);
    }
}
