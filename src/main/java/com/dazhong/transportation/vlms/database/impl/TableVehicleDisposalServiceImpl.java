package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleDisposalService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleDisposalDetailListDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.mapper.VehicleDisposalMapper;
import com.dazhong.transportation.vlms.model.VehicleDisposal;
import com.dazhong.transportation.vlms.service.impl.UserServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.QueryExpressionDSL;
import org.mybatis.dynamic.sql.select.SelectModel;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleDisposalDetailDynamicSqlSupport.vehicleDisposalDetail;
import static com.dazhong.transportation.vlms.mapper.VehicleDisposalDynamicSqlSupport.vehicleDisposal;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleDisposalServiceImpl implements TableVehicleDisposalService {

    @Autowired
    VehicleDisposalMapper vehicleDisposalMapper;

    @Override
    public VehicleDisposal selectById(Long id) {
        return vehicleDisposalMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int updateSelectiveById(VehicleDisposal vehicleDisposal) {
        return vehicleDisposalMapper.updateByPrimaryKeySelective(vehicleDisposal);
    }

    @Override
    public VehicleDisposal insert(VehicleDisposal vehicleDisposal, TokenUserInfo tokenUserInfo) {
        vehicleDisposal.setCreateTime(new Date());
        vehicleDisposal.setCreateOperId(tokenUserInfo.getUserId());
        vehicleDisposal.setCreateOperName(tokenUserInfo.getName());
        vehicleDisposalMapper.insertSelective(vehicleDisposal);
        return vehicleDisposal;
    }

    @Override
    public int updateSelectiveById(VehicleDisposal vehicleDisposal, TokenUserInfo tokenUserInfo) {
        vehicleDisposal.setUpdateTime(new Date());
        vehicleDisposal.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleDisposal.setUpdateOperName(tokenUserInfo.getName());
        return vehicleDisposalMapper.updateByPrimaryKeySelective(vehicleDisposal);
    }

    @Override
    public List<VehicleDisposalListDto> queryVehicleDisposalList(SearchVehicleDisposalListRequest searchVehicleDisposalListRequest, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();

        QueryExpressionDSL<SelectModel>.QueryExpressionWhereBuilder builder = select(vehicleDisposal.allColumns())
            .from(vehicleDisposal)
            .leftJoin(vehicleDisposalDetail).on(vehicleDisposal.id, equalTo(vehicleDisposalDetail.disposalId))
            .where()
            .and(vehicleDisposal.dingTalkNo, isLikeWhenPresent(transFuzzyQueryParam(searchVehicleDisposalListRequest.getDingTalkNo())).filter(StringUtils::isNotBlank))
            .and(vehicleDisposal.createOperName, isLikeWhenPresent(transFuzzyQueryParam(searchVehicleDisposalListRequest.getCreateOperName())).filter(StringUtils::isNotBlank))
            .and(vehicleDisposal.documentStatus, isEqualToWhenPresent(searchVehicleDisposalListRequest.getDocumentStatus()))
            .and(vehicleDisposal.documentStatus, isInWhenPresent(searchVehicleDisposalListRequest.getDocumentStatusList()))
            .and(vehicleDisposalDetail.vin, isEqualToWhenPresent(searchVehicleDisposalListRequest.getVin()).filter(StringUtils::isNotBlank))
            .and(vehicleDisposalDetail.licensePlate, isInWhenPresent(searchVehicleDisposalListRequest.getLicensePlate()).filter(StringUtils::isNotBlank));

        // isBusinessSale = 0：非商务出售
        if (searchVehicleDisposalListRequest.getIsBusinessSale() != null &&
                searchVehicleDisposalListRequest.getIsBusinessSale().equals(0)) {
            builder.and(vehicleDisposal.documentType, isIn(Arrays.asList(1, 2)))
                    .and(vehicleDisposal.organizationId, isInWhenPresent(orgIdList))
                    .and(vehicleDisposal.ownerId, isInWhenPresent(ownerIdList));
        }

        // isBusinessSale = 1：商务出售
        if (searchVehicleDisposalListRequest.getIsBusinessSale() != null &&
                searchVehicleDisposalListRequest.getIsBusinessSale().equals(1)) {
            builder.and(vehicleDisposal.documentType, isEqualTo(3))
                    .and(vehicleDisposal.createOperId, isEqualTo(tokenUserInfo.getUserId()));
        }

        SelectStatementProvider provider = builder.groupBy(vehicleDisposal.id)
                .orderBy(vehicleDisposal.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return vehicleDisposalMapper.selectVehicleDisposalList(provider);
    }

    @Override
    public VehicleDisposal selectByDingTalkNo(String dingTalkNo) {
        return vehicleDisposalMapper.selectByDingTalkNo(dingTalkNo).orElse(null);
    }

    @Override
    public VehicleDisposal selectByDocumentNo(String documentNo) {
        return vehicleDisposalMapper.selectByDocumentNo(documentNo).orElse(null);
    }

    @Override
    public List<VehicleDisposalDetailListDto> queryVehicleDisposalList(String vin) {
        SelectStatementProvider provider = select(
                vehicleDisposal.documentNo,
                vehicleDisposal.documentType,
                vehicleDisposal.dingTalkNo,
                vehicleDisposal.submitDate,
                vehicleDisposal.documentStatus,
                vehicleDisposal.sellingCompanyName,
                vehicleDisposalDetail.originalValue,
                vehicleDisposalDetail.accumulatedDepreciation,
                vehicleDisposalDetail.netValue,
                vehicleDisposalDetail.actualSellingPrice,
                vehicleDisposalDetail.saleGainLoss
        )
                .from(vehicleDisposal)
                .leftJoin(vehicleDisposalDetail).on(vehicleDisposal.id, equalTo(vehicleDisposalDetail.disposalId))
                .where()
                .and(vehicleDisposalDetail.vin, isEqualTo(vin))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleDisposalMapper.selectVehicleDisposalByVin(provider);
    }
}
