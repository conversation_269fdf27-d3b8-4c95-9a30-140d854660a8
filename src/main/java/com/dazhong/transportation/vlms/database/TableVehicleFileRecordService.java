package com.dazhong.transportation.vlms.database;

import java.util.List;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehicleFileRecord;

/**
 * 车辆附件记录表服务
 * <AUTHOR>
 * @date 2025-02-08 10:37
 */
public interface TableVehicleFileRecordService extends BaseTableService<VehicleFileRecord, Long> {


    /**
     * 根据车架号查询车辆附件信息
     * @param vin
     * @return
     */
    List<VehicleFileRecord> queryVehicleFileRecord(String vin);

}
