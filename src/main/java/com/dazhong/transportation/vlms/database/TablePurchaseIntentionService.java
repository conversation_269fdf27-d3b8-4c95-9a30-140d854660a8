package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.request.SearchPurchaseIntentionRequest;
import com.dazhong.transportation.vlms.model.VehiclePurchaseIntention;

import java.util.List;

/**
 * 车辆采购意向表服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface TablePurchaseIntentionService extends BaseTableService<VehiclePurchaseIntention, Long> {

    /**
     * 查询采购意向列表
     * @param request
     * @param orgIdList
     * @return
     */
    List<VehiclePurchaseIntention> queryPurchaseIntention(SearchPurchaseIntentionRequest request, List<Long> orgIdList);

    /**
     * 查询采购意向详情
     * @param intentionNo
     * @return
     */
    VehiclePurchaseIntention queryPurchaseIntention(String intentionNo);

    /**
     * 查询采购意向详情
     * @param purchaseApplyId
     * @return
     */
    VehiclePurchaseIntention queryPurchaseIntention(Long purchaseApplyId);

    /**
     * 更新采购意向
     * @param vehiclePurchaseIntention
     * @return
     */
    int updatePurchaseIntention(VehiclePurchaseIntention vehiclePurchaseIntention);

    /**
     * 新增采购意向
     * @param vehiclePurchaseIntention
     * @return
     */
    int insertPurchaseIntention(VehiclePurchaseIntention vehiclePurchaseIntention);

    /**
     * 根据合同号查询
     * @param contractNo
     * @return
     */
    VehiclePurchaseIntention queryByContractNo(String contractNo);

}
