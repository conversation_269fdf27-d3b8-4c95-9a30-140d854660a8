package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.database.TableOperateLogService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchLogRequest;
import com.dazhong.transportation.vlms.mapper.OperateLogMapper;
import com.dazhong.transportation.vlms.model.OperateLog;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.OperateLogDynamicSqlSupport.operateLog;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableOperateLogServiceImpl implements TableOperateLogService {

    @Autowired
    private OperateLogMapper operateLogMapper;


    @Override
    public List<OperateLog> searchOperateLog(SearchLogRequest request) {
        if (StringUtils.isNotBlank(request.getOperateBeginDate())){
            request.setOperateBeginDate(request.getOperateBeginDate()+ " 00:00:00");
        }
        if (StringUtils.isNotBlank(request.getOperateEndDate())){
            request.setOperateEndDate(request.getOperateEndDate()+ " 23:59:59");
        }
        SelectStatementProvider selectStatement = select(operateLog.allColumns())
                .from(operateLog)
                .where()
                .and(operateLog.businessType, isEqualTo(request.getBusinessType()))
                .and(operateLog.operateType, isEqualToWhenPresent(request.getOperateType()).filter(ObjectValidUtil::isValid))
                .and(operateLog.createOperName, isLikeWhenPresent(transFuzzyQueryParam(request.getOperateUser())).filter(StringUtils::isNotBlank))
                .and(operateLog.foreignId, isEqualToWhenPresent(request.getForeignId()).filter(ObjectValidUtil::isValid))
                .and(operateLog.createTime, isGreaterThanOrEqualToWhenPresent(DateTimeUtils.stringToDate(request.getOperateBeginDate(), DateTimeUtils.DATE_TYPE1)).filter(ObjectValidUtil::isValid))
                .and(operateLog.createTime, isLessThanOrEqualToWhenPresent(DateTimeUtils.stringToDate(request.getOperateEndDate(), DateTimeUtils.DATE_TYPE1)).filter(ObjectValidUtil::isValid))
                .and(operateLog.isDeleted, isEqualTo(0))
                .orderBy(operateLog.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return operateLogMapper.selectMany(selectStatement);
    }

    @Override
    public void batchInsertLog(List<OperateLog> logList, TokenUserInfo tokenUserInfo) {
        if (CollUtil.isNotEmpty(logList)) {
            logList.forEach(log -> {
                String operateContent = log.getOperateContent();
                if (StringUtils.isNotBlank(operateContent) && operateContent.length() > 2048) {
                    log.setOperateContent(operateContent.substring(0, 2040) + "......");
                }
                log.setCreateOperId(tokenUserInfo.getUserId());
                log.setCreateOperName(tokenUserInfo.getName());
            });
            operateLogMapper.insertMultiple(logList);
        }
    }

    @Override
    public void insertLog(Long foreignId, Integer businessType, Integer operateType, String operateContent, TokenUserInfo tokenUserInfo) {
        OperateLog log = new OperateLog();
        log.setForeignId(foreignId);
        log.setBusinessType(businessType);
        if (operateType != null) {
            log.setOperateType(operateType);
        }
        log.setOperateContent(operateContent);
        if (StringUtils.isNotBlank(operateContent) && operateContent.length() > 2048) {
            log.setOperateContent(operateContent.substring(0, 2040) + "......");
        }
        log.setCreateOperId(tokenUserInfo.getUserId());
        log.setCreateOperName(tokenUserInfo.getName());
        operateLogMapper.insertSelective(log);
    }
}
