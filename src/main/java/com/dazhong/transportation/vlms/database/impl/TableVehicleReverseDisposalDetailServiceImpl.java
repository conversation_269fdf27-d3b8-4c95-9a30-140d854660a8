package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleReverseDisposalDetailService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleReverseDisposalDetailMapper;
import com.dazhong.transportation.vlms.model.VehicleReverseDisposalDetail;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleReverseDisposalDetailDynamicSqlSupport.vehicleReverseDisposalDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.delete.DeleteDSL.deleteFrom;

@Service
public class TableVehicleReverseDisposalDetailServiceImpl implements TableVehicleReverseDisposalDetailService {

    @Autowired
    private VehicleReverseDisposalDetailMapper vehicleReverseDisposalDetailMapper;

    @Override
    public void batchInsert(List<VehicleReverseDisposalDetail> vehicleReverseDisposalDetailList, TokenUserInfo tokenUserInfo) {
        for (VehicleReverseDisposalDetail vehicleReverseDisposalDetail : vehicleReverseDisposalDetailList) {
            vehicleReverseDisposalDetail.setCreateTime(new Date());
            vehicleReverseDisposalDetail.setCreateOperId(tokenUserInfo.getUserId());
            vehicleReverseDisposalDetail.setCreateOperName(tokenUserInfo.getName());
        }
        vehicleReverseDisposalDetailMapper.insertMultiple(vehicleReverseDisposalDetailList);
    }

    @Override
    public void deleteByReverseDisposalId(Long reverseDisposalId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehicleReverseDisposalDetail)
                .where()
                .and(vehicleReverseDisposalDetail.reverseDisposalId, isEqualTo(reverseDisposalId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        vehicleReverseDisposalDetailMapper.delete(deleteStatementProvider);
    }

    @Override
    public List<VehicleReverseDisposalDetail> getVehicleReverseDisposalDetailListByReverseDisposalId(Long reverseDisposalId) {
        SelectStatementProvider selectStatement = select(vehicleReverseDisposalDetail.allColumns())
                .from(vehicleReverseDisposalDetail)
                .where()
                .and(vehicleReverseDisposalDetail.reverseDisposalId, isEqualTo(reverseDisposalId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleReverseDisposalDetailMapper.selectMany(selectStatement);
    }
}
