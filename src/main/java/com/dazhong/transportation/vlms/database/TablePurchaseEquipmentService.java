package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.VehiclePurchaseEquipment;

import java.util.List;

/**
 * 采购车载设备表服务
 * <AUTHOR>
 * @date 2025-01-06 10:37
 */
public interface TablePurchaseEquipmentService extends BaseTableService<VehiclePurchaseEquipment, Long> {

    /**
     * 查询
     * @param purchaseApplyId
     * @return
     */
    List<VehiclePurchaseEquipment> queryPurchaseEquipmentList(Long purchaseApplyId);


    /**
     * 新增
     * @param purchaseEquipment
     * @return
     */
    int savePurchaseEquipment(VehiclePurchaseEquipment purchaseEquipment);


    /**
     * 删除
     * @param purchaseApplyId
     * @return
     */
    int deletePurchaseEquipment(Long purchaseApplyId);
}
