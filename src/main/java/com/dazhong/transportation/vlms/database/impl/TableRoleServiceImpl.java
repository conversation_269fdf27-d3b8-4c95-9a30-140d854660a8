package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.util.ObjUtil;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableRoleService;
import com.dazhong.transportation.vlms.mapper.RoleInfoMapper;
import com.dazhong.transportation.vlms.model.RoleInfo;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.RoleInfoDynamicSqlSupport.roleInfo;
import static com.dazhong.transportation.vlms.mapper.UserRoleInfoDynamicSqlSupport.userRoleInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableRoleServiceImpl implements TableRoleService {

    @Autowired
    private RoleInfoMapper roleInfoMapper;


    @Override
    public RoleInfo selectById(Long id) {
        SelectStatementProvider selectStatement = select(roleInfo.allColumns())
                .from(roleInfo)
                .where(roleInfo.id, isEqualTo(id))
                .and(roleInfo.systemCode, isEqualTo(BizConstant.system_code))
                .and(roleInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<RoleInfo> optional = roleInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public RoleInfo queryRoleInfoByName(String roleName) {
        SelectStatementProvider selectStatement = select(roleInfo.allColumns())
                .from(roleInfo)
                .where()
                .and(roleInfo.roleName, isEqualTo(roleName))
                .and(roleInfo.systemCode, isEqualTo(BizConstant.system_code))
                .and(roleInfo.isDeleted, isEqualTo(0))
                .orderBy(roleInfo.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<RoleInfo> optional = roleInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int addRoleInfo(RoleInfo info) {
        return roleInfoMapper.insertSelective(info);
    }

    @Override
    public int updateRoleInfo(RoleInfo info) {
        info.setUpdateTime(new Date());
        return roleInfoMapper.updateByPrimaryKeySelective(info);
    }

    @Override
    public int deleteRoleInfo(Long id) {
        return roleInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<RoleInfo> queryAllRoleList() {
        SelectStatementProvider provider = select(roleInfo.allColumns())
                .from(roleInfo)
                .where()
                .and(roleInfo.isDeleted, isEqualTo(0))
                .orderBy(roleInfo.updateTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return roleInfoMapper.selectMany(provider);
    }

    @Override
    public List<RoleInfo> queryAllRoleListByUserId(Long userId) {
        SelectStatementProvider provider = select(roleInfo.allColumns())
                .from(userRoleInfo)
                .leftJoin(roleInfo).on(userRoleInfo.roleId,equalTo(roleInfo.id))
                .where()
                .and(userRoleInfo.userId, isEqualTo(userId))
                .and(userRoleInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<RoleInfo> roleInfos = roleInfoMapper.selectMany(provider);
        return roleInfos;
    }
}
