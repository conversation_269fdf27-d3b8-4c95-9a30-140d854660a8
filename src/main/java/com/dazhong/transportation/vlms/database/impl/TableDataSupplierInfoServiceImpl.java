package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableDataSupplierInfoService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.DataSupplierInfoMapper;
import com.dazhong.transportation.vlms.model.DataSupplierInfo;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.DataSupplierInfoDynamicSqlSupport.dataSupplierInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableDataSupplierInfoServiceImpl implements TableDataSupplierInfoService {

    @Autowired
    private DataSupplierInfoMapper dataSupplierInfoMapper;

    @Override
    public DataSupplierInfo insert(DataSupplierInfo dataSupplierInfo) {
        Date now = new Date();
        dataSupplierInfo.setCreateTime(now);
        dataSupplierInfo.setUpdateTime(now);
        dataSupplierInfoMapper.insertSelective(dataSupplierInfo);
        return dataSupplierInfo;
    }

    @Override
    public DataSupplierInfo insert(DataSupplierInfo dataSupplierInfo, String operator) {
        dataSupplierInfo.setCreateOperId(-1L);
        dataSupplierInfo.setCreateOperName(operator);
        dataSupplierInfo.setUpdateOperId(-1L);
        dataSupplierInfo.setUpdateOperName(operator);
        return this.insert(dataSupplierInfo);
    }

    @Override
    public DataSupplierInfo insert(DataSupplierInfo dataSupplierInfo, TokenUserInfo tokenUserInfo) {
        dataSupplierInfo.setCreateOperId(tokenUserInfo.getUserId());
        dataSupplierInfo.setCreateOperName(tokenUserInfo.getName());
        dataSupplierInfo.setUpdateOperId(tokenUserInfo.getUserId());
        dataSupplierInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(dataSupplierInfo);
    }


    @Override
    public int updateSelectiveById(DataSupplierInfo dataSupplierInfo) {
        dataSupplierInfo.setUpdateTime(new Date());
        return dataSupplierInfoMapper.updateByPrimaryKeySelective(dataSupplierInfo);
    }

    @Override
    public int updateSelectiveById(DataSupplierInfo dataSupplierInfo, String operator) {
        dataSupplierInfo.setUpdateOperId(-1L);
        dataSupplierInfo.setUpdateOperName(operator);
        return this.updateSelectiveById(dataSupplierInfo);
    }

    @Override
    public int updateSelectiveById(DataSupplierInfo dataSupplierInfo, TokenUserInfo tokenUserInfo) {
        dataSupplierInfo.setUpdateOperId(tokenUserInfo.getUserId());
        dataSupplierInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.updateSelectiveById(dataSupplierInfo);
    }

    @Override
    public List<DataSupplierInfo> queryAllSupplier() {
        SelectStatementProvider selectStatement = select(dataSupplierInfo.allColumns())
                .from(dataSupplierInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataSupplierInfoMapper.selectMany(selectStatement);
    }

    @Override
    public boolean hasDuplicateSupplierName(String supplierName, Long excludeId) {
        if(StringUtils.isBlank(supplierName)){
            return true;
        }

        SelectStatementProvider selectStatement = select(dataSupplierInfo.allColumns())
                .from(dataSupplierInfo)
                .where(dataSupplierInfo.id, isNotEqualToWhenPresent(excludeId))
                .and(dataSupplierInfo.name, isEqualTo(supplierName))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        DataSupplierInfo duplicateSupplierInfo = dataSupplierInfoMapper.selectOne(selectStatement).orElse(null);
        return duplicateSupplierInfo != null;
    }
}
