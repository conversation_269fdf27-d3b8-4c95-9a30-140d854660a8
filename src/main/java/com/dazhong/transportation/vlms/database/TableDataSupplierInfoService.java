package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.model.DataSupplierInfo;

import java.util.List;

public interface TableDataSupplierInfoService extends BaseTableService<DataSupplierInfo, Long> {

    /**
     * 查询所有供应商信息
     * @return 供应商列表
     */
    List<DataSupplierInfo> queryAllSupplier();


    /**
     * 是否存在重复的经销商名称
     * @param supplierName 判断的经销商名称
     * @param excludeId 需要排除的供应商ID
     * @return true:已经存在  false:不存在
     */
    boolean hasDuplicateSupplierName(String supplierName, Long excludeId);
}
