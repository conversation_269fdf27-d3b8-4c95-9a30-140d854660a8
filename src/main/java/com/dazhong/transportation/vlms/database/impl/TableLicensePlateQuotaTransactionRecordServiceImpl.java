package com.dazhong.transportation.vlms.database.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import com.dazhong.transportation.vlms.database.TableLicensePlateQuotaTransactionRecordService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaTransactionRecordRequest;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.mapper.LicensePlateQuotaTransactionRecordMapper;
import com.dazhong.transportation.vlms.model.LicensePlateQuotaTransactionRecord;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaTransactionRecordDynamicSqlSupport.licensePlateQuotaTransactionRecord;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
@Slf4j
public class TableLicensePlateQuotaTransactionRecordServiceImpl implements TableLicensePlateQuotaTransactionRecordService {

    @Autowired
    LicensePlateQuotaTransactionRecordMapper licensePlateQuotaTransactionRecordMapper;

    @Override
    public LicensePlateQuotaTransactionRecord selectById(Long id) {
        return licensePlateQuotaTransactionRecordMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public LicensePlateQuotaTransactionRecord insert(LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord, TokenUserInfo tokenUserInfo) {
        licensePlateQuotaTransactionRecord.setCreateOperId(tokenUserInfo.getUserId());
        licensePlateQuotaTransactionRecord.setCreateOperName(tokenUserInfo.getName());
        licensePlateQuotaTransactionRecord.setTaskNumber("");
        licensePlateQuotaTransactionRecord.setLicensePlate("");
        licensePlateQuotaTransactionRecordMapper.insertSelective(licensePlateQuotaTransactionRecord);
        return licensePlateQuotaTransactionRecord;
    }

    @Override
    public int updateSelectiveById(LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord, TokenUserInfo tokenUserInfo) {
        licensePlateQuotaTransactionRecord.setUpdateOperId(tokenUserInfo.getUserId());
        licensePlateQuotaTransactionRecord.setUpdateOperName(tokenUserInfo.getName());
        licensePlateQuotaTransactionRecord.setUpdateTime(new Date());
        return licensePlateQuotaTransactionRecordMapper.updateByPrimaryKeySelective(licensePlateQuotaTransactionRecord);
    }

    @Override
    public int updateTaskNumber(LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord, TokenUserInfo tokenUserInfo) {
        licensePlateQuotaTransactionRecord.setUpdateOperId(tokenUserInfo.getUserId());
        licensePlateQuotaTransactionRecord.setUpdateOperName(tokenUserInfo.getName());
        licensePlateQuotaTransactionRecord.setUpdateTime(new Date());
        return licensePlateQuotaTransactionRecordMapper.updateTaskNumber(licensePlateQuotaTransactionRecord);
    }

    @Override
    public List<LicensePlateQuotaTransactionRecord> queryList(SearchLicensePlateQuotaTransactionRecordRequest searchLicensePlateQuotaTransactionRecordRequest) {
        SelectStatementProvider provider = select(licensePlateQuotaTransactionRecord.allColumns())
                .from(licensePlateQuotaTransactionRecord)
                .where()
                .and(licensePlateQuotaTransactionRecord.licensePlate, isEqualToWhenPresent(searchLicensePlateQuotaTransactionRecordRequest.getLicensePlate()).filter(StringUtils::isNotBlank))
                .and(licensePlateQuotaTransactionRecord.quotaNumber, isEqualToWhenPresent(searchLicensePlateQuotaTransactionRecordRequest.getQuotaNumber()).filter(StringUtils::isNotBlank))
                .and(licensePlateQuotaTransactionRecord.quotaPrintDate, isBetweenWhenPresent(searchLicensePlateQuotaTransactionRecordRequest.getQuotaPrintDateStart()).and(searchLicensePlateQuotaTransactionRecordRequest.getQuotaPrintDateEnd()))
                .and(licensePlateQuotaTransactionRecord.quotaType, isEqualToWhenPresent(searchLicensePlateQuotaTransactionRecordRequest.getQuotaType()))
                .orderBy(licensePlateQuotaTransactionRecord.createTime.descending(), licensePlateQuotaTransactionRecord.id)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateQuotaTransactionRecordMapper.selectMany(provider);
    }

    @Override
    public List<LicensePlateQuotaTransactionRecord> exportList(Long index, SearchLicensePlateQuotaTransactionRecordRequest request) {
        SelectStatementProvider provider = select(licensePlateQuotaTransactionRecord.allColumns())
                .from(licensePlateQuotaTransactionRecord)
                .where()
                .and(licensePlateQuotaTransactionRecord.licensePlate, isEqualToWhenPresent(request.getLicensePlate()).filter(StringUtils::isNotBlank))
                .and(licensePlateQuotaTransactionRecord.quotaNumber, isEqualToWhenPresent(request.getQuotaNumber()).filter(StringUtils::isNotBlank))
                .and(licensePlateQuotaTransactionRecord.quotaPrintDate, isBetweenWhenPresent(request.getQuotaPrintDateStart()).and(request.getQuotaPrintDateEnd()))
                .and(licensePlateQuotaTransactionRecord.quotaType, isEqualToWhenPresent(request.getQuotaType()))
                //根据ID排序
                .and(licensePlateQuotaTransactionRecord.id, isGreaterThanWhenPresent(index))
                .orderBy(licensePlateQuotaTransactionRecord.createTime.descending(), licensePlateQuotaTransactionRecord.id)
                .limit(1000)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return licensePlateQuotaTransactionRecordMapper.selectMany(provider);
    }

    @Override
    public ComboResponse<String, String> queryQuotaNumberCombo(String quotaNumber) {
        if (StrUtil.isNotBlank(quotaNumber)) {
            quotaNumber = StrUtil.format("%{}%", quotaNumber);
        }

        ComboResponse<String, String> result = new ComboResponse<>();
        SelectStatementProvider provider = select(licensePlateQuotaTransactionRecord.allColumns())
                .from(licensePlateQuotaTransactionRecord)
                .where()
                .and(licensePlateQuotaTransactionRecord.quotaNumber, isLikeWhenPresent(quotaNumber).filter(StringUtils::isNotBlank))
                .and(licensePlateQuotaTransactionRecord.taskNumber, isEqualTo(StringUtils.EMPTY))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<LicensePlateQuotaTransactionRecord> licensePlateQuotaTransactionRecords = licensePlateQuotaTransactionRecordMapper.selectMany(provider);

        if (CollectionUtil.isEmpty(licensePlateQuotaTransactionRecords)) {
            return result;
        }

        for (LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord : licensePlateQuotaTransactionRecords) {
            String key = licensePlateQuotaTransactionRecord.getQuotaNumber();
            String value = DateUtil.format(licensePlateQuotaTransactionRecord.getQuotaPrintDate(), "yyyy-MM-dd");
            ComboResponse.ComboData<String, String> comboData = new ComboResponse.ComboData<>(key, value);
            result.getList().add(comboData);
        }

        return result;
    }

    @Override
    public LicensePlateQuotaTransactionRecord selectByQuotaNumberAndQuotaPrintDate(String quotaNumber, Date quotaPrintDate) {
        SelectStatementProvider provider = select(licensePlateQuotaTransactionRecord.allColumns())
                .from(licensePlateQuotaTransactionRecord)
                .where()
                .and(licensePlateQuotaTransactionRecord.quotaNumber, isEqualTo(quotaNumber))
                .and(licensePlateQuotaTransactionRecord.quotaPrintDate, isEqualTo(quotaPrintDate))
                .and(licensePlateQuotaTransactionRecord.taskNumber, isEqualTo(StringUtils.EMPTY))
                .orderBy(licensePlateQuotaTransactionRecord.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        // 修改日志输出，添加参数信息
        log.info("Generated SQL: {} with parameters: [quotaNumber={}, quotaPrintDate={}]", 
                provider.getSelectStatement(), quotaNumber, quotaPrintDate);
        return licensePlateQuotaTransactionRecordMapper.selectOne(provider).orElse(null);
    }
}
