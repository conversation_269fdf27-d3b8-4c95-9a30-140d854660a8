package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.model.LicensePlateTaskInfo;

import java.util.List;

/**
 * 车牌任务信息表服务
 *
 * <AUTHOR>
 * @date 2025/01/06
 */
public interface TableLicensePlateTaskInfoService extends BaseTableService<LicensePlateTaskInfo, Long> {

    /**
     * 查询车牌任务列表
     *
     * @param searchLicensePlateTaskInfoRequest 车牌任务列表入参
     * @return 返回车牌任务列表
     */
    List<LicensePlateTaskVehicleDetailDto> queryList(SearchLicensePlateTaskInfoRequest searchLicensePlateTaskInfoRequest);

    /**
     * 查询车牌任务列表
     *
     * @param taskNumber 车牌任务编号
     * @return 返回车牌任务详情
     */
    LicensePlateTaskInfo selectByTaskNumber(String taskNumber);

    /**
     * 查询车牌任务列表
     *
     * @param vin 车架号
     * @return 返回车牌任务详情
     */
    LicensePlateTaskVehicleDetailDto selectLatestVehicleDetailByVin(String vin);
}
