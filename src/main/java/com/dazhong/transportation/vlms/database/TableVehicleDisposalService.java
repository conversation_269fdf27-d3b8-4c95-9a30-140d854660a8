package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleDisposalDetailListDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.model.VehicleDisposal;

import java.util.List;

public interface TableVehicleDisposalService extends BaseTableService<VehicleDisposal, Long> {

    /**
     * 新增车辆处置
     *
     * @param vehicleDisposal 插入对象
     * @param tokenUserInfo   操作人信息
     * @return
     */
    VehicleDisposal insert(VehicleDisposal vehicleDisposal, TokenUserInfo tokenUserInfo);

    /**
     * 查询车辆处置列表
     *
     * @param searchVehicleDisposalListRequest 查询车辆处置列表入参
     * @return 返回车辆处置列表
     */
    List<VehicleDisposalListDto> queryVehicleDisposalList(SearchVehicleDisposalListRequest searchVehicleDisposalListRequest, TokenUserInfo tokenUserInfo);

    /**
     * 根据钉钉流水号查询车辆处置
     *
     * @param dingTalkNo 钉钉审批单号
     * @return
     */
    VehicleDisposal selectByDingTalkNo(String dingTalkNo);

    /**
     * 根据单据号查询车辆处置
     *
     * @param documentNo 钉钉审批单号
     * @return
     */
    VehicleDisposal selectByDocumentNo(String documentNo);

    /**
     * 查询车辆处置列表
     *
     * @param vin 车架号
     * @return 返回车辆处置列表
     */
    List<VehicleDisposalDetailListDto> queryVehicleDisposalList(String vin);

}
