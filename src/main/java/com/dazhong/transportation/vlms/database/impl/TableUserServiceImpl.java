package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableUserService;
import com.dazhong.transportation.vlms.dto.request.SearchUserRequest;
import com.dazhong.transportation.vlms.mapper.UserInfoMapper;
import com.dazhong.transportation.vlms.model.UserInfo;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.UserInfoDynamicSqlSupport.userInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class TableUserServiceImpl implements TableUserService {

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Override
    public UserInfo selectById(Long id) {
        SelectStatementProvider selectStatement = select(userInfo.allColumns())
                .from(userInfo)
                .where(userInfo.id, isEqualTo(id))
                .and(userInfo.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<UserInfo> optional = userInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public List<UserInfo> queryUserList(SearchUserRequest request, List<Long> userIdList) {
        SelectStatementProvider provider = select(userInfo.allColumns())
                .from(userInfo)
                .where()
                .and(userInfo.userAccount, isLikeWhenPresent(transFuzzyQueryParam(request.getUserAccount())))
                .and(userInfo.name, isLikeWhenPresent(transFuzzyQueryParam(request.getName())))
                .and(userInfo.id, isInWhenPresent(userIdList))
                .and(userInfo.systemCode, isEqualTo(BizConstant.system_code))
                .and(userInfo.isDeleted, isEqualTo(0))
                .orderBy(userInfo.updateTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userInfoMapper.selectMany(provider);
    }

    @Override
    public void saveUser(UserInfo userInfo) {
        userInfoMapper.insertSelective(userInfo);
    }

    @Override
    public int updateUser(UserInfo userInfo) {
        userInfo.setUpdateTime(new Date());
        return userInfoMapper.updateByPrimaryKeySelective(userInfo);
    }

    @Override
    public int deleteUser(Long userId) {
        return userInfoMapper.deleteByPrimaryKey(userId);
    }

    @Override
    public UserInfo queryUserByUserAccount(String userAccount) {
        SelectStatementProvider selectStatement = select(userInfo.allColumns())
                .from(userInfo)
                .where(userInfo.userAccount, isEqualTo(userAccount))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<UserInfo> optional = userInfoMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public List<UserInfo> batchQueryByDingTalkNum(List<String> dingTalkNumList) {
        SelectStatementProvider selectStatement = select(userInfo.allColumns())
                .from(userInfo)
                .where(userInfo.dingTalkNum, isIn(dingTalkNumList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return userInfoMapper.selectMany(selectStatement);
    }
}
