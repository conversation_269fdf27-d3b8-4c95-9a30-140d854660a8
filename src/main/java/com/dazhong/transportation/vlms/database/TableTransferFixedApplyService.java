package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchTransferFixedRequest;
import com.dazhong.transportation.vlms.model.VehicleTransferFixedApply;

import java.util.List;

/**
 * 车辆转固申服务
 * <AUTHOR>
 * @date 2025-01-10 13:57
 */
public interface TableTransferFixedApplyService extends BaseTableService<VehicleTransferFixedApply, Long> {

    /**
     * 查询车辆转固申请
     * @param request 查询参数
     * @param tokenUserInfo 用户信息
     * @return 车辆转固申请列表
     */
    List<VehicleTransferFixedApply> searchVehicleTransferFixedApply(SearchTransferFixedRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询车辆转固申请
     * @param applyNo 车辆转固申请编号
     * @return 车辆转固申请
     */
    VehicleTransferFixedApply queryVehicleTransferFixedApply(String applyNo);


    /**
     * 查询车辆转固申请
     * @param approvalNumber 车辆转固审批编号
     * @return 车辆转固申请
     */
    VehicleTransferFixedApply queryTransferFixedApplyByApprovalNumber(String approvalNumber);

    /**
     * 更新车辆转固申请
     * @param vehicleTransferFixedApply
     * @param tokenUserInfo
     * @return
     */
    int updateVehicleTransferFixedApply(VehicleTransferFixedApply vehicleTransferFixedApply,TokenUserInfo tokenUserInfo);
}
