package com.dazhong.transportation.vlms.database;

import com.dazhong.transportation.vlms.database.base.BaseTableService;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.model.VehicleTransferFixedDetails;

import java.util.List;

/**
 * 车辆转固明细服务
 * <AUTHOR>
 * @date 2025-01-10 13:57
 */
public interface TableTransferFixedDetailsService extends BaseTableService<VehicleTransferFixedDetails, Long> {


    /**
     * 根据申请ID查询转固明细
     * @param applyId 申请ID
     * @return 转固明细列表
     */
    List<VehicleTransferFixedDetails> queryTransferFixedDetailsByApplyId(Long applyId);

    /**
     * 查询转固明细审批记录
     * @return
     */
    List<VehicleTransferFixedDetails> queryVehicleApprovalRecordList();

    /**
     * 根据申请ID删除转固明细
     * @param applyId 申请ID
     * @return 删除数量
     */
    int deleteTransferFixedDetailsByApplyId(Long applyId);


    /**
     * 更新审批状态
     * @param applyId 申请ID
     * @param approvalStatus 对应转固审批状态 1 审批中 2 审批完成 3 审批取消
     * @return 更新数量
     */
    int updateApprovalStatus(Long applyId,Integer approvalStatus);

    /**
     * 根据VIN查询转固明细
     * @param vinList 车辆VIN
     * @return 转固明细
     */
    List<VehicleTransferFixedResponse> queryTransferFixedDetailsByVin(List<String> vinList);
}
