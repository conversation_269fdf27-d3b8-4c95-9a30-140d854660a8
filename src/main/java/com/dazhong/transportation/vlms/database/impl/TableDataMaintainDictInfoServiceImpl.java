package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableDataMaintainDictInfoService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.DataMaintainDictInfoMapper;
import com.dazhong.transportation.vlms.model.DataMaintainDictInfo;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.DataMaintainDictInfoDynamicSqlSupport.dataMaintainDictInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableDataMaintainDictInfoServiceImpl implements TableDataMaintainDictInfoService {

    @Autowired
    private DataMaintainDictInfoMapper dataMaintainDictInfoMapper;

    @Override
    public DataMaintainDictInfo insert(DataMaintainDictInfo dataMaintainDictInfo) {
        Date now = new Date();
        dataMaintainDictInfo.setCreateTime(now);
        dataMaintainDictInfo.setUpdateTime(now);
        dataMaintainDictInfoMapper.insertSelective(dataMaintainDictInfo);
        return dataMaintainDictInfo;
    }

    @Override
    public DataMaintainDictInfo insert(DataMaintainDictInfo dataMaintainDictInfo, TokenUserInfo tokenUserInfo) {
        dataMaintainDictInfo.setCreateOperId(tokenUserInfo.getUserId());
        dataMaintainDictInfo.setCreateOperName(tokenUserInfo.getName());
        dataMaintainDictInfo.setUpdateOperId(tokenUserInfo.getUserId());
        dataMaintainDictInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.insert(dataMaintainDictInfo);
    }

    @Override
    public int updateSelectiveById(DataMaintainDictInfo dataMaintainDictInfo) {
        dataMaintainDictInfo.setUpdateTime(new Date());
        return dataMaintainDictInfoMapper.updateByPrimaryKeySelective(dataMaintainDictInfo);
    }

    @Override
    public int updateSelectiveById(DataMaintainDictInfo dataMaintainDictInfo, TokenUserInfo tokenUserInfo) {
        dataMaintainDictInfo.setUpdateOperId(tokenUserInfo.getUserId());
        dataMaintainDictInfo.setUpdateOperName(tokenUserInfo.getName());
        return this.updateSelectiveById(dataMaintainDictInfo);
    }

    @Override
    public List<DataMaintainDictInfo> queryBySystemCode(String systemCode) {
        if(StringUtils.isBlank(systemCode)){
            return new ArrayList<>();
        }
        SelectStatementProvider selectStatement = select(dataMaintainDictInfo.allColumns())
                .from(dataMaintainDictInfo)
                .where(dataMaintainDictInfo.systemCode, isEqualTo(systemCode))
                
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataMaintainDictInfoMapper.selectMany(selectStatement);
    }

    @Override
    public List<DataMaintainDictInfo> selectBySystemCode(List<String> systemCodeList) {
        if(systemCodeList == null || systemCodeList.isEmpty()){
            return new ArrayList<>();
        }
        SelectStatementProvider selectStatement = select(dataMaintainDictInfo.allColumns())
                .from(dataMaintainDictInfo)
                .where(dataMaintainDictInfo.systemCode, isIn(systemCodeList))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return dataMaintainDictInfoMapper.selectMany(selectStatement);
    }


    @Override
    public boolean hasDuplicateMaintainDictDataCode(String systemCode, Long excludeId, String dataCode){
        if(StringUtils.isBlank(systemCode) || StringUtils.isBlank(dataCode)){
            return true;
        }
        SelectStatementProvider selectStatement = select(dataMaintainDictInfo.allColumns())
                .from(dataMaintainDictInfo)
                .where(dataMaintainDictInfo.id, isNotEqualToWhenPresent(excludeId))
                .and(dataMaintainDictInfo.systemCode, isEqualTo(systemCode))
                .and(dataMaintainDictInfo.dataCode, isEqualTo(dataCode))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        DataMaintainDictInfo duplicateDictCodeInfo = dataMaintainDictInfoMapper.selectOne(selectStatement).orElse(null);
        return duplicateDictCodeInfo != null;
    }
}
