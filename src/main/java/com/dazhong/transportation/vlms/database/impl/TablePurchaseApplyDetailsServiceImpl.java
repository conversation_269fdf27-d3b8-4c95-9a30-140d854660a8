package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TablePurchaseApplyDetailsService;
import com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyDetailsMapper;
import com.dazhong.transportation.vlms.model.VehiclePurchaseApplyDetails;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehiclePurchaseApplyDetailsDynamicSqlSupport.vehiclePurchaseApplyDetails;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2025-01-06 10:38
 */
@Service
public class TablePurchaseApplyDetailsServiceImpl implements TablePurchaseApplyDetailsService {

    @Autowired
    private VehiclePurchaseApplyDetailsMapper vehiclePurchaseApplyDetailsMapper;

    @Override
    public List<VehiclePurchaseApplyDetails> queryPurchaseDetailsList(Long purchaseApplyId) {
        SelectStatementProvider selectStatement = select(vehiclePurchaseApplyDetails.allColumns())
                .from(vehiclePurchaseApplyDetails)
                .where()
                .and(vehiclePurchaseApplyDetails.purchaseApplyId, isEqualTo(purchaseApplyId))
                .and(vehiclePurchaseApplyDetails.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehiclePurchaseApplyDetailsMapper.selectMany(selectStatement);
    }

    @Override
    public VehiclePurchaseApplyDetails queryPurchaseApplyDetails(String applyDetailsNo) {
        SelectStatementProvider selectStatement = select(vehiclePurchaseApplyDetails.allColumns())
                .from(vehiclePurchaseApplyDetails)
                .where()
                .and(vehiclePurchaseApplyDetails.applyDetailsNo, isEqualTo(applyDetailsNo))
                .and(vehiclePurchaseApplyDetails.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehiclePurchaseApplyDetails> optional = vehiclePurchaseApplyDetailsMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int savePurchaseApplyDetails(VehiclePurchaseApplyDetails applyDetails) {
        return vehiclePurchaseApplyDetailsMapper.insertSelective(applyDetails);
    }

    @Override
    public int updatePurchaseApplyDetails(VehiclePurchaseApplyDetails applyDetails) {
        return vehiclePurchaseApplyDetailsMapper.updateByPrimaryKeySelective(applyDetails);
    }

    @Override
    public int deletePurchaseApplyDetails(Long purchaseApplyId) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehiclePurchaseApplyDetails)
                .where()
                .and(vehiclePurchaseApplyDetails.purchaseApplyId, isEqualTo(purchaseApplyId))
                .and(vehiclePurchaseApplyDetails.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehiclePurchaseApplyDetailsMapper.delete(deleteStatementProvider);
    }
}
