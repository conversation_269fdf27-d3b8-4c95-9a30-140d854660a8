package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableApplyFileService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.mapper.VehicleApplyFileMapper;
import com.dazhong.transportation.vlms.model.VehicleApplyFile;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleApplyFileDynamicSqlSupport.vehicleApplyFile;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * <AUTHOR>
 * @date 2025-01-09 09:12
 */
@Service
public class TableApplyFileServiceImpl implements TableApplyFileService {

    @Autowired
    private VehicleApplyFileMapper vehicleApplyFileMapper;


    @Override
    public List<VehicleApplyFile> queryFileList(Long foreignId,Integer businessType) {
        SelectStatementProvider selectStatement = select(vehicleApplyFile.allColumns())
                .from(vehicleApplyFile)
                .where()
                .and(vehicleApplyFile.foreignId, isEqualTo(foreignId))
                .and(vehicleApplyFile.businessType, isEqualTo(businessType))
                .and(vehicleApplyFile.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleApplyFileMapper.selectMany(selectStatement);
    }

    @Override
    public VehicleApplyFile insert(VehicleApplyFile vehicleApplyFile, TokenUserInfo tokenUserInfo) {
        vehicleApplyFile.setCreateOperId(tokenUserInfo.getUserId());
        vehicleApplyFile.setCreateOperName(tokenUserInfo.getName());
        vehicleApplyFile.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleApplyFile.setUpdateOperName(tokenUserInfo.getName());
        vehicleApplyFileMapper.insertSelective(vehicleApplyFile);
        return vehicleApplyFile;
    }

    @Override
    public int deleteApplyFile(Long foreignId,Integer businessType) {
        DeleteStatementProvider deleteStatementProvider = deleteFrom(vehicleApplyFile)
                .where()
                .and(vehicleApplyFile.foreignId, isEqualTo(foreignId))
                .and(vehicleApplyFile.businessType, isEqualTo(businessType))
                .and(vehicleApplyFile.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleApplyFileMapper.delete(deleteStatementProvider);
    }
}
