package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleContractService;
import com.dazhong.transportation.vlms.mapper.VehicleContractMapper;
import com.dazhong.transportation.vlms.model.VehicleContract;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.dazhong.transportation.vlms.mapper.VehicleContractDynamicSqlSupport.vehicleContract;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * <AUTHOR>
 * @date 2025-01-16 10:38
 */
@Service
public class TableVehicleContractServiceImpl implements TableVehicleContractService {

    @Autowired
    private VehicleContractMapper vehicleContractMapper;

    @Override
    public List<VehicleContract> queryVehicleContractByVin(String vin) {
        SelectStatementProvider provider = select(vehicleContract.allColumns())
                .from(vehicleContract)
                .where()
                .and(vehicleContract.vin,isEqualTo(vin))
                .and(vehicleContract.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return vehicleContractMapper.selectMany(provider);
    }

    @Override
    public VehicleContract queryByContractNo(String contractNo) {
        SelectStatementProvider provider = select(vehicleContract.allColumns())
                .from(vehicleContract)
                .where()
                .and(vehicleContract.contractNo,isEqualTo(contractNo))
                .and(vehicleContract.isDeleted, isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<VehicleContract> optional = vehicleContractMapper.selectOne(provider);
        return optional.orElse(null);

    }

    @Override
    public VehicleContract insert(VehicleContract vehicleContract) {
        vehicleContractMapper.insertSelective(vehicleContract);
        return vehicleContract;
    }

    @Override
    public int updateSelectiveById(VehicleContract vehicleContract) {
        vehicleContract.setUpdateTime(new Date());
        vehicleContractMapper.updateByPrimaryKeySelective(vehicleContract);
        return 1;
    }
}
