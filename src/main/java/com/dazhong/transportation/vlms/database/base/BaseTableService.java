package com.dazhong.transportation.vlms.database.base;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;

import java.util.Date;
import java.util.List;

/**
 * 默认
 *
 * @param <T> Model Type
 * @param <K> PrimaryKey Type
 */
public interface BaseTableService<T, K> {

    /**
     * 插入数据
     *
     * @param t 插入对象
     * @return 插入后对象信息（若自增主键，则会给主键字段赋自增值）
     */
    default T insert(T t) {
        throw new UnsupportedOperationException();
    }

    /**
     * 插入数据
     *
     * @param t        插入对象
     * @param operator 操作人名称
     * @return 插入后对象信息（若自增主键，则会给主键字段赋自增值）
     */
    default T insert(T t, String operator) {
        throw new UnsupportedOperationException();
    }

    /**
     * 插入数据
     * @param t             插入对象
     * @param tokenUserInfo 操作人信息
     * @return 插入后对象信息（若自增主键，则会给主键字段赋自增值）
     */
    default T insert(T t, TokenUserInfo tokenUserInfo){
        throw new UnsupportedOperationException();
    }


    /**
     * 根据主键更新
     *
     * @param t 更新数据
     * @return 影响行数
     */
    default int updateSelectiveById(T t) {
        throw new UnsupportedOperationException();
    }

    /**
     * 根据主键更新
     *
     * @param t        更新数据
     * @param operator 操作人
     * @return 影响行数
     */
    default int updateSelectiveById(T t, String operator) {
        throw new UnsupportedOperationException();
    }

    /**
     * 根据主键更新
     *
     * @param t             更新数据
     * @param tokenUserInfo 操作人信息
     * @return 影响行数
     */
    default int updateSelectiveById(T t, TokenUserInfo tokenUserInfo) {
        throw new UnsupportedOperationException();
    }


    /**
     * 根据主键查询对象信息
     *
     * @param id 主键ID
     * @return 对象信息
     */
    default T selectById(K id) {
        throw new UnsupportedOperationException();
    }



    /**
     * 转换模糊查询参数 - 字符串类型参数
     * @param param 参数
     * @return 组装后参数
     */
    default String transFuzzyQueryParam(String param){
        String result = null;
        if(StringUtils.isNotBlank(param)){
            result =  "%" + param + "%";
        }
        return result;
    }


    /**
     * 转换右模糊查询参数 - 字符串类型参数
     * @param param 参数
     * @return 组装后参数
     */
    default String transRightFuzzyQueryParam(String param){
        String result = null;
        if(StringUtils.isNotBlank(param)){
            result = param + "%";
        }
        return result;
    }

}
