package com.dazhong.transportation.vlms.database.impl;

import com.dazhong.transportation.vlms.database.TableVehicleApplicationService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleApplicationListDto;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleApplicationListRequest;
import com.dazhong.transportation.vlms.mapper.VehicleApplicationMapper;
import com.dazhong.transportation.vlms.model.VehicleApplication;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.dazhong.transportation.vlms.mapper.VehicleApplicationDetailDynamicSqlSupport.vehicleApplicationDetail;
import static com.dazhong.transportation.vlms.mapper.VehicleApplicationDynamicSqlSupport.vehicleApplication;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

@Service
public class TableVehicleApplicationServiceImpl implements TableVehicleApplicationService {
    @Autowired
    VehicleApplicationMapper vehicleApplicationMapper;

    @Override
    public VehicleApplication selectById(Long id) {
        return vehicleApplicationMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public VehicleApplication insert(VehicleApplication vehicleApplication, TokenUserInfo tokenUserInfo) {
        vehicleApplication.setCreateTime(new Date());
        vehicleApplication.setCreateOperId(tokenUserInfo.getUserId());
        vehicleApplication.setCreateOperName(tokenUserInfo.getName());
        vehicleApplicationMapper.insertSelective(vehicleApplication);
        return vehicleApplication;
    }

    @Override
    public int updateSelectiveById(VehicleApplication vehicleApplication, TokenUserInfo tokenUserInfo) {
        vehicleApplication.setUpdateTime(new Date());
        vehicleApplication.setUpdateOperId(tokenUserInfo.getUserId());
        vehicleApplication.setUpdateOperName(tokenUserInfo.getName());
        return vehicleApplicationMapper.updateByPrimaryKeySelective(vehicleApplication);
    }

    @Override
    public int updateSelectiveById(VehicleApplication vehicleApplication) {
        return vehicleApplicationMapper.updateByPrimaryKeySelective(vehicleApplication);
    }

    @Override
    public List<VehicleApplicationListDto> queryVehicleApplicationList(SearchVehicleApplicationListRequest searchVehicleApplicationListRequest, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = tokenUserInfo.getOrgIdList();
        List<Integer> ownerIdList = tokenUserInfo.getOwnerIdList();
        SelectStatementProvider provider = select(
                vehicleApplication.allColumns(),
                count(vehicleApplicationDetail.id).as("vehicleNumber")
        )
                .from(vehicleApplication)
                .leftJoin(vehicleApplicationDetail).on(vehicleApplication.id, equalTo(vehicleApplicationDetail.applicationId))
                .where()
                .and(vehicleApplication.dingTalkNo, isLikeWhenPresent(transFuzzyQueryParam(searchVehicleApplicationListRequest.getDingTalkNo())).filter(StringUtils::isNotBlank))
                .and(vehicleApplication.createOperName, isLikeWhenPresent(transFuzzyQueryParam(searchVehicleApplicationListRequest.getCreateOperName())).filter(StringUtils::isNotBlank))
                .and(vehicleApplication.documentStatus, isInWhenPresent(searchVehicleApplicationListRequest.getDocumentStatusList()))
                .and(vehicleApplication.organizationId, isInWhenPresent(orgIdList))
                .and(vehicleApplication.ownerId, isInWhenPresent(ownerIdList))
                .groupBy(vehicleApplication.id)
                .orderBy(vehicleApplication.createTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return vehicleApplicationMapper.selectVehicleApplicationList(provider);
    }

    @Override
    public VehicleApplication selectByDingTalkNo(String dingTalkNo) {
        return vehicleApplicationMapper.selectByDingTalkNo(dingTalkNo).orElse(null);
    }
}
