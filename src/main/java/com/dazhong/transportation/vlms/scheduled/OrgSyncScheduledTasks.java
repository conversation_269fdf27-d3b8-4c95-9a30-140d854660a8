package com.dazhong.transportation.vlms.scheduled;

import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 组织架构信息同步定时任务实现类
 */
@Slf4j
@Component
@EnableScheduling
public class OrgSyncScheduledTasks {

    
    @Autowired
    private RedisUtils redisUtils;


    @Autowired
    private IOrgService orgService;

    private static final String TASK_LOCK_KEY = "task_lock:";
    private static final int LOCK_EXPIRE_TIME = 300; // 锁过期时间（秒）

    /**
     * 定时任务，每三天凌晨2点55执行
     */
    @Scheduled(cron = "55 55 2 */3 * ?")
    public void syncOrganizationTask() {
        String lockKey = TASK_LOCK_KEY + "sync_org_from_dazhong";
        try {
            // 尝试获取分布式锁
            boolean acquired = redisUtils.setIfAbsent(lockKey, "1", LOCK_EXPIRE_TIME);
            if (!acquired) {
                log.info("定时任务已被其他实例执行，跳过本次执行");
                return;
            }
            orgService.syncOrganization();
        } catch (Exception e) {
            log.error("定时任务执行异常", e);
        } finally {
            redisUtils.del(lockKey);
        }
    }
}