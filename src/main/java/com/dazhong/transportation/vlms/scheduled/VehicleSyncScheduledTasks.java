package com.dazhong.transportation.vlms.scheduled;

import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 车辆信息同步定时任务实现类
 */
@Slf4j
@Component
@EnableScheduling
public class VehicleSyncScheduledTasks {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IVehicleService vehicleService;

    private static final String TASK_LOCK_KEY = "task_lock:";
    private static final int LOCK_EXPIRE_TIME = 300; // 锁过期时间（秒）

    /**
     * 定时任务，每天凌晨2点执行车辆同步到SAAS系统
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncVehicleToSaasTask() {
        String lockKey = TASK_LOCK_KEY + "sync_vehicle_to_saas";
        try {
            // 尝试获取分布式锁
            boolean acquired = redisUtils.setIfAbsent(lockKey, "1", LOCK_EXPIRE_TIME);
            if (!acquired) {
                log.info("车辆同步定时任务已被其他实例执行，跳过本次执行");
                return;
            }

            log.info("开始执行车辆同步到SAAS系统定时任务...");
            // 执行具体的业务逻辑
            ResultResponse<Void> resultResponse = vehicleService.syncVehicleToSaasManually(false);
            // 处理同步结果
            if (resultResponse.getCode() == 0) {
                log.info("车辆信息同步到SAAS系统成功: {}", resultResponse.getData());
            } else {
                log.error("车辆信息同步到SAAS系统失败: {}", resultResponse.getMessage());
            }
            log.info("车辆同步定时任务执行完成");
        } catch (Exception e) {
            log.error("车辆同步定时任务执行异常", e);
        } finally {
            redisUtils.del(lockKey);
        }
    }
}