package com.dazhong.transportation.vlms.scheduled;

import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时任务实现类
 */
@Slf4j
@Component
@EnableScheduling
public class ScheduledTasks {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IVehicleService vehicleService;

    private static final String TASK_LOCK_KEY = "task_lock:";
    private static final int LOCK_EXPIRE_TIME = 300; // 锁过期时间（秒）

    /**
     * 定时任务，每日凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void exampleTask() {
        String lockKey = TASK_LOCK_KEY + "sync_vehicle_from_dazhong";
        try {
            // 尝试获取分布式锁
            boolean acquired = redisUtils.setIfAbsent(lockKey, "1", LOCK_EXPIRE_TIME);
            if (!acquired) {
                log.info("定时任务已被其他实例执行，跳过本次执行");
                return;
            }

            log.info("开始执行定时任务...");
            // 执行具体的业务逻辑
            ResultResponse<Void> resultResponse = vehicleService.syncAllVehicleInfoFromDaZhong();
            // 处理同步结果
            if (resultResponse.getCode() == 0) {
                log.info("车辆信息同步成功: {}", resultResponse.getData());
                // 可以在这里添加同步成功后的其他操作，例如发送通知等
            } else {
                log.error("车辆信息同步失败: {}", resultResponse.getMessage());
            }
            log.info("定时任务执行完成");
        } catch (Exception e) {
            log.error("定时任务执行异常", e);
        } finally {
            redisUtils.del(lockKey);
        }
    }
}