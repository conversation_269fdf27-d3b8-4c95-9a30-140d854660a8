package com.dazhong.transportation.vlms.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DingTalkConfig {

    @Value("${ding.talk.app.key}")
    private String dingTalkAppKey;

    @Value("${ding.talk.app.secret}")
    private String dingTalkAppSecret;

    @Value("${ding.talk.agent.id}")
    private Long dingTalkAgentId;

    /**
     * 车辆处置钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.disposal.process.code}")
    private String vehicleDisposalProcessCode;

    /**
     * 车辆报废钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.scrap.process.code}")
    private String vehicleScrapProcessCode;

    /**
     * 车辆采购钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.purchase.process.code}")
    private String vehiclePurchaseProcessCode;

    /**
     * 车辆转籍钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.transfer.process.code}")
    private String vehicleTransferProcessCode;

    /**
     * 车辆调拨钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.allocate.process.code}")
    private String vehicleAllocateProcessCode;

    /**
     * 车辆切换业务线钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.modify.business.process.code}")
    private String vehicleModifyBusinessProcessCode;

    /**
     * 车辆转固钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.transfer.fixed.process.code}")
    private String vehicleTransferFixedProcessCode;

    /**
     * 车辆逆处置钉钉审批流CODE
     */
    @Value("${ding.talk.flow.vehicle.reverse.disposal.process.code}")
    private String vehicleReverseDisposalProcessCode;

    /**
     * 车辆商务业务出售流程CODE
     */
    @Value("${ding.talk.flow.vehicle.business.sell.process.code}")
    private String vehicleBusinessSellProcessCode;



    public String getDingTalkAppKey() {
        return dingTalkAppKey;
    }

    public String getDingTalkAppSecret() {
        return dingTalkAppSecret;
    }

    public Long getDingTalkAgentId() {
        return dingTalkAgentId;
    }

    public String getVehicleDisposalProcessCode() {
        return vehicleDisposalProcessCode;
    }

    public String getVehicleScrapProcessCode() {
        return vehicleScrapProcessCode;
    }

    public String getVehiclePurchaseProcessCode() {
        return vehiclePurchaseProcessCode;
    }

    public String getVehicleTransferProcessCode() {
        return vehicleTransferProcessCode;
    }

    public String getVehicleAllocateProcessCode() {
        return vehicleAllocateProcessCode;
    }

    public String getVehicleModifyBusinessProcessCode() {
        return vehicleModifyBusinessProcessCode;
    }

    public String getVehicleTransferFixedProcessCode() {
        return vehicleTransferFixedProcessCode;
    }

    public String getVehicleReverseDisposalProcessCode() {
        return vehicleReverseDisposalProcessCode;
    }

    public String getVehicleBusinessSellProcessCode(){return vehicleBusinessSellProcessCode;};
}
