package com.dazhong.transportation.vlms.config;

import com.dazhong.transportation.vlms.service.*;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
public class Global {
    @Value("${file.mfs.url}")
    public String mfsUrl;

    @Value("${file.mfs.root.path}")
    public String mfsRootPath;

    @Value("${model.queryVehicleBaseList.url}")
    public String queryVehicleBaseListUrl;

    @Value("${model.getVehicleBaseInfo.url}")
    public String getVehicleBaseInfoUrl;

    @Value("${vin.getVehicleModelInfo.url}")
    public String getVehicleModelInfo;

    @Value("${dzjt.getCaeInfo.url}")
    public String getCarInfoUrl;

    @Value("${saas.sync.key}")
    public String saasSyncKey;

    @Value("${saas.sync.vehicle.url}")
    public String saasVehicleSyncUrl;

    @Value("${saas.sync.model.url}")
    public String saasModelSyncUrl;

    @Value("${saas.sync.org.url}")
    public String saasOrgSyncUrl;

    @Value("${jdy.app.key}")
    public String jdyAppKey;

    @Value("${jdy.app.id}")
    public String jdyAppId;

    @Value("${jdy.entry.id}")
    public String jdyEntryId;

    @Value("${jdy.entry.widget.url}")
    public String jdyEntryWidgetUrl;

    @Value("${jdy.entry.data.url}")
    public String jdyEntryDataUrl;

    public static Global instance;

    @Autowired
    public RedisUtils redisUtil;

    @Autowired
    public IVehicleApplicationService vehicleApplicationService;

    @Autowired
    public ITransferFixedService transferFixedService;

    @Autowired
    public IVehiclePurchaseService vehiclePurchaseService;

    @Autowired
    public IVehicleDisposalService vehicleDisposalService;

    @Autowired
    public IVehicleReverseDisposalService vehicleReverseDisposalService;

    @Resource
    public DingTalkConfig dingTalkConfig;

    @PostConstruct
    public void init() {
        instance = this;
    }

}
