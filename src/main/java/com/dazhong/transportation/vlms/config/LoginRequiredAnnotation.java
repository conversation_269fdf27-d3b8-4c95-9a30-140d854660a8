package com.dazhong.transportation.vlms.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 校验用户是否登录注解
 * <AUTHOR>
 * @date 2024-12-28 13:02
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface LoginRequiredAnnotation {

    boolean required() default false;
}