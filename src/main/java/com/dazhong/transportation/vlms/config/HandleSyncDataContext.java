package com.dazhong.transportation.vlms.config;

import com.dazhong.transportation.vlms.service.sync.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步数据上下文
 *
 * <AUTHOR>
 * @date 2025-01-07 17:57
 */
@Component
public class HandleSyncDataContext {

    private final Map<String, IHandleSyncDataService> serviceMap = new HashMap<>();

    public HandleSyncDataContext(List<IHandleSyncDataService> services) {
        for (IHandleSyncDataService service : services) {
            // 3.6 同步组织架构
            if (service instanceof VehicleSyncExternalCompanyServiceImpl) {
                serviceMap.put("vlms.open.company.sync", service);
            }
            // 3.7 批量同步采购意向信息
            else if (service instanceof VehicleSyncExternalPurchaseServiceImpl) {
                serviceMap.put("vlms.open.purchase.intention.sync", service);
            }
            // 3.2 同步设备CAN信息
            else if (service instanceof VehicleLocationCanServiceImpl) {
                serviceMap.put("vlms.open.vehicle.can.sync", service);
            }
            // 3.1 同步终端关联信息
            else if (service instanceof VehicleTerminalSyncServiceImpl) {
                serviceMap.put("vlms.open.terminal.sync", service);
            }
            // 3.3 同步车辆保险信息
            else if (service instanceof VehicleSyncExternalInsuranceServiceImpl) {
                serviceMap.put("vlms.open.vehicle.insurance.sync", service);
            }
            // 3.4 同步车辆违章信息
            else if (service instanceof VehicleSyncExternalIllegalServiceImpl) {
                serviceMap.put("vlms.open.vehicle.illegal.sync", service);
            }
            // 3.5 同步车辆事故信息
            else if (service instanceof VehicleSyncExternalAccidentServiceImpl) {
                serviceMap.put("vlms.open.vehicle.accident.sync", service);
            }
            // 3.8 批量同步合同信息
            else if (service instanceof VehicleSyncExternalContractServiceImpl) {
                serviceMap.put("vlms.open.contract.sync", service);
            }
            // 3.9 同步所属车队、运营状态
            else if (service instanceof VehicleSyncExternalStatusServiceImpl) {
                serviceMap.put("vlms.open.vehicle.status.sync", service);
            }
            // 同步简道云表单数据
            else if (service instanceof SyncExternalJianDaoYunDataServiceImpl) {
                serviceMap.put("jian.dao.yun.Data.sync", service);
            }
        }
    }

    public IHandleSyncDataService getSyncDataService(String method) {
        return serviceMap.get(method);
    }
}
