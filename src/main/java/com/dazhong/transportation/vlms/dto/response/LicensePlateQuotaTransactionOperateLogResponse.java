package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 响应类：额度单流水操作日志记录
 */
@ApiModel(description = "额度单流水操作日志记录")
@Data
public class LicensePlateQuotaTransactionOperateLogResponse {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "外键id", example = "1001")
    private Long foreignId;

    @ApiModelProperty(value = "额度资产归属公司id", example = "1001")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "额度资产归属公司名", example = "1001")
    private String assetCompanyName;

    @ApiModelProperty(value = "操作类型 (业务模块自定义)", example = "10")
    private Integer operateType;

    @ApiModelProperty(value = "操作内容", example = "更新车辆信息")
    private String operateContent;

    @ApiModelProperty(value = "备注", example = "无")
    private String miscDesc;

    @ApiModelProperty(value = "删除标记 (0: 正常 1: 删除)", example = "0")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "创建人id", example = "user123")
    private Long createOperId;

    @ApiModelProperty(value = "创建人", example = "Operator A")
    private String createOperName;

    @ApiModelProperty(value = "修改时间", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "修改人id", example = "user123")
    private Long updateOperId;

    @ApiModelProperty(value = "修改人", example = "Operator A")
    private String updateOperName;

}
