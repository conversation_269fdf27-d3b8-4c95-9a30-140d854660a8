package com.dazhong.transportation.vlms.dto.response.base;


import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 返回结果
 * <AUTHOR>
 * @date 2024-12-20 17:25
 */
@ApiModel(description = "结果对象")
public class ResultResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  状态码  -1001 参数错误 -1002 记录不存在 -1003 业务异常 -1004 系统异常  0 成功
     */
    @ApiModelProperty("状态码  -1001 参数错误 -1002 记录不存在 -1003 业务异常 -1004 系统异常 -1005 未登录  0 成功")
    private Integer code;

    /**
     * 返回信息
     */
    @ApiModelProperty("结果描述")
    private String message;

    /**
     * 返回数据
     */
    @ApiModelProperty("返回数据")
    private T data;

    public ResultResponse() {
    }

    public ResultResponse(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResultResponse(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 查询成功 状态码 1
     * @param data 对象
     * @return ResultVO
     */
    public static <T> ResultResponse success(T data){
        return new ResultResponse(0,"成功", data);
    }

    /**
     * 请求成功  状态码 1
     * @return ResultVO
     */
    public static ResultResponse success() {
        return new ResultResponse(0, "成功");
    }

    /**
     * 请求成功
     * @param message 消息内容
     * @return
     */
    public static ResultResponse success(String message){
        return new ResultResponse(0, message);
    }

    /**
     * 业务异常返回   状态码 -1
     *
     * @param msg 返回信息
     * @return ResultVO
     */
    public static ResultResponse businessFailed(String msg) {
        return new ResultResponse(ExceptionEnum.BUSINESS_FAIL.getCode(), msg);
    }

    /**
     * 系统异常返回
     * @return ResultVO
     */
    public static ResultResponse exceptionFailed(ExceptionEnum exceptionEnum) {
        return new ResultResponse(exceptionEnum.getCode(), exceptionEnum.getMessage());
    }

    public Integer getCode() {

        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
