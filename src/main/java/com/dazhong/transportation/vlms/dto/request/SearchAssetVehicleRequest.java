package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "车辆列表查询参数")
@Data
public class SearchAssetVehicleRequest extends PageRequest {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "资产所属公司列表")
    private List<Integer> assetCompanyIdList;

    @ApiModelProperty(value = "实际运营公司（所属）列表")
    private List<Long> ownOrganizationIdList;

    @ApiModelProperty(value = "实际运营公司（使用）列表")
    private List<Long> usageOrganizationIdList;

    @ApiModelProperty(value = "资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废")
    private List<Integer> propertyStatus;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线")
    private Integer productLine;

    @ApiModelProperty(value = "业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车")
    private List<Integer> businessLine;

    private List<String> vinList;

    @ApiModelProperty(value = "运营状态 1-待运 2-租出 ......")
    private List<Integer> operatingStatus;

}
