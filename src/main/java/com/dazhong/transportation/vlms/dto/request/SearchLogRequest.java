package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024-12-23 9:11
 */
@ApiModel(description = "查询日志参数")
@Data
public class SearchLogRequest extends PageRequest {

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型 1-系统管理 2-组织架构 3-车辆采购 4-额度流水单 5-车辆主数据 ",required = true)
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;


    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private Integer operateType;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operateUser;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String operateBeginDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String operateEndDate;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    private Long foreignId;
}
