package com.dazhong.transportation.vlms.dto.response.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ComboResponse<K, V> implements Serializable {

    /**
     * 下拉框数据
     */
    private List<ComboData> list = new ArrayList<>();

    /**
     * 组合数据项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ComboData<K, V> implements Serializable{
        private K key;
        private V value;
    }
}
