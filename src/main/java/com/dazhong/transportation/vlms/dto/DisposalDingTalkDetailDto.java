package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "车辆处置-钉钉返回明细DTO")
public class DisposalDingTalkDetailDto {

    @ApiModelProperty(value = "主键id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "处置单号", example = "D001")
    private String documentNo;

    @ApiModelProperty(value = "资产编号", example = "A001")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A123456")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "原值（元）", example = "10")
    private BigDecimal originalValue;

    @ApiModelProperty(value = "已提折旧（元）", example = "10")
    private String accumulatedDepreciation;

    @ApiModelProperty(value = "净值/残值（元）", example = "10")
    private BigDecimal netValue;

    @ApiModelProperty(value = "财务评估", example = "财务评估")
    private String financialEvaluation;

    @ApiModelProperty(value = "附件地址", example = "10")
    private List<String> fileUrlList;

    @ApiModelProperty(value = "实际售价（元）", example = "10")
    private BigDecimal actualSellingPrice;

    @ApiModelProperty(value = "实际净售价（元）", example = "10")
    private BigDecimal actualNetPrice;

    @ApiModelProperty(value = "实际净售价差值（元）", example = "10")
    private BigDecimal netPriceDiff;

    @ApiModelProperty(value = "实际出售损益", example = "10")
    private BigDecimal saleGainLoss;

    @ApiModelProperty(value = "实际出售数量", example = "10")
    private Integer actualSoldQuantity;

    @ApiModelProperty(value = "计划数量与实际数量差值", example = "10")
    private Integer quantityDiff;

    @ApiModelProperty(value = "实际出售说明", example = "10")
    private String actualSaleDesc;

    @ApiModelProperty(value = "实际售价附件", example = "10")
    private List<String> actualSaleFileUrlList;

    @ApiModelProperty(value = "预估报废损失金额（元）", example = "1000")
    private BigDecimal estimatedScrapLossAmount;

    @ApiModelProperty(value = "是否保险公司拍卖 1-是 2-否", example = "1")
    private Integer isInsuranceAuction;

    @ApiModelProperty(value = "实际拍卖金额（元）", example = "5000")
    private BigDecimal actualAuctionAmount;

    @ApiModelProperty(value = "实际报废损益（元）", example = "-2000")
    private BigDecimal actualScrapProfitLoss;

    @ApiModelProperty(value = "退役日期", example = "2020-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date realRetirementDate;

}
