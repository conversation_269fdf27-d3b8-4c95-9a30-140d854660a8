package com.dazhong.transportation.vlms.dto.response;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.dazhong.transportation.vlms.dto.DingTalkFileDto;
import com.dazhong.transportation.vlms.dto.RowDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalDetailDto;
import com.dazhong.transportation.vlms.enums.PublicBooleanStatusEnum;
import com.dazhong.transportation.vlms.model.VehicleDisposalDingTalkResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(description = "钉钉审批流审批节点列表对象")
@Data
public class GetDingTalkDetailResponse {

    /**
     * 审批结果 agree 表示同意，为 refuse 表示拒绝
     */
    String workFlowResult;

    /**
     * 审批单号
     */
    List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues;

    /**
     * 明细数据
     */
    List<RowDto> detailData;

    /**
     * 获取明细数据
     *
     * @return
     */
    public List<RowDto> getDetailData() {
        GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues values = this.formComponentValues.stream()
            .filter(formComponentValue -> formComponentValue != null && formComponentValue.getName() != null && formComponentValue.getName().equals("明细数据"))
            .findFirst()
            .orElse(null);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            if (null != values) {
                return objectMapper.readValue(values.getValue(), new TypeReference<List<RowDto>>() {
                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return detailData;
    }

    /**
     * 获取采购明细数据
     *
     * @return
     */
    public List<RowDto> getPurchaseDetailData() {
        GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues values = this.formComponentValues.stream()
            .filter(formComponentValue -> formComponentValue != null && formComponentValue.getName() != null && formComponentValue.getName().equals("采购详情"))
            .findFirst()
            .orElse(null);
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            if (null != values) {
                return objectMapper.readValue(values.getValue(), new TypeReference<List<RowDto>>() {
                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取钉钉附件
     *
     * @return
     */
    public List<DingTalkFileDto> getDingTalkFileList(String value) {
        JSONArray fileJsonArray = new JSONArray(value);
        if (CollectionUtil.isNotEmpty(fileJsonArray)) {
            return fileJsonArray.toList(DingTalkFileDto.class);
        }
        return null;
    }

    /**
     * 获取审批节点数据
     *
     * @return
     */
    public GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues  getValue(String name) {
        if (this.formComponentValues == null) {
            return null;
        }
        return this.formComponentValues.stream()
                .filter(formComponentValue -> formComponentValue != null && formComponentValue.getName() != null && formComponentValue.getName().equals(name))
                .findFirst()
                .orElse(null);
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(String name) {
        try {
            GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues value = getValue(name);
            return value != null ? value.getValue() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(String name) {
        try {
            String stringValue = getStringValue(name);
            return stringValue != null && !stringValue.trim().isEmpty() ? new BigDecimal(stringValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Integer值
     */
    private Integer getIntegerValue(String name) {
        try {
            String stringValue = getStringValue(name);
            return stringValue != null && !stringValue.trim().isEmpty() ? Integer.valueOf(stringValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Date值
     */
    private Date getDateValue(String name) {
        try {
            String stringValue = getStringValue(name);
            return stringValue != null && !stringValue.trim().isEmpty() ? DateUtil.parse(stringValue) : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 转换为处置明细数据
     * 
     * @return
     */
    public VehicleDisposalDetailDto convertToVehicleDisposalDetailDto() {
        VehicleDisposalDetailDto vehicleDisposalDetailDto = new VehicleDisposalDetailDto();
        
        vehicleDisposalDetailDto.setVehicleAssetId(getStringValue("资产编号"));
        vehicleDisposalDetailDto.setVin(getStringValue("车架号"));
        vehicleDisposalDetailDto.setLicensePlate(getStringValue("车牌号"));
        vehicleDisposalDetailDto.setVehicleModelName(getStringValue("车型"));
        vehicleDisposalDetailDto.setAssetCompanyName(getStringValue("资产所属公司"));
        vehicleDisposalDetailDto.setOwnOrganizationName(getStringValue("实际运营公司（所属）"));
        vehicleDisposalDetailDto.setUsageOrganizationName(getStringValue("实际运营公司（使用）"));
        vehicleDisposalDetailDto.setBelongingTeam(getStringValue("所属车队"));
        vehicleDisposalDetailDto.setStartDate(getDateValue("投产日期"));
        vehicleDisposalDetailDto.setUsageMonthLimit(getIntegerValue("使用期限（月）"));
        vehicleDisposalDetailDto.setUsedMonths(getIntegerValue("到填表日已用月数"));
        vehicleDisposalDetailDto.setEstimatedMarketPrice(getBigDecimalValue("市场预估价（元）"));
        vehicleDisposalDetailDto.setEstimatedNetSaleValue(getBigDecimalValue("预估最低净售价（元）"));
        vehicleDisposalDetailDto.setMinimumAcceptablePrice(getBigDecimalValue("保留价（元）"));
        vehicleDisposalDetailDto.setHandlingFee(getBigDecimalValue("手续费（元）（商务业务）"));
        vehicleDisposalDetailDto.setSaleReason(getStringValue("出售原因"));
        vehicleDisposalDetailDto.setSaleGainLoss(getBigDecimalValue("实际出售损益-自动计算"));
        vehicleDisposalDetailDto.setDisposalReason(getStringValue("报废原因"));
        vehicleDisposalDetailDto.setActualSaleReason(getStringValue("实际出售原因"));
        vehicleDisposalDetailDto.setEstimatedVehicleLoss(getBigDecimalValue("预估车辆损失（元）"));
        vehicleDisposalDetailDto.setGovernmentSubsidyAmount(getBigDecimalValue("政府补贴金额（元）"));
        vehicleDisposalDetailDto.setOriginalValue(getBigDecimalValue("原值（元）"));
        vehicleDisposalDetailDto.setAccumulatedDepreciation(getStringValue("已提折旧（元）"));
        
        // 净值处理逻辑
        BigDecimal netValue = getBigDecimalValue("净值/残值（元）-自动计算");
        if (netValue == null) {
            netValue = getBigDecimalValue("净值（元）");
        }
        vehicleDisposalDetailDto.setNetValue(netValue);
        
        vehicleDisposalDetailDto.setFinancialEvaluation(getStringValue("财务评估说明"));
        vehicleDisposalDetailDto.setActualSellingPrice(getBigDecimalValue("实际售价（元）"));
        vehicleDisposalDetailDto.setActualNetPrice(getBigDecimalValue("实际净售价（元）-自动计算"));
        vehicleDisposalDetailDto.setNetPriceDiff(getBigDecimalValue("实际净售价差值（元）-自动计算"));
        vehicleDisposalDetailDto.setActualSoldQuantity(getIntegerValue("实际出售数量"));
        vehicleDisposalDetailDto.setQuantityDiff(getIntegerValue("计划数量与实际数量差值"));
        vehicleDisposalDetailDto.setActualSaleDesc(getStringValue("实际出售说明"));
        vehicleDisposalDetailDto.setEstimatedScrapLossAmount(getBigDecimalValue("预估报废损失金额（元）"));
        
        // 布尔值处理
        try {
            String insuranceAuctionValue = getStringValue("是否保险公司拍卖");
            vehicleDisposalDetailDto.setIsInsuranceAuction(insuranceAuctionValue != null ? 
                PublicBooleanStatusEnum.getCode(insuranceAuctionValue) : null);
        } catch (Exception e) {
            vehicleDisposalDetailDto.setIsInsuranceAuction(null);
        }
        
        vehicleDisposalDetailDto.setActualAuctionAmount(getBigDecimalValue("实际拍卖金额（元）"));
        vehicleDisposalDetailDto.setActualScrapProfitLoss(getBigDecimalValue("实际报废损益（元）"));
        
        // 附件处理
        try {
            String attachmentValue = getStringValue("附件");
            vehicleDisposalDetailDto.setDingTalkFileList(attachmentValue != null ? 
                getDingTalkFileList(attachmentValue) : null);
        } catch (Exception e) {
            vehicleDisposalDetailDto.setDingTalkFileList(null);
        }
        
        try {
            String actualSaleAttachmentValue = getStringValue("实际售价附件");
            vehicleDisposalDetailDto.setActualSaleDingTalkFileList(actualSaleAttachmentValue != null ? 
                getDingTalkFileList(actualSaleAttachmentValue) : null);
        } catch (Exception e) {
            vehicleDisposalDetailDto.setActualSaleDingTalkFileList(null);
        }
        
        vehicleDisposalDetailDto.setRealRetirementDate(getDateValue("退役日期"));
        
        return vehicleDisposalDetailDto;
    }

    /**
     * 转换为处置明细数据
     * 
     * @return
     */
    public VehicleDisposalDingTalkResult convertToVehicleDisposalDingTalkResultDto() {
        VehicleDisposalDingTalkResult vehicleDisposalDingTalkResult = new VehicleDisposalDingTalkResult();
        
        vehicleDisposalDingTalkResult.setOriginalValue(getBigDecimalValue("原值（元）"));
        vehicleDisposalDingTalkResult.setDepreciationTaken(getBigDecimalValue("已提折旧（元）"));
        vehicleDisposalDingTalkResult.setNetValueOrSalvage(getBigDecimalValue("净值/残值（元）"));
        vehicleDisposalDingTalkResult.setEstimatedProfitLoss(getBigDecimalValue("预计收益/损失（元）"));
        vehicleDisposalDingTalkResult.setActualVehiclesSold(getIntegerValue("实际出售车辆数"));
        vehicleDisposalDingTalkResult.setActualTotalProfitLoss(getBigDecimalValue("实际总盈亏（元）"));
        vehicleDisposalDingTalkResult.setActualPerVehicleProfitLoss(getBigDecimalValue("实际单车盈亏（元）"));
        vehicleDisposalDingTalkResult.setActualVsEstimatedDiffOne(getBigDecimalValue("实预1差额/扣除撤拍车辆预计总盈亏1（元）"));
        vehicleDisposalDingTalkResult.setActualVsEstimatedDiffTwo(getBigDecimalValue("实预2差额/扣除撤拍车辆预计总盈亏2（元）"));
        
        return vehicleDisposalDingTalkResult;
    }
}
