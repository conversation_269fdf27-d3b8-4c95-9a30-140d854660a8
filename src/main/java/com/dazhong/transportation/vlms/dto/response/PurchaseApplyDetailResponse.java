package com.dazhong.transportation.vlms.dto.response;

import java.util.List;

import com.dazhong.transportation.vlms.dto.PurchaseApplyDetailDto;
import com.dazhong.transportation.vlms.dto.PurchaseEquipmentDto;
import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.dto.VehicleReceivingDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "车辆采购申请详情")
@Data
public class PurchaseApplyDetailResponse {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "采购申请编号")
    private String purchaseApplyNo;

    @ApiModelProperty(value = "钉钉审批编号")
    private String approvalNumber;

    @ApiModelProperty(value = "采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)")
    private Integer purchaseApplyStatus;

    @ApiModelProperty(value = "采购名称")
    private String applyName;

    @ApiModelProperty(value = "资产所有公司ID")
    private Integer ownerId;

    @ApiModelProperty(value = "资产所有公司名称")
    private String ownerName;

    @ApiModelProperty(value = "申请公司ID")
    private Long applyOrgId;

    @ApiModelProperty(value = "申购理由")
    private String applyRemark;

    @ApiModelProperty(value = "申请公司名称")
    private String applyOrgName;

    @ApiModelProperty(value = "申购公司名称")
    private String subscriptionCompanyName;

    @ApiModelProperty(value = "申购公司code")
    private String subscriptionCompanyCode;

    @ApiModelProperty(value = "申请人姓名")
    private String applyUser;

    @ApiModelProperty(value = "采购车辆数量")
    private Integer purchaseQuantity;

    @ApiModelProperty(value = "收货车辆数")
    private Integer takeDeliveryQuantity;

    @ApiModelProperty(value = "采购条线 1-寻网业务 2-商务业务")
    private Integer applyProductLine;

    @ApiModelProperty(value = "采购意向")
    private PurchaseIntentionResponse intentionResponse;

    @ApiModelProperty(value = "采购详情")
    private List<PurchaseApplyDetailDto> applyDetailList;

    @ApiModelProperty(value = "车载设备（巡网）列表")
    private List<PurchaseEquipmentDto> equipmentList;

    @ApiModelProperty(value = "收货信息列表")
    private List<VehicleReceivingDto> receivingList;

    @ApiModelProperty(value = "附件列表")
    private List<VehicleApplyFileDto> fileList;

    @ApiModelProperty(value = "采购意向单号", example = "INTENTION001")
    private String intentionNo;

    @ApiModelProperty(value = "供应商类型")
    private Integer supplierType;

    @ApiModelProperty(value = "申请人钉钉部门ID",required = true)
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人钉钉部门名称",required = true)
    private String originatorDeptName;

}
