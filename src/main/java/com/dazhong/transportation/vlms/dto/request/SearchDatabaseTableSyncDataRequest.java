package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SearchDatabaseTableSyncDataRequest {

    @ApiModelProperty(value = "同步数据库表名", example = "Vehicle")
    @NotEmpty(message = "同步表名不能为空")
    private String tableName;

    @ApiModelProperty(value = "索引标识", example = "-1")
    private Long index;

    @ApiModelProperty(value = "查询同步数据量", example = "1000")
    private Long pageSize;

    @ApiModelProperty(value = "车牌号", example = "沪CU6398")
    private String carNo;

    @ApiModelProperty(value = "车牌号模糊查询", example = "CU")
    private String carNoLike;

    @ApiModelProperty(value = "查询机构列表", example = "[1,2]")
    private List<Long> orgIdList;

    @ApiModelProperty(value = "查询车辆型号列表", example = "[1,2]")
    private List<Long> vehicleModelIdList;
    

}
