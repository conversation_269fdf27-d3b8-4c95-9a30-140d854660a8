package com.dazhong.transportation.vlms.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ExportAssetVehicleInfoDto {
    /**
     * 车辆唯一标识ID
     */
    private Long id;

    /**
     * 车架号（VIN码）
     */
    private String vin;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 发动机型号（车辆）
     */
    private String engineModel;

    /**
     * 发动机型号（车型）
     */
    private String engineModelNo;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 额度类型（1-社会牌照，2-纳管，3-网约，4-新能源网约，5-营运）
     */
    private Integer quotaType;

    /**
     * 额度类型
     */
     private String quotaTypeStr;

    /**
     * 额度资产归属公司ID
     */
    private Integer quotaAssetCompanyId;

    /**
     * 额度资产归属公司
     */
    private String quotaAssetCompanyName;

    /**
     * 资产状态（0-在建工程，1-固定资产，2-处置审批中，3-待处置（未交付），4-待报废（未交付），5-已处置，6-已报废）
     */
    private Integer propertyStatus;

    /**
     * 资产状态
     */
    private String propertyStatusStr;

    /**
     * 使用年限限制
     */
    private Integer usageAgeLimit;

    /**
     * 账面净值（车辆当前价值）
     */
    private BigDecimal remainPrice;

    /**
     * 资产所属公司ID
     */
    private Integer assetCompanyId;

    /**
     * 资产所属公司
     */
    private String assetCompanyName;

    /**
     * 实际运营公司（所属）ID
     */
    private Long ownOrganizationId;

    /**
     * 实际运营公司（所属）
     */
    private String ownOrganizationName;

    /**
     * 实际运营公司（使用）ID
     */
    private Long usageOrganizationId;

    /**
     * 实际运营公司（使用）
     */
    private String usageOrganizationName;

    /**
     * 所属车队
     */
    private String belongingTeam;

    /**
     * 产品线（1-巡网业务，2-商务业务）
     */
    private Integer productLine;

    /**
     * 产品线
     */
    private String productLineStr;

    /**
     * 业务线（1-巡网，2-长包，3-临租，4-大巴，5-公务用车）
     */
    private Integer businessLine;

    /**
     * 业务线
     */
    private String businessLineStr;

    /**
     * 运营状态（1-待运，2-租出）
     */
    private Integer operatingStatus;

    /**
     * 运营状态
     */
    private String operatingStatusStr;

    /**
     * 使用性质（行驶证）
     */
    private Integer usageIdRegistrationCard;

    /**
     * 使用性质
     */
    private String usageRegistrationCard;

    /**
     * 车辆类型（行驶证）
     */
    private Integer vehicleTypeRegistrationCard;

    /**
     * 车辆类型
     */
    private String vehicleTypeStrRegistrationCard;


    /**
     * 强制报废日期（行驶证）
     */
    private Date retirementDateRegistrationCard;

    /**
     * 强制报废日期（行驶证）
     */
    private String retirementDateStrRegistrationCard;

    /**
     * 营运/非营运标识（旧系统字段）
     */
    private Integer operateTypeId;

    /**
     * 营运/非营运标识
     */
    private String operateTypeStr;

    /**
     * 营运证号（旧系统字段）
     */
    private String operatingNo;

    /**
     * 投产日期（旧系统字段）
     */
    private Date startDate;

    /**
     * 投产日期
     */
    private String startDateStr;

    /**
     * 上牌日期（旧系统字段）
     */
    private Date licenseDate;

    /**
     * 上牌日期
     */
    private String licenseDateStr;

    /**
     * 车辆型号（车辆型号编号）
     */
    private String vehicleModelNo;

    /**
     * 车型名称
     */
    private String vehicleModelName;

    /**
     * 财务车型名称
     */
    private String financialModelName;

    /**
     * 商品车型编号
     */
    private Integer vehicleAbbreviationId;

    /**
     * 商品车型名称
     */
    private String vehicleAbbreviationName;

    /**
     * 座位数
     */
    private Integer assessPassenger;

    /**
     * 大巴实际座位数
     */
    private Integer busAssessPassenger;

    /**
     * 能源类型（如汽油、柴油、电动等）
     */
    private Integer gasTypeId;

    /**
     * 能源类型
     */
    private String gasTypeStr;


    /**
     * 轮胎规格
     */
    private String wheelParam;

    /**
     * 排放标准
     */
    private Integer exhaustId;

    /**
     * 排放标准
     */
    private String exhaustStr;

    /**
     * 车身长度（单位：毫米）
     */
    private Integer outLength;

    /**
     * 整备质量（单位：千克）
     */
    private Integer totalMass;

    /**
     * 油箱容积（单位：升）
     */
    private Integer fuelTankCapacity;

    /**
     * 电池容量（单位：千瓦时）
     */
    private BigDecimal batteryCapacity;

    /**
     * 原始购置金额（折旧数据）
     */
    private BigDecimal originalAmount;

    /**
     * 折旧月数
     */
    private Integer depreciationMonths;

    /**
     * 折旧开始日期
     */
    private Date depreciationStartDate;

    /**
     * 折旧开始日期
     */
    private String depreciationStartDateStr;

    /**
     * 当前折旧年月
     */
    private String currentMonth;

    /**
     * 累计折旧月数
     */
    private Integer accumulatedDepreciationMonths;

    /**
     * 折旧金额
     */
    private BigDecimal depreciationAmount;

    /**
     * 剩余残值
     */
    private BigDecimal remainingResidualValue;

    /**
     * 退役日期
     */
    private Date realRetirementDate;

    /**
     * 退役日期
     */
    private String realRetirementDateStr;
}