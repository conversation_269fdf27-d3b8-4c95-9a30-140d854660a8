package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "车辆申请附件DTO")
@Data
public class VehicleApplyFileDto implements Serializable {

    @ApiModelProperty(value = "文件类型 前端数据字典")
    private Integer fileType;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    private String fileUrl;

    @ApiModelProperty(value = "文件描述")
    private String fileDesc;

}
