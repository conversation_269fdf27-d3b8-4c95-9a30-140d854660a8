package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.SyncAssetVehicleInfoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "中间表同步数据查询结果")
public class SearchAssetVehicleSyncDataResponse implements Serializable {

    @ApiModelProperty(value = "中间表同步数据")
    private List<SyncAssetVehicleInfoDto> list;

    @ApiModelProperty(value = "下一次索引标识")
    private Long lastIndex;
}
