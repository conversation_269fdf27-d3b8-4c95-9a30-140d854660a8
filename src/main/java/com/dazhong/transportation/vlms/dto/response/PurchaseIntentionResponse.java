package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "车辆采购意向响应对象")
public class PurchaseIntentionResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID", example = "12345")
    private Long id;

    @ApiModelProperty(value = "采购意向单号", example = "INTENTION001")
    private String intentionNo;

    @ApiModelProperty(value = "合同号", example = "CONTRACT001")
    private String contractNo;

    @ApiModelProperty(value = "合同开始日期", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractStartDate;

    @ApiModelProperty(value = "合同结束日期", example = "2023-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractEndDate;

    @ApiModelProperty(value = "客户信息", example = "张三")
    private String customerUser;

    @ApiModelProperty(value = "车型名称", example = "Model S")
    private String vehicleModelName;

    @ApiModelProperty(value = "车型ID")
    private Long vehicleModelId;

    @ApiModelProperty(value = "数量", example = "5")
    private Integer quantity;

    @ApiModelProperty(value = "指导价", example = "300000.00")
    private BigDecimal guidePrice;

    @ApiModelProperty(value = "车身颜色", example = "黑色")
    private String vehicleBodyColor;

    @ApiModelProperty(value = "内饰颜色", example = "白色")
    private String vehicleInteriorColor;

    @ApiModelProperty(value = "期望到车日期", example = "2023-06-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectedArrivalDate;

    @ApiModelProperty(value = "期望到车最后日期", example = "2023-06-15")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectedArrivalLastDate;

    @ApiModelProperty(value = "合同约定牌照属性", example = "普通")
    private String licensePlateAttribute;

    @ApiModelProperty(value = "合同约定牌照所属地", example = "北京市")
    private String licensePlateLocation;

    @ApiModelProperty(value = "装潢需求", example = "豪华内饰")
    private String decorationDemand;

    @ApiModelProperty(value = "数据同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dataSyncTime;

    @ApiModelProperty(value = "发起人")
    private String applyUser;

    @ApiModelProperty(value = "后续单据号")
    private String purchaseApplyNo;

    @ApiModelProperty(value = "后续单据状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)")
    private String purchaseApplyStatus;

    @ApiModelProperty(value = "所属公司code")
    private String orgCode;

    @ApiModelProperty(value = "所属公司名称")
    private String orgName;

    @ApiModelProperty(value = "销售姓名")
    private String saleName;

    @ApiModelProperty(value = "汽车之家售价(元)")
    private Integer priceOnAutohome;

    @ApiModelProperty(value = "采购备注")
    private String purchaseRemark;

    @ApiModelProperty(value = "钉钉ID")
    private String dingTalkId;
}
