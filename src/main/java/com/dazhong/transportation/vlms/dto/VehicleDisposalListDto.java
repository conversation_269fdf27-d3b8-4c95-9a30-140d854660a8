package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class VehicleDisposalListDto {

    @ApiModelProperty(value = "序号", example = "1")
    private Long rowNumber;

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "单据类型 1-出售申请（巡网业务） 2-报废申请 3-出售申请单（商务业务）", example = "1")
    private String documentType;

    @ApiModelProperty(value = "单据号", example = "1")
    private String documentNo;

    @ApiModelProperty(value = "钉钉审批单号", example = "1")
    private String dingTalkNo;

    @ApiModelProperty(value = "提交日期", example = "1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;

    @ApiModelProperty(value = "单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废", example = "1")
    private String documentStatus;

    @ApiModelProperty(value = "出售车辆公司code", example = "1")
    private String sellingCompanyCode;

    @ApiModelProperty(value = "出售车辆公司名", example = "1")
    private String sellingCompanyName;

    @ApiModelProperty(value = "车辆使用部门code", example = "1")
    private String useDepartmentCode;

    @ApiModelProperty(value = "车辆使用部门名", example = "XX公司")
    private String useDepartmentName;

    @ApiModelProperty(value = "车辆所属部门code", example = "1")
    private String departmentCode;

    @ApiModelProperty(value = "车辆所属部门名", example = "XX公司")
    private String departmentName;

    @ApiModelProperty(value = "单据标题", example = "1")
    private String documentTitle;

    @ApiModelProperty(value = "条线", example = "1")
    private String productLine;

    @ApiModelProperty(value = "车辆数", example = "1")
    private Integer vehicleNumber;

    @ApiModelProperty(value = "创建人名称", example = "1")
    private String createOperName;

    @ApiModelProperty(value = "关联任务编号（退牌任务）", example = "1")
    private String taskNumber;
}
