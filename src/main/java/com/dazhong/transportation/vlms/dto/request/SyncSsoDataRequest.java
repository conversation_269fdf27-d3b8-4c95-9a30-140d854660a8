package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024-12-23 15:42
 */
@ApiModel(description = "同步单点账号参数")
@Data
public class SyncSsoDataRequest {


    @ApiModelProperty(value = "请求参数" ,required = true)
    @NotBlank(message = "请求参数param不能为空")
    private String param;


    private String clientId;
}
