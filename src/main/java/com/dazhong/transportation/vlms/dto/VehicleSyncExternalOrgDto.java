package com.dazhong.transportation.vlms.dto;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleSyncExternalOrgDto implements Serializable {

    /**
     *   主键
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     *   组织机构code
     */
    @NotBlank(message = "组织机构code不能为空")
    private String companyCode;

    /**
     *   组织机构名称
     */
    @NotBlank(message = "组织机构名称不能为空")
    private String companyName;

    private String pgUid;

    @NotNull(message = "状态不能为空")
    private Integer vStatus;

    @NotBlank(message = "唯一标识不能为空")
    private String vgUid;

    /**
     *   类型 0 公司 1 部门
     */
    @NotNull(message = "类型不能为空")
    private Integer orgType;


}
