package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SearchAssetVehicleSyncDataRequest {

    @ApiModelProperty(value = "车牌号", example = "沪CU6398")
    private String carNo;

    @ApiModelProperty(value = "车牌号模糊查询", example = "CU")
    private String carNoLike;

    @ApiModelProperty(value = "查询机构列表", example = "[1,2]")
    private List<Long> orgIdList;
}
