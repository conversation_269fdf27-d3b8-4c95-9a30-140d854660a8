package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.dazhong.transportation.vlms.dto.VehicleAttachmentDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-08 14:47
 */
@Data
@ApiModel(description = "车辆附件内容参数")
public class BatchSaveVehicleAttachmentRequest {

    @ApiModelProperty(value = "文件类型 3-行驶证文件 4-产证文件 5-合格证文件 6-发票文件 7-购置税文件 8-营运证")
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    @ApiModelProperty(value = "装潢列表")
    @NotEmpty(message = "附件列表不能为空")
    private List<VehicleAttachmentDto> list;
}
