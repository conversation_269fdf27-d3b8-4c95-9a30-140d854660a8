package com.dazhong.transportation.vlms.dto.response;

import java.io.Serializable;
import java.util.List;

import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "车辆转固申请详情对象")
@Data
public class TransferFixedDetailsResponse implements Serializable {

    @ApiModelProperty(value = "转固申请编号", example = "TF001")
    private String applyNo;

    @ApiModelProperty(value = "钉钉审批编号", example = "DING001")
    private String approvalNumber;

    @ApiModelProperty(value = "转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)", example = "3")
    private Integer applyStatus;

    @ApiModelProperty(value = "申请名称", example = "转固申请示例")
    private String applyName;

    @ApiModelProperty(value = "申请备注", example = "无")
    private String applyRemark;

    @ApiModelProperty(value = "申请车辆数量", example = "5")
    private Integer transferQuantity;

    @ApiModelProperty(value = "申请人姓名", example = "张三")
    private String applyUser;

    @ApiModelProperty(value = "资产所有公司ID")
    private Integer ownerId;

    @ApiModelProperty(value = "资产所有公司名称")
    private String ownerName;

    @ApiModelProperty(value = "申请公司ID")
    private Long applyOrgId;

    @ApiModelProperty(value = "申请公司名称")
    private String applyOrgName;

    @ApiModelProperty(value = "转固车辆列表")
    private List<VehicleTransferFixedResponse> vehicleList;

    @ApiModelProperty(value = "附件列表")
    private List<VehicleApplyFileDto> fileList;

    @ApiModelProperty(value = "资产公司所属部门code",required = true)
    private String departmentCode;

    @ApiModelProperty(value = "资产公司所属部门名称",required = true)
    private String departmentName;

    @ApiModelProperty(value = "申请人钉钉部门ID",required = true)
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人钉钉部门名称",required = true)
    private String originatorDeptName;
}
