package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.PurchaseApplyDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.dto.PurchaseEquipmentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-06 14:02
 */
@ApiModel(description = "创建采购申请参数")
@Data
public class AddPurchaseApplyRequest {

    @ApiModelProperty(value = "采购意向单号(采购意向发起必填)")
    private String intentionNo;

    @ApiModelProperty(value = "单据标题",required = true)
    @NotBlank(message = "单据标题不能为空")
    private String applyName;

    @ApiModelProperty(value = "采购车辆数量",required = true)
    @NotNull(message = "采购车辆数量不能为空")
    @Min(value = 1,message = "采购车辆数不能小于1")
    private Integer purchaseQuantity;

    @ApiModelProperty(value = "单据所属资产公司ID",required = true)
    @NotNull(message = "单据所属资产公司不能为空")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属机构ID",required = true)
    @NotNull(message = "单据所属机构不能为空")
    private Long applyOrgId;

    @ApiModelProperty(value = "申购公司code",required = true)
    @NotBlank(message = "申购公司code不能为空")
    private String subscriptionCompanyCode;

    @ApiModelProperty(value = "申购公司名称",required = true)
    @NotBlank(message = "申购公司名称不能为空")
    private String subscriptionCompanyName;

    @ApiModelProperty(value = "采购条线 1-寻网业务 2-商务业务",required = true)
    @NotNull(message = "采购条线不能为空")
    private Integer applyProductLine;

    @ApiModelProperty(value = "申购理由",required = true)
    @NotBlank(message = "申购理由不能为空")
    @Size(max = 300,message = "申购理由不能超过300个字符")
    private String applyRemark;

    @ApiModelProperty(value = "采购详情",required = true)
    @NotEmpty(message = "采购详情不能为空")
    private List<PurchaseApplyDetailDto> applyDetailList;

    @ApiModelProperty(value = "车载设备（巡网）列表")
    private List<PurchaseEquipmentDto> equipmentList;

    @ApiModelProperty(value = "附件列表")
    private List<VehicleApplyFileDto> fileList;

    @ApiModelProperty(value = "操作类型 1-保存 2-提交 ",required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;

    @ApiModelProperty(value = "供应商类型")
    private Integer supplierType;

    @ApiModelProperty(value = "申请人钉钉部门ID",required = true)
    @NotNull(message = "申请人钉钉部门不能为空")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人钉钉部门名称",required = true)
    @NotBlank(message = "申请人钉钉部门名称不能为空")
    private String originatorDeptName;

}
