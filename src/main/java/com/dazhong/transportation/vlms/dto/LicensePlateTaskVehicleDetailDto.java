package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "车牌任务车辆详情信息")
public class LicensePlateTaskVehicleDetailDto {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "任务编号", example = "T001")
    private String taskNumber;

    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "车辆主数据id", notes = "t_vehicle_info.id")
    private Long vehicleId;

    @ApiModelProperty(value = "车架号", example = "VIN12345678901234567")
    private String vin;

    @ApiModelProperty(value = "车型id", example = "101")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名", example = "101")
    private String vehicleModelName;

    @ApiModelProperty(value = "资产所属公司id", example = "1")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产所属公司名", example = "XYZ")
    private String assetCompanyName;

    @ApiModelProperty(value = "资产机构id", example = "1")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "资产机构名", example = "XYZ")
    private String ownOrganizationName;

    @ApiModelProperty(value = "使用机构id", example = "1")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "使用机构名", example = "XYZ")
    private String usageOrganizationName;

    @ApiModelProperty(value = "发证日期（行驶证）", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDateRegistrationCard;

    @ApiModelProperty(value = "车辆类型 (行驶证)", example = "SUV")
    private Integer vehicleTypeRegistrationCard;

    @ApiModelProperty(value = "使用性质 (行驶证)", example = "SUV")
    private Integer usageIdRegistrationCard;

    @ApiModelProperty(value = "注册日期(行驶证)", example = "2023-01-02")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registrationDateRegistrationCard;

    @ApiModelProperty(value = "档案编号", example = "112342314")
    private String fileNumber;

    @ApiModelProperty(value = "强制报废日期 (行驶证)", example = "2023-01-02")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date retirementDateRegistrationCard;

    @ApiModelProperty(value = "年检到期日 (行驶证)", example = "2023-01-02")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date annualInspectionDueDateRegistrationCard;

    @ApiModelProperty(value = "退还额度类型 1-退还 2-不退还 3-不涉及", example = "1")
    private Integer returnQuotaType;

    @ApiModelProperty(value = "是否使用额度（上牌） 1-是 2-否 ", example = "1")
    private Integer useQuotaType;

    @ApiModelProperty(value = "额度类型（1-社会牌照，2-纳管，3-网约，4-新能源网约，5-营运）", example = "1")
    private Integer quotaType;

    @ApiModelProperty(value = "额度资产归属公司Id", example = "ABC")
    private Integer quotaAssetCompanyId;

    @ApiModelProperty(value = "额度资产归属公司名", example = "ABC")
    private String quotaAssetCompanyName;

    @ApiModelProperty(value = "额度编号", example = "Q001")
    private String quotaNumber;

    @ApiModelProperty(value = "额度单打印时间", example = "2023-01-02")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date quotaPrintDate;

    @ApiModelProperty(value = "登记人", example = "李四")
    private String createOperName;

    @ApiModelProperty(value = "登记时间", example = "李四")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
