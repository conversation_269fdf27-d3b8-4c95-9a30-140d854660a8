package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "查询车型列表条件")
public class SearchVehicleModelListRequest extends PageRequest {

    @ApiModelProperty(value = "车型名称",  example = "丰田", notes = "车型名称 - 模糊查询")
    private String vehicleModelName;

    @ApiModelProperty(value = "车型编号", example = "BMW320i", notes = "车型编号 - 精确查询，支持根据车辆型号进行精确匹配筛选")
    private String vehicleModelNo;
}
