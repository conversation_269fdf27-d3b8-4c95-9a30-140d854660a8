package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "车辆申请单列表dto")
public class VehicleApplicationListDto {

    @ApiModelProperty(value = "序号", example = "1")
    private Long rowNumber;

    @ApiModelProperty(value = "主鍵id", example = "1")
    private Long id;

    @ApiModelProperty(value = "单据类型 1-车辆调拨 2-车辆转籍 3-切换业务类型", example = "1")
    private Integer applicationType;

    @ApiModelProperty(value = "钉钉审批单号", example = "1")
    private String dingTalkNo;

    @ApiModelProperty(value = "提交日期", example = "1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;

    @ApiModelProperty(value = "单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废", example = "1")
    private Integer documentStatus;

    @ApiModelProperty(value = "单据标题", example = "1")
    private String documentTitle;

    @ApiModelProperty(value = "车辆数量", example = "1")
    private Integer vehicleNumber;

    @ApiModelProperty(value = "单据所属资产公司id", example = "1")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属资产公司名", example = "出售申请单")
    private String ownerName;

    @ApiModelProperty(value = "单据所属机构id", example = "1")
    private Long organizationId;

    @ApiModelProperty(value = "单据所属机构名", example = "出售申请单")
    private String organizationName;

    @ApiModelProperty(value = "创建人名称", example = "1")
    private String createOperName;
}
