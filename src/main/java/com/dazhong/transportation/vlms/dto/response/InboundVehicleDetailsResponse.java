package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.dto.VehiclePurchaseReceiptDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-08 13:05
 */
@Data
@ApiModel(description = "入库车辆详情信息")
public class InboundVehicleDetailsResponse {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "车辆ID")
    private Long id;

    @ApiModelProperty(value = "车辆采购收货信息")
    private VehiclePurchaseReceiptDto receiptInfo;

    @ApiModelProperty(value = "上牌信息列表")
    private List<LicensePlateTaskVehicleDetailDto> licensePlateList;

    @ApiModelProperty(value = "装潢记录列表")
    private List<VehicleDecorationResponse> decorationList;

    @ApiModelProperty(value = "附件记录列表")
    private List<VehicleFileRecordResponse> fileRecordList;

    @ApiModelProperty(value = "其他记录列表")
    private VehicleOtherInfoResponse otherInfo;
}
