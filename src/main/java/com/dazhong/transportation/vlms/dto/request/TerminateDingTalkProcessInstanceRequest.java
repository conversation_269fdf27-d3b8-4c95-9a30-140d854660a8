package com.dazhong.transportation.vlms.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TerminateDingTalkProcessInstanceRequest {

    /**
     * 钉钉审批流ID
     */
    private String processInstanceId;

    /**
     * 撤销备注
     */
    private String remark;

    /**
     * 发起审批人的 userId
     */
    private String operatingUserId;

    /**
     * 是否系统撤销 如果为false 则必须填写operatingUserId 且 与发起人ID 相同
     */
    private boolean isSystem = false;
}
