package com.dazhong.transportation.vlms.dto.response;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-18 17:47
 */
@Data
@ApiModel(description = "车辆其他信息参数")
public class VehicleOtherInfoResponse {

    @ApiModelProperty(value = "营运类别")
    private Integer operationCategoryId;

    @ApiModelProperty(value = "营运证号")
    private String operatingNo;

    @ApiModelProperty(value = "是否营运")
    private Integer operateTypeId;

    @ApiModelProperty(value = "号牌种类")
    private Integer vehicleCategoryId;

    @ApiModelProperty(value = "管辖区域")
    private Integer areaId;

    @ApiModelProperty(value = "车辆出厂日期 (产证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productDate;

    @ApiModelProperty(value = "发证日期 (产证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDate;

    @ApiModelProperty(value = "产证编号")
    private String certificateNumber;

    @ApiModelProperty(value = "资产所属公司")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产机构")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "使用机构")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "使用年限")
    private Integer usageYears;

    @ApiModelProperty(value = "折旧年限")
    private Integer depreciationYears;

    @ApiModelProperty(value = "裸车价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "购置税")
    private BigDecimal purchaseTax;

    @ApiModelProperty(value = "牌照费")
    private BigDecimal licensePlatePrice;

    @ApiModelProperty(value = "上牌杂费")
    private BigDecimal licensePlateOtherPrice;

    @ApiModelProperty(value = "装潢费")
    private BigDecimal upholsterPrice;

    @ApiModelProperty(value = "购置总价")
    private BigDecimal totalPrice;
}
