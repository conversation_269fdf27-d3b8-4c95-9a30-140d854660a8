package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024-12-23 15:42
 */
@ApiModel(description = "同步数据参数")
@Data
public class SyncDataRequest {

    @ApiModelProperty(value = "请求参数", required = true)
    @NotBlank(message = "请求参数bizRequest不能为空")
    private String bizRequest;

    @ApiModelProperty(value = "方法名称", required = true)
    @NotBlank(message = "方法名称method不能为空")
    private String method;

}
