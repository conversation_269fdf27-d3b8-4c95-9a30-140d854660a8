package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 额度总数信息响应对象
 *
 * <AUTHOR>
 * @date 2025-01-13 14:08:00
 */
@Data
public class LicensePlateQuotaResponse {

    /**
     * 额度总数
     */
    @ApiModelProperty(value = "额度总数", example = "100")
    private Integer quota;

    /**
     * 预占用
     */
    @ApiModelProperty(value = "预占用", example = "10")
    private Integer preOccupied;

    /**
     * 占用
     */
    @ApiModelProperty(value = "占用", example = "20")
    private Integer occupied;

    /**
     * 剩余可用
     */
    @ApiModelProperty(value = "剩余可用", example = "70")
    private Integer remaining;
}
