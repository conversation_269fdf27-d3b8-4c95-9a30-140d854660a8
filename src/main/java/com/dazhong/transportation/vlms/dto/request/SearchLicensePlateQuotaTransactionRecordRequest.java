package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "额度单流水表入参")
public class SearchLicensePlateQuotaTransactionRecordRequest extends PageRequest {

    /**
     * 额度单
     */
    @ApiModelProperty("额度单 非必传")
    private String quotaNumber;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号 非必传")
    private String licensePlate;

    /**
     * 额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @ApiModelProperty("额度类型 非必传")
    private Integer quotaType;

    /**
     * 额度单打印时间-起始
     */
    @ApiModelProperty("额度单打印时间-起始 非必传")
    private Date quotaPrintDateStart;

    /**
     * 额度单打印时间-结束
     */
    @ApiModelProperty("额度单打印时间-结束 非必传")
    private Date quotaPrintDateEnd;

}
