package com.dazhong.transportation.vlms.dto.response;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 组织机构详情结果对象
 * <AUTHOR>
 * @date 2024-12-20 17:30
 */
@ApiModel(description = "组织机构详情结果对象")
@Data
public class OrgDetailsResponse implements Serializable {


    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private Long id;

    /**
     * 机构名称
     */
    @ApiModelProperty("资源ID")
    private String companyName;

    /**
     * 是否选中 1-是 2-否
     */
    @ApiModelProperty("是否禁用 1-是 2-否")
    private Integer disabledState;

    /**
     * 总经理
     */
    @ApiModelProperty("总经理")
    private String generalManager;

    /**
     * 钉钉号
     */
    @ApiModelProperty(value = "钉钉号")
    private String dingTalkNum;

    @ApiModelProperty("总经理手机号")
    private String generalPhone;

}