package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(description = "可维护数据字典结果")
@NoArgsConstructor
public class DataMaintainDictResponse<T> {

    @ApiModelProperty(value = "数据字典编码")
    private String dataCode;

    @ApiModelProperty(value = "数据字典名称")
    private String dataName;

    @ApiModelProperty(value = "数据字典内容")
    @NotEmpty(message = "内容不能为空")
    private List<T> dataDictList;

    public DataMaintainDictResponse(List<T> dataDictList){
        if(dataDictList == null){
            dataDictList = new ArrayList<>();
        }
        this.dataDictList = dataDictList;
    }
}
