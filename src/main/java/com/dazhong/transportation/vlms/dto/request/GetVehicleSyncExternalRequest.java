package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "车辆同步数据信息参数")
public class GetVehicleSyncExternalRequest {

    @ApiModelProperty(value = "车牌号")
    @NotBlank(message = "车牌号不能为空")
    private String licensePlate;

}
