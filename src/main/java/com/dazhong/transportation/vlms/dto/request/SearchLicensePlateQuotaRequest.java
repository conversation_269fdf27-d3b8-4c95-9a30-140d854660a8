package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "额度一览入参")
public class SearchLicensePlateQuotaRequest extends PageRequest {

    /**
     * 额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @ApiModelProperty("额度类型 非必传")
    private Integer quotaType;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty("额度资产归属公司列表 非必传")
    private List<Integer> assetCompanyIdList;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty("额度资产归属公司 非必传")
    private Integer assetCompanyId;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty("是否公司展示额度 必传 1-是 2-否")
    private Integer isCompanyShowQuota = 2;
}
