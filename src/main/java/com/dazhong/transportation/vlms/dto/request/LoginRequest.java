package com.dazhong.transportation.vlms.dto.request;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-25 18:42
 */
@ApiModel(description = "用户登录参数")
@Data
public class LoginRequest {

    /**
     *   用户账号信息
     */
    @ApiModelProperty(value = "用户账号信息" ,required = true)
    @NotBlank(message = "param参数不能为空")
    private String param;

}
