package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "车型表数据")
public class SyncDatabaseVehicleModelDto {

    @ApiModelProperty(value = "主键", notes = "id")
    private Long id;

    @ApiModelProperty(value = "车型", notes = "vehicle_model_no")
    private String modelNo;

    @ApiModelProperty(value = "发动机型号", notes = "engine_model_no")
    private String engineModelNo;

    @ApiModelProperty(value = "排量", notes = "capacity")
    private BigDecimal capacity;

    @ApiModelProperty(value = "功率", notes = "power")
    private Integer power;

    @ApiModelProperty(value = "前轮距", notes = "tread_front")
    private Integer treadFront;

    @ApiModelProperty(value = "后轮距", notes = "tread_rear")
    private Integer treadRear;

    @ApiModelProperty(value = "轮胎数", notes = "wheel_quantity")
    private Integer wheelQuantity;

    @ApiModelProperty(value = "轮胎规格", notes = "wheel_param")
    private String wheelParam;

    @ApiModelProperty(value = "后轴钢板弹簧数", notes = "spring_lamination")
    private Integer springLamination;

    @ApiModelProperty(value = "轴距1", notes = "wheel_base1")
    private Integer wheelBase1;

    @ApiModelProperty(value = "轴距2", notes = "wheel_base2")
    private Integer wheelBase2;

    @ApiModelProperty(value = "轴距3", notes = "wheel_base3")
    private Integer wheelBase3;

    @ApiModelProperty(value = "轴数", notes = "axle_quantity")
    private Integer axleQuantity;

    @ApiModelProperty(value = "外廓尺寸长", notes = "out_length")
    private Integer outLength;

    @ApiModelProperty(value = "外廓尺寸宽", notes = "out_width")
    private Integer outWidth;

    @ApiModelProperty(value = "外廓尺寸高", notes = "out_height")
    private Integer outHeight;

    @ApiModelProperty(value = "货箱内部尺寸长", notes = "container_length")
    private Integer containerLength;

    @ApiModelProperty(value = "货箱内部尺寸宽", notes = "container_width")
    private Integer containerWidth;

    @ApiModelProperty(value = "货箱内部尺寸高", notes = "container_height")
    private Integer containerHeight;

    @ApiModelProperty(value = "总质量", notes = "total_mass")
    private Integer totalMass;

    @ApiModelProperty(value = "核定载质量", notes = "assess_mass")
    private Integer assessMass;

    @ApiModelProperty(value = "核定载客", notes = "assess_passenger")
    private Integer assessPassenger;

    @ApiModelProperty(value = "准牵引总质量", notes = "traction_mass")
    private Integer tractionMass;

    @ApiModelProperty(value = "驾驶室载客", notes = "cab_passenger")
    private Integer cabPassenger;

    @ApiModelProperty(value = "国产/进口", notes = "manufacture_location")
    private String manufactureLocation;

    @ApiModelProperty(value = "车门数", notes = "door_quantity")
    private Integer doorQuantity;

    @ApiModelProperty(value = "档位数", notes = "gear_quantity")
    private Integer gearQuantity;

    @ApiModelProperty(value = "0-100加速", notes = "acceleration")
    private BigDecimal acceleration;

    @ApiModelProperty(value = "最高时速", notes = "speed")
    private Integer speed;

    @ApiModelProperty(value = "最小转弯半径", notes = "turning_radius")
    private BigDecimal turningRadius;

    @ApiModelProperty(value = "最小离地间隙", notes = "road_clearance")
    private Integer roadClearance;

    @ApiModelProperty(value = "最大爬坡度", notes = "gradient")
    private Integer gradient;

    @ApiModelProperty(value = "等速油耗", notes = "fuel_economy")
    private BigDecimal fuelEconomy;

    @ApiModelProperty(value = "工信部油耗", notes = "fuel_economy_miit")
    private BigDecimal fuelEconomyMiit;

    @ApiModelProperty(value = "扭矩", notes = "torque")
    private Integer torque;

    @ApiModelProperty(value = "压缩比", notes = "compression_ratio")
    private String compressionRatio;

    @ApiModelProperty(value = "制造商", notes = "manufacturer_id")
    private Integer manufacturerId;

    @ApiModelProperty(value = "燃料种类", notes = "gas_type_id")
    private Integer gasTypeId;

    @ApiModelProperty(value = "车辆品牌", notes = "vehicle_brand_id")
    private Integer vehicleBrandId;

    @ApiModelProperty(value = "车辆类型", notes = "vehicle_type_id")
    private Integer vehicleTypeId;

    @ApiModelProperty(value = "驱动类型", notes = "wheel_drive_id")
    private Integer wheelDriveId;

    @ApiModelProperty(value = "制动形式", notes = "break_mode_id")
    private Integer breakModeId;

    @ApiModelProperty(value = "转向方式", notes = "turn_mode_id")
    private Integer turnModeId;

    @ApiModelProperty(value = "驾驶位置", notes = "drive_position_id")
    private Integer drivePositionId;

    @ApiModelProperty(value = "发动机位置", notes = "engine_position_id")
    private Integer enginePositionId;

    @ApiModelProperty(value = "车辆简称", notes = "vehicle_abbreviation_id")
    private Integer vehicleAbbreviationId;

    @ApiModelProperty(value = "排放标准", notes = "exhaust_id")
    private Integer exhaustId;

    @ApiModelProperty(value = "变速箱形式", notes = "gear_box_type_id")
    private Integer gearBoxTypeId;
}
