package com.dazhong.transportation.vlms.dto.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-23 15:42
 */
@ApiModel(description = "修改资源参数")
@Data
public class UpdateResourceRequest {

    /**
     *   资源key
     */
    @ApiModelProperty(value = "资源key" ,required = true)
    @NotBlank(message = "资源key不能为空")
    private String resourceKey;

    /**
     *   资源名称
     */
    @ApiModelProperty(value = "资源名称" ,required = true)
    @NotBlank(message = "资源名称不能为空")
    private String resourceName;

    /**
     *   资源url
     */
    @ApiModelProperty(value = "资源url")
    private String resourceUrl;


    /**
     *   资源code
     */
    @ApiModelProperty(value = "资源code" ,required = true)
    @NotBlank(message = "资源code")
    private String resourceCode;


    /**
     *   菜单是否隐藏 1-是 2-否
     */
    @ApiModelProperty(value = "菜单是否隐藏 1-是 2-否" ,required = true)
    @NotBlank(message = "菜单是否隐藏不能为空")
    private String hide;

    /**
     *   标题是否隐藏 1-是 2-否
     */
    @ApiModelProperty(value = "标题是否隐藏 1-是 2-否" ,required = true)
    @NotBlank(message = "标题是否隐藏不能为空")
    private String hideTitle;

    /**
     *   资源类型  1-静态资源 2-功能点
     */
    @ApiModelProperty(value = "资源类型 1-静态资源 2-功能点" ,required = true)
    @NotBlank(message = "资源类型不能为空")
    private String resourceType;


    /**
     *   资源图标url
     */
    @ApiModelProperty(value = "资源图标url")
    private String resourceIconUrl;

    /**
     *  资源id
     */
    @ApiModelProperty(value = "资源id" ,required = true)
    @NotNull(message = "资源id不能为空")
    private Long id;


    /**
     *   排序标记
     */
    @ApiModelProperty(value = "排序标记" ,required = true)
    @NotNull(message = "排序标记不能为空")
    private Integer sort;
}
