package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 额度信息表响应对象
 *
 * <AUTHOR>
 * @date 2024-12-30 09:08:00
 */
@ApiModel(description = "额度调整入参")
@Data
public class UpdateQuotaDto {

    /**
     * 调整类型 1-总数增加 2-总数减少
     */
    @ApiModelProperty(value = "调整类型", example = "1")
    private Integer adjustType;

    /**
     * 额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @ApiModelProperty(value = "额度类型", example = "1")
    private Integer quotaType;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty(value = "额度资产归属公司id")
    private Integer assetCompanyId;

    /**
     * 调整个数
     */
    @ApiModelProperty(value = "调整个数", example = "100")
    private Integer adjustNum;

    /**
     * 调整方式 1-预占用 2-占用
     */
    @ApiModelProperty(value = "调整原因")
    private Integer adjustWay;
}
