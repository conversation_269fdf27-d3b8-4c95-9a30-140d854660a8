package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-08 14:45
 */
@Data
@ApiModel(description = "车辆装潢内容信息")
public class VehicleAttachmentDto {

    @ApiModelProperty(value = "文件类型 3-行驶证文件 4-产证文件 5-合格证文件 6-发票文件 7-购置税文件 8-营运证")
    private Integer fileType;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件名称")
    private String fileName;


}
