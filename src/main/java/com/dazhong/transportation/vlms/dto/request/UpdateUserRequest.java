package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-23 9:11
 */
@ApiModel(description = "修改用户信息")
@Data
public class UpdateUserRequest {


    /**
     *  用户id
     */
    @ApiModelProperty(value = "用户id" ,required = true)
    @NotNull(message = "用户id不能为空")
    private Long id;

    /**
     * 关联角色
     */
    @ApiModelProperty(value = "关联角色",required = true)
    @NotEmpty(message = "关联角色不能为空")
    private List<Long> roleIdList;

    /**
     * 关联机构
     */
    @ApiModelProperty(value = "关联机构")
    private List<Long> orgIdList;

    /**
     * 用户关联owner公司ID
     */
    @ApiModelProperty(value = "用户关联owner公司ID")
    private List<Integer> ownerIdList;
}
