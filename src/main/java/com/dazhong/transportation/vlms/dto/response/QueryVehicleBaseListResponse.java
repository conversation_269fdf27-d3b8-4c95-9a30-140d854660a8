package com.dazhong.transportation.vlms.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryVehicleBaseListResponse implements Serializable {

    /**
     * 状态码 0：成功
     */
    private int code;

    /**
     * 失败原因
     */
    private String message;

    /**
     * 消息体
     */
    private QueryVehicleBaseListData data;

    @Data
    public static class QueryVehicleBaseListData implements Serializable {
        /**
         * 汽车之家车型列表
         */
        private List<VehicleBaseInfo> info;
    }

    @Data
    public static class VehicleBaseInfo implements Serializable {
        /**
         * 汽车之家车型表主键ID
         */
        private Long id;

        /**
         * 汽车之家车型名称
         */
        private String fullModelName;
    }
}