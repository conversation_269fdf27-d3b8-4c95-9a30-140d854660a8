package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DataOwnerDto {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "车辆拥有公司名称")
    @NotBlank(message = "车辆拥有公司名称不能为空")
    private String name;

    @ApiModelProperty(value = "车辆拥有公司地址")
    private String address;

    @ApiModelProperty(value = "车辆拥有公司电话")
    private String phone;

    @ApiModelProperty(value = "总经理名称")
    private String ceoName;

    @ApiModelProperty(value = "总经理手机号")
    private String ceoPhone;

    @ApiModelProperty(value = "钉钉号")
    private String dingTalkNo;

}
