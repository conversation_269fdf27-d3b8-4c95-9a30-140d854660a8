package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.LicensePlateTaskQuotaDetailDto;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class LicensePlateTaskDetailResponse {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "任务类型 1-上牌任务 2-退牌任务", example = "1")
    private Integer taskType;

    @ApiModelProperty(value = "是否使用额度（上牌） 1-是 2-否 ", example = "1")
    private Integer useQuotaType;

    @ApiModelProperty(value = "是否使用额度（退牌） 1-退还 2-不退还 3-不执行", example = "1")
    private Integer returnQuotaType;

    @ApiModelProperty(value = "额度资产归属公司code", example = "ABC")
    private String assetCompanyId;

    @ApiModelProperty(value = "额度资产归属公司名字", example = "ABC")
    private String assetCompanyName;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运", example = "1")
    private Integer quotaType;

    @ApiModelProperty(value = "匹配类型 1-明确匹配额度编号 2-未明确匹配额度编号 3-不知道额度编号", example = "1")
    private Integer matchType;

    @ApiModelProperty(value = "车辆详情列表")
    private List<LicensePlateTaskVehicleDetailDto> vehicleDetailList;

    @ApiModelProperty(value = "额度详情列表")
    private List<LicensePlateTaskQuotaDetailDto> quotaDetailList;
}
