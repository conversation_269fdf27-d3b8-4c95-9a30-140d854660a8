package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalDingTalkResultDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "车辆处置申请单详情响应对象")
public class DisposalApplicationDetailResponse {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "单据号", example = "D001")
    private String documentNo;

    @ApiModelProperty(value = "钉钉审批号", example = "D001")
    private String dingTalkNo;

    @ApiModelProperty(value = "单据类型 1-出售申请 2-报废申请", example = "1")
    private Integer documentType;

    @ApiModelProperty(value = "单据标题", example = "出售申请单")
    private String documentTitle;

    @ApiModelProperty(value = "单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废", example = "1")
    private String documentStatus;

    @ApiModelProperty(value = "申请人钉钉部门ID")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人钉钉部门名称")
    private String originatorDeptName;

    @ApiModelProperty(value = "所在部门ID", example = "技术部")
    private Long organizationId;

    @ApiModelProperty(value = "所在部门", example = "技术部")
    private String organizationName;

    @ApiModelProperty(value = "单据所属资产公司ID", required = true)
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属机构名", required = true)
    private String ownerName;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "出售车辆公司code", example = "111")
    private String sellingCompanyCode;

    @ApiModelProperty(value = "出售车辆公司名", example = "XX公司")
    private String sellingCompanyName;

    @ApiModelProperty(value = "车辆使用部门code", example = "1")
    private String useDepartmentCode;

    @ApiModelProperty(value = "车辆使用部门名", example = "XX公司")
    private String useDepartmentName;

    @ApiModelProperty(value = "车辆所属部门code", example = "1")
    private String departmentCode;

    @ApiModelProperty(value = "车辆所属部门名", example = "XX公司")
    private String departmentName;

    @ApiModelProperty(value = "车辆数量", example = "1")
    private Integer vehicleNumber;

    @ApiModelProperty(value = "关联任务编号（退牌任务）", example = "T001")
    private String taskNumber;

    @ApiModelProperty(value = "备注", example = "beizhu")
    private String remark;

    @ApiModelProperty(value = "本月批次", example = "202403")
    private String monthBatch;

    @ApiModelProperty(value = "申请人", example = "beizhu")
    private Long createOperId;

    @ApiModelProperty(value = "申请人", example = "beizhu")
    private String createOperName;

    @ApiModelProperty(value = "车辆明细")
    private List<VehicleDisposalDetailDto> vehicleDisposalDetailList;

    @ApiModelProperty(value = "附件")
    private List<VehicleApplyFileDto> vehicleApplyFileDtoList;

    @ApiModelProperty(value = "钉钉回调信息")
    private VehicleDisposalDingTalkResultDto vehicleDisposalDingTalkResult;

}
