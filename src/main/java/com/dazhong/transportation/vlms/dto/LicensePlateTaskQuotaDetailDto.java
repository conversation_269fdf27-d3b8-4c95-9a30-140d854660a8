package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(description = "车牌任务额度详情信息")
public class LicensePlateTaskQuotaDetailDto implements Serializable {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "任务编号", example = "T001")
    private String taskNumber;

    @ApiModelProperty(value = "额度编号", example = "1")
    private String quotaNumber;

    @ApiModelProperty(value = "额度单打印时间", example = "1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date quotaPrintDate;

    @ApiModelProperty(value = "额度资产归属公司id")
    private Integer quotaAssetCompanyId;

    @ApiModelProperty(value = "额度资产归属公司名", example = "ABC")
    private String quotaAssetCompanyName;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运", example = "1")
    private Integer quotaType;

    @ApiModelProperty(value = "车牌号", example = "111")
    private String licensePlate;

    @ApiModelProperty(value = "退牌日期", example = "1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licensePlateWithdrawalDate;

}
