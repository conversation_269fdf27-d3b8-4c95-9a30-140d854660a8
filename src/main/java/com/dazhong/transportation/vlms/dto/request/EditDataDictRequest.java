package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.dazhong.transportation.vlms.dto.DataDictDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-27 15:42
 */
@Data
@ApiModel(description = "数据字典参数")
public class EditDataDictRequest<K> {


    @ApiModelProperty(value = "数据字典编码" ,required = true)
    @NotBlank(message = "数据字典编码不能为空")
    private String dataCode;

    @ApiModelProperty(value = "数据字典名称" ,required = true)
    @NotBlank(message = "数据字典名称不能为空")
    private String dataName;

    @ApiModelProperty(value = "数据字典内容" ,required = true)
    @NotEmpty(message = "内容不能为空")
    private List<DataDictDto<K>> dataDictList;


    @ApiModelProperty(value = "数据字典value值类型")
    private Integer codeType;

}
