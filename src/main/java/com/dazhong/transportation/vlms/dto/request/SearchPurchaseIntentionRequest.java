package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-23 9:11
 */
@ApiModel(description = "采购意向查询参数")
@Data
public class SearchPurchaseIntentionRequest extends PageRequest {

    @ApiModelProperty(value = "合同号")
    private String contractNo;

    @ApiModelProperty(value = "是否有单号 1-是 2-否")
    private Integer applyNoStatus;

}
