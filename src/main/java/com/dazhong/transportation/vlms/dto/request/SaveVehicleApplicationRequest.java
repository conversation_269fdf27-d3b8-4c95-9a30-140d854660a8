package com.dazhong.transportation.vlms.dto.request;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.dto.VehicleApplicationDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.enums.ProductLineEnum;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "保存车辆申请单入参")
public class SaveVehicleApplicationRequest {

    @ApiModelProperty(value = "处置申请单id", example = "1")
    private Long id;

    @ApiModelProperty(value = "单据类型 1-车辆调拨 2-车辆转籍 3-切换业务类型", example = "1")
    private Integer applicationType;

    @ApiModelProperty(value = "单据标题", example = "出售申请单")
    private String documentTitle;

    @ApiModelProperty(value = "单据所属资产公司id", example = "出售申请单")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属资产公司名", example = "出售申请单")
    private String ownerName;

    @ApiModelProperty(value = "单据所属机构id", example = "出售申请单")
    private Long organizationId;

    @ApiModelProperty(value = "单据所属机构名", example = "出售申请单")
    private String organizationName;

    @ApiModelProperty(value = "申请人审批部门id", example = "1")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人审批部门名", example = "部门")
    private String originatorDeptName;
    
    @ApiModelProperty(value = "调出部门编码", example = "DEPT001")
    private String transferFromDepartmentCode;
    
    @ApiModelProperty(value = "调出部门名称", example = "技术部")
    private String transferFromDepartmentName;
    
    @ApiModelProperty(value = "调入部门编码", example = "DEPT002")
    private String transferToDepartmentCode;
    
    @ApiModelProperty(value = "调入部门名称", example = "运营部")
    private String transferToDepartmentName;
    
    @ApiModelProperty(value = "业务线 1-巡网业务线 2-商务业务线", example = "1")
    private Integer productLine;
    
    @ApiModelProperty(value = "调拨承担方", example = "公司A")
    private String costBearer;

    @ApiModelProperty(value = "合同号", example = "aaaaa")
    private String contractNumber;

    @ApiModelProperty(value = "变动原因", example = "变动原因")
    private String updateReason;

    @ApiModelProperty(value = "备注", example = "技术部")
    private String remark;

    @ApiModelProperty(value = "提交人", example = "技术部")
    private String createOperName;

    @ApiModelProperty(value = "车辆明细")
    private List<VehicleApplicationDetailDto> vehicleApplicationDetailList;

    @ApiModelProperty(value = "附件")
    private List<VehicleApplyFileDto> vehicleApplyFileList;

    /**
     * 将车辆申请单转换为钉钉审批流需要的数据
     *
     * @return 钉钉审批流需要的数据
     */
    public Map<String, String> convertToDingTalkData(Map<String, Map<Integer, String>> dictInfoMap) {
        Map<String, String> processComponentValues = new HashMap<>();
        processComponentValues.put("单据标题", ObjectValidUtil.formatStr(this.getDocumentTitle()));
        processComponentValues.put("备注", ObjectValidUtil.formatStr(this.getRemark()));
        processComponentValues.put("申请人", ObjectValidUtil.formatStr(this.getCreateOperName()));
        processComponentValues.put("单据所属资产公司", ObjectValidUtil.formatStr(this.getOwnerName()));
        processComponentValues.put("单据所属机构", ObjectValidUtil.formatStr(this.getOrganizationName()));
        processComponentValues.put("调出部门", ObjectValidUtil.formatStr(this.getTransferFromDepartmentCode()));
        processComponentValues.put("调入部门", ObjectValidUtil.formatStr(this.getTransferToDepartmentCode()));
        processComponentValues.put("条线", ObjectValidUtil.formatStr(ProductLineEnum.getDesc(this.getProductLine())));
        processComponentValues.put("费用承担方", ObjectValidUtil.formatStr(this.getCostBearer()));
        processComponentValues.put("合同号", ObjectValidUtil.formatStr(this.getContractNumber()));
        // 添加空值检查，防止空指针异常
        String updateReason = this.getUpdateReason();
        if (updateReason != null && dictInfoMap != null && dictInfoMap.get("updateReason") != null) {
            try {
                processComponentValues.put("变动原因", ObjectValidUtil.formatStr(dictInfoMap.get("updateReason").get(Integer.parseInt(updateReason))));
            } catch (Exception e) {
                processComponentValues.put("变动原因", "");
            }
        } else {
            processComponentValues.put("变动原因", "");
        }
        processComponentValues.put("明细数据", JSON.toJSONString(convertVehicleApplicationDetailList()));
        return processComponentValues;
    }


    /**
     * 将车辆明细转换为钉钉审批流需要的数据
     *
     * @return 钉钉审批流需要的数据
     */
    public List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> convertVehicleApplicationDetailList() {
        List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> result = new ArrayList<>();
        for (VehicleApplicationDetailDto vehicleApplicationDetailDto : this.vehicleApplicationDetailList) {
            result.add(vehicleApplicationDetailDto.convertToDingTalkData());
        }
        return result;
    }
}
