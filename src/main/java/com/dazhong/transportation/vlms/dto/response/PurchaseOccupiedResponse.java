package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "车辆采购额度详情列表")
@Data
public class PurchaseOccupiedResponse {

    @ApiModelProperty(value = "采购申请明细行号", example = "Toyota Camry")
    private String applyDetailsNo;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营", example = "1")
    private Integer quotaType;

    @ApiModelProperty(value = "额度资产归属公司")
    private Integer quotaAssetOwnership;

    @ApiModelProperty(value = "预占用额度数")
    private Integer preOccupied;

}
