package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(description = "车辆附件记录响应对象")
public class VehicleFileRecordResponse implements Serializable {

    @ApiModelProperty(value = "业务类型", example = "车辆保险")
    private Integer fileType;

    @ApiModelProperty(value = "文件名称", example = "保险单.pdf")
    private String fileName;

    @ApiModelProperty(value = "文件路径", example = "/files/insurance/12345.pdf")
    private String fileUrl;

    @ApiModelProperty(value = "创建时间", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "创建人名称", example = "张三")
    private String createOperName;

    @ApiModelProperty(value = "业务类型名称")
    private String fileTypeName;

}
