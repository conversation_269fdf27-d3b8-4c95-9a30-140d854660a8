
package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 公司额外信息DTO
 */
@Data
@ApiModel("公司额外信息DTO")
public class CompanyExtraInfoDto {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "外键id")
    private Long foreignId;

    @ApiModelProperty(value = "业务类型 1-组织架构 2-资产所有者")
    private Integer businessType;

    @ApiModelProperty(value = "总经理名称")
    private String ceoName;

    @ApiModelProperty(value = "总经理手机号")
    private String ceoPhone;

    @ApiModelProperty(value = "钉钉号")
    private String dingTalkNo;

    @ApiModelProperty(value = "状态（0=正常   1=已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称")
    private String createOperName;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人id")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称")
    private String updateOperName;
}