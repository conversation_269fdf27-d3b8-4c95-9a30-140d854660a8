package com.dazhong.transportation.vlms.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "车辆处置明细DTO-车辆详情使用")
public class VehicleReverseDisposalDetailListDto {

    @ApiModelProperty(value = "钉钉审批单号", example = "1")
    private String dingTalkNo;

    @ApiModelProperty(value = "单据号", example = "1")
    private String documentNo;

    @ApiModelProperty(value = "提交日期", example = "1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;

    @ApiModelProperty(value = "单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废", example = "1")
    private String documentStatus;
}
