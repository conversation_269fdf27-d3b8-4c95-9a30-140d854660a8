package com.dazhong.transportation.vlms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleSyncExternalAccidentDetailDto  extends VehicleSyncExternalDto{


    /**
     * 事故编号
     */
    private String accidentNo;
    /**
     * 事故状态：报案中、报案完成 、事故处理中 、事故处理完毕 、已作废
     */
    private String accidentStatus;

    /**
     * 事故类型：单车事故、单车含物损 、双车事故、多车事故
     */
    private String accidentType;

    /**
     * 事故发生时间
     */
    private String accidentTime;

    /**
     * 责任情况：我方全责 、我方同责 、我方主责 、我方次责、我方无责 、责任未定
     */
    private String accidentResponseType;

    /**
     * 事故等级：一级 、二级
     */
    private String accidentLevel;

    /**
     * 	处理人
     */
    private String accidentDealUser;
}
