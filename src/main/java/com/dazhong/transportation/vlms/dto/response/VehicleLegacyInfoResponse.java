package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "车辆历史信息响应类")
@Data
public class VehicleLegacyInfoResponse implements Serializable {

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A123456")
    private String vin;

    @ApiModelProperty(value = "营运/非营运", example = "1")
    private Integer operateTypeId;

    @ApiModelProperty(value = "号牌种类 1-小型汽车 2-大型汽车 3-教练车", example = "1")
    private Integer vehicleCategoryId;

    @ApiModelProperty(value = "运营类别 1-营运车辆 2-生产用车 3-公务车辆", example = "1")
    private Integer operationCategoryId;

    @ApiModelProperty(value = "机动车拥有公司ID", example = "1001")
    private Integer ownerId;

    @ApiModelProperty(value = "资产拥有公司ID", example = "1002")
    private Integer assetOwnerId;

    @ApiModelProperty(value = "车辆拥有公司表名称", example = "张三")
    private String ownerName;

    @ApiModelProperty(value = "所有人企业名称", example = "中汽公司")
    private String assetCompanyName;

    @ApiModelProperty(value = "营运证企业名称", example = "中汽公司")
    private String companyOwnerName;

    @ApiModelProperty(value = "资产机构ID", example = "2001")
    private Integer ownOrganizationId;

    @ApiModelProperty(value = "使用机构ID", example = "2002")
    private Integer usageOrganizationId;

    @ApiModelProperty(value = "合同形式 默认通用合同", example = "1")
    private Integer contractTypeId;

    @ApiModelProperty(value = "营运证号", example = "ABC123456")
    private String operatingNo;

    @ApiModelProperty(value = "投产日期", example = "2020-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "购买日期", example = "2019-12-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date purchaseDate;

    @ApiModelProperty(value = "上牌日期", example = "2020-02-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseDate;

    @ApiModelProperty(value = "投运日期", example = "2020-03-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date operationStartDate;

    @ApiModelProperty(value = "是否有产权证 1-是 2-否 ", example = "1")
    private Integer hasRight;

    @ApiModelProperty(value = "老车管车型", example = "丰田卡罗拉")
    private String vehicleModelName;

    @ApiModelProperty(value = "实际报废日期", example = "2020-03-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date realRetirementDate;


}
