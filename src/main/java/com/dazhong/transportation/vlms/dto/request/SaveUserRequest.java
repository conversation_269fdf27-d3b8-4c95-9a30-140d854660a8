package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.DaZhongUserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-23 9:11
 */
@Data
@ApiModel(description = "新增用户信息")
public class SaveUserRequest {


    /**
     * 用户列表
     */
    @ApiModelProperty(value = "用户信息",required = true)
    @NotEmpty(message = "用户不能为空")
    private List<DaZhongUserInfo> userList;


    /**
     * 关联角色
     */
    @ApiModelProperty(value = "关联角色",required = true)
    @NotEmpty(message = "关联角色不能为空")
    private List<Long> roleIdList;

    /**
     * 关联机构
     */
    @ApiModelProperty(value = "关联机构",required = true)
    private List<Long> orgIdList;

    /**
     * 用户关联资产所有公司ID
     */
    @ApiModelProperty(value = "用户关联资产所有公司ID",required = true)
    private List<Integer> ownerIdList;

}
