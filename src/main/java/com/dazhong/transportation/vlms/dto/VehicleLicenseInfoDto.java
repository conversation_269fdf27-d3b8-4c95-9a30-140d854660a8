package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "车辆证照信息")
public class VehicleLicenseInfoDto {

    @ApiModelProperty(value = "使用性质 (行驶证)")
    private Integer usageIdRegistrationCard;

    @ApiModelProperty(value = "车辆类型 (行驶证)")
    private Integer vehicleTypeRegistrationCard;

    @ApiModelProperty(value = "注册日期(行驶证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registrationDateRegistrationCard;

    @ApiModelProperty(value = "发证日期 (行驶证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDateRegistrationCard;

    @ApiModelProperty(value = "强制报废日期 (行驶证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date retirementDateRegistrationCard;

    @ApiModelProperty(value = "年检到期日 (行驶证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date annualInspectionDueDateRegistrationCard;

    @ApiModelProperty(value = "档案编号")
    private String fileNumber;

    @ApiModelProperty(value = "车辆出厂日期 (产证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productDate;

    @ApiModelProperty(value = "发证日期 (产证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDate;

    @ApiModelProperty(value = "产证编号")
    private String certificateNumber;

    @ApiModelProperty(value = "行驶证文件地址")
    private String vehicleLicenseUrl;

    @ApiModelProperty(value = "产证文件地址")
    private String certificateOwnershipUrl;

    @ApiModelProperty(value = "合格证文件地址")
    private String certificateConformityUrl;

    @ApiModelProperty(value = "车辆发票文件地址")
    private String vehicleInvoiceUrl;

    @ApiModelProperty(value = "购置税文件地址")
    private String purchaseTaxUrl;

    @ApiModelProperty(value = "营运证文件地址")
    private String operatingPermitUrl;
}
