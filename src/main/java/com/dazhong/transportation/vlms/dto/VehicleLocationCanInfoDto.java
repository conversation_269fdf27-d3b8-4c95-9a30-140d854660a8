package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class VehicleLocationCanInfoDto implements Serializable {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "终端编号")
    private String terminalNo;

    @ApiModelProperty(value = "纬度", example = "34.123211")
    private String latitude;

    @ApiModelProperty(value = "经度", example = "121.232123")
    private String longitude;

    @ApiModelProperty(value = "车机里程数")
    private BigDecimal mileage;

    @ApiModelProperty(value = "油量百分比")
    private BigDecimal oilPercent;

    @ApiModelProperty(value = "电量百分比")
    private BigDecimal electricPercent;

    @ApiModelProperty(value = "采集时间")
    private String collectionTime;

    @ApiModelProperty(value = "终端渠道")
    private String terminalChannel;
}
