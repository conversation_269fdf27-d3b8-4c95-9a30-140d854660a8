package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "车辆保险信息明细")
public class VehicleExternalInsuranceResponse implements Serializable {


    @ApiModelProperty(value = "车牌号", example = "")
    private String licensePlate;

    @ApiModelProperty(value = "保险类型", example = "商业险")
    private String insuranceType;

    @ApiModelProperty(value = "保单号", example = "")
    private String insuranceNo;

    @ApiModelProperty(value = "起保日期", example = "")
    private String insuranceStartDate;

    @ApiModelProperty(value = "终保日期", example = "")
    private String insuranceEndDate;

    @ApiModelProperty(value = "保险公司名称", example = "")
    private String insurerName;

    @ApiModelProperty(value = "保费", example = "")
    private String insuranceAmount;

    @ApiModelProperty(value = "三者保额", example = "")
    private String thirdPartAmount;

    @ApiModelProperty(value = "数据同步时间", example = "")
    private String syncDateTime;
}
