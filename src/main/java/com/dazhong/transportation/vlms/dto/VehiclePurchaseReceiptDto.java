package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-02-07 17:29
 */
@ApiModel(description = "车辆采购收货信息")
@Data
public class VehiclePurchaseReceiptDto {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "车身颜色")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "内饰颜色")
    private String interiorColor;

    @ApiModelProperty(value = "下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "收货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;

    @ApiModelProperty(value = "供应商")
    private Integer supplierId;

    @ApiModelProperty(value = "使用年限")
    private Integer usageAgeLimit;

    @ApiModelProperty(value = "折旧年限")
    private Integer depreciationAgeLimit;

    @ApiModelProperty(value = "是否回购 1-是 2-否")
    private Integer isRepurchase;

    @ApiModelProperty(value = "回购时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date repurchaseDate;

    @ApiModelProperty(value = "回购要求")
    private String repurchaseRequirements;

    @ApiModelProperty(value = "裸车价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "购置税")
    private BigDecimal purchaseTax;

    @ApiModelProperty(value = "牌照费")
    private BigDecimal licensePlatePrice;

    @ApiModelProperty(value = "上牌杂费")
    private BigDecimal licensePlateOtherPrice;

    @ApiModelProperty(value = "装潢费")
    private BigDecimal upholsterPrice;

    @ApiModelProperty(value = "购置总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "业务类型", example = "1")
    private Integer businessLine;

    @ApiModelProperty(value = "资产编号", example = "ASSET001")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车型ID")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名称")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆型号")
    private String vehicleModelNo;

    @ApiModelProperty(value = "发动机号")
    private String engineNo;

    @ApiModelProperty(value = "采购申请钉钉号")
    private String approvalNumber;

    @ApiModelProperty(value = "车辆资产所属公司ID")
    private Integer ownerId;
}
