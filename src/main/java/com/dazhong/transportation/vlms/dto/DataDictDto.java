package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-27 16:14
 */
@ApiModel(description = "数据字典内容")
@Data
public class DataDictDto<K> {

    @ApiModelProperty(value = "主键" )
    private Long id;


    @ApiModelProperty(value = "值" )
    private K value;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

}
