package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(description = "修改组织架构")
@Data
public class UpdateOrgInfoRequest {


    @ApiModelProperty(value = "组织机构ID列表" ,required = true)
    @NotEmpty(message = "组织机构ID列表不能为空")
    private List<Long> orgIdList;

}
