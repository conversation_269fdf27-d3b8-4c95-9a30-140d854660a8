package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-12-23 9:11
 */
@ApiModel(description = "查询日志参数")
@Data
public class SearchLicensePlateQuotaOperateLogRequest extends PageRequest {

    /**
     * 调整类型
     */
    @ApiModelProperty(value = "调整类型 1-总数增加 2-总数减少 3-类型调整", example = "1")
    private Integer adjustType;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String createOperName;

    /**
     * 操作时间-起始
     */
    @ApiModelProperty(value = "操作时间-起始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeStart;

    /**
     * 操作时间-结束
     */
    @ApiModelProperty(value = "操作时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeEnd;

}
