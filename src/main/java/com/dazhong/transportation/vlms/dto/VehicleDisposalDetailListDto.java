package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "车辆处置明细DTO-车辆详情使用")
public class VehicleDisposalDetailListDto {

    @ApiModelProperty(value = "单据类型 1-出售申请 2-报废申请", example = "1")
    private String documentType;

    @ApiModelProperty(value = "钉钉审批单号", example = "1")
    private String dingTalkNo;

    @ApiModelProperty(value = "单据号", example = "1")
    private String documentNo;

    @ApiModelProperty(value = "提交日期", example = "1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitDate;

    @ApiModelProperty(value = "单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废", example = "1")
    private String documentStatus;

    @ApiModelProperty(value = "出售车辆公司code", example = "1")
    private String sellingCompanyCode;

    @ApiModelProperty(value = "出售车辆公司名", example = "1")
    private String sellingCompanyName;

    @ApiModelProperty(value = "原值", example = "1")
    private BigDecimal originalValue;

    @ApiModelProperty(value = "已提折旧", example = "1")
    private BigDecimal accumulatedDepreciation;

    @ApiModelProperty(value = "净值/残值", example = "1")
    private BigDecimal netValue;

    @ApiModelProperty(value = "实际售价", example = "1")
    private BigDecimal actualSellingPrice;

    @ApiModelProperty(value = "实际出售损益", example = "1")
    private BigDecimal saleGainLoss;

}
