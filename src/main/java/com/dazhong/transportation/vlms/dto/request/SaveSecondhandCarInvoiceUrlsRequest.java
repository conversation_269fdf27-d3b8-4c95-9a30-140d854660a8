package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import com.dazhong.transportation.vlms.dto.response.UploadFileResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "保存二手车交易发票URL入参")
public class SaveSecondhandCarInvoiceUrlsRequest {

    @ApiModelProperty(value = "发票文件地址url列表")
    private List<UploadFileResponse> urlList;

    @ApiModelProperty(value = "处置单id")
    private Long disposalId;
}
