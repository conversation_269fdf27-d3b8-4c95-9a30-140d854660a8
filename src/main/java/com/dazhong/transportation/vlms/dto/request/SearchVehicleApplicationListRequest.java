package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "查询车辆申请列表入参")
public class SearchVehicleApplicationListRequest extends PageRequest {

    /**
     * 钉钉审批单号
     */
    @ApiModelProperty("钉钉审批单号 非必传")
    private String dingTalkNo;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人 非必传")
    private String createOperName;

    /**
     * 单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废
     */
    @ApiModelProperty("单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废 非必传")
    private List<Integer> documentStatusList;
}
