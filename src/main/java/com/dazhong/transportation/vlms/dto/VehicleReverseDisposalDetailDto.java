package com.dazhong.transportation.vlms.dto;

import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(description = "车辆处置明细DTO")
public class VehicleReverseDisposalDetailDto {

    @ApiModelProperty(value = "主键id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "处置单号", example = "D001")
    private String reserveDocumentNo;

    @ApiModelProperty(value = "资产编号", example = "A001")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A123456")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "车型ID", example = "1001")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名", example = "1001")
    private String vehicleModelName;

    @ApiModelProperty(value = "关联审批中处置单号", example = "CZ2222")
    private String disposalDocumentNo;

    @ApiModelProperty(value = "资产所属公司id", example = "35")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产所属公司名", example = "XX公司")
    private String assetCompanyName;

    @ApiModelProperty(value = "实际运营公司（所属）ID", example = "35")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "实际运营公司（所属）名", example = "YY公司")
    private String ownOrganizationName;

    @ApiModelProperty(value = "实际运营公司（使用）ID", example = "YY公司")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "实际运营公司（使用）名", example = "YY公司")
    private String usageOrganizationName;

    /**
     * 将当前对象转换为钉钉审批流中的表单数据
     *
     * @return 钉钉审批流中的表单数据结构
     */
    public List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> convertToDingTalkData() {
        List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> result = new ArrayList<>();

        // 车辆基础字段
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("资产编号").setValue(ObjectValidUtil.formatStr(this.vehicleAssetId)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车架号").setValue(ObjectValidUtil.formatStr(this.vin)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车牌号").setValue(ObjectValidUtil.formatStr(this.licensePlate)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车型").setValue(ObjectValidUtil.formatStr(this.vehicleModelName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("资产所属公司").setValue(ObjectValidUtil.formatStr(this.assetCompanyName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（所属）").setValue(ObjectValidUtil.formatStr(this.ownOrganizationName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（使用）").setValue(ObjectValidUtil.formatStr(this.usageOrganizationName)));
        return result;
    }
}
