package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "车辆资产表数据")
public class SyncDatabaseVehicleAssetDto {

    @ApiModelProperty(value = "主键", notes = "Id")
    private Long id;

    @ApiModelProperty(value = "关联车辆ID", notes = "VehicleId")
    private Long vehicleId;

    @ApiModelProperty(value = "裸车价", notes = "PurchasePrice")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "购置税", notes = "PurchaseTax")
    private BigDecimal purchaseTax;

    @ApiModelProperty(value = "牌照费", notes = "licensePlatePrice")
    private BigDecimal licensePrice;

    @ApiModelProperty(value = "上牌杂费", notes = "licensePlateOtherPrice")
    private BigDecimal licenseOtherPrice;

    @ApiModelProperty(value = "装潢费", notes = "UpholsterPrice")
    private BigDecimal upholsterPrice;

    @ApiModelProperty(value = "购置总价", notes = "TotalPrice")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "账面净值", notes = "RemainPrice")
    private BigDecimal remainPrice;

    @ApiModelProperty(value = "旧车销售价", notes = "SecondHandPrice")
    private BigDecimal secondHandPrice;
}
