package com.dazhong.transportation.vlms.dto.request;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalDetailDto;
import com.dazhong.transportation.vlms.enums.DisposalTypeEnum;
import com.dazhong.transportation.vlms.enums.ProductLineEnum;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "保存车辆处置申请单入参")
public class SaveDisposalApplicationRequest {

    @ApiModelProperty(value = "处置申请单id", example = "D001")
    private Long id;

    @ApiModelProperty(value = "单据号", example = "D001")
    private String documentNo;

    @ApiModelProperty(value = "单据类型 1-出售申请（巡网业务） 2-报废申请 3-出售申请单（商务业务）", example = "1")
    private Integer documentType;

    @ApiModelProperty(value = "单据标题", example = "出售申请单")
    private String documentTitle;

    @ApiModelProperty(value = "所在部门ID", example = "技术部")
    private Long organizationId;

    @ApiModelProperty(value = "所在部门", example = "技术部")
    private String organizationName;

    @ApiModelProperty(value = "单据所属资产公司id", example = "1")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属资产公司名", example = "技术部")
    private String ownerName;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "申请人审批部门id", example = "1")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人审批部门名", example = "XX公司")
    private String originatorDeptName;

    @ApiModelProperty(value = "出售车辆公司code", example = "1")
    private String sellingCompanyCode;

    @ApiModelProperty(value = "出售车辆公司名", example = "XX公司")
    private String sellingCompanyName;

    @ApiModelProperty(value = "车辆使用部门code", example = "1")
    private String useDepartmentCode;

    @ApiModelProperty(value = "车辆使用部门名", example = "XX公司")
    private String useDepartmentName;

    @ApiModelProperty(value = "车辆所属部门code", example = "1")
    private String departmentCode;

    @ApiModelProperty(value = "车辆所属部门名", example = "XX公司")
    private String departmentName;

    @ApiModelProperty(value = "关联任务编号（退牌任务）", example = "T001")
    private String taskNumber;

    @ApiModelProperty(value = "备注", example = "beizhu111")
    private String remark;

    @ApiModelProperty(value = "本月批次", example = "202403")
    private String monthBatch;

    /**
     * 是否提交 1-是 2-否
     */
    private Integer isSubmit = 2;

    @ApiModelProperty(value = "车辆明细")
    private List<VehicleDisposalDetailDto> vehicleDisposalDetailList;

    @ApiModelProperty(value = "附件")
    private List<VehicleApplyFileDto> vehicleApplyFileList;

    /**
     * 将处置申请单转换为钉钉审批流需要的数据
     *
     * @return 钉钉审批流需要的数据
     */
    public Map<String, String> convertToDingTalkData(Map<String, Map<Integer, String>> dictInfoMap) {
        Map<String, String> processComponentValues = new HashMap<>();
        processComponentValues.put("单据标题", ObjectValidUtil.formatStr(this.getDocumentTitle()));
        processComponentValues.put("条线", ObjectValidUtil.formatStr(ProductLineEnum.getDesc(this.getProductLine())));
        processComponentValues.put("单据所属资产公司", ObjectValidUtil.formatStr(this.getOwnerName()));
        processComponentValues.put("单据所属机构", ObjectValidUtil.formatStr(this.getOrganizationName()));
        processComponentValues.put("备注", ObjectValidUtil.formatStr(this.getRemark()));
        if (this.documentType == 1) {
            processComponentValues.put("出售车辆单位", ObjectValidUtil.formatStr(this.getSellingCompanyCode()));
            processComponentValues.put("车辆归属部门", ObjectValidUtil.formatStr(this.getDepartmentCode()));
            processComponentValues.put("出售车辆数", ObjectValidUtil.formatStr(this.getVehicleDisposalDetailList().size()));
            processComponentValues.put("明细数据", JSON.toJSONString(convertVehicleDisposalDetailList(dictInfoMap)));
        }
        if (this.documentType == 2) {
            VehicleDisposalDetailDto vehicleDisposalDetailDto = this.vehicleDisposalDetailList.get(0);
            processComponentValues.put("报废类型", ObjectValidUtil.formatStr(DisposalTypeEnum.getDesc(vehicleDisposalDetailDto.getDisposalType())));
            processComponentValues.put("车架号", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getVin()));
            processComponentValues.put("车牌号", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getLicensePlate()));
            processComponentValues.put("车型", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getVehicleModelName()));
            processComponentValues.put("车辆使用部门", ObjectValidUtil.formatStr(this.useDepartmentCode));
            processComponentValues.put("车辆所属部门", ObjectValidUtil.formatStr(this.departmentCode));
            if (this.getProductLine() == 1) {
                processComponentValues.put("巡网线车辆归属单位（行驶证抬头）", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getAssetCompanyName()));
            }
            if (this.getProductLine() == 2) {
                processComponentValues.put("商务线车辆归属单位（行驶证抬头）", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getAssetCompanyName()));
            }
            if (vehicleDisposalDetailDto.getDisposalType() == 1) {
                processComponentValues.put("预估车辆损失（元）", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getEstimatedVehicleLoss()));
            }
            if (vehicleDisposalDetailDto.getDisposalType() == 2) {
                processComponentValues.put("预估事故损失（元）", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getEstimatedVehicleLoss()));
            }
            processComponentValues.put("车辆使用性质", ObjectValidUtil.formatStr(dictInfoMap.get("usage").get(vehicleDisposalDetailDto.getUsageIdRegistrationCard())));
            processComponentValues.put("实际报废损益（元）", ObjectValidUtil.formatStr(vehicleDisposalDetailDto.getActualScrapProfitLoss()));
        }
        if (this.documentType == 3) {
            processComponentValues.put("出售车辆单位", ObjectValidUtil.formatStr(this.getSellingCompanyCode()));
            processComponentValues.put("本月批次", ObjectValidUtil.formatStr(this.monthBatch));
            processComponentValues.put("车辆数", ObjectValidUtil.formatStr(this.getVehicleDisposalDetailList().size()));
            processComponentValues.put("预计总盈亏1（元）", ObjectValidUtil.formatStr(this.getVehicleDisposalDetailList().stream()
                    .map(VehicleDisposalDetailDto::getExpectedProfitLossOne)
                    .filter(ObjectValidUtil::isValidBigDecimal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).toString()));
            processComponentValues.put("预计总盈亏2（元）", ObjectValidUtil.formatStr(this.getVehicleDisposalDetailList().stream()
                    .map(VehicleDisposalDetailDto::getExpectedProfitLossTwo)
                    .filter(ObjectValidUtil::isValidBigDecimal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).toString()));
            processComponentValues.put("明细数据", JSON.toJSONString(convertVehicleDisposalDetailList(dictInfoMap)));
        }

        return processComponentValues;
    }


    /**
     * 将处置明细转换为钉钉审批流需要的数据
     *
     * @return 钉钉审批流需要的数据
     */
    public List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> convertVehicleDisposalDetailList(Map<String, Map<Integer, String>> dictInfoMap) {
        List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> result = new ArrayList<>();
        for (VehicleDisposalDetailDto vehicleDisposalDetailDto : this.vehicleDisposalDetailList) {
            result.add(vehicleDisposalDetailDto.convertToDingTalkData(dictInfoMap));
        }
        return result;
    }
}
