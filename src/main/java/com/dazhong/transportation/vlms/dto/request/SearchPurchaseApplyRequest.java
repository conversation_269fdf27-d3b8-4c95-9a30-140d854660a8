package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-06 13:11
 */
@ApiModel(description = "采购申请查询参数")
@Data
public class SearchPurchaseApplyRequest extends PageRequest {

    @ApiModelProperty(value = "采购申请编号", example = "P001")
    private String purchaseApplyNo;

    @ApiModelProperty(value = "钉钉审批编号")
    private String approvalNumber;

    @ApiModelProperty(value = "采购申请状态列表 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)")
    private List<Integer> purchaseApplyStatus;

    @ApiModelProperty(value = "单据所属资产公司ID")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属机构ID")
    private Long applyOrgId;

    @ApiModelProperty(value = "申购公司code")
    private String subscriptionCompanyCode;

    @ApiModelProperty(value = "创建人")
    private String createUser;


}
