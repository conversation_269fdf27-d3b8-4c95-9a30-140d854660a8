package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 额度信息表响应对象
 *
 * <AUTHOR>
 * @date 2024-12-30 09:08:00
 */
@ApiModel(description = "额度信息表响应对象")
@Data
public class LicensePlateQuotaDto {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    /**
     * 额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     * 字典String类型
     */
    @ApiModelProperty(value = "额度类型", example = "1")
    private String quotaType;

    /**
     * 额度总数
     */
    @ApiModelProperty(value = "额度总数", example = "100")
    private Integer quota;

    /**
     * 预占用
     */
    @ApiModelProperty(value = "预占用", example = "10")
    private Integer preOccupied;

    /**
     * 占用
     */
    @ApiModelProperty(value = "占用", example = "20")
    private Integer occupied;

    /**
     * 剩余可用
     */
    @ApiModelProperty(value = "剩余可用", example = "70")
    private Integer remaining;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty(value = "额度资产归属公司", example = "001")
    private Long assetCompanyId;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty(value = "额度资产归属公司", example = "001")
    private String assetCompanyName;

    /**
     * 状态（0=正常   1=已删除）
     */
    @ApiModelProperty(value = "状态", example = "0")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-01-01T12:00:00Z")
    private Date createTime;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id", example = "1001")
    private Long createOperId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", example = "张三")
    private String createOperName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2024-01-02T12:00:00Z")
    private Date updateTime;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id", example = "1002")
    private Long updateOperId;

    /**
     * 更新人名称
     */
    @ApiModelProperty(value = "更新人名称", example = "李四")
    private String updateOperName;
}
