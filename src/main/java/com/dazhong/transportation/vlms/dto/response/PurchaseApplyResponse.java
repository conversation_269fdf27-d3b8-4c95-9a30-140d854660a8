package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "车辆采购申请结果")
@Data
public class PurchaseApplyResponse {

    @ApiModelProperty(value = "采购申请ID")
    private Long id;

    @ApiModelProperty(value = "采购申请编号", example = "P001")
    private String purchaseApplyNo;

    @ApiModelProperty(value = "钉钉审批编号", example = "A001")
    private String approvalNumber;

    @ApiModelProperty(value = "提交日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDate;

    @ApiModelProperty(value = "采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)")
    private Integer purchaseApplyStatus;

    @ApiModelProperty(value = "采购名称", example = "车辆采购")
    private String applyName;

    @ApiModelProperty(value = "采购车辆数量")
    private Integer purchaseQuantity;

    @ApiModelProperty(value = "收货车辆数")
    private Integer takeDeliveryQuantity;

    @ApiModelProperty(value = "申请公司结构ID")
    private Integer applyOrgId;

    @ApiModelProperty(value = "申请公司结构名称")
    private String applyOrgName;

    @ApiModelProperty(value = "申请人姓名")
    private String applyUser;

    @ApiModelProperty(value = "申购公司名称")
    private String subscriptionCompanyName;

    @ApiModelProperty(value = "采购意向单号")
    private String intentionNo;

    @ApiModelProperty(value = "单据资产所有公司ID")
    private Integer ownerId;

    @ApiModelProperty(value = "单据资产所有公司名称")
    private String ownerName;

    @ApiModelProperty(value = "预占用额度数")
    private Integer preOccupied;


    @ApiModelProperty(value = "车辆上牌占用额度数")
    private Integer vehicleOccupied;

}
