package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2025-01-06 14:02
 */
@ApiModel(description = "绑定用户钉钉信息参数")
@Data
public class BindDingTalkRequest {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "手机号",required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;

}
