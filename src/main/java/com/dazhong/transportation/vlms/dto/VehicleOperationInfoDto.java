package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "车辆运营信息")
public class VehicleOperationInfoDto {

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线")
    private Integer productLine;

    @ApiModelProperty(value = "业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车")
    private Integer businessLine;

    @ApiModelProperty(value = "运营状态 1-待运 2-租出")
    private Integer operatingStatus;

    @ApiModelProperty(value = "资产所属公司id")
    private Integer  assetCompanyId;

    @ApiModelProperty(value = "资产所属公司")
    private String assetCompanyName;


    @ApiModelProperty(value = "资产机构")
    private String ownOrganizationName;

    @ApiModelProperty(value = "使用机构")
    private String usageOrganizationName;

    @ApiModelProperty(value = "资产所属公司id")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "资产所属公司id")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "所属车队")
    private String belongingTeam;

    @ApiModelProperty(value = "获得方式 数据字典详细描叙")
    private Integer obtainWayId;

    @ApiModelProperty(value = "管辖区县")
    private Integer areaId;

    @ApiModelProperty(value = "最新定位地点")
    private String latestPosition;

    @ApiModelProperty(value = "最新总里程 (车机)")
    private String latestTotalMileage;

    @ApiModelProperty(value = "强制报废日期 (行驶证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date realRetirementDate;

}
