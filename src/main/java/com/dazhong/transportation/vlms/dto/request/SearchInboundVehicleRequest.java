package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "入库车辆列表查询参数")
public class SearchInboundVehicleRequest extends PageRequest {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "申购公司code")
    private String subscriptionCompanyCode;

}
