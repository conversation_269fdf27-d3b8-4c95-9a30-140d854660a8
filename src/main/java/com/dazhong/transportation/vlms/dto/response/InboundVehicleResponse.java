package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "固定资产车辆响应对象")
@Data
public class InboundVehicleResponse {

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A004352")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "资产编号", example = "ASSET001")
    private String vehicleAssetId;

    @ApiModelProperty(value = "发动机号", example = "**********")
    private String engineNo;

    @ApiModelProperty(value = "发动机型号(车辆)", example = "V6")
    private String engineModel;

    @ApiModelProperty(value = "车型名称", example = "问界M9")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆型号(合格证产证车型字段)", example = "Sedan")
    private String vehicleModelNo;

    @ApiModelProperty(value = "车身颜色", example = "1")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "内饰颜色", example = "Black")
    private String interiorColor;

    @ApiModelProperty(value = "供应商", example = "1")
    private Integer supplierId;

    @ApiModelProperty(value = "是否回购 1-是 2-否", example = "2")
    private Integer isRepurchase;

    @ApiModelProperty(value = "回购时间", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date repurchaseDate;

    @ApiModelProperty(value = "回购要求", example = "None")
    private String repurchaseRequirements;

    @ApiModelProperty(value = "下单日期", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "收货日期", example = "None")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;

    @ApiModelProperty(value = "转固操作人", example = "李成祥")
    private String createOperName;

    @ApiModelProperty(value = "转固操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "转固申请编号", example = "TF001")
    private String applyNo;

    @ApiModelProperty(value = "转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)", example = "3")
    private Integer applyStatus;


}