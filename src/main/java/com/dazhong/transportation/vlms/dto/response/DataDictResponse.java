package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.DataDictDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-27 15:42
 */
@Data
@ApiModel(description = "数据字典结果")
@NoArgsConstructor
public class DataDictResponse<K> {


    @ApiModelProperty(value = "数据字典编码")
    private String dataCode;

    @ApiModelProperty(value = "数据字典名称")
    private String dataName;

    @ApiModelProperty(value = "数据字典内容")
    private List<DataDictDto<K>> dataDictList;

    @ApiModelProperty(value = "数据字典value值类型 1-数字 2-字符串")
    private Integer codeType;

    public DataDictResponse(List<DataDictDto<K>> dataDictList){
        if(dataDictList == null){
            dataDictList = new ArrayList<>();
        }
        this.dataDictList = dataDictList;
    }
}
