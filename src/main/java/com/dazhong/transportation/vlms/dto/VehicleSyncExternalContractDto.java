package com.dazhong.transportation.vlms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleSyncExternalContractDto implements Serializable {

    /**
     * 车架号
     */
    private String vin;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 合同开始日期 yyyy-MM-dd HH:mm:ss
     */
    private String contractStartDate;

    /**
     * 合同结束日期 yyyy-MM-dd HH:mm:ss
     */
    private String contractEndDate;

    /**
     * 发车日期 yyyy-MM-dd HH:mm:ss
     */
    private String deliverDate;

    /**
     * 发车任务编号
     */
    private String deliverTaskNo;

    /**
     * 收车日期 yyyy-MM-dd HH:mm:ss
     */
    private String collectDate;

    /**
     * 收车任务编号
     */
    private String collectTaskNo;

    /**
     * 裸车价
     */
    private String bareCarPrice;
}
