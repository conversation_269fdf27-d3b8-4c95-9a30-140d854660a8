package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(description = "新增角色参数")
public class SaveRoleRequest {

    @ApiModelProperty(value = "角色名称" ,required = true)
    @NotNull(message = "角色名称不能为空")
    private String roleName;

    @ApiModelProperty(value = "角色状态 1-启用 2-禁用" ,required = true)
    @Range(min = 1,max = 2, message = "角色状态 1-启用 2-禁用")
    private Integer roleStatus;

    @ApiModelProperty(value = "资源列表" ,required = true)
    @NotEmpty(message = "资源信息不能为空")
    private List<Long> resourceIdList;

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getRoleStatus() {
        return roleStatus;
    }

    public void setRoleStatus(Integer roleStatus) {
        this.roleStatus = roleStatus;
    }

    public List<Long> getResourceIdList() {
        return resourceIdList;
    }

    public void setResourceIdList(List<Long> resourceIdList) {
        this.resourceIdList = resourceIdList;
    }
}
