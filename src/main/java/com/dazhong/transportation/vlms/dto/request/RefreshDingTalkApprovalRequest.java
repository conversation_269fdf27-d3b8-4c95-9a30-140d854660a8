package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "刷新采购审批结果参数")
@Data
public class RefreshDingTalkApprovalRequest {


    @ApiModelProperty(value = "审批编号",required = true)
    @NotBlank(message = "审批编号不能为空")
    private String approvalNumber;


}
