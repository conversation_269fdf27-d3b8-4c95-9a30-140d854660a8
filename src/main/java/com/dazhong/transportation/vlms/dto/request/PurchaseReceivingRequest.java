package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.VehicleReceivingDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-06 14:02
 */
@ApiModel(description = "采购收货参数")
@Data
public class PurchaseReceivingRequest {

    @ApiModelProperty(value = "采购申请编号")
    @NotBlank(message = "采购申请编号不能为空")
    private String purchaseApplyNo;


    @ApiModelProperty(value = "车辆收货列表")
    @NotEmpty(message = "车辆收货列表不能为空")
    private List<VehicleReceivingDto> receivingList;


}
