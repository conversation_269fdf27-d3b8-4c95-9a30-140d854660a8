package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "操作日志响应")
@Data
public class OperateLogResponse implements Serializable {


    @ApiModelProperty(value = "操作类型 业务模块自定义")
    private Integer operateType;

    @ApiModelProperty(value = "操作内容", example = "用户登录系统")
    private String operateContent;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "创建人", example = "admin")
    private String createOperName;

}
