package com.dazhong.transportation.vlms.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel(description = "修改额度流水")
@Data
public class UpdateQuotaTransactionRecordRequest {

    @ApiModelProperty(value = "额度流水ID", required = true)
    @NotNull(message = "额度流水ID")
    private Long id;

    @ApiModelProperty(value = "额度单编号")
    private String quotaNumber;

    @ApiModelProperty(value = "额度单打印时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date quotaPrintDate;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运", required = true)
    private Integer quotaType;

}
