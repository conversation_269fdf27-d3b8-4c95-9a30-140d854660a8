package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "数据库表同步数据查询结果")
public class SearchDatabaseTableSyncDataResponse<T> implements Serializable {

    @ApiModelProperty(value = "同步数据库表名")
    private String tableName;

    @ApiModelProperty(value = "同步数据")
    private List<T> list;

    @ApiModelProperty(value = "下一次索引标识")
    private Long lastIndex;
}
