package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "车辆基础信息")
public class VehicleBasicInfoDto {

    @ApiModelProperty(value = "车辆基础信息ID")
    private Long id;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "资产编号")
    private String vehicleAssetId;

    @ApiModelProperty(value = "发动机号")
    private String engineNo;

    @ApiModelProperty(value = "发动机型号(车辆)")
    private String engineModel;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "是否占用管控额度 1-是 2-否")
    private Integer isQuotaOccupied;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运")
    private Integer quotaType;

    @ApiModelProperty(value = "额度资产归属公司")
    private String quotaAssetCompanyName;

    @ApiModelProperty(value = "车身颜色")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "内饰颜色")
    private String interiorColor;

    @ApiModelProperty(value = "供应商")
    private String supplierId;

    @ApiModelProperty(value = "使用年限")
    private Integer usageAgeLimit;

    @ApiModelProperty(value = "折旧年限")
    private String depreciationAgeLimit;

    @ApiModelProperty(value = "裸车价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "购置税")
    private BigDecimal purchaseTax;

    @ApiModelProperty(value = "牌照费")
    private BigDecimal licensePlatePrice;

    @ApiModelProperty(value = "上牌杂费")
    private BigDecimal licensePlateOtherPrice;

    @ApiModelProperty(value = "装潢费")
    private BigDecimal upholsterPrice;

    @ApiModelProperty(value = "购置总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "申购公司名称")
    private String subscriptionCompanyName;

    @ApiModelProperty(value = "资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废")
    private Integer propertyStatus;

}
