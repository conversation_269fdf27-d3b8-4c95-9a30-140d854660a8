package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RowValueDto {

    private String bizAlias;

    private String label;

    private Object value;

    private String key;

    private boolean mask;
}
