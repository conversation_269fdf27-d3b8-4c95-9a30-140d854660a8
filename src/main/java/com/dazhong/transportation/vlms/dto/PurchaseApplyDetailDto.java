package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "车辆采购申请明细信息")
@Data
public class PurchaseApplyDetailDto {

    @ApiModelProperty(value = "采购申请明细行号", example = "Toyota Camry")
    private String applyDetailsNo;

    @ApiModelProperty(value = "预算情况 1-预算内 2-预算外", example = "1")
    private Integer budgetStatus;

    @ApiModelProperty(value = "采购类型 1-新车 2-二手车", example = "1")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购车型ID", example = "1")
    private Long purchaseModelId;

    @ApiModelProperty(value = "采购车型名称", example = "Toyota Camry")
    private String purchaseModelName;

    @ApiModelProperty(value = "单价（巡网）", example = "250000.00")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "数量", example = "5")
    private Integer quantity;

    @ApiModelProperty(value = "其他费用（巡网）", example = "5000.00")
    private BigDecimal otherCosts;

    @ApiModelProperty(value = "总价（巡网）", example = "1255000.00")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "业务类型 1-巡讯 2-长包 3-临租 4-大巴 5-公务用车", example = "1")
    private Integer businessType;

    @ApiModelProperty(value = "制造商ID", example = "1")
    private Integer manufacturerId;

    @ApiModelProperty(value = "供应商ID", example = "1")
    private Integer supplierId;

    @ApiModelProperty(value = "已使用年限（二手车）", example = "3")
    private String usedYears;

    @ApiModelProperty(value = "已使用公里数（二手车）", example = "60000")
    private String usedKm;

    @ApiModelProperty(value = "期望归还日期", example = "2023-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectedReturnDate;

    @ApiModelProperty(value = "期望到货日期", example = "2023-10-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectedDeliveryDate;

    @ApiModelProperty(value = "是否占用管控额度 1-是 2-否", example = "1")
    private Integer isQuotaOccupied;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营", example = "1")
    private Integer quotaType;

    @ApiModelProperty(value = "额度资产归属公司")
    private Integer quotaAssetOwnership;

    @ApiModelProperty(value = "预占用")
    private Integer preOccupied;

    @ApiModelProperty(value = "汽车之家售价(元)")
    private Integer priceOnAutohome;

    @ApiModelProperty(value = "投资回报率")
    private String returnOnInvestment;

    @ApiModelProperty(value = "月单车收益")
    private String monthlyBicycleRevenue;

    @ApiModelProperty(value = "车辆残值率")
    private String vehicleResidualValueRate;

    /**
     * 车身尺寸
     */
    private String vehicleSize;

    /**
     * 牌照性质
     */
    private String licensePlateNature;


    /**
     * 上牌费
     */
    private String licensePlatePrice;

    /**
     * 服务费
     */
    private String servicePrice;

    /**
     * 单价（元）（商务线）
     */
    private String unitPrice2;
}
