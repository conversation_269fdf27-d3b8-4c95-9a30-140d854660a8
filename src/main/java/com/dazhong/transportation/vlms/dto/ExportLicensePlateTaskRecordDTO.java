package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 额度单流水记录导出数据
 */
@Data
public class ExportLicensePlateTaskRecordDTO {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "任务编号", example = "T001")
    private String taskNumber;

    @ApiModelProperty(value = "任务类型")
    private String taskType;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "车架号", example = "VIN12345678901234567")
    private String vin;

    @ApiModelProperty(value = "车型名", example = "101")
    private String vehicleModelName;

    @ApiModelProperty(value = "资产所属公司名", example = "XYZ")
    private String assetCompanyName;

    @ApiModelProperty(value = "实际运营公司（所属）", example = "XYZ")
    private String ownOrganizationName;


    @ApiModelProperty(value = "实际运营公司(使用)", example = "XYZ")
    private String usageOrganizationName;

    @ApiModelProperty(value = "发证日期（行驶证）", example = "2023-01-01")
    private String issuanceDateRegistrationCard;

    @ApiModelProperty(value = "退还额度类型 1-退还 2-不退还 3-不涉及", example = "1")
    private String returnQuotaType;

    @ApiModelProperty(value = "额度类型（1-社会牌照，2-纳管，3-网约，4-新能源网约，5-营运）", example = "1")
    private String quotaType;

    @ApiModelProperty(value = "额度资产归属公司名", example = "ABC")
    private String quotaAssetCompanyName;

    @ApiModelProperty(value = "额度编号", example = "Q001")
    private String quotaNumber;

    @ApiModelProperty(value = "登记人", example = "李四")
    private String createOperName;

    @ApiModelProperty(value = "登记时间", example = "李四")
    private String createTime;
}