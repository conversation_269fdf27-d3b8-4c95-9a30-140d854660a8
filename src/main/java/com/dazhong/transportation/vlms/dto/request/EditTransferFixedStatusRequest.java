package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-01-06 14:02
 */
@ApiModel(description = "编辑转固申请状态参数")
@Data
public class EditTransferFixedStatusRequest {

    @ApiModelProperty(value = "转固申请编号", example = "P001")
    @NotBlank(message = "转固申请编号不能为空")
    private String applyNo;

    @ApiModelProperty(value = "操作类型 1-作废 2-撤回 ")
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;

}
