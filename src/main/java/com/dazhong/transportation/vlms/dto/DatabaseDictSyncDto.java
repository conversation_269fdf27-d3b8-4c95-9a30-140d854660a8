package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DatabaseDictSyncDto {

    /**
     * 通用字段
     */
    @ApiModelProperty(value = "主键" )
    private Long id;

    @ApiModelProperty(value = "字典名称" )
    private String name;

    /**
     * 补充字段
     */
    @ApiModelProperty(value = "地址" )
    private String address;

    @ApiModelProperty(value = "手机号" )
    private String phone;


    /**
     * 机构字段
     */
    @ApiModelProperty(value = "编码" )
    private String levelCode;

    @ApiModelProperty(value = "父ID" )
    private Long parentId;
}
