package com.dazhong.transportation.vlms.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleSyncExternalPurchaseDto implements Serializable {

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 合同开始日期
     */
    private String contractStartDate;

    /**
     * 合同结束日期
     */
    private String contractEndDate;

    /**
     * 客户信息
     */
    private String customerUser;

    /**
     * 车辆采购意向车型名称
     */
    private String vehicleModelName;

    /**
     * 车型ID
     */
    private Long vehicleModelId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 指导价
     */
    private BigDecimal guidePrice;

    /**
     * 车身颜色
     */
    private String vehicleBodyColor;

    /**
     * 内饰颜色
     */
    private String vehicleInteriorColor;

    /**
     * 期望到车日期
     */
    private String expectedArrivalDate;

    /**
     * 期望到车最后日期
     */
    private String expectedArrivalLastDate;

    /**
     * 合同约定牌照属性
     */
    private String licensePlateAttribute;

    /**
     * 合同约定牌照所属地
     */
    private String licensePlateLocation;

    /**
     * 装潢需求
     */
    private String decorationDemand;

    /**
     * 销售姓名
     */
    private String saleName;

    /**
     * 采购备注
     */
    private String purchaseRemark;

    /**
     * 钉钉ID
     */
    private String dingTalkId;
}
