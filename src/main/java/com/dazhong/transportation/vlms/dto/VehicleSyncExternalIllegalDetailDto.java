package com.dazhong.transportation.vlms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleSyncExternalIllegalDetailDto extends VehicleSyncExternalDto{

    /**
     * 违章时间
     */
    private String illegalTime;
    /**
     * 违章地址
     */
    private String illegalAddress;
    /**
     * 违章代码
     */
    private String illegalCode;
    /**
     * 违章行为
     */
    private String illegalAction;

    /**
     * 违章金额
     */
    private String illegalAmount;

    /**
     * 违章分数
     */
    private Integer illegalScore;

    /**
     * 违章处理状态：已处理 未处理
     */
    private String illegalDealStatus;

    /**
     * 违法程度
     */
    private String illegalDegress;
}
