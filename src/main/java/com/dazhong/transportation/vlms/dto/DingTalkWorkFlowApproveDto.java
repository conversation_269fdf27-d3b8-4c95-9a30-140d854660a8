package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(description = "钉钉审批流审批节点对象")
@AllArgsConstructor
@NoArgsConstructor
public class DingTalkWorkFlowApproveDto {

    @ApiModelProperty(value = "节点操作人展示名称：提交人 还是审批人")
    private String showName;

    @ApiModelProperty(value = "操作人")
    private String optUserName;

    @ApiModelProperty(value = "操作时间")
    private String optDateTime;

    @ApiModelProperty(value = "操作类型")
    private String optType;

    @ApiModelProperty(value = "操作结果")
    private String optResult;

    @ApiModelProperty(value = "审批意见")
    private String remark;
}
