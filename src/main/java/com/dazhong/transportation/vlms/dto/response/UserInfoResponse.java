package com.dazhong.transportation.vlms.dto.response;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户查询结果
 */
@ApiModel(description = "用户查询结果")
@Data
public class UserInfoResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private Long id;

    @ApiModelProperty(value = "用户账号")
    private String userAccount;

    @ApiModelProperty(value = "手机号")
    private String mobilePhone;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "钉钉号")
    private String dingTalkNum;

    @ApiModelProperty(value = "关联角色名称")
    private String roleName;

    @ApiModelProperty(value = "是否超管 1-是 2-否")
    private Integer isSystemAdmin;

    @ApiModelProperty(value = "关联角色列表")
    private List<Long> roleIdList;

    @ApiModelProperty(value = "关联组织机构列表")
    private List<Long> orgIdList;

    @ApiModelProperty(value = "用户关联owner公司ID")
    private List<Long> ownerIdList;
}