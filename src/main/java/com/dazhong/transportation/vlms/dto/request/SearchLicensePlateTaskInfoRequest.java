package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "车牌任务列表入参")
public class SearchLicensePlateTaskInfoRequest extends PageRequest {

    /**
     * 车架号
     */
    @ApiModelProperty("车架号 非必填")
    private String vin;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号 非必填")
    private String licensePlate;

    /**
     * 任务类型
     */
    @ApiModelProperty("任务类型 1-上牌任务 2-退牌任务 非必传")
    private Integer taskType;

    /**
     * 额度资产归属公司list
     */
    @ApiModelProperty("额度资产归属公司list 非必传")
    private List<Integer> quotaAssetCompanyIdList;

    /**
     * 额度编号
     */
    @ApiModelProperty("额度编号 非必传")
    private String quotaNumber;
}
