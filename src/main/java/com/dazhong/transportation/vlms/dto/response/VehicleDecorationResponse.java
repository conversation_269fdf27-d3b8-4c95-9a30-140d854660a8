package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-08 14:45
 */
@Data
@ApiModel(description = "车辆装潢内容信息")
public class VehicleDecorationResponse {

    @ApiModelProperty(value = "装潢类型 1-贴膜 2-脚垫 .....(数据字典详细描叙)")
    private Integer decorationType;

    /**
     * Database Column Remarks:
     *   装潢内容
     */
    @ApiModelProperty(value = "装潢内容")
    private String decorationContent;

    /**
     * Database Column Remarks:
     *   基本价格
     */
    @ApiModelProperty(value = "基本价格")
    private String basePrice;

}
