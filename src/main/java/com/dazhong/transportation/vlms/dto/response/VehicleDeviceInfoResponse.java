package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "设备信息")
public class VehicleDeviceInfoResponse {

    @ApiModelProperty(value = "设备ID", notes = "系统自动生成的唯一编码流水号", example = "1")
    private Long id;

    @ApiModelProperty(value = "设备类型", notes = "有源，无源，原厂，ETC，TBOX", example = "1")
    private String deviceType;

    @ApiModelProperty(value = "设备编号", notes = "设备的唯一编号", example = "D001234567")
    private String deviceSeq;

    @ApiModelProperty(value = "设备品牌", notes = "设备的品牌名称", example = "华为")
    private String deviceBrand;

    @ApiModelProperty(value = "设备型号", notes = "设备的具体型号", example = "型号A123")
    private String deviceModel;

    @ApiModelProperty(value = "SIM卡号", notes = "设备关联的SIM卡号", example = "89860412345678901234")
    private String simCardNumber;

    @ApiModelProperty(value = "安装时间", notes = "设备的安装时间", example = "2024-01-01T10:00:00Z")
    private Date installTime;

    @ApiModelProperty(value = "安装地点", notes = "设备的安装地点", example = "上海市浦东新区")
    private String installLocation;

    @ApiModelProperty(value = "车架号", notes = "设备关联的车架号", example = "LFV3A28K7J3000001")
    private String vin;

    @ApiModelProperty(value = "车牌号", notes = "设备关联的车牌号", example = "京A12345")
    private String licensePlate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "激活时间", notes = "设备的激活时间", example = "2024-01-02T14:30:00Z")
    private Date activationTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "到期时间", notes = "设备的到期时间", example = "2025-01-01T00:00:00Z")
    private Date expirationTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最新定位时间", notes = "GPS最新的定位时间", example = "2024-12-31T15:45:00Z")
    private Date latestLocationTime;

    @ApiModelProperty(value = "最新定位经度", notes = "GPS最新的定位经度", example = "121.4737")
    private BigDecimal longitude;

    @ApiModelProperty(value = "最新定位纬度", notes = "GPS最新的定位纬度", example = "31.2304")
    private BigDecimal latitude;

    @ApiModelProperty(value = "最新定位地点", notes = "GPS最新的定位地点", example = "上海市徐汇区")
    private String latestLocation;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最新里程数据更新时间", notes = "设备读取到最新里程数据的时间", example = "2024-12-31T16:00:00Z")
    private Date latestMileageUpdateTime;

    @ApiModelProperty(value = "最新里程数据", notes = "设备读取到的最新里程数据", example = "12345.67")
    private BigDecimal totalMileage;

}
