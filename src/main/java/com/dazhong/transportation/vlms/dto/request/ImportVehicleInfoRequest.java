package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "导入车辆信息")
@Data
public class ImportVehicleInfoRequest {

    @ApiModelProperty(value = "文件类型 1-装潢信息 2-其他信息 3-行驶证文件 4-产证文件 5-合格证文件 6-发票文件 7-购置税文件 8-营运证 9-折旧数据 10-年检到期日 11-车辆主数据")
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    @ApiModelProperty(value = "文件路径")
    @NotBlank(message = "文件路径不能为空")
    private String filePath;
}
