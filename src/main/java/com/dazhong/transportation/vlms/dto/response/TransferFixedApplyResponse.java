package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "车辆转固申请响应对象")
@Data
public class TransferFixedApplyResponse implements Serializable {

    @ApiModelProperty(value = "ID", example = "12345")
    private Long id;

    @ApiModelProperty(value = "转固申请编号", example = "TF001")
    private String applyNo;

    @ApiModelProperty(value = "钉钉审批编号", example = "DING001")
    private String approvalNumber;

    @ApiModelProperty(value = "转固申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)", example = "3")
    private Integer applyStatus;

    @ApiModelProperty(value = "申请名称", example = "转固申请示例")
    private String applyName;

    @ApiModelProperty(value = "申请备注", example = "无")
    private String applyRemark;

    @ApiModelProperty(value = "申请车辆数量", example = "5")
    private Integer transferQuantity;

    @ApiModelProperty(value = "申请人姓名", example = "张三")
    private String applyUser;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
