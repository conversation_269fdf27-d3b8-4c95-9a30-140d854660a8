package com.dazhong.transportation.vlms.dto.request;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalDetailDto;
import com.dazhong.transportation.vlms.enums.ProductLineEnum;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "保存车辆逆处置申请单入参")
public class SaveReverseDisposalApplicationRequest {

    @ApiModelProperty(value = "处置申请单id", example = "D001")
    private Long id;

    @ApiModelProperty(value = "单据号", example = "D001")
    private String documentNo;

    @ApiModelProperty(value = "单据标题", example = "出售申请单")
    private String documentTitle;

    @ApiModelProperty(value = "所在部门ID", example = "技术部")
    private Long organizationId;

    @ApiModelProperty(value = "所在部门", example = "技术部")
    private String organizationName;

    @ApiModelProperty(value = "单据所属资产公司id", example = "1")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属资产公司名", example = "技术部")
    private String ownerName;
    
    @ApiModelProperty(value = "资产公司所属部门CODE", example = "123")
    private String departmentCode;
    
    @ApiModelProperty(value = "资产公司所属部门名称", example = "财务部")
    private String departmentName;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "申请人审批部门id", example = "1")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人审批部门名", example = "部门")
    private String originatorDeptName;

    @ApiModelProperty(value = "备注", example = "beizhu111")
    private String remark;

    @ApiModelProperty(value = "车辆明细")
    private List<VehicleReverseDisposalDetailDto> vehicleReverseDisposalDetailList;

    @ApiModelProperty(value = "附件")
    private List<VehicleApplyFileDto> vehicleApplyFileList;

    /**
     * 将处置申请单转换为钉钉审批流需要的数据
     *
     * @return 钉钉审批流需要的数据
     */
    public Map<String, String> convertToDingTalkData() {
        Map<String, String> processComponentValues = new HashMap<>();
        processComponentValues.put("单据标题", ObjectValidUtil.formatStr(this.getDocumentTitle()));
        processComponentValues.put("条线", ObjectValidUtil.formatStr(ProductLineEnum.getDesc(this.getProductLine())));
        processComponentValues.put("单据所属资产公司", ObjectValidUtil.formatStr(this.getOwnerName()));
        processComponentValues.put("单据所属机构", ObjectValidUtil.formatStr(this.getOrganizationName()));
        processComponentValues.put("资产公司所属部门", ObjectValidUtil.formatStr(this.getDepartmentCode()));
        processComponentValues.put("备注", ObjectValidUtil.formatStr(this.getRemark()));
        processComponentValues.put("明细数据", JSON.toJSONString(convertVehicleDisposalDetailList()));
        return processComponentValues;
    }

    /**
     * 将处置明细转换为钉钉审批流需要的数据
     *
     * @return 钉钉审批流需要的数据
     */
    public List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> convertVehicleDisposalDetailList() {
        List<List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails>> result = new ArrayList<>();
        for (VehicleReverseDisposalDetailDto vehicleReverseDisposalDetailDto : this.vehicleReverseDisposalDetailList) {
            result.add(vehicleReverseDisposalDetailDto.convertToDingTalkData());
        }
        return result;
    }
}
