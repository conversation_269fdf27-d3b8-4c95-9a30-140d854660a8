package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "查询上牌/退牌任务详情入参")
public class SearchLicensePlateTaskDetailRequest {

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号 必传", required = true)
    @NotNull(message = "任务编号不能为空")
    private String taskNumber;
}
