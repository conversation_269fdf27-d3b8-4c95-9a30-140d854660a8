package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.dazhong.transportation.vlms.dto.ReleasePurchaseOccupiedDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-14 11:02
 */
@Data
@ApiModel(description = "释放采购占用额度数参数")
public class ReleasePurchaseOccupiedRequest {

    @ApiModelProperty(value = "采购申请编号")
    @NotBlank(message = "采购申请编号不能为空")
    private String applyNo;

    @ApiModelProperty(value = "释放采购占用额度数列表")
    @NotEmpty(message = "释放采购占用额度数列表不能为空")
    private List<ReleasePurchaseOccupiedDto> list;
}
