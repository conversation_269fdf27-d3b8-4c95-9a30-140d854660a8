package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.VehicleDecorationDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-08 14:47
 */
@Data
@ApiModel(description = "车辆装潢内容参数")
public class VehicleDecorationRequest {

    @ApiModelProperty(value = "车架号")
    @NotBlank(message = "车架号不能为空")
    private String vin;

    @ApiModelProperty(value = "装潢列表")
    @NotEmpty(message = "装潢列表不能为空")
    private List<VehicleDecorationDto> list;
}
