package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "车辆折旧信息")
@Data
public class VehicleDepreciationDataResponse {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "当前年月")
    private Integer currentMonth ;

    @ApiModelProperty(value = "原值")
    private BigDecimal originalAmount;

    @ApiModelProperty(value = "折旧月")
    private String depreciationMonths;

    @ApiModelProperty(value = "折旧开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date depreciationStartDate;

    @ApiModelProperty(value = "已折旧月")
    private Integer accumulatedDepreciationMonths;

    @ApiModelProperty(value = "折旧金额")
    private BigDecimal depreciationAmount;

    @ApiModelProperty(value = "剩余残值")
    private BigDecimal remainingResidualValue;

    @ApiModelProperty(value = "登记时间", example = "2024-01-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "登记人", example = "张三")
    private String createOperName;

}
