package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DingTalkFileDto {
    private String spaceId;
    private String fileName;
    private Integer fileSize;
    private String fileType;
    private String fileId;
}
