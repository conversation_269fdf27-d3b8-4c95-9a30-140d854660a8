package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "车辆合同数据响应")
@Data
public class VehicleContractDataResponse implements Serializable {

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A004352")
    private String vin;

    @ApiModelProperty(value = "合同号", example = "CON-001")
    private String contractNo;

    @ApiModelProperty(value = "合同开始日期", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractStartDate;

    @ApiModelProperty(value = "合同结束日期", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date contractEndDate;

    @ApiModelProperty(value = "发车日期", example = "2023-02-01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliverDate;

    @ApiModelProperty(value = "发车任务编号", example = "DEL-001")
    private String deliverTaskNo;

    @ApiModelProperty(value = "收车日期", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectDate;

    @ApiModelProperty(value = "收车任务编号", example = "COL-001")
    private String collectTaskNo;

    @ApiModelProperty(value = "裸车价", example = "200000.00")
    private BigDecimal bareCarPrice;

}
