package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 用户信息
 * <AUTHOR>
 * @date 2024-12-27 13:21
 */
@ApiModel(description = "用户信息结")
@Data
public class LoginUserResponse implements Serializable {


    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty(value = "是否超管 1-是 2-否", example = "1")
    private Integer isSystemAdmin;

}