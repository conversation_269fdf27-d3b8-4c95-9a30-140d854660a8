package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel(description = "车辆转固查询参数")
@Data
public class SearchTransferFixedRequest extends PageRequest {

    @ApiModelProperty(value = "钉钉审批编号")
    private String approvalNumber;

    @ApiModelProperty(value = "采购申请状态列表 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)")
    private List<Integer> applyStatus;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "单据所属资产公司ID")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属机构ID")
    private Long applyOrgId;

}
