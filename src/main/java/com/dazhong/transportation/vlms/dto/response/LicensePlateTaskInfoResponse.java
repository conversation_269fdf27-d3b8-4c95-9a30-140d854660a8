package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 车牌任务信息响应类
 */
@Data
@ApiModel(description = "车牌任务信息响应类")
public class LicensePlateTaskInfoResponse {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "T001")
    private String taskNumber;

    /**
     * 任务类型 1-上牌任务 2-退牌任务
     */
    @ApiModelProperty(value = "任务类型 1-上牌任务 2-退牌任务", example = "1")
    private Integer taskType;

    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", example = "LSJDXC857D3000001")
    private String vin;

    /**
     * 车型id
     */
    @ApiModelProperty(value = "车型id")
    private Long vehicleModelId;

    /**
     * 车型名
     */
    @ApiModelProperty(value = "车型名", example = "Model S")
    private String vehicleModelName;

    /**
     * 资产所属公司code
     */
    @ApiModelProperty(value = "资产所属公司id")
    private Long assetCompanyId;

    /**
     * 资产所属公司
     */
    @ApiModelProperty(value = "资产所属公司", example = "Company A")
    private String assetCompanyName;

    @ApiModelProperty(value = "资产机构id", example = "1")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "资产机构名", example = "XYZ")
    private String ownOrganizationName;

    @ApiModelProperty(value = "使用机构id", example = "1")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "使用机构名", example = "XYZ")
    private String usageOrganizationName;

    /**
     * 发证日期（行驶证）
     */
    @ApiModelProperty(value = "发证日期（行驶证）", example = "2023-10-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDateRegistrationCard;

    /**
     * 额度是否退还
     */
    @ApiModelProperty(value = "额度是否退还", example = "true")
    private Integer returnQuotaType;

    /**
     * 额度类型
     */
    @ApiModelProperty(value = "额度类型", example = "Type A")
    private Integer quotaType;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty(value = "额度资产归属公司", example = "Company C")
    private String quotaAssetCompanyName;

    /**
     * 额度编号
     */
    @ApiModelProperty(value = "额度编号", example = "Q001")
    private String quotaNumber;

    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间", example = "2023-10-01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 登记人
     */
    @ApiModelProperty(value = "登记人", example = "John Doe")
    private String createOperName;

}