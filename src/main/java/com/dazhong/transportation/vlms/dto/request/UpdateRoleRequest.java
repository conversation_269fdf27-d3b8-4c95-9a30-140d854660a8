package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Range;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "修改角色参数")
@Data
public class UpdateRoleRequest {

    @ApiModelProperty(value = "角色ID" ,required = true)
    @NotNull(message = "角色ID")
    private Long id;

    @ApiModelProperty(value = "角色名称" ,required = true)
    @NotNull(message = "角色名称不能为空")
    private String roleName;

    @ApiModelProperty(value = "角色状态 1-启用 2-禁用" ,required = true)
    @Range(min = 1,max = 2, message = "角色状态 1-启用 2-禁用")
    private Integer roleStatus;

    @ApiModelProperty(value = "资源列表" ,required = true)
    private List<Long> resourceIdList;

}
