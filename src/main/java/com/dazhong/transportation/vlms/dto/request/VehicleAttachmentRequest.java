package com.dazhong.transportation.vlms.dto.request;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import com.dazhong.transportation.vlms.dto.VehicleAttachmentDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-08 14:47
 */
@Data
@ApiModel(description = "车辆附件内容参数")
public class VehicleAttachmentRequest {

    @ApiModelProperty(value = "车架号")
    @NotBlank(message = "车架号不能为空")
    private String vin;

    @ApiModelProperty(value = "装潢列表")
    @NotEmpty(message = "附件列表不能为空")
    private List<VehicleAttachmentDto> list;
}
