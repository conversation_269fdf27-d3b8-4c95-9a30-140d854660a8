package com.dazhong.transportation.vlms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DingTalkFlowNotifyDto {

    /**
     * 审批流ID
     */
    private String processInstanceId;

    /**
     * 审批流模板code
     */
    private String processCode;

    /**
     * 审批结果 "agree"："同意" , "refuse":"拒绝"
     * 参考 DingTalkConstant.dingTalkResultMap
     */
    private String result;

    /**
     * 完成时间
     */
    private Long finishTime;

    /**
     * 事件ID
     */
    private String eventId;

}
