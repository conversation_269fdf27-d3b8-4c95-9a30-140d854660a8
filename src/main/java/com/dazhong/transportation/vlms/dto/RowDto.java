package com.dazhong.transportation.vlms.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.dazhong.transportation.vlms.enums.PublicBooleanStatusEnum;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import lombok.Data;

@Data
public class RowDto {

    private List<RowValueDto> rowValue;

    private String rowNumber;

    public VehicleDisposalDetailDto convertToVehicleDisposalDetailDto() {
        List<RowValueDto> rowValueDto = this.rowValue;

        VehicleDisposalDetailDto vehicleDisposalDetailDto = new VehicleDisposalDetailDto();

        Map<String, RowValueDto> rowMap = rowValueDto.stream()
                .collect(Collectors.toMap(
                        RowValueDto::getLabel,
                        row -> row,
                        (existing, replacement) -> existing
                ));

        vehicleDisposalDetailDto.setVehicleAssetId(getValue(rowMap, "资产编号"));
        vehicleDisposalDetailDto.setVin(getValue(rowMap, "车架号"));
        vehicleDisposalDetailDto.setLicensePlate(getValue(rowMap, "车牌号"));
        vehicleDisposalDetailDto.setVehicleModelName(getValue(rowMap, "车型"));
        vehicleDisposalDetailDto.setAssetCompanyName(getValue(rowMap, "资产所属公司"));
        vehicleDisposalDetailDto.setOwnOrganizationName(getValue(rowMap, "实际运营公司（所属）"));
        vehicleDisposalDetailDto.setUsageOrganizationName(getValue(rowMap, "实际运营公司（使用）"));
        vehicleDisposalDetailDto.setBelongingTeam(getValue(rowMap, "所属车队"));
        vehicleDisposalDetailDto.setStartDate(getDateValue(rowMap, "投产日期"));
        vehicleDisposalDetailDto.setUsageMonthLimit(getIntegerValue(rowMap, "使用期限（月）"));
        vehicleDisposalDetailDto.setUsedMonths(getIntegerValue(rowMap, "到填表日已用月数"));
        vehicleDisposalDetailDto.setEstimatedMarketPrice(getBigDecimalValue(rowMap, "市场预估价（元）"));
        vehicleDisposalDetailDto.setEstimatedNetSaleValue(getBigDecimalValue(rowMap, "预估最低净售价（元）"));
        vehicleDisposalDetailDto.setMinimumAcceptablePrice(getBigDecimalValue(rowMap, "保留价（元）"));
        vehicleDisposalDetailDto.setHandlingFee(getBigDecimalValue(rowMap, "手续费（元）（商务业务）"));
        vehicleDisposalDetailDto.setSaleReason(getValue(rowMap, "出售原因"));
        vehicleDisposalDetailDto.setSaleGainLoss(getBigDecimalValue(rowMap, "实际出售损益-自动计算"));
        vehicleDisposalDetailDto.setDisposalReason(getValue(rowMap, "报废原因"));
        vehicleDisposalDetailDto.setActualSaleReason(getValue(rowMap, "实际出售原因"));
        vehicleDisposalDetailDto.setEstimatedVehicleLoss(getBigDecimalValue(rowMap, "预估车辆损失（元）"));
        vehicleDisposalDetailDto.setGovernmentSubsidyAmount(getBigDecimalValue(rowMap, "政府补贴金额（元）"));
        vehicleDisposalDetailDto.setOriginalValue(getBigDecimalValue(rowMap, "原值（元）"));
        vehicleDisposalDetailDto.setAccumulatedDepreciation(getValue(rowMap, "已提折旧（元）"));
        vehicleDisposalDetailDto.setNetValue(getBigDecimalValue(rowMap, "净值/残值（元）-自动计算"));
        if (null == vehicleDisposalDetailDto.getNetValue()) {
            vehicleDisposalDetailDto.setNetValue(getBigDecimalValue(rowMap, "净值（元）"));
        }
        vehicleDisposalDetailDto.setFinancialEvaluation(getValue(rowMap, "财务评估说明"));
        vehicleDisposalDetailDto.setActualSellingPrice(getBigDecimalValue(rowMap, "实际售价（元）"));
        vehicleDisposalDetailDto.setActualNetPrice(getBigDecimalValue(rowMap, "实际净售价（元）-自动计算"));
        vehicleDisposalDetailDto.setNetPriceDiff(getBigDecimalValue(rowMap, "实际净售价差值（元）-自动计算"));
        vehicleDisposalDetailDto.setActualSoldQuantity(getIntegerValue(rowMap, "实际出售数量"));
        vehicleDisposalDetailDto.setQuantityDiff(getIntegerValue(rowMap, "计划数量与实际数量差值"));
        vehicleDisposalDetailDto.setActualSaleDesc(getValue(rowMap, "实际出售说明"));
        vehicleDisposalDetailDto.setEstimatedScrapLossAmount(getBigDecimalValue(rowMap, "预估报废损失金额（元）"));
        vehicleDisposalDetailDto.setIsInsuranceAuction(PublicBooleanStatusEnum.getCode(getValue(rowMap, "是否保险公司拍卖")));
        vehicleDisposalDetailDto.setActualAuctionAmount(getBigDecimalValue(rowMap, "实际拍卖金额（元）"));
        vehicleDisposalDetailDto.setActualScrapProfitLoss(getBigDecimalValue(rowMap, "实际报废损益（元）"));
        vehicleDisposalDetailDto.setDingTalkFileList(getDingTalkFileList(rowMap, "附件"));
        vehicleDisposalDetailDto.setActualSaleDingTalkFileList(getDingTalkFileList(rowMap, "实际售价附件"));
        vehicleDisposalDetailDto.setRealRetirementDate(getDateValue(rowMap, "退役日期"));
        return vehicleDisposalDetailDto;
    }

    public PurchaseApplyDetailDto convertToPurchaseApplyDetailDto() {
        List<RowValueDto> rowValueDto = this.rowValue;

        PurchaseApplyDetailDto purchaseApplyDetailDto = new PurchaseApplyDetailDto();

        Map<String, RowValueDto> rowMap = rowValueDto.stream()
                .collect(Collectors.toMap(
                        RowValueDto::getLabel,
                        row -> row,
                        (existing, replacement) -> existing
                ));

        purchaseApplyDetailDto.setApplyDetailsNo(getValue(rowMap, "申请明细行号"));
        purchaseApplyDetailDto.setReturnOnInvestment(getValue(rowMap, "投资回报率"));
        purchaseApplyDetailDto.setMonthlyBicycleRevenue(getValue(rowMap, "月单车收益"));
        purchaseApplyDetailDto.setVehicleResidualValueRate(getValue(rowMap, "车辆残值率"));
        return purchaseApplyDetailDto;
    }
    private String getValue(Map<String, RowValueDto> rowMap, String label) {
        RowValueDto rowDto = rowMap.get(label);
        return rowDto != null ? (String) rowDto.getValue() : null;
    }

    private Date getDateValue(Map<String, RowValueDto> rowMap, String label) {
        String value = getValue(rowMap, label);
        return value != null ? DateUtil.parse(value) : null;
    }

    private Integer getIntegerValue(Map<String, RowValueDto> rowMap, String label) {
        String value = getValue(rowMap, label);
        return value != null ? Integer.parseInt(value) : null;
    }

    private BigDecimal getBigDecimalValue(Map<String, RowValueDto> rowMap, String label) {
        String value = getValue(rowMap, label);
        return value != null ? new BigDecimal(value) : null;
    }

    private List<DingTalkFileDto> getDingTalkFileList(Map<String, RowValueDto> rowMap, String label) {
        RowValueDto rowDto = rowMap.get(label);
        if (null == rowDto || null == rowDto.getValue()) {
            return null;
        }
        JSONArray fileJsonArray = new JSONArray(rowDto.getValue());
        if (CollectionUtil.isNotEmpty(fileJsonArray)) {
            return fileJsonArray.toList(DingTalkFileDto.class);
        }
        return null;
    }

    private String getFileName(Map<String, RowValueDto> rowMap, String label) {
        RowValueDto rowDto = rowMap.get(label);
        if (null == rowDto || null == rowDto.getValue()) {
            return null;
        }
        JSONArray fileJsonArray = new JSONArray(rowDto.getValue());
        if (CollectionUtil.isNotEmpty(fileJsonArray)) {
            return fileJsonArray.getJSONObject(0).getStr("fileName");
        }
        return null;
    }
}
