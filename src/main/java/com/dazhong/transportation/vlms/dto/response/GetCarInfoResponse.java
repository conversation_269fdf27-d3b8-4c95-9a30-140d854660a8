package com.dazhong.transportation.vlms.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class GetCarInfoResponse implements Serializable {

    /**
     * 状态码 0：成功
     */
    private Integer stateCode;

    /**
     * 失败原因
     */
    private String msg;

    /**
     * 消息体
     */
    private List<CarInfo> payload;

    @Data
    public static class CarInfo implements Serializable {
        private Integer carId;
        private Integer groupId;
        private Integer companyId;
        private String companyCode;
        private String companyName;
        private String propertyOfName;
        private Integer carStatusId;
        private String carStatusName;
        private Integer managementModelId;
        private String managementModelName;
        private Integer shiftId;
        private String shiftName;
        private String carTeamId;
        private String carTeamName;
        private Integer version;
        private Integer enabled;
        private String license;
        private String carNoName;
        private String frameNo;
        private String engineNo;
        private String productDate;
        private String purchaseDate;
        private String startDate;
        private String licenseDate;
        private String retireDate;
        private String operatingNo;
        private String hasRight;
        private BigDecimal usageAgeLimit;
        private BigDecimal depreciationAgeLimit;
        private Integer vehicleAssetId;
        private String obtainWay;
        private String usage;
        private String vehicleColor;
        private String owner;
        private String vehicleCategory;
        private String area;
        private String supplier;
        private String vehicleModel;
        private Integer vehicleModelId;
        private String operationCategory;
        private Integer assessPassenger;
        private String vehicleBrand;
        private String carType;
        private String gasType;
        private String modelNo;
    }
}