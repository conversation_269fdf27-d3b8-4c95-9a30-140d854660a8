package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-01-06 16:41
 */
@ApiModel(description = "车辆列表结果")
@Data
public class VehicleBaseListResponse {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "车型id")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名")
    private String vehicleModelName;
}
