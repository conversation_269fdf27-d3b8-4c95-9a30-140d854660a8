package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 响应类：额度单流水记录
 */
@ApiModel(description = "额度单流水记录")
@Data
public class LicensePlateQuotaTransactionRecordResponse {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "退牌时间", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licensePlateWithdrawalDate;

    @ApiModelProperty(value = "额度单打印日期", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date quotaPrintDate;

    @ApiModelProperty(value = "额度单", example = "Q123456789")
    private String quotaNumber;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运", example = "1")
    private Integer quotaType;

    @ApiModelProperty(value = "额度资产归属公司id")
    private Long assetCompanyId;

    @ApiModelProperty(value = "额度资产归属名", example = "ABC123")
    private String assetCompanyName;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "状态（0=正常   1=已删除）", example = "0")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "创建人id", example = "1001")
    private Long createOperId;

    @ApiModelProperty(value = "创建人名称", example = "张三")
    private String createOperName;

    @ApiModelProperty(value = "更新时间", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "更新人id", example = "1002")
    private Long updateOperId;

    @ApiModelProperty(value = "更新人名称", example = "李四")
    private String updateOperName;

    @ApiModelProperty(value = "上牌任务编号", example = "T123456789")
    private String taskNumber;

}
