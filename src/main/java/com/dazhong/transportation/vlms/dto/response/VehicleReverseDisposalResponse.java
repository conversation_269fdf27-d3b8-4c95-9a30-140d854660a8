package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "车辆逆处置信息")
@Data
public class VehicleReverseDisposalResponse implements Serializable {

    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A123456")
    private String vin;

    @ApiModelProperty(value = "资产编号", example = "ASSET123456")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车型ID", example = "1001")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名称", example = "问界")
    private String vehicleModelName;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "资产所属公司ID", example = "100")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产所属公司", example = "COMPANY123")
    private String assetCompanyName;

    @ApiModelProperty(value = "资产机构id", example = "100")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "资产机构名", example = "COMPANY123")
    private String ownOrganizationName;

    @ApiModelProperty(value = "使用机构id", example = "100")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "使用机构名", example = "COMPANY123")
    private String usageOrganizationName;

    @ApiModelProperty(value = "关联审批中处置单", example = "COMPANY123")
    private String disposalDocumentNo;
}
