package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@ApiModel(description = "修改角色状态参数")
public class UpdateRoleStatusRequest {

    @ApiModelProperty(value = "角色ID不能为空" ,required = true)
    @NotNull(message = "角色ID不能为空")
    private Long id; // 页码

    @ApiModelProperty(value = "角色状态 1-启用 2-禁用" ,required = true)
    @Range(min = 1,max = 2, message = "角色状态 1-启用 2-禁用")
    private Integer roleStatus; // 每页大小

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRoleStatus() {
        return roleStatus;
    }

    public void setRoleStatus(Integer roleStatus) {
        this.roleStatus = roleStatus;
    }
}
