package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-13 14:32
 */
@ApiOperation(value = "转固新增参数")
@Data
public class FixedAssetVehicleDto {

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车", example = "1")
    private Integer businessLine;

    @ApiModelProperty(value = "车架号")
    private String vin;

}
