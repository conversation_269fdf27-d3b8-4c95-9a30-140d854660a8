package com.dazhong.transportation.vlms.dto.request;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-23 15:42
 */
@ApiModel(description = "删除字典参数")
@Data
public class DeleteDataDictRequest {

    /**
     *  资源id
     */
    @ApiModelProperty(value = "数据字典编码" ,required = true)
    @NotBlank(message = "数据字典编码不能为空")
    private String dataCode;

}
