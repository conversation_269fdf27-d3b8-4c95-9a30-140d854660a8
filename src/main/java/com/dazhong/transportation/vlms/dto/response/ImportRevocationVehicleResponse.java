package com.dazhong.transportation.vlms.dto.response;

import java.util.List;

import com.dazhong.transportation.vlms.dto.LicensePlateTaskQuotaDetailDto;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "文件下载信息")
public class ImportRevocationVehicleResponse {

    @ApiModelProperty(value = "车辆详情列表")
    private List<LicensePlateTaskVehicleDetailDto> vehicleDetailList;

    @ApiModelProperty(value = "额度详情列表")
    private List<LicensePlateTaskQuotaDetailDto> quotaDetailList;

    public ImportRevocationVehicleResponse(List<LicensePlateTaskVehicleDetailDto> vehicleDetailList, List<LicensePlateTaskQuotaDetailDto> quotaDetailList) {
        this.vehicleDetailList = vehicleDetailList;
        this.quotaDetailList = quotaDetailList;
    }
}
