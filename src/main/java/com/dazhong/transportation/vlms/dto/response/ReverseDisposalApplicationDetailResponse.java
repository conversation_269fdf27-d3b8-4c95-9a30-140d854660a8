package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalDetailDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "车辆逆处置申请单详情响应对象")
public class ReverseDisposalApplicationDetailResponse {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "单据号", example = "D001")
    private String documentNo;

    @ApiModelProperty(value = "钉钉审批号", example = "D001")
    private String dingTalkNo;

    @ApiModelProperty(value = "单据标题", example = "出售申请单")
    private String documentTitle;

    @ApiModelProperty(value = "单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废", example = "1")
    private String documentStatus;

    @ApiModelProperty(value = "所在部门ID", example = "技术部")
    private Long organizationId;

    @ApiModelProperty(value = "所在部门", example = "技术部")
    private String organizationName;

    @ApiModelProperty(value = "单据所属资产公司ID", required = true)
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属机构名", required = true)
    private String ownerName;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "申请人审批部门id", example = "1")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人审批部门名", example = "部门")
    private String originatorDeptName;

    @ApiModelProperty(value = "资产公司所属部门CODE")
    private String departmentCode;
    
    @ApiModelProperty(value = "资产公司所属部门名称")
    private String departmentName;

    @ApiModelProperty(value = "车辆数量", example = "1")
    private Integer vehicleNumber;

    @ApiModelProperty(value = "备注", example = "beizhu")
    private String remark;

    @ApiModelProperty(value = "申请人", example = "beizhu")
    private Long createOperId;

    @ApiModelProperty(value = "申请人", example = "beizhu")
    private String createOperName;

    @ApiModelProperty(value = "车辆明细")
    private List<VehicleReverseDisposalDetailDto> vehicleReverseDisposalDetailList;

    @ApiModelProperty(value = "附件")
    private List<VehicleApplyFileDto> vehicleApplyFileList;
}
