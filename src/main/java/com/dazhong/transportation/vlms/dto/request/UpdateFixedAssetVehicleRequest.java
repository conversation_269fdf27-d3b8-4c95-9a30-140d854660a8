package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

@ApiModel(description = "转固修改参数")
@Data
public class UpdateFixedAssetVehicleRequest {

    @ApiModelProperty(value = "操作类型 1-保存 2-提交 ",required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;

    @ApiModelProperty(value = "转固申请编号")
    @NotBlank(message = "转固申请编号不能为空")
    private String applyNo;

    @ApiModelProperty(value = "单据标题")
    @NotBlank(message = "单据标题不能为空")
    private String applyName;

    @ApiModelProperty(value = "转固说明")
    @NotBlank(message = "转固说明不能为空")
    @Size(max = 300,message = "转固说明不能超过300个字符")
    private String applyRemark;

    @ApiModelProperty(value = "资产所有公司ID",required = true)
    @NotNull(message = "资产所有公司不能为空")
    private Integer ownerId;

    @ApiModelProperty(value = "申请公司ID",required = true)
    @NotNull(message = "申请公司不能为空")
    private Long applyOrgId;

    @ApiModelProperty(value = "车架集合")
    @NotEmpty(message = "车架信息集合不能为空")
    private List<FixedAssetVehicleDto> vehicleList;

    @ApiModelProperty(value = "附件列表")
    private List<VehicleApplyFileDto> fileList;

    @ApiModelProperty(value = "资产公司所属部门code",required = true)
    @NotBlank(message = "资产公司所属部门不能为空")
    private String departmentCode;

    @ApiModelProperty(value = "资产公司所属部门名称",required = true)
    @NotBlank(message = "资产公司所属部门不能为空")
    private String departmentName;

    @ApiModelProperty(value = "申请人钉钉部门ID",required = true)
    @NotNull(message = "申请人钉钉部门不能为空")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人钉钉部门名称",required = true)
    @NotBlank(message = "申请人钉钉部门名称不能为空")
    private String originatorDeptName;
}
