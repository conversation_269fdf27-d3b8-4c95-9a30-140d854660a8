package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-23 9:11
 */
@Data
@ApiModel(description = "用户查询参数")
public class SearchUserRequest extends PageRequest {

    /**
     * 用户账号
     */
    @ApiModelProperty("用户账号 非必传")
    private String userAccount;

    /**
     * 用户姓名
     */
    @ApiModelProperty("用户姓名 非必传")
    private String name;

    /**
     * 关联角色
     */
    @ApiModelProperty("关联角色 非必传")
    private Long roleId;

    @ApiModelProperty("关联机构 非必传")
    private Long orgId;

    @ApiModelProperty("关联公司 非必传")
    private Long ownerId;

}
