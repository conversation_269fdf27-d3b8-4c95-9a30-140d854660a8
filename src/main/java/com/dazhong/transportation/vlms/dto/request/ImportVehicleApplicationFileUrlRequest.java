package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "导入文件信息")
@Data
public class ImportVehicleApplicationFileUrlRequest extends BaseImportFileUrlRequest {


    @ApiModelProperty(value = "单据类型 1-车辆调拨 2-车辆转籍 3-切换业务类型")
    @NotBlank(message = "单据类型不能为空")
    private Integer applicationType;
}
