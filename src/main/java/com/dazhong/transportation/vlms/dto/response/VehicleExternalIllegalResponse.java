package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "车辆违章信息明细")
public class VehicleExternalIllegalResponse implements Serializable {


    @ApiModelProperty(value = "车牌号", example = "")
    private String licensePlate;

    @ApiModelProperty(value = "违章时间", example = "")
    private String illegalTime;

    @ApiModelProperty(value = "违章地址", example = "")
    private String illegalAddress;

    @ApiModelProperty(value = "违章代码", example = "")
    private String illegalCode;

    @ApiModelProperty(value = "违章行为", example = "")
    private String illegalAction;

    @ApiModelProperty(value = "违章金额", example = "")
    private String illegalAmount;

    @ApiModelProperty(value = "违章分数", example = "")
    private Integer illegalScore;

    @ApiModelProperty(value = "违章处理状态", example = "已处理/未处理")
    private String illegalDealStatus;

    @ApiModelProperty(value = "违法程度", example = "")
    private String illegalDegress;

    @ApiModelProperty(value = "数据同步时间", example = "")
    private String syncDateTime;
}
