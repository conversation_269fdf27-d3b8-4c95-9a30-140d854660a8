package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "用户查询结果")
@Data
public class VehicleBasicResponse implements Serializable {

    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A123456")
    private String vin;

    @ApiModelProperty(value = "资产编号", example = "ASSET123456")
    private String vehicleAssetId;

    @ApiModelProperty(value = "发动机号", example = "EN123456")
    private String engineNo;

    @ApiModelProperty(value = "发动机型号", example = "V8")
    private String engineModel;

    @ApiModelProperty(value = "车型ID", example = "1001")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名称", example = "问界")
    private String vehicleModelName;

    @ApiModelProperty(value = "商品车型")
    private Integer vehicleAbbreviationId;

    @ApiModelProperty(value = "汽车之家售价(元)")
    private Integer priceOnAutohome;

    @ApiModelProperty(value = "汽车之家售价(元)")
    private Integer msrp;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "是否占用管控额度 1-是 2-否", example = "1")
    private Integer useQuotaType;

    @ApiModelProperty(value = "额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运", example = "1")
    private Integer quotaType;

    @ApiModelProperty(value = "额度资产归属公司", example = "35")
    private Integer quotaAssetCompanyId;

    @ApiModelProperty(value = "额度资产归属公司名", example = "COMPANY123")
    private String quotaAssetCompanyName;

    @ApiModelProperty(value = "车身颜色ID", example = "101")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "内饰颜色", example = "黑色")
    private String interiorColor;

    @ApiModelProperty(value = "供应商ID", example = "201")
    private Integer supplierId;

    @ApiModelProperty(value = "是否回购 1-是 2-否", example = "2")
    private Integer isRepurchase;

    @ApiModelProperty(value = "回购时间", example = "2023-10-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date repurchaseDate;

    @ApiModelProperty(value = "回购要求", example = "无特殊要求")
    private String repurchaseRequirements;

    @ApiModelProperty(value = "使用年限", example = "5")
    private Integer usageAgeLimit;

    @ApiModelProperty(value = "折旧年限", example = "10")
    private Integer depreciationAgeLimit;

    @ApiModelProperty(value = "裸车价", example = "200000.00")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "购置税", example = "20000.00")
    private BigDecimal purchaseTax;

    @ApiModelProperty(value = "牌照费", example = "5000.00")
    private BigDecimal licensePlatePrice;

    @ApiModelProperty(value = "上牌杂费", example = "3000.00")
    private BigDecimal licensePlateOtherPrice;

    @ApiModelProperty(value = "装潢费", example = "10000.00")
    private BigDecimal upholsterPrice;

    @ApiModelProperty(value = "购置总价", example = "238000.00")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "账面净值", example = "180000.00")
    private BigDecimal remainPrice;

    @ApiModelProperty(value = "旧车销售价", example = "150000.00")
    private BigDecimal secondHandPrice;

    @ApiModelProperty(value = "资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废", example = "1")
    private Integer propertyStatus;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车", example = "1")
    private Integer businessLine;

    @ApiModelProperty(value = "运营状态 1-待运 2-租出", example = "1")
    private Integer operatingStatus;

    @ApiModelProperty(value = "车辆申购公司", example = "35")
    private Long subscriptionCompanyId;

    @ApiModelProperty(value = "申购公司名称", example = "公司A")
    private String subscriptionCompanyName;

    @ApiModelProperty(value = "资产所属公司ID", example = "100")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产所属公司", example = "COMPANY123")
    private String assetCompanyName;

    @ApiModelProperty(value = "资产机构id", example = "100")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "资产机构名", example = "COMPANY123")
    private String ownOrganizationName;

    @ApiModelProperty(value = "使用机构id", example = "100")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "使用机构名", example = "COMPANY123")
    private String usageOrganizationName;

    @ApiModelProperty(value = "所属车队", example = "车队A")
    private String belongingTeam;

    @ApiModelProperty(value = "获得方式 数据字典详细描叙", example = "1")
    private Integer obtainWayId;

    @ApiModelProperty(value = "管辖区县", example = "北京市朝阳区")
    private Integer areaId;

    @ApiModelProperty(value = "最新定位地点", example = "北京市朝阳区")
    private String latestPosition;

    @ApiModelProperty(value = "最新总里程 (车机)", example = "50000")
    private String latestTotalMileage;

    @ApiModelProperty(value = "使用性质 (行驶证)", example = "运营")
    private Integer usageIdRegistrationCard;

    @ApiModelProperty(value = "车辆类型 (行驶证)", example = "轿车")
    private String vehicleTypeRegistrationCard;

    @ApiModelProperty(value = "注册日期(行驶证)", example = "2020-01-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registrationDateRegistrationCard;

    @ApiModelProperty(value = "发证日期 (行驶证)", example = "2020-01-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDateRegistrationCard;

    @ApiModelProperty(value = "强制报废日期 (行驶证)", example = "2030-01-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date retirementDateRegistrationCard;

    @ApiModelProperty(value = "年检到期日 (行驶证)", example = "2024-01-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date annualInspectionDueDateRegistrationCard;

    @ApiModelProperty(value = "档案编号", example = "FILE123456")
    private String fileNumber;

    @ApiModelProperty(value = "车辆出厂日期 (产证)", example = "2019-01-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productDate;

    @ApiModelProperty(value = "发证日期 (产证)", example = "2019-01-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issuanceDate;

    @ApiModelProperty(value = "上牌日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseDate;

    @ApiModelProperty(value = "产证编号", example = "CERT123456")
    private String certificateNumber;

    @ApiModelProperty(value = "行驶证文件地址", example = "http://example.com/vehicle_license.jpg")
    private String vehicleLicenseUrl;

    @ApiModelProperty(value = "产证文件地址", example = "http://example.com/certificate_ownership.jpg")
    private String certificateOwnershipUrl;

    @ApiModelProperty(value = "合格证文件地址", example = "http://example.com/certificate_conformity.jpg")
    private String certificateConformityUrl;

    @ApiModelProperty(value = "车辆发票文件地址", example = "http://example.com/vehicle_invoice.jpg")
    private String vehicleInvoiceUrl;

    @ApiModelProperty(value = "购置税文件地址", example = "http://example.com/purchase_tax.jpg")
    private String purchaseTaxUrl;

    @ApiModelProperty(value = "营运证文件地址", example = "http://example.com/operating_permit.jpg")
    private String operatingPermitUrl;

    @ApiModelProperty(value = "投产日期", example = "2025-02-16")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "使用期限（月）", example = "5")
    private Integer usageMonthLimit;

    @ApiModelProperty(value = "到填表日已用月数", example = "1")
    private Integer monthsUsed;
}