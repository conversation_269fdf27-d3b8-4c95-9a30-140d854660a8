package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-01-06 14:29
 */
@ApiModel(description = "车辆收货信息")
@Data
public class VehicleReceivingDto {

    @ApiModelProperty(value = "采购申请明细行号")
    private String applyDetailsNo;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "资产编号")
    private String vehicleAssetId;

    @ApiModelProperty(value = "发动机号")
    private String engineNo;

    @ApiModelProperty(value = "车型ID")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名称")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆型号")
    private String vehicleModelNo;

    @ApiModelProperty(value = "车身颜色")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "内饰颜色")
    private String interiorColor;

    @ApiModelProperty(value = "下单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty(value = "收货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;

    @ApiModelProperty(value = "供应商ID")
    private Integer supplierId;

    @ApiModelProperty(value = "是否回购 1-是 2-否")
    private Integer isRepurchase;

    @ApiModelProperty(value = "回购时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date repurchaseDate;

    @ApiModelProperty(value = "回购要求")
    private String repurchaseRequirements;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "回购要求")
    private String createOperName;

    @ApiModelProperty(value = "车辆资产所属公司ID")
    private Integer ownerId;
}
