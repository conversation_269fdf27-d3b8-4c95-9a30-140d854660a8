package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "车型信息参数")
public class UpsertVehicleModelRequest {

    @ApiModelProperty(value = "车型ID", example = "12345", notes = "车型唯一标识")
    private Long id;

    @ApiModelProperty(value = "汽车之家车型ID", example = "12345", notes = "来源于汽车之家平台的车型ID")
    private Long autohomeVehicleModelId;

    @ApiModelProperty(value = "品牌", example = "宝马", notes = "车辆的品牌名称")
    private String vehicleBrandName;

    @ApiModelProperty(value = "车系", example = "宝马3系", notes = "车辆所属的车系")
    private String vehicleSeriesName;

    @ApiModelProperty(value = "车型", example = "宝马320i", notes = "车辆的具体型号")
    private String vehicleModelName;

    @ApiModelProperty(value = "财务车型", example = "宝马320i财务版", notes = "财务管理用车型名称")
    private String financialModelName;

    @ApiModelProperty(value = "商品车型", example = "宝马320i商品版", notes = "商品管理用车型名称")
    private Integer vehicleAbbreviationId;

    @ApiModelProperty(value = "座位数", example = "5", notes = "车辆可容纳的座位数")
    private Integer assessPassenger;

    @ApiModelProperty(value = "大巴实际座位数", example = "20", notes = "车辆可容纳的座位数")
    private Integer busAssessPassenger;

    @ApiModelProperty(value = "发动机型号（车型）", example = "N47D20A", notes = "车型所搭载的发动机型号")
    private String engineModelNo;

    @ApiModelProperty(value = "能源类型", example = "1", notes = "1代表汽油，2代表柴油，3代表电动")
    private Integer gasTypeId;

    @ApiModelProperty(value = "车辆级别", example = "4", notes = "1-经济轿车, 2-舒适轿车, 3-豪华轿车, 4-SUV, 5-商务车, 6-轻客, 7-跑车, 8-皮卡, 9-大巴")
    private Integer vehicleLevel;

    @ApiModelProperty(value = "轮胎规格", example = "225/50R17", notes = "车辆所使用的轮胎规格")
    private String wheelParam;

    @ApiModelProperty(value = "环保标准", example = "Euro 6", notes = "车辆符合的环保排放标准")
    private Integer exhaustId;

    @ApiModelProperty(value = "上市时间", example = "2023-01", notes = "车型的上市时间")
    private String ttmMonth;

    @ApiModelProperty(value = "车身长度（mm）", example = "4800", notes = "车辆的车身长度，单位mm")
    private Integer outLength;

    @ApiModelProperty(value = "车身宽度（mm）", example = "1850", notes = "车辆的车身宽度，单位mm")
    private Integer outWidth;

    @ApiModelProperty(value = "车身高度（mm）", example = "1450", notes = "车辆的车身高度，单位mm")
    private Integer outHeight;

    @ApiModelProperty(value = "车身轴距（mm）", example = "2850", notes = "车辆的轴距，单位mm")
    private Integer wheelBase1;

    @ApiModelProperty(value = "整备质量(kg)", example = "1500", notes = "车辆的整备质量，单位kg")
    private Integer totalMass;

    @ApiModelProperty(value = "油箱容积（升）", example = "50", notes = "车辆油箱的容积，单位升")
    private Integer fuelTankCapacity;

    @ApiModelProperty(value = "燃油标号", example = "92号", notes = "适用的燃油标号")
    private String fuelLabelName;

    @ApiModelProperty(value = "发动机排量(mL)", example = "2000", notes = "发动机的排量，单位mL")
    private BigDecimal capacity;

    @ApiModelProperty(value = "电池容量（kWh）", example = "50.0", notes = "电动汽车的电池容量，单位kWh")
    private BigDecimal batteryCapacity;

    @ApiModelProperty(value = "车辆续航里程（km）", example = "500", notes = "电动汽车的续航里程，单位km")
    private Integer vehicleRange;

    @ApiModelProperty(value = "快速充能时间（h）", example = "1.5", notes = "快速充电所需时间，单位小时")
    private BigDecimal fastChargingTime;

    @ApiModelProperty(value = "慢充能时间（h）", example = "8", notes = "慢充电所需时间，单位小时")
    private BigDecimal slowChargingTime;

    @ApiModelProperty(value = "百公里油耗（L/100km）", example = "6.5", notes = "车辆百公里油耗，单位L/100km")
    private BigDecimal fuelEconomyMiit;

    @ApiModelProperty(value = "售价（汽车之家）", example = "300000", notes = "汽车之家平台上的售价")
    private Integer priceOnAutohome;

    @ApiModelProperty(value = "车辆型号", example = "320i", notes = "车辆的具体型号")
    private String vehicleModelNo;

    @ApiModelProperty(value = "功率", example = "160", notes = "发动机的功率，单位kW")
    private Integer power;

    @ApiModelProperty(value = "前轮距", example = "1500", notes = "车辆前轮的轮距，单位mm")
    private Integer treadFront;

    @ApiModelProperty(value = "后轮距", example = "1500", notes = "车辆后轮的轮距，单位mm")
    private Integer treadRear;

    @ApiModelProperty(value = "轮胎数", example = "4", notes = "车辆的轮胎数量")
    private Integer wheelQuantity;

    @ApiModelProperty(value = "后轴钢板弹簧数", example = "5", notes = "后轴上的钢板弹簧数量")
    private Integer springLamination;

    @ApiModelProperty(value = "轴距2", example = "2900", notes = "车辆的第二轴距，单位mm")
    private Integer wheelBase2;

    @ApiModelProperty(value = "轴距3", example = "3000", notes = "车辆的第三轴距，单位mm")
    private Integer wheelBase3;

    @ApiModelProperty(value = "轴数", example = "2", notes = "车辆的轴数")
    private Integer axleQuantity;

    @ApiModelProperty(value = "货箱内部尺寸长", example = "2000", notes = "货箱的内部长度，单位mm")
    private Integer containerLength;

    @ApiModelProperty(value = "货箱内部尺寸宽", example = "1500", notes = "货箱的内部宽度，单位mm")
    private Integer containerWidth;

    @ApiModelProperty(value = "货箱内部尺寸高", example = "1200", notes = "货箱的内部高度，单位mm")
    private Integer containerHeight;

    @ApiModelProperty(value = "核定载质量", example = "5000", notes = "车辆的核定载质量，单位kg")
    private Integer assessMass;

    @ApiModelProperty(value = "准牵引总质量", example = "7000", notes = "车辆的准牵引总质量，单位kg")
    private Integer tractionMass;

    @ApiModelProperty(value = "驾驶室载客", example = "3", notes = "驾驶室内可容纳的座位数")
    private Integer cabPassenger;

    @ApiModelProperty(value = "国产/进口", example = "0", notes = "0表示国产，1表示进口")
    private Integer manufactureLocation;

    @ApiModelProperty(value = "车门数", example = "4", notes = "车辆的车门数量")
    private Integer doorQuantity;

    @ApiModelProperty(value = "档位数", example = "6", notes = "车辆变速箱的档位数")
    private Integer gearQuantity;

    @ApiModelProperty(value = "0-100加速", example = "7.5", notes = "车辆从0到100公里加速所需时间，单位秒")
    private BigDecimal acceleration;

    @ApiModelProperty(value = "最高车速", example = "240", notes = "车辆的最高车速，单位km/h")
    private Integer speed;

    @ApiModelProperty(value = "最小转弯半径", example = "5.5", notes = "车辆的最小转弯半径，单位m")
    private BigDecimal turningRadius;

    @ApiModelProperty(value = "最小离地间隙", example = "200", notes = "车辆的最小离地间隙，单位mm")
    private Integer roadClearance;

    @ApiModelProperty(value = "最大爬坡度", example = "30", notes = "车辆能爬升的最大坡度，单位度")
    private Integer gradient;

    @ApiModelProperty(value = "等速油耗", example = "5", notes = "车辆在等速情况下的油耗，单位L/100km")
    private BigDecimal fuelEconomy;

    @ApiModelProperty(value = "扭矩", example = "250", notes = "车辆的最大扭矩，单位Nm")
    private Integer torque;

    @ApiModelProperty(value = "压缩比", example = "12.0", notes = "发动机的压缩比")
    private String compressionRatio;

    @ApiModelProperty(value = "制造商", example = "1", notes = "车辆制造商ID")
    private Integer manufacturerId;

    @ApiModelProperty(value = "车辆品牌", example = "宝马", notes = "车辆品牌ID")
    private Integer vehicleBrandId;

    @ApiModelProperty(value = "车辆类型", example = "1", notes = "车辆类型ID")
    private Integer vehicleTypeId;

    @ApiModelProperty(value = "驱动类型", example = "1", notes = "车辆的驱动类型ID")
    private Integer wheelDriveId;

    @ApiModelProperty(value = "制动形式", example = "1", notes = "车辆的制动类型ID")
    private Integer breakModeId;

    @ApiModelProperty(value = "转向方式", example = "1", notes = "车辆的转向类型ID")
    private Integer turnModeId;

    @ApiModelProperty(value = "驾驶位置", example = "1", notes = "车辆的驾驶位置ID")
    private Integer drivePositionId;

    @ApiModelProperty(value = "发动机位置", example = "1", notes = "车辆的发动机位置ID")
    private Integer enginePositionId;

    @ApiModelProperty(value = "变速箱形式", example = "1", notes = "车辆的变速箱类型ID")
    private Integer gearBoxTypeId;
}
