package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "车辆事故信息明细")
public class VehicleExternalAccidentResponse implements Serializable {


    @ApiModelProperty(value = "车牌号", example = "")
    private String licensePlate;

    @ApiModelProperty(value = "事故编号", example = "")
    private String accidentNo;

    @ApiModelProperty(value = "事故状态", example = "报案中、报案完成 、事故处理中 、事故处理完毕 、已作废")
    private String accidentStatus;

    @ApiModelProperty(value = "事故类型", example = "单车事故、单车含物损 、双车事故、多车事故")
    private String accidentType;

    @ApiModelProperty(value = "事故发生时间", example = "")
    private String accidentTime;

    @ApiModelProperty(value = "责任情况", example = "我方全责 、我方同责 、我方主责 、我方次责、我方无责 、责任未定")
    private String accidentResponseType;

    @ApiModelProperty(value = "事故等级", example = "一级 、二级")
    private String accidentLevel;

    @ApiModelProperty(value = "处理人", example = "一级 、二级")
    private String accidentDealUser;

    @ApiModelProperty(value = "数据同步时间", example = "")
    private String syncDateTime;
}
