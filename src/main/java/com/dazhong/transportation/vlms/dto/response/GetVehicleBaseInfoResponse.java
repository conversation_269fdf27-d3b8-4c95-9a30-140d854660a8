package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GetVehicleBaseInfoResponse implements Serializable {

    /**
     * 自定义Integer反序列化器，处理"-"等非数字字符串
     */
    public static class SafeIntegerDeserializer extends JsonDeserializer<Integer> {
        @Override
        public Integer deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String value = p.getValueAsString();
            if (value == null || value.trim().isEmpty() || "-".equals(value.trim())) {
                return null;
            }
            try {
                return Integer.valueOf(value.trim());
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }

    /**
     * 状态码 0：成功
     */
    private int code;

    /**
     * 失败原因
     */
    private String message;

    /**
     * 消息内容
     */
    private GetVehicleBaseInfo data;

    @Data
    public static class GetVehicleBaseInfo implements Serializable {
        /**
         * 汽车之家车型列表
         */
        private VehicleBaseInfo info;
    }

    @Data
    public static class VehicleBaseInfo implements Serializable {
        /**
         * 汽车之家车型表主键ID
         */
        private Long id;

        /**
         * 汽车之家车型名称
         */
        private String fullModelName;

        /**
         * 座位数
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer seatCount;

        /**
         * 发动机型号
         */
        private String engineType;

        /**
         * 上市时间
         */
        private String ttmMonth;

        /**
         * 车身长度
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer bodyLength;

        /**
         * 车身宽度
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer bodyWidth;

        /**
         * 车身高度
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer bodyHeight;

        /**
         * 车身轴距
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer wheelBase;

        /**
         * 总质量
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer curbWeight;

        /**
         * 油箱容积 L
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer tankage;

        /**
         * 燃油标号
         */
        private String fuelLabelName;

        /**
         * 发动机排量 ml
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer engineDisplacementMl;

        /**
         * 电池容量 kWh
         */
        private String batteryPower;

        /**
         * 车辆续驶里程 km
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer actualTotal;

        /**
         * CLTC工况续航里程 km
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer cltcTotal;

        /**
         * 百公里油耗 L/100km
         */
        private BigDecimal wltcCost;

        /**
         * 前轮距
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer wheelFront;

        /**
         * 后轮距
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer wheelBack;

        /**
         * 车门数
         */
        @JsonDeserialize(using = SafeIntegerDeserializer.class)
        private Integer doorCount;

        /**
         * 车系名称
         */
        private String vehicleSeriesName;

        /**
         * 品牌名称
         */
        private String serialTypeName;

        /**
         *   0-100加速（秒）
         */
        private String hundredKilometerAcceleration;

        /**
         *   最高时速（km/h）
         */
        private String topSpeed;

        /**
         * 扭矩
         */
        private String torque;

        /**
         * 燃料类型
         */
        private String fuelTypeName;


        /**
         * 轮胎规格
         */
        private String wheelSize;


        /**
         * 厂商指导价
         */
        private String mrsp;
    }
}
