package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.DingTalkWorkFlowApproveDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "钉钉审批流审批节点列表对象")
@Data
public class GetDingTalkWorkFlowResponse {

    @ApiModelProperty(value = "审批单号")
    private String instanceId;

    @ApiModelProperty(value = "审批状态")
    private String workFlowStatus;

    @ApiModelProperty(value = "审批结果")
    private String workFlowResult;

    @ApiModelProperty(value = "审批进度节点列表")
    private List<DingTalkWorkFlowApproveDto> approveDtoList;

}
