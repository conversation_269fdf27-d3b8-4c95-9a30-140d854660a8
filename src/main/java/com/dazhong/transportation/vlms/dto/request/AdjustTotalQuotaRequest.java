package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 额度信息表响应对象
 *
 * <AUTHOR>
 * @date 2024-12-30 09:08:00
 */
@ApiModel(description = "额度调整入参")
@Data
public class AdjustTotalQuotaRequest {

    /**
     * 调整类型 1-总数增加 2-总数减少 3-类型调整
     */
    @ApiModelProperty(value = "调整类型", example = "1")
    private Integer adjustType;

    /**
     * 额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @ApiModelProperty(value = "额度类型", example = "1")
    private Integer quotaType;

    /**
     * 额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @ApiModelProperty(value = "调整额度类型", example = "1")
    private Integer adjustQuotaType;

    /**
     * 额度资产归属公司
     */
    @ApiModelProperty(value = "额度资产归属公司", example = "001")
    private String assetCompanyCode;

    /**
     * 额度资产归属公司ID
     */
    @ApiModelProperty(value = "额度资产归属公司ID", example = "1")
    private Integer assetCompanyId;

    /**
     * 调整个数
     */
    @ApiModelProperty(value = "调整个数", example = "100")
    private Integer adjustNum;

    /**
     * 调整原因
     */
    @ApiModelProperty(value = "调整原因", example = "调整原因")
    private String adjustReason;
}
