package com.dazhong.transportation.vlms.dto;

import cn.hutool.core.date.DateUtil;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.enums.DisposalTypeEnum;
import com.dazhong.transportation.vlms.enums.QuotaTypeEnum;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "车辆处置明细DTO")
public class VehicleDisposalDetailDto {

    @ApiModelProperty(value = "主键id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "处置单号", example = "D001")
    private String documentNo;

    @ApiModelProperty(value = "资产编号", example = "A001")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车架号", example = "1HGCM82633A123456")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "车型ID", example = "1001")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名", example = "1001")
    private String vehicleModelName;

    @ApiModelProperty(value = "资产所属公司id", example = "35")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产所属公司名", example = "XX公司")
    private String assetCompanyName;

    @ApiModelProperty(value = "实际运营公司（所属）ID", example = "35")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "实际运营公司（所属）名", example = "YY公司")
    private String ownOrganizationName;

    @ApiModelProperty(value = "实际运营公司（使用）ID", example = "YY公司")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "实际运营公司（使用）名", example = "YY公司")
    private String usageOrganizationName;

    @ApiModelProperty(value = "所属车队", example = "车队A")
    private String belongingTeam;

    @ApiModelProperty(value = "投产日期", example = "2020-01-01T00:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "使用期限（月）", example = "60")
    private Integer usageMonthLimit;

    @ApiModelProperty(value = "到填表日已用月数", example = "36")
    private Integer usedMonths;

    @ApiModelProperty(value = "市场预估价（元）", example = "100000.00")
    private BigDecimal estimatedMarketPrice;

    @ApiModelProperty(value = "起拍价（元）", example = "100000.00")
    private BigDecimal startingPrice;

    @ApiModelProperty(value = "预估净售值（元）", example = "80000.00")
    private BigDecimal estimatedNetSaleValue;

    @ApiModelProperty(value = "最低成交价（元）", example = "70000.00")
    private BigDecimal minimumAcceptablePrice;

    @ApiModelProperty(value = "手续费（元）", example = "5000.00")
    private BigDecimal handlingFee;

    @ApiModelProperty(value = "出售原因", example = "车辆老旧")
    private String saleReason;

    @ApiModelProperty(value = "实际出售损益", example = "6.6")
    private BigDecimal saleGainLoss;

    @ApiModelProperty(value = "实际出售原因", example = "车辆老旧")
    private String actualSaleReason;

    @ApiModelProperty(value = "使用性质", example = "运营")
    private Integer usageIdRegistrationCard;

    @ApiModelProperty(value = "报废类型", example = "正常报废")
    private Integer disposalType;

    @ApiModelProperty(value = "预估车辆损失（元）", example = "10000.00")
    private BigDecimal estimatedVehicleLoss;

    @ApiModelProperty(value = "政府补贴金额（元）", example = "5000.00")
    private BigDecimal governmentSubsidyAmount;

    @ApiModelProperty(value = "报废原因", example = "车辆老旧")
    private String disposalReason;

    @ApiModelProperty(value = "原值（元）", example = "10")
    private BigDecimal originalValue;

    @ApiModelProperty(value = "已提折旧（元）", example = "10")
    private String accumulatedDepreciation;

    @ApiModelProperty(value = "净值/残值（元）", example = "10")
    private BigDecimal netValue;

    @ApiModelProperty(value = "财务评估", example = "财务评估")
    private String financialEvaluation;

    @ApiModelProperty(value = "实际售价（元）", example = "10")
    private BigDecimal actualSellingPrice;

    @ApiModelProperty(value = "实际净售价（元）", example = "10")
    private BigDecimal actualNetPrice;

    @ApiModelProperty(value = "实际净售价差值（元）", example = "10")
    private BigDecimal netPriceDiff;

    @ApiModelProperty(value = "实际出售数量", example = "10")
    private Integer actualSoldQuantity;

    @ApiModelProperty(value = "计划数量与实际数量差值", example = "10")
    private Integer quantityDiff;

    @ApiModelProperty(value = "实际出售说明", example = "10")
    private String actualSaleDesc;

    @ApiModelProperty(value = "预估报废损失金额（元）", example = "10")
    private BigDecimal estimatedScrapLossAmount;

    @ApiModelProperty(value = "是否保险公司拍卖 1-是 2-否", example = "10")
    private Integer isInsuranceAuction;

    @ApiModelProperty(value = "实际拍卖金额（元）", example = "10")
    private BigDecimal actualAuctionAmount;

    @ApiModelProperty(value = "实际报废损益（元）", example = "10")
    private BigDecimal actualScrapProfitLoss;

    @ApiModelProperty(value = "附件id", example = "10")
    private List<DingTalkFileDto> dingTalkFileList;

    @ApiModelProperty(value = "附件url", example = "10")
    private String fileUrl;

    @ApiModelProperty(value = "实际售价附件id", example = "10")
    private List<DingTalkFileDto> actualSaleDingTalkFileList;

    @ApiModelProperty(value = "实际售价附件url", example = "10")
    private String actualSaleFileUrl;

    @ApiModelProperty(value = "退役日期", example = "2020-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date realRetirementDate;

    /**
     * 商售单字段
     */
    @ApiModelProperty(value = "牌照性质（手工）", example = "10")
    private String licenseTypeManual;

    @ApiModelProperty(value = "上牌日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date licenseDate;

    @ApiModelProperty(value = "额度类型", example = "10")
    private Integer quotaType;

    @ApiModelProperty(value = "商品车型id", example = "10")
    private Integer vehicleAbbreviationId;

    @ApiModelProperty(value = "颜色id", example = "10")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "msrp", example = "10")
    private String msrp;

    @ApiModelProperty(value = "公里数", example = "10")
    private BigDecimal mileage;

    @ApiModelProperty(value = "车况", example = "10") 
    private String vehicleCondition;

    @ApiModelProperty(value = "当月资产净值", example = "10")
    private BigDecimal currentMonthNetValue;

    @ApiModelProperty(value = "最低出售价（元）", example = "10")
    private BigDecimal minimumSellingPrice;

    @ApiModelProperty(value = "拍卖保留价（元）", example = "10")
    private BigDecimal auctionReservePrice;

    @ApiModelProperty(value = "预计盈亏1", example = "10")
    private BigDecimal expectedProfitLossOne;

    @ApiModelProperty(value = "预计盈亏2", example = "10")
    private BigDecimal expectedProfitLossTwo;

    @ApiModelProperty(value = "二手车发票URL", example = "10")
    private String secondhandCarInvoiceUrl;

    /**
     * 将当前对象转换为钉钉审批流中的表单数据
     *
     * @return 钉钉审批流中的表单数据结构
     */
    public List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> convertToDingTalkData(Map<String, Map<Integer, String>> dictInfoMap) {
        List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> result = new ArrayList<>();

        // 车辆基础字段
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("资产编号").setValue(ObjectValidUtil.formatStr(this.vehicleAssetId)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车架号").setValue(ObjectValidUtil.formatStr(this.vin)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车牌号").setValue(ObjectValidUtil.formatStr(this.licensePlate)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车型").setValue(ObjectValidUtil.formatStr(this.vehicleModelName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("资产所属公司").setValue(ObjectValidUtil.formatStr(this.assetCompanyName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（所属）").setValue(ObjectValidUtil.formatStr(this.ownOrganizationName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（使用）").setValue(ObjectValidUtil.formatStr(this.usageOrganizationName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("所属车队").setValue(ObjectValidUtil.formatStr(this.belongingTeam)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("投产日期").setValue(this.startDate != null ? DateUtil.format(this.startDate, "yyyy-MM-dd") : ""));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("使用期限（月）").setValue(ObjectValidUtil.formatStr(this.usageMonthLimit)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("到填表日已用月数").setValue(ObjectValidUtil.formatStr(this.usedMonths)));
        // 出售字段
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("市场预估价（元）").setValue(ObjectValidUtil.formatStr(this.estimatedMarketPrice)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("起拍价（元）").setValue(ObjectValidUtil.formatStr(this.startingPrice)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("预估最低净售价（元）").setValue(ObjectValidUtil.formatStr(this.estimatedNetSaleValue)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("保留价（元）").setValue(ObjectValidUtil.formatStr(this.minimumAcceptablePrice)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("手续费（元）（商务业务）").setValue(ObjectValidUtil.formatStr(this.handlingFee)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("出售原因").setValue(ObjectValidUtil.formatStr(this.saleReason)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际出售损益").setValue(ObjectValidUtil.formatStr(this.saleGainLoss)));
        // 报废字段
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("使用性质").setValue(ObjectValidUtil.formatStr(dictInfoMap.get("usage").get(this.usageIdRegistrationCard))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("报废类型").setValue(ObjectValidUtil.formatStr(DisposalTypeEnum.getDesc(this.disposalType))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("预计车辆损失（元）").setValue(ObjectValidUtil.formatStr(this.estimatedVehicleLoss)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("政府补贴金额（大巴）（元）").setValue(ObjectValidUtil.formatStr(this.governmentSubsidyAmount)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("报废原因").setValue(ObjectValidUtil.formatStr(this.disposalReason)));
        // 出售字段（商务业务）
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("商品车型").setValue(ObjectValidUtil.formatStr(dictInfoMap.get("vehicleAbbreviation").get(this.getVehicleAbbreviationId()))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("颜色").setValue(ObjectValidUtil.formatStr(dictInfoMap.get("vehicleColor").get(this.getVehicleColorId()))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("额度类型（系统）").setValue(ObjectValidUtil.formatStr(QuotaTypeEnum.getDesc(this.quotaType))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("牌照性质（手工）").setValue(ObjectValidUtil.formatStr(this.licenseTypeManual)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("上牌日期").setValue(ObjectValidUtil.formatStr(DateUtil.format(this.getLicenseDate(), "yyyy-MM-dd"))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车龄（月）").setValue(ObjectValidUtil.formatStr(this.getUsedMonths())));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("MSRP").setValue(ObjectValidUtil.formatStr(this.msrp)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("公里数").setValue(ObjectValidUtil.formatStr(this.mileage)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车况").setValue(ObjectValidUtil.formatStr(this.vehicleCondition)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("原值（元）").setValue(ObjectValidUtil.formatStr(this.getOriginalValue())));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("累计折旧（元）").setValue(ObjectValidUtil.formatStr(this.getAccumulatedDepreciation())));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("当月资产净值（元）").setValue(ObjectValidUtil.formatStr(this.getCurrentMonthNetValue())));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("最低出售价（元）").setValue(ObjectValidUtil.formatStr(this.minimumSellingPrice)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("拍卖保留价（元）").setValue(ObjectValidUtil.formatStr(this.auctionReservePrice)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("预计盈亏1（元）").setValue(ObjectValidUtil.formatStr(this.expectedProfitLossOne)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("预计盈亏2（元）").setValue(ObjectValidUtil.formatStr(this.expectedProfitLossTwo)));
        return result;
    }
}
