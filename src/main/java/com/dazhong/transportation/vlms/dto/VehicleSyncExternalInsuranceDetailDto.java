package com.dazhong.transportation.vlms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleSyncExternalInsuranceDetailDto extends VehicleSyncExternalDto {
    /**
     * 保险类型：(1：商业险，2：交强险)
     */
    private Integer insuranceType;
    /**
     * 保单号
     */
    private String insuranceNo;
    /**
     * 起保日期
     */
    private String insuranceStartDate;

    /**
     * 终保日期
     */
    private String insuranceEndDate;

    /**
     * 保险公司名称
     */
    private String insurerName;

    /**
     * 保费
     */
    private String insuranceAmount;

    /**
     * 三者保额
     */
    private String thirdPartAmount;
}
