package com.dazhong.transportation.vlms.dto.response;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-02-07 13:08
 */
@Data
@ApiModel(description = "车辆列表信息")
public class VehicleListResponse {

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "资产编号")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车型id")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名称", example = "问界M9")
    private String vehicleModelName;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废")
    private Integer propertyStatus;

    @ApiModelProperty(value = "条线 1-出租车业务线 2-商务长包业务线 3-SVIP业务线 4-网约车业务线 5-新快车业务线")
    private Integer productLine;

    @ApiModelProperty(value = "业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车")
    private Integer businessLine;

    @ApiModelProperty(value = "运营状态 1-待运 2-租出")
    private Integer operatingStatus;

    @ApiModelProperty(value = "资产所属公司ID")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "资产机构")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "使用机构")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "所属车队")
    private String belongingTeam;

    @ApiModelProperty(value = "获得方式 数据字典详细描叙")
    private Integer obtainWayId;

    @ApiModelProperty(value = "年检到期日(行驶证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date annualInspectionDueDateRegistrationCard;

    @ApiModelProperty(value = "注册日期(行驶证)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registrationDateRegistrationCard;

    @ApiModelProperty(value = "车辆所属公司")
    private String assetCompanyName;

    @ApiModelProperty(value = "资产机构")
    private String ownOrganizationName;

    @ApiModelProperty(value = "使用机构")
    private String usageOrganizationName;


    @ApiModelProperty(value = "车辆年限")
    private String vehicleLife;


    @ApiModelProperty(value = "收货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptDate;

    @ApiModelProperty(value = "车身颜色ID")
    private Integer vehicleColorId;

    @ApiModelProperty(value = "车身颜色")
    private String vehicleColor;

    @ApiModelProperty(value = "商品车型ID")
    private Integer vehicleAbbreviationId;

    @ApiModelProperty(value = "商品车型")
    private String vehicleAbbreviation;

    @ApiModelProperty(value = "座位数")
    private Integer assessPassenger;
}
