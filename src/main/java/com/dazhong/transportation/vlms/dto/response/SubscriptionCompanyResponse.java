package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-12-27 15:42
 */
@ApiModel(description = "申购公司列表结果")
@Data
public class SubscriptionCompanyResponse {



    @ApiModelProperty(value = "公司code" ,required = true)
    private String companyCode;


    @ApiModelProperty(value = "数据字典名称" ,required = true)
    private String companyName;

}
