package com.dazhong.transportation.vlms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "车牌额度操作日志")
public class LicensePlateQuotaOperateLogDto {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    /**
     * 调整类型 (1-总数增加 2-总数减少 3-类型调整)
     * 字典String类型
     */
    @ApiModelProperty(value = "调整类型 (1-总数增加 2-总数减少 3-类型调整)", example = "1")
    private String adjustType;

    @ApiModelProperty(value = "公司code", example = "ABC123")
    private String assetCompanyCode;

    @ApiModelProperty(value = "公司名", example = "Company A")
    private String assetCompanyName;

    @ApiModelProperty(value = "调整原因", example = "Reason for adjustment")
    private String adjustReason;

    @ApiModelProperty(value = "操作内容", example = "Content of the operation")
    private String operateContent;

    @ApiModelProperty(value = "Delete flag (0: normal 1: deleted)", example = "0")
    private Integer isDeleted;

    @ApiModelProperty(value = "Create time", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "Create operator ID", example = "user123")
    private Long createOperId;

    @ApiModelProperty(value = "Create operator name", example = "Operator A")
    private String createOperName;

    @ApiModelProperty(value = "Update time", example = "2023-10-01T12:00:00Z")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "Update operator ID", example = "user123")
    private Long updateOperId;

    @ApiModelProperty(value = "Update operator name", example = "Operator A")
    private String updateOperName;

    public LicensePlateQuotaOperateLogDto() {
    }

    /**
     * 构造函数，用于初始化部分字段
     *
     * @param adjustType       调整类型
     * @param assetCompanyCode 公司code
     * @param assetCompanyName 公司名
     * @param adjustReason     调整原因
     * @param operateContent   操作内容
     */
    public LicensePlateQuotaOperateLogDto(Integer adjustType, String assetCompanyCode, String assetCompanyName, String adjustReason, String operateContent) {
        this.adjustType = adjustType.toString();
        this.assetCompanyCode = assetCompanyCode;
        this.assetCompanyName = assetCompanyName;
        this.adjustReason = adjustReason;
        this.operateContent = operateContent;
    }
}
