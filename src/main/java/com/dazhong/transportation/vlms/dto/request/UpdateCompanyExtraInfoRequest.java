package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(description = "新增角色参数")
@Data
public class UpdateCompanyExtraInfoRequest {

    @ApiModelProperty(value = "外键id", required = true)
    @NotNull(message = "外键id不能为空")
    private Long foreignId;

    @ApiModelProperty(value = "业务类型 1-组织架构 2-资产所有者", required = true)
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @ApiModelProperty(value = "总经理名称", required = true)
    @NotNull(message = "总经理名称不能为空")
    private String ceoName;

    @ApiModelProperty(value = "总经理手机号", required = true)
    private String ceoPhone;

    @ApiModelProperty(value = "钉钉号", required = true)
    private String dingTalkNo;
}
