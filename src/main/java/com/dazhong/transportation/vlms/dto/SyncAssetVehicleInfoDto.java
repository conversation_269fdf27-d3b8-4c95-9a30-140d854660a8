package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

@Data
public class SyncAssetVehicleInfoDto {

    @ApiModelProperty(value = "车ID", notes = "vehicleInfo.id")
    private Long id;

    @ApiModelProperty(value = "车辆ID", notes = "vehicleInfo.id")
    private Long cardId;

    @ApiModelProperty(value = "车牌号", notes = "vehicleInfo.licensePlate")
    private String licenseTag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "上牌日期", notes = "vehicleManagementLegacyInfo.licenseDate")
    private Date licenseDate;

    @ApiModelProperty(value = "使用性质ID", notes = "vehicleInfo.usageIdRegistrationCard")
    private Integer licenseTypeId;

    @ApiModelProperty(value = "使用性质", notes = "vehicleInfo.usageIdRegistrationCard 查询 usage 字典表")
    private String licenseType;

    @ApiModelProperty(value = "商品车型ID", notes = "vehicleModelInfo.vehicleAbbreviationId")
    private Integer carNameId;

    @ApiModelProperty(value = "商品车型名称", notes = "vehicleModelInfo.vehicleAbbreviationId 查询 vehicleAbbreviation 字典表")
    private String name;

    @ApiModelProperty(value = "车型名称", notes = "vehicleModelInfo.vehicleModelName")
    private String vehicleModelName;

    @ApiModelProperty(value = "财务车型名称", notes = "vehicleModelInfo.financialModelName")
    private String financialModelName;

    @ApiModelProperty(value = "车型ID", notes = "vehicleModelInfo.id")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车身颜色ID", notes = "vehicleInfo.vehicleColorId")
    private Integer carColorId;

    @ApiModelProperty(value = "车身颜色", notes = "vehicleInfo.vehicleColorId 查询 vehicleColor 字典表")
    private String carColor;

    @ApiModelProperty(value = "使用机构ID", notes = "vehicleInfo.usageOrganizationId")
    private Long companyId;

    @ApiModelProperty(value = "使用机构", notes = "vehicleInfo.usageOrganizationId 关联 org_info表")
    private String company;

    @ApiModelProperty(value = "资产车辆拥有公司ID", notes = "vehicleManagementLegacyInfo.ownerId")
    private Integer ownerId;

    @ApiModelProperty(value = "资产车辆拥有公司", notes = "vehicleManagementLegacyInfo.ownerId 查询 owner 字典表")
    private String owner;

    @ApiModelProperty(value = "资产车辆拥有公司地址", notes = "vehicleManagementLegacyInfo.ownerId 查询 owner 字典表")
    private String address;

    @ApiModelProperty(value = "座位数", notes = "vehicleModelInfo.assessPassenger")
    private Integer seatNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆出厂日期 (产证)", notes = "vehicleInfo.productDate")
    private Date produceDate;

    @ApiModelProperty(value = "购置总价", notes = "vehicleInfo.totalPrice")
    private BigDecimal carPrice;

    @ApiModelProperty(value = "制造商ID", notes = "vehicleModelInfo.manufacturerId")
    private Integer productId;

    @ApiModelProperty(value = "制造商", notes = "vehicleModelInfo.manufacturerId 查询 manufacturer 字典表")
    private String product;

    @ApiModelProperty(value = "轮胎数", notes = "vehicleModelInfo.wheelQuantity")
    private Integer tyreNumber1;

    @ApiModelProperty(value = "轮胎数2", notes = "vehicleModelInfo.wheelQuantity")
    private Integer tyreNumber2;

    @ApiModelProperty(value = "轮胎规格", notes = "vehicleModelInfo.wheelParam")
    private String tyreType;

    @ApiModelProperty(value = "车身长度", notes = "vehicleModelInfo.outLength")
    private Integer length;

    @ApiModelProperty(value = "车身宽度", notes = "vehicleModelInfo.outWidth")
    private Integer width;

    @ApiModelProperty(value = "车身高度", notes = "vehicleModelInfo.outHeight")
    private Integer height;

    @ApiModelProperty(value = "能源类型ID", notes = "vehicleModelInfo.gasTypeId")
    private Integer oilTradeId;

    @ApiModelProperty(value = "能源类型", notes = "vehicleModelInfo.gasTypeId 查询 gasType 字典表")
    private String oilTrade;

    @ApiModelProperty(value = "轴线距离1", notes = "0")
    private Integer axesDistance1 = 0 ;

    @ApiModelProperty(value = "轴线距离2", notes = "0")
    private Integer axesDistance2 = 0;

    @ApiModelProperty(value = "轴线距离3", notes = "0")
    private Integer axesDistance3 = 0;

    @ApiModelProperty(value = "车身轴距1", notes = "vehicleModelInfo.wheelBase1")
    private Integer wheelDistance1;

    @ApiModelProperty(value = "车身轴距2", notes = "vehicleModelInfo.wheelBase2")
    private Integer wheelDistance2;

    @ApiModelProperty(value = "核定载质量", notes = "vehicleModelInfo.assessMass")
    private Integer netWeight;

    @ApiModelProperty(value = "整备质量", notes = "vehicleModelInfo.totalMass")
    private Integer totalWeight;

    @ApiModelProperty(value = "GPRS", notes = "null")
    private Integer gprs;

    @ApiModelProperty(value = "账面净值（车辆当前价值）", notes = "vehicleInfo.remainPrice")
    private BigDecimal remainFee;

    @ApiModelProperty(value = "旧车售价", notes = "vehicleInfo.secondHandPrice")
    private BigDecimal soldPrice;

    @ApiModelProperty(value = "buyer", notes = "null")
    private Integer buyer;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "投产日期", notes = "vehicleManagementLegacyInfo.startDate")
    private Date verifyDate;

    @ApiModelProperty(value = "装潢费", notes = "vehicleInfo.upholsterPrice")
    private BigDecimal upholsterFee;

    @ApiModelProperty(value = "上牌杂费", notes = "vehicleInfo.licensePlateOtherPrice")
    private BigDecimal licenseOtherPrice;

    @ApiModelProperty(value = "裸车价", notes = "vehicleInfo.purchasePrice")
    private BigDecimal carPurePrice;

    @ApiModelProperty(value = "购置税", notes = "vehicleInfo.purchaseTax")
    private BigDecimal purchaseCost;

    @ApiModelProperty(value = "车辆类型ID", notes = "vehicleModelInfo.vehicleTypeId")
    private Integer carClassId;

    @ApiModelProperty(value = "车辆类型", notes = "vehicleModelInfo.vehicleTypeId 查询 vehicleType 字典表")
    private String carClass;

    @ApiModelProperty(value = "发动机号", notes = "vehicleInfo.engineNo")
    private String engineNumber;

    @ApiModelProperty(value = "车架号", notes = "vehicleInfo.vin")
    private String frameNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "实际报废日期", notes = "vehicleInfo.realRetirementDate")
    private Date rejectDate;
}
