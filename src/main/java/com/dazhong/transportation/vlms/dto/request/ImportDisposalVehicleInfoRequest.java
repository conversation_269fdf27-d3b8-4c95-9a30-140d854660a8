package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "导入处置车辆信息请求")
public class ImportDisposalVehicleInfoRequest extends BaseImportFileUrlRequest {

    @ApiModelProperty(value = "处置申请单号")
    private String documentNo;

    @ApiModelProperty(value = "单据类型 1-出售申请 2-报废申请 3-商售申请")
    private Integer documentType;
}