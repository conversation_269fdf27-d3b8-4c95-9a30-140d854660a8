package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 组织机构树结果对象
 * <AUTHOR>
 * @date 2024-12-20 17:30
 */
@ApiModel(description = "组织机构树结果对象")
@Data
public class DingTalkDepartmentTreeResponse implements Serializable {


    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private Long id;
    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String name;

    /**
     * 子机构列表
     */
    @ApiModelProperty("子机构列表")
    private List<DingTalkDepartmentTreeResponse> children;

}