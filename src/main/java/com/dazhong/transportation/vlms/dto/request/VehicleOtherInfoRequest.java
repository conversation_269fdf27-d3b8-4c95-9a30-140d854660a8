package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025-02-08 14:47
 */
@Data
@ApiModel(description = "车辆其他信息参数")
public class VehicleOtherInfoRequest {

    @ApiModelProperty(value = "车架号")
    @NotBlank(message = "车架号不能为空")
    private String vin;

    @ApiModelProperty(value = "营运类别")
    @NotNull(message = "营运类别不能为空")
    private Integer operationCategoryId;

    @ApiModelProperty(value = "营运证号")
    private String operatingNo;

    @ApiModelProperty(value = "是否营运")
    @NotNull(message = "是否营运不能为空")
    private Integer operateTypeId;

    @ApiModelProperty(value = "号牌种类")
    @NotNull(message = "号牌种类不能为空")
    private Integer vehicleCategoryId;

    @ApiModelProperty(value = "管辖区域")
    @NotNull(message = "管辖区域不能为空")
    private Integer areaId;

    @ApiModelProperty(value = "车辆出厂日期 (产证)")
    @NotBlank(message = "车辆出厂日期 (产证)不能为空")
    private String productDate;

    @ApiModelProperty(value = "发证日期 (产证)")
    @NotBlank(message = "发证日期 (产证)不能为空")
    private String issuanceDate;

    @ApiModelProperty(value = "产证编号")
    @NotBlank(message = "产证编号不能为空")
    private String certificateNumber;

    @ApiModelProperty(value = "资产所属公司")
    @NotNull(message = "资产所属公司不能为空")
    private Integer assetCompanyId;

    @ApiModelProperty(value = "实际运营公司（所属）")
    @NotNull(message = "实际运营公司（所属）不能为空")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "实际运营公司（使用）")
    @NotNull(message = "实际运营公司（使用）不能为空")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "使用年限")
    @NotNull(message = "使用年限不能为空")
    private Integer usageYears;

    @ApiModelProperty(value = "折旧年限")
    @NotNull(message = "折旧年限不能为空")
    private Integer depreciationYears;

    @ApiModelProperty(value = "裸车价")
    private String purchasePrice;

    @ApiModelProperty(value = "购置税")
    private String purchaseTax;

    @ApiModelProperty(value = "牌照费")
    private String licensePlatePrice;

    @ApiModelProperty(value = "上牌杂费")
    private String licensePlateOtherPrice;

    @ApiModelProperty(value = "装潢费")
    private String upholsterPrice;

    @ApiModelProperty(value = "购置总价")
    private String totalPrice;
}
