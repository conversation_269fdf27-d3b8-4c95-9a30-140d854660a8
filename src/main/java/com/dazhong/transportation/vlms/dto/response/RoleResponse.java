package com.dazhong.transportation.vlms.dto.response;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色查询结果
 */
@ApiModel(description = "角色查询结果")
@Data
public class RoleResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "角色ID")
    private Long id;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色状态 1-启用 2-禁用")
    private Integer roleStatus;

    @ApiModelProperty(value = "角色资源iD列表")
    private List<Long> resourceIdList;
}
