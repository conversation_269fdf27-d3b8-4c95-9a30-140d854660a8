package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-01-07 13:15
 */
@ApiModel(description = "关闭采购申请参数")
@Data
public class ClosePurchaseApplyRequest {

    @ApiModelProperty(value = "采购申请编号列表", required = true)
    @NotEmpty(message = "采购申请编号列表不能为空")
    private List<String> purchaseApplyNoList;

}
