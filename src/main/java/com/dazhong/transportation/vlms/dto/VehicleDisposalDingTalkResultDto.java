package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "车辆处置钉钉回调信息DTO")
public class VehicleDisposalDingTalkResultDto {

    @ApiModelProperty(value = "主键id", example = "1")
    private Integer id;

    @ApiModelProperty(value = "处置单号", example = "D001")
    private String documentNo;

    @ApiModelProperty(value = "原值（元）", example = "10")
    private BigDecimal originalValue;

    @ApiModelProperty(value = "已提折旧（元）", example = "10")
    private BigDecimal depreciationTaken;

    @ApiModelProperty(value = "预计收益/损失（元）", example = "10")
    private BigDecimal estimatedProfitLoss;

    @ApiModelProperty(value = "净值/残值（元）", example = "10")
    private BigDecimal netValueOrSalvage;

    @ApiModelProperty(value = "实际出售数量", example = "10")
    private Integer actualVehiclesSold;

    @ApiModelProperty(value = "实际总盈亏（元）", example = "10")
    private BigDecimal actualTotalProfitLoss;

    @ApiModelProperty(value = "实际单车盈亏（元）", example = "10")
    private BigDecimal actualPerVehicleProfitLoss;

    @ApiModelProperty(value = "实预1差额/扣除撤拍车辆预计总盈亏1（元）", example = "10")
    private BigDecimal actualVsEstimatedDiffOne;

    @ApiModelProperty(value = "实预2差额/扣除撤拍车辆预计总盈亏2（元）", example = "10")
    private BigDecimal actualVsEstimatedDiffTwo;
}
