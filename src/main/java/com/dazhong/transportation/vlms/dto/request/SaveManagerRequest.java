package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "转固新增参数")
@Data
public class SaveManagerRequest {

    @ApiModelProperty(value = "公司ID ",required = true)
    @NotNull(message = "公司ID不能为空")
    private Long id;

    @ApiModelProperty("总经理名称")
    @NotBlank(message = "总经理不能为空")
    private String generalManager;

    @ApiModelProperty("总经理手机号")
    private String generalPhone;

    @ApiModelProperty(value = "钉钉号")
    @NotBlank(message = "钉钉号不能为空")
    private String dingTalkNum;

}
