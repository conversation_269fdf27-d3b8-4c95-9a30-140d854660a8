package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.VehicleApplicationDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleApplyFileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "车辆主数据申请单详情响应对象")
public class VehicleApplicationDetailResponse {

    @ApiModelProperty(value = "主键id", example = "1")
    private Long id;

    @ApiModelProperty(value = "单据类型 1-车辆调拨 2-车辆转籍 3-切换业务类型", example = "1")
    private Integer applicationType;

    @ApiModelProperty(value = "钉钉审批单号", example = "1")
    private String dingTalkNo;

    @ApiModelProperty(value = "单据标题", example = "出售申请单")
    private String documentTitle;

    @ApiModelProperty(value = "申请人", example = "申请人")
    private String applyName;

    @ApiModelProperty(value = "申请人钉钉部门id")
    private Long originatorDeptId;

    @ApiModelProperty(value = "申请人钉钉部门名")
    private String originatorDeptName;

    @ApiModelProperty(value = "单据状态 1-未提交 2-审批中 3-审批拒绝 4-审批通过 5-已作废", example = "1")
    private Integer documentStatus;

    @ApiModelProperty(value = "单据所属资产公司id", example = "出售申请单")
    private Integer ownerId;

    @ApiModelProperty(value = "单据所属资产公司名", example = "出售申请单")
    private String ownerName;

    @ApiModelProperty(value = "单据所属机构id", example = "出售申请单")
    private Long organizationId;

    @ApiModelProperty(value = "单据所属机构名", example = "出售申请单")
    private String organizationName;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "车辆明细")
    private List<VehicleApplicationDetailDto> vehicleApplicationDetailList;

    @ApiModelProperty(value = "附件")
    private List<VehicleApplyFileDto> vehicleApplyFileList;
    
    @ApiModelProperty(value = "调出部门编码")
    private String transferFromDepartmentCode;
    
    @ApiModelProperty(value = "调出部门名称")
    private String transferFromDepartmentName;
    
    @ApiModelProperty(value = "调入部门编码")
    private String transferToDepartmentCode;
    
    @ApiModelProperty(value = "调入部门名称")
    private String transferToDepartmentName;
    
    @ApiModelProperty(value = "产品线 1-巡网业务线 2-商务业务线")
    private Integer productLine;
    
    @ApiModelProperty(value = "承担方")
    private String costBearer;

    @ApiModelProperty(value = "合同号", example = "aaaaa")
    private String contractNumber;
    
    @ApiModelProperty(value = "变动原因")
    private String updateReason;
}
