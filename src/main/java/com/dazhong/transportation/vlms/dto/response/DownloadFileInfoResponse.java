package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "文件下载信息")
public class DownloadFileInfoResponse {

    @ApiModelProperty(value = "主键自增", example = "1")
    private Long id;

    @ApiModelProperty(value = "文件名称", example = "example.txt")
    private String fileName;

    @ApiModelProperty(value = "文件路径", example = "/path/to/file/example.txt")
    private String filePath;

    @ApiModelProperty(value = "文件状态，0=导出中，1=成功，2=失败", allowableValues = "0,1,2", example = "2")
    private Integer fileStatus;

    @ApiModelProperty(value = "失败原因", example = "文件不存在")
    private String remark;

    @ApiModelProperty(value = "创建时间，格式为 yyyy-MM-dd HH:mm:ss", example = "2025-02-16 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
