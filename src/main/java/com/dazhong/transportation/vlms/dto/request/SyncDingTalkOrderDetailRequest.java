package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@ApiModel(description = "同步钉钉单据详情请求对象")
public class SyncDingTalkOrderDetailRequest {

    @ApiModelProperty(value = "钉钉单据号", required = true, example = "7rMkC-O-RPGy4qstTYMmRA04961736478588")
    @NotEmpty(message = "钉钉单据号不能为空")
    private String dingTalkNo;
}
