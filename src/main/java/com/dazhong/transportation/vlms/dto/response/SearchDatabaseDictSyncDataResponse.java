package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.DatabaseDictSyncDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "字典表同步数据查询结果")
public class SearchDatabaseDictSyncDataResponse implements Serializable {

    @ApiModelProperty(value = "同步字典表名")
    private String tableName;

    @ApiModelProperty(value = "字典表数据")
    private List<DatabaseDictSyncDto> list;
}
