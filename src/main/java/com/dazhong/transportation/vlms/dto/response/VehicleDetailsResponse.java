package com.dazhong.transportation.vlms.dto.response;

import com.dazhong.transportation.vlms.dto.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-07 14:38
 */
@Data
@ApiModel(description = "车辆详情信息")
public class VehicleDetailsResponse {

    @ApiModelProperty(value = "车辆基础信息")
    private VehicleBasicInfoDto basicInfo;

    @ApiModelProperty(value = "车辆运营信息")
    private VehicleOperationInfoDto operationInfo;

    @ApiModelProperty(value = "车辆型号信息")
    private VehicleModelInfoResponse vehicleModelInfo;

    @ApiModelProperty(value = "车辆证照信息")
    private VehicleLicenseInfoDto licenseInfo;

    @ApiModelProperty(value = "车辆采购收货信息")
    private VehiclePurchaseReceiptDto purchaseReceiptInfo;

    @ApiModelProperty(value = "车辆历史信息")
    private VehicleLegacyInfoResponse legacyInfo;

    @ApiModelProperty(value = "车辆处置信息")
    List<VehicleDisposalDetailListDto> disposalList;
}
