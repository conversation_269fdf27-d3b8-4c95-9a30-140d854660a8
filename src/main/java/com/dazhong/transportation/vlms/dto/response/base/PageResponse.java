package com.dazhong.transportation.vlms.dto.response.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * 分页结果对象
 * <AUTHOR>
 * @date 2024-12-20 17:30
 */
@ApiModel(description = "分页结果对象")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> implements Serializable {


    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private Long total;

    /**
     * 数据列表
     */
    @ApiModelProperty("数据列表")
    private List<T> list;
}