package com.dazhong.transportation.vlms.dto.request.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@ApiModel(description = "分页参数")
@Data
public class PageRequest  {
    @ApiModelProperty(value = "页码",example = "1",required = true)
    @Range(min = 1, message = "分页参数有误")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小",example = "10" ,required = true)
    @Range(min = 10, message = "分页参数有误")
    private Integer pageSize;

}
