package com.dazhong.transportation.vlms.dto.request;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotEmpty;

import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.dto.CommitDingFlowAttachmentDto;

import lombok.Data;

@Data
public class CreateDingTalkWorkFlowRequest {

    /**
     * 审批流流程CODE 钉钉审批流内 值唯一
     * 在钉钉后台创建后生成
     */
    @NotEmpty(message = "审批流程CODE不能为空")
    private String processCode;

    /**
     * 审批流内容
     * 表格数据参考：{"name":"明细数据","value":"[[{\"name\":\"班级\",\"value\":\"1班\"},{\"name\":\"学号\",\"value\":\"18\"}],[{\"name\":\"班级\",\"value\":\"2班\"},{\"name\":\"学号\",\"value\":\"16\"}]]"}]
     */
    @NotEmpty(message = "审批流内容不能为空")
    private Map<String,String> processComponentValues;

    /**
     * 自定义审批者（非必填）会覆盖所有审批节点
     */
    List<StartProcessInstanceRequest.StartProcessInstanceRequestApprovers> approves = new ArrayList<>();


    /**
     * 自定义审批节点审批人
     */
    List<String>  targetSelectUserIds;

    /**
     * 审批流提交者
     */
    @NotEmpty(message = "审批流提交人不能为空")
    private String originatorUserId;


    /**
     * 附件信息
     */
    private CommitDingFlowAttachmentDto attachmentInfo;


    /**申请人审批部门*/
    private Long originatorDeptId;
}
