package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "采购车载设备信息")
@Data
public class PurchaseEquipmentDto {

    @ApiModelProperty(value = "设备名称", example = "GPS导航系统")
    private String equipmentName;

    @ApiModelProperty(value = "制造商ID", example = "1")
    private Integer manufacturerId;

    @ApiModelProperty(value = "供应商ID", example = "1")
    private Integer supplierId;

    @ApiModelProperty(value = "单价", example = "500.00")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "数量", example = "2")
    private Integer quantity;

    @ApiModelProperty(value = "其他费用", example = "100.00")
    private BigDecimal otherCosts;

    @ApiModelProperty(value = "设备总价", example = "1100.00")
    private BigDecimal totalPrice;

}
