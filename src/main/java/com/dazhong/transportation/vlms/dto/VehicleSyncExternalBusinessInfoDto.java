package com.dazhong.transportation.vlms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleSyncExternalBusinessInfoDto implements Serializable {
    /**
     * 车架号
     */
    private String vin;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 业务编号，保证唯一性 有就存
     */
    private String  businessNo;

    /**
     * 业务类型 insurance-保险、illegal-违章、accident-事故
     */
    private String businessType;

    /**
     * 业务时间，业务发生时间 用于查询使用 有就存
     */
    private String  businessTime;

    /**
     * 同步的业务数据 JSON存储
     */
    private String  businessInfo;
}
