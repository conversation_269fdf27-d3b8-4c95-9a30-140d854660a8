package com.dazhong.transportation.vlms.dto.request;

import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "查询设备列表条件")
public class SearchVehicleDeviceListRequest extends PageRequest {

    @ApiModelProperty(value = "车牌号", example = "京A12345", notes = "车牌号 - 模糊查询")
    private String licensePlate;

    @ApiModelProperty(value = "车架号", example = "VIN12345678901234567", notes = "车架号 - 模糊查询")
    private String vin;

}
