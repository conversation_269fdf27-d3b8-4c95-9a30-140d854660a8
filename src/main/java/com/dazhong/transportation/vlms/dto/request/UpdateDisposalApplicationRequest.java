package com.dazhong.transportation.vlms.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "修改车辆处置申请单入参")
public class UpdateDisposalApplicationRequest {

    @ApiModelProperty(value = "处置申请单id", example = "1")
    private Long disposalApplicationId;

    @ApiModelProperty(value = "任务编号", example = "1")
    private String taskNumber;
}
