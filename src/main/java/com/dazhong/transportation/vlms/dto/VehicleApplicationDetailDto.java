package com.dazhong.transportation.vlms.dto;

import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
import com.dazhong.transportation.vlms.enums.BusinessLineEnum;
import com.dazhong.transportation.vlms.enums.OperatingStatusEnum;
import com.dazhong.transportation.vlms.enums.ProductLineEnum;
import com.dazhong.transportation.vlms.utils.ObjectValidUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(description = "车辆申请详情dto")
public class VehicleApplicationDetailDto {

    @ApiModelProperty(value = "主键id", example = "12345")
    private Long id;

    @ApiModelProperty(value = "申请单号", example = "APP00001")
    private String applicationNo;

    @ApiModelProperty(value = "资产编号", example = "ASSET00001")
    private String vehicleAssetId;

    @ApiModelProperty(value = "车架号", example = "VIN1234567890")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    private String licensePlate;

    @ApiModelProperty(value = "车型ID", example = "1")
    private Long vehicleModelId;

    @ApiModelProperty(value = "车型名", example = "1")
    private String vehicleModelName;

    @ApiModelProperty(value = "资产所属公司id", example = "10001")
    private Long assetCompanyId;

    @ApiModelProperty(value = "资产所属公司名", example = "公司A")
    private String assetCompanyName;

    @ApiModelProperty(value = "资产所属公司id修改为", example = "10002")
    private Long assetCompanyIdUpdate;

    @ApiModelProperty(value = "资产所属公司名修改为", example = "公司B")
    private String assetCompanyNameUpdate;

    @ApiModelProperty(value = "资产机构id", example = "1")
    private Long ownOrganizationId;

    @ApiModelProperty(value = "资产机构名", example = "XYZ")
    private String ownOrganizationName;

    @ApiModelProperty(value = "资产机构id修改为", example = "1")
    private Long ownOrganizationIdUpdate;

    @ApiModelProperty(value = "资产机构名修改为", example = "XYZ")
    private String ownOrganizationNameUpdate;

    @ApiModelProperty(value = "使用机构id", example = "1")
    private Long usageOrganizationId;

    @ApiModelProperty(value = "使用机构名", example = "XYZ")
    private String usageOrganizationName;

    @ApiModelProperty(value = "使用机构id", example = "1")
    private Long usageOrganizationIdUpdate;

    @ApiModelProperty(value = "使用机构名", example = "XYZ")
    private String usageOrganizationNameUpdate;

    @ApiModelProperty(value = "产品线 1-巡网业务 2-商务业务", example = "1")
    private Integer productLine;

    @ApiModelProperty(value = "产品线修改为", example = "1")
    private Integer productLineUpdate;

    @ApiModelProperty(value = "业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车", example = "1")
    private Integer businessLine;

    @ApiModelProperty(value = "业务线修改为", example = "2")
    private Integer businessLineUpdate;

    @ApiModelProperty(value = "运营状态 1-待运 2-租出", example = "1")
    private Integer operatingStatus;

    /**
     * 将当前对象转换为钉钉审批流中的表单数据
     *
     * @return 钉钉审批流中的表单数据结构
     */
    public List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> convertToDingTalkData() {
        List<StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails> result = new ArrayList<>();
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车架号").setValue(ObjectValidUtil.formatStr(this.vin)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车牌号").setValue(ObjectValidUtil.formatStr(this.licensePlate)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("资产所属公司").setValue(ObjectValidUtil.formatStr(this.assetCompanyName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（所属）").setValue(ObjectValidUtil.formatStr(this.ownOrganizationName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（所属）修改为").setValue(ObjectValidUtil.formatStr(this.ownOrganizationNameUpdate)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（使用）").setValue(ObjectValidUtil.formatStr(this.usageOrganizationName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("实际运营公司（使用）修改为").setValue(ObjectValidUtil.formatStr(this.usageOrganizationNameUpdate)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("车型").setValue(ObjectValidUtil.formatStr(this.vehicleModelName)));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("条线").setValue(ObjectValidUtil.formatStr(ProductLineEnum.getDesc(this.productLine))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("条线修改为").setValue(ObjectValidUtil.formatStr(ProductLineEnum.getDesc(this.productLineUpdate))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("业务类型").setValue(ObjectValidUtil.formatStr(BusinessLineEnum.getDesc(this.businessLine))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("业务类型修改为").setValue(ObjectValidUtil.formatStr(BusinessLineEnum.getDesc(this.businessLineUpdate))));
        result.add(new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValuesDetails().setName("业务状态").setValue(OperatingStatusEnum.getDesc(this.operatingStatus)));
        return result;
    }
}
    