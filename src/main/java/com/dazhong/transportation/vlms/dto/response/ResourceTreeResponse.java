package com.dazhong.transportation.vlms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 资源树结果对象
 * <AUTHOR>
 * @date 2024-12-20 17:30
 */
@ApiModel(description = "资源树结果对象")
@Data
public class ResourceTreeResponse implements Serializable {

    @ApiModelProperty("资源ID")
    private Long id;

    /**
     *   资源key(推荐uuid)
     */
    @ApiModelProperty("资源key")
    private String resourceKey;

    /**
     *   资源名称
     */
    @ApiModelProperty("资源名称")
    private String resourceName;

    /**
     *   资源url
     */
    @ApiModelProperty("资源url")
    private String resourceUrl;

    /**
     *   资源类型 0-系统 1-静态资源 2-功能点
     */
    @ApiModelProperty("资源类型 0-系统 1-静态资源 2-功能点")
    private Integer resourceType;

    /**
     *   是否列表隐藏 1-是 2-否
     */
    @ApiModelProperty(value = "是否列表隐藏 1-是 2-否" ,required = true)
    private Integer hide;

    /**
     *   是否列表隐藏 1-是 2-否
     */
    @ApiModelProperty(value = "标题是否隐藏 1-是 2-否" ,required = true)
    private Integer hideTitle;

    /**
     *   资源图标url
     */
    @ApiModelProperty("资源图标url")
    private String resourceIconUrl;

    /**
     *   父资源id
     */
    @ApiModelProperty("父资源id")
    private Long parentResourceId;

    @ApiModelProperty("子节点资源")
    private List<ResourceTreeResponse> children;


    /**
     *   资源code
     */
    @ApiModelProperty(value = "资源code" ,required = true)
    private String resourceCode;


    /**
     *   排序标记
     */
    @ApiModelProperty(value = "排序标记" ,required = true)
    private Integer sort;

}