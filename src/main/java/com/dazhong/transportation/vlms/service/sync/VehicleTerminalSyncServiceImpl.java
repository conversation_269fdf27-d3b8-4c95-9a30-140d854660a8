package com.dazhong.transportation.vlms.service.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.database.TableVehicleDeviceInfoService;
import com.dazhong.transportation.vlms.dto.VehicleTerminalInfoDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.VehicleDeviceInfo;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class VehicleTerminalSyncServiceImpl implements IHandleSyncDataService{

    @Autowired
    private TableVehicleDeviceInfoService tableVehicleDeviceInfoService;

    @Override
    public ResultResponse handleSyncData(SyncDataRequest request) {
        //请求内容
        String requestBody = request.getBizRequest();
        if(StringUtils.isBlank(requestBody)){
            return ResultResponse.businessFailed("同步终端信息参数异常，消息体内容缺失");
        }

        //解析请求内容
        List<VehicleTerminalInfoDto> vehicleTerminalInfoDtoList;
        try{
            vehicleTerminalInfoDtoList = JSONObject.parseArray(requestBody, VehicleTerminalInfoDto.class);
        }catch (Exception e){
            log.error("同步终端信参数异常，解析消息体内容失败:{}，内容：{}",e.getLocalizedMessage(), requestBody , e);
            return ResultResponse.businessFailed("同步终端信参数异常，解析消息体内容失败");
        }

        //遍历循环处理请求信息
        for (VehicleTerminalInfoDto vehicleTerminalInfoDto : vehicleTerminalInfoDtoList) {
            //校验参数内容
            if(!checkParamsPass(vehicleTerminalInfoDto)){
                log.error("同步终端信参数异常，略过同步，内容：{}", JSON.toJSONString(vehicleTerminalInfoDto));
                continue;
            }

            //判断设备信息是否存在
            VehicleDeviceInfo deviceInfo = tableVehicleDeviceInfoService.selectByDeviceSeq(vehicleTerminalInfoDto.getTerminalNo());
            if(deviceInfo == null){
                //保存终端信息
                createDeviceInfo(vehicleTerminalInfoDto);
            }
            else {
                //修改终端信息
                updateDeviceInfo(deviceInfo, vehicleTerminalInfoDto);
            }
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        return null;
    }


    /**
     * 根据同步信息保存终端设备信息
     * @param vehicleTerminalInfoDto 同步信息
     */
    private void createDeviceInfo(VehicleTerminalInfoDto vehicleTerminalInfoDto){
        //保存终端信息
        VehicleDeviceInfo saveDeviceInfo = new VehicleDeviceInfo();
        //终端号
        saveDeviceInfo.setDeviceSeq(vehicleTerminalInfoDto.getTerminalNo());
        //车架号
        saveDeviceInfo.setVin(vehicleTerminalInfoDto.getVin());
        //终端类型
        saveDeviceInfo.setDeviceType(vehicleTerminalInfoDto.getTerminalType());
        //安装时间
        saveDeviceInfo.setInstallTime(parseDate(vehicleTerminalInfoDto.getInstallTime()));
        //激活时间
        saveDeviceInfo.setActivationTime(parseDate(vehicleTerminalInfoDto.getActivationTime()));
        tableVehicleDeviceInfoService.insert(saveDeviceInfo, "system");
    }


    /**
     * 根据同步信息修改终端设备信息
     * @param deviceInfo 现有的终端设备信息
     * @param vehicleTerminalInfoDto 同步信息
     */
    private void updateDeviceInfo(VehicleDeviceInfo deviceInfo, VehicleTerminalInfoDto vehicleTerminalInfoDto){
        //修改终端信息
        VehicleDeviceInfo updateDeviceInfo = new VehicleDeviceInfo();
        updateDeviceInfo.setId(deviceInfo.getId());
        //终端号
        updateDeviceInfo.setDeviceSeq(vehicleTerminalInfoDto.getTerminalNo());
        //车架号
        updateDeviceInfo.setVin(vehicleTerminalInfoDto.getVin());
        //终端类型
        updateDeviceInfo.setDeviceType(vehicleTerminalInfoDto.getTerminalType());
        //安装时间
        updateDeviceInfo.setInstallTime(parseDate(vehicleTerminalInfoDto.getInstallTime()));
        //激活时间
        updateDeviceInfo.setActivationTime(parseDate(vehicleTerminalInfoDto.getActivationTime()));
        tableVehicleDeviceInfoService.updateSelectiveById(updateDeviceInfo, "system");
    }


    /**
     * 检查参数是否通过
     * @param vehicleTerminalInfoDto 请求参数信息
     * @return true:校验通过 false:校验不通过
     */
    public boolean checkParamsPass(VehicleTerminalInfoDto vehicleTerminalInfoDto){
        if(StringUtils.isBlank(vehicleTerminalInfoDto.getVin())){
            return false;
        }
        if(StringUtils.isBlank(vehicleTerminalInfoDto.getTerminalNo())){
            return false;
        }
        if(StringUtils.isBlank(vehicleTerminalInfoDto.getTerminalType())){
            return false;
        }
        return true;
    }

    /**
     * 将字符串类型转换成时间格式
     * @param value
     * @return
     */
    private Date parseDate(String value){
        if(StringUtils.isBlank(value)){
            return null;
        }
        Date result = null;
        try{
            result = DateTimeUtils.stringToDate(value, DateTimeUtils.DATE_TYPE1);
        }catch (Exception e){
            e.printStackTrace();
        }

        return result;
    }
}
