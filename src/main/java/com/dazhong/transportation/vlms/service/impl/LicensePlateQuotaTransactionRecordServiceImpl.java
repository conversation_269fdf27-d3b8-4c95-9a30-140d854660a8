package com.dazhong.transportation.vlms.service.impl;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableLicensePlateQuotaTransactionRecordService;
import com.dazhong.transportation.vlms.database.TableOperateLogService;
import com.dazhong.transportation.vlms.dto.ExportLicensePlateQuotaTransactionRecordDTO;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.ComboQuotaNumberRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaTransactionRecordRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLogRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateQuotaTransactionRecordRequest;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaTransactionOperateLogResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaTransactionRecordResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.ExportFileTypeEnum;
import com.dazhong.transportation.vlms.enums.OperateLogBusinessTypeEnum;
import com.dazhong.transportation.vlms.enums.QuotaTypeEnum;
import com.dazhong.transportation.vlms.model.LicensePlateQuotaTransactionRecord;
import com.dazhong.transportation.vlms.model.OperateLog;
import com.dazhong.transportation.vlms.service.IFileService;
import com.dazhong.transportation.vlms.service.ILicensePlateQuotaTransactionRecordService;
import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

@Service
public class LicensePlateQuotaTransactionRecordServiceImpl implements ILicensePlateQuotaTransactionRecordService {

    @Autowired
    private TableLicensePlateQuotaTransactionRecordService tableLicensePlateQuotaTransactionRecordService;

    @Autowired
    private TableOperateLogService operateLogService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IFileService fileService;

    @Override
    public PageResponse<LicensePlateQuotaTransactionRecordResponse> queryPageResponse(SearchLicensePlateQuotaTransactionRecordRequest request) {
        // 查询额度单流水记录列表并创建分页信息对象
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<LicensePlateQuotaTransactionRecord> transactionRecords = tableLicensePlateQuotaTransactionRecordService.queryList(request);
        PageInfo<LicensePlateQuotaTransactionRecord> pageInfo = new PageInfo<>(transactionRecords);

        // 将查询结果转换为响应对象列表
        List<LicensePlateQuotaTransactionRecordResponse> result = CollectionUtil.newArrayList();
        transactionRecords.forEach(transactionRecord -> {
            LicensePlateQuotaTransactionRecordResponse response = BeanUtil.copyProperties(transactionRecord, LicensePlateQuotaTransactionRecordResponse.class);
            result.add(response);
        });

        // 返回分页响应对象
        return new PageResponse<>(pageInfo.getTotal(), result);
    }

    @Override
    public ResultResponse<Void> updateQuotaTransactionRecord(UpdateQuotaTransactionRecordRequest updateQuotaTransactionRecordRequest, TokenUserInfo tokenUserInfo) {
        LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord = tableLicensePlateQuotaTransactionRecordService.selectById(updateQuotaTransactionRecordRequest.getId());
        if (null == licensePlateQuotaTransactionRecord) {
            return ResultResponse.businessFailed("额度流水记录不存在");
        }
        if (StringUtils.isNotBlank(licensePlateQuotaTransactionRecord.getTaskNumber())) {
            return ResultResponse.businessFailed("存在上牌任务编号，不允许编辑额度单！");
        }

        // 增加判断：如果额度编号和额度单打印时间都没有变化，则不进行重复检查
        boolean quotaNumberChanged = !StringUtils.equals(licensePlateQuotaTransactionRecord.getQuotaNumber(), updateQuotaTransactionRecordRequest.getQuotaNumber());
        boolean quotaPrintDateChanged = false;
        
        if ((licensePlateQuotaTransactionRecord.getQuotaPrintDate() == null && updateQuotaTransactionRecordRequest.getQuotaPrintDate() != null) ||
                (licensePlateQuotaTransactionRecord.getQuotaPrintDate() != null && updateQuotaTransactionRecordRequest.getQuotaPrintDate() != null &&
                !licensePlateQuotaTransactionRecord.getQuotaPrintDate().equals(updateQuotaTransactionRecordRequest.getQuotaPrintDate()))) {
            quotaPrintDateChanged = true;
        }
        
        // 只有当额度编号或打印日期变化时，才进行重复检查
        if (quotaNumberChanged || quotaPrintDateChanged) {
            LicensePlateQuotaTransactionRecord oldRecord = tableLicensePlateQuotaTransactionRecordService
                    .selectByQuotaNumberAndQuotaPrintDate(updateQuotaTransactionRecordRequest.getQuotaNumber(),
                            updateQuotaTransactionRecordRequest.getQuotaPrintDate());
            if (null != oldRecord) {
                return ResultResponse.businessFailed("存在相同额度单号和打印日期的额度流水记录，不允许编辑额度单！");
            }
        }

        // 更新额度流水记录
        LicensePlateQuotaTransactionRecord updateRecord = BeanUtil.copyProperties(updateQuotaTransactionRecordRequest, LicensePlateQuotaTransactionRecord.class);
        tableLicensePlateQuotaTransactionRecordService.updateSelectiveById(updateRecord, tokenUserInfo);

        // 构建变更日志内容
        StringBuilder logContent = new StringBuilder();
        
        // 比较额度单编号变更
        if (!StringUtils.equals(licensePlateQuotaTransactionRecord.getQuotaNumber(), updateRecord.getQuotaNumber())) {
            logContent.append("额度单编号：").append(licensePlateQuotaTransactionRecord.getQuotaNumber())
                    .append(" -> ").append(updateRecord.getQuotaNumber()).append("；");
        }
        
        // 比较额度单打印时间变更
        if ((licensePlateQuotaTransactionRecord.getQuotaPrintDate() == null && updateRecord.getQuotaPrintDate() != null) ||
                (licensePlateQuotaTransactionRecord.getQuotaPrintDate() != null && updateRecord.getQuotaPrintDate() != null &&
                !licensePlateQuotaTransactionRecord.getQuotaPrintDate().equals(updateRecord.getQuotaPrintDate()))) {
            logContent.append("额度单打印时间：").append(licensePlateQuotaTransactionRecord.getQuotaPrintDate())
                    .append(" -> ").append(updateRecord.getQuotaPrintDate()).append("；");
        }
        
        // 比较额度类型变更
        if ((licensePlateQuotaTransactionRecord.getQuotaType() == null && updateRecord.getQuotaType() != null) ||
                (licensePlateQuotaTransactionRecord.getQuotaType() != null && updateRecord.getQuotaType() != null &&
                !licensePlateQuotaTransactionRecord.getQuotaType().equals(updateRecord.getQuotaType()))) {
            String oldQuotaTypeDesc = QuotaTypeEnum.getDesc(licensePlateQuotaTransactionRecord.getQuotaType());
            String newQuotaTypeDesc = QuotaTypeEnum.getDesc(updateRecord.getQuotaType());
            logContent.append("额度类型：").append(oldQuotaTypeDesc)
                    .append(" -> ").append(newQuotaTypeDesc).append("；");
        }
        
        // 如果没有变更内容，则使用默认文本
        String operateContent = logContent.length() > 0 ? logContent.toString() : "变更额度单信息";
        
        // 记录操作日志
        operateLogService.insertLog(updateRecord.getId(), OperateLogBusinessTypeEnum.QUOTA_TRANSACTION.getCode(), 
                BizConstant.OperateType.OperateType_1, operateContent, tokenUserInfo);

        return ResultResponse.success();
    }

    @Override
    public PageResponse<LicensePlateQuotaTransactionOperateLogResponse> queryOperateLogPageResponse(SearchLogRequest request) {
        // 查询额度单流水记录列表并创建分页信息对象
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<OperateLog> operateLogList = operateLogService.searchOperateLog(request);
        PageInfo<OperateLog> pageInfo = new PageInfo<>(operateLogList);

        // 将查询结果转换为响应对象列表
        List<LicensePlateQuotaTransactionOperateLogResponse> transactionRecordList = CollectionUtil.newArrayList();
        operateLogList.forEach(operateLog -> {
            LicensePlateQuotaTransactionOperateLogResponse operateLogResponse = BeanUtil.copyProperties(operateLog, LicensePlateQuotaTransactionOperateLogResponse.class);
            LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord = tableLicensePlateQuotaTransactionRecordService.selectById(operateLog.getForeignId());
            operateLogResponse.setAssetCompanyId(licensePlateQuotaTransactionRecord.getAssetCompanyId());
            operateLogResponse.setAssetCompanyName(licensePlateQuotaTransactionRecord.getAssetCompanyName());
            transactionRecordList.add(operateLogResponse);
        });

        // 返回分页响应对象
        return new PageResponse<>(pageInfo.getTotal(), transactionRecordList);
    }

    @Override
    public ComboResponse<String, String> queryQuotaNumberCombo(ComboQuotaNumberRequest comboQuotaNumberRequest) {
        return tableLicensePlateQuotaTransactionRecordService.queryQuotaNumberCombo(comboQuotaNumberRequest.getQuotaNumber());
    }

    @Override
    public void exportTransactionRecord(SearchLicensePlateQuotaTransactionRecordRequest requset, TokenUserInfo tokenUserInfo) {
        //导出文件类型
        ExportFileTypeEnum fileTypeEnum = ExportFileTypeEnum.EXPORT_TRANSACTION_RECORD;
        //导出模板信息
        InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("template/导出额度流水记录.xlsx");

        //查询机构映射信息
        Map<Long, String> orgNameMap = orgService.getOrgNameMap();

        //开始导出文件
        fileService.export(fileTypeEnum, templateStream, tokenUserInfo,
                ((excelWriter, writeSheet) -> {
                    //是否查询到导出数据
                    boolean hasResult = false;
                    //标识位
                    Long index = -1L;

                    while (true) {
                        //分批查询填充导出数据
                        List<LicensePlateQuotaTransactionRecord> licensePlateQuotaTransactionRecords = tableLicensePlateQuotaTransactionRecordService.exportList(index, requset);
                        if (licensePlateQuotaTransactionRecords.isEmpty()) {
                            break;
                        }
                        //存在导出数据
                        hasResult = true;
                        List<ExportLicensePlateQuotaTransactionRecordDTO> exportDataList = CollectionUtil.newArrayList();
                        //遍历处理查询字段
                        for (LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord : licensePlateQuotaTransactionRecords) {
                            ExportLicensePlateQuotaTransactionRecordDTO exportData = transExportTransactionRecord(licensePlateQuotaTransactionRecord, orgNameMap);
                            exportDataList.add(exportData);
                        }
                        //更新标识位
                        index = exportDataList.get(exportDataList.size() - 1).getId();
                        excelWriter.fill(exportDataList, writeSheet);
                    }
                    return hasResult;
                }
                ));
    }

    /**
     * 处理导出字段
     *
     * @param exportVehicleData   原始数据
     * @param orgNameMap          机构名称映射表
     * @param dataMaintainDictMap 字典映射表
     */
    public ExportLicensePlateQuotaTransactionRecordDTO transExportTransactionRecord(LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord, Map<Long, String> orgNameMap) {
        ExportLicensePlateQuotaTransactionRecordDTO exportData = BeanUtil.copyProperties(licensePlateQuotaTransactionRecord, ExportLicensePlateQuotaTransactionRecordDTO.class);
        // 额度类型
        exportData.setQuotaTypeString(QuotaTypeEnum.getDesc(licensePlateQuotaTransactionRecord.getQuotaType()));
        // 退牌日期
        exportData.setLicensePlateWithdrawalDateString(DateTimeUtils.dateToString(licensePlateQuotaTransactionRecord.getLicensePlateWithdrawalDate(), DateTimeUtils.DATE_TYPE3));
        // 额度单打印日期
        exportData.setQuotaPrintDateString(DateTimeUtils.dateToString(licensePlateQuotaTransactionRecord.getQuotaPrintDate(), DateTimeUtils.DATE_TYPE3));
        return exportData;
    }
}
