package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveManagerRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateOrgInfoRequest;
import com.dazhong.transportation.vlms.dto.response.DataDictResponse;
import com.dazhong.transportation.vlms.dto.response.OrgListResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.UserOrgInfo;

import java.util.List;
import java.util.Map;

/**
 * 系统组织机构服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface IOrgService {

    /**
     * 查询组织机构
     * @return
     */
    ResultResponse queryAllOrgTree();

    /**
     * 查询组织机构
     * @return
     */
    ResultResponse queryEnableStatusOrgTree();



    /**
     * 查询用户组织机构树
     * @param userId
     * @return
     */
    List<OrgListResponse> queryUserOrgList(Long userId);

    /**
     * 查询用户组织机构树
     * @param userId
     * @return
     */
    ResultResponse queryUserOrgTree(Long userId);

    /**
     * 查询用户关联公司列表
     * @param userId 用户ID
     * @return 数据字典配置信息
     */
    DataDictResponse<Integer> queryUserOwnerList(Long userId);


    /**
     * 保存管理者信息
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse saveManagerInfo(SaveManagerRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询组织机构详情
     * @param id
     * @return
     */
    ResultResponse queryOrgDetails(Long id);

    /**
     * 修改组织架构
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse updateOrgInfo(UpdateOrgInfoRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 导入组织架构
     * @return
     */
    ResultResponse importOrgInfo();

    /**
     * 查询全部机构名称Map
     * @return key:id  value:orgName 机构名称
     */
    Map<Long, String> getOrgNameMap();

    /**
     * 查询用户关联公司信息
     * @param userId
     * @return
     */
    List<UserOrgInfo> queryUserOrgInfo(Long userId);

    /**
     * 同步组织架构信息
     */
    void syncOrganization();
}
