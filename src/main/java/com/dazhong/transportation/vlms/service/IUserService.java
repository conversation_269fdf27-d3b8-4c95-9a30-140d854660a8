package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.SyncSsoUserDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.SsoUserInfo;
import com.dazhong.transportation.vlms.model.UserInfo;

import java.util.List;

/**
 * 系统用户服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface IUserService {

    /**
     * 查询所有用户信息
     * @param request
     * @return
     */
    PageResponse queryUserList(SearchUserRequest request);

    /**
     * 根据账号查询用户信息
     * @param userAccount
     * @return
     */
    UserInfo queryUserByAccount(String userAccount);


    /**
     * 根据用户ID查询用户信息
     * @param userId
     * @return
     */
    ResultResponse queryUserInfo(long userId);

    /**
     * 新增用户信息
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse addUser(SaveUserRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 修改用户信息
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse updateUser(UpdateUserRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询sso用户列表
     * @return
     */
    List<SsoUserInfo> querySsoUserList();

    /**
     * 同步sso用户
     * @param dto
     * @return
     */
    ResultResponse syncSsoUser(SyncSsoUserDto dto);

    /**
     * 绑定钉钉用户
     * @param request
     * @return
     */
    ResultResponse bindDingTalkUser(BindDingTalkRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 删除用户
     * @param request
     * @return
     */
    ResultResponse deleteUser(BaseIdRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 查询钉钉用户
     * @param request
     * @return
     */
    ResultResponse queryUserDingTalk(QueryDingTalkRequest request, TokenUserInfo tokenUserInfo);

}
