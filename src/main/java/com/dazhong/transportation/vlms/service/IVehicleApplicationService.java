package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleApplicationDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleApplicationListDto;
import com.dazhong.transportation.vlms.dto.request.ImportVehicleApplicationFileUrlRequest;
import com.dazhong.transportation.vlms.dto.request.SaveVehicleApplicationRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleApplicationListRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleListResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;

import java.util.List;

public interface IVehicleApplicationService {

    /**
     * 查询车辆申请单列表
     *
     * @param searchVehicleApplicationListRequest 查询车辆处置列表入参
     * @param tokenUserInfo                       用户登录信息
     * @return 返回车辆处置列表
     */
    PageResponse<VehicleApplicationListDto> queryVehicleApplicationPageResponse(SearchVehicleApplicationListRequest searchVehicleApplicationListRequest, TokenUserInfo tokenUserInfo);

    /**
     * 保存车辆申请单
     *
     * @param saveVehicleApplicationRequest 保存出售申请单入参
     * @param tokenUserInfo                 用户登录信息
     * @return 空
     */
    ResultResponse<Long> saveApplication(SaveVehicleApplicationRequest saveVehicleApplicationRequest, TokenUserInfo tokenUserInfo);

    /**
     * 提交车辆申请单
     *
     * @param saveVehicleApplicationRequest 创建退牌任务请求
     * @param tokenUserInfo                 用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> submitApplication(SaveVehicleApplicationRequest saveVehicleApplicationRequest, TokenUserInfo tokenUserInfo);

    /**
     * 作废车辆申请单
     *
     * @param id            创建退牌任务请求
     * @param tokenUserInfo 用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> cancelApplication(Long id, TokenUserInfo tokenUserInfo);

    /**
     * 撤回处置申请单
     *
     * @param id            创建退牌任务请求
     * @param tokenUserInfo 用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> withdrawApplication(Long id, TokenUserInfo tokenUserInfo);

    /**
     * 查询详情
     *
     * @param id 主键id
     * @return 返回处置申请单详情
     */
    VehicleApplicationDetailResponse queryApplicationDetail(Long id);

    /**
     * 钉钉回调处理
     *
     * @param dingTalkNo     钉钉审批单号
     * @param dingTalkResult 钉钉审批结果
     */
    void dingTalkResultProcess(String dingTalkNo, String dingTalkResult);

    /**
     * 获取批量导入上牌任务车辆信息
     *
     * @param request 导入文件信息入参
     * @return 返回符合条件的车牌号列表
     */
    List<VehicleListResponse> getImportVehicleApplicationDetail(BaseImportFileUrlRequest request);

    /**
     * 获取批量导入处置任务车辆明细
     *
     * @param request 导入文件信息入参
     * @return 返回符合条件的车牌号列表
     */
    List<VehicleApplicationDetailDto> getImportVehicleDetailList(ImportVehicleApplicationFileUrlRequest request);
}
