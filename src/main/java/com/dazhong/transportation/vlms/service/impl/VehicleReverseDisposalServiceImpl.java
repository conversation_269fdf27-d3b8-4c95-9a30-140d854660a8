package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.*;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkDetailResponse;
import com.dazhong.transportation.vlms.dto.response.ReverseDisposalApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleBasicResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleReverseDisposalResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.DocumentStatusEnum;
import com.dazhong.transportation.vlms.enums.FileBusinessTypeEnum;
import com.dazhong.transportation.vlms.enums.OperateLogBusinessTypeEnum;
import com.dazhong.transportation.vlms.enums.PropertyStatusEnum;
import com.dazhong.transportation.vlms.excel.ImportReverseDisposalVehicleDetail;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.IVehicleReverseDisposalService;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VehicleReverseDisposalServiceImpl implements IVehicleReverseDisposalService {

    @Autowired
    private TableVehicleReverseDisposalService tableVehicleReverseDisposalService;

    @Autowired
    private TableVehicleDisposalService tableVehicleDisposalService;

    @Autowired
    private TableVehicleReverseDisposalDetailService tableVehicleReverseDisposalDetailService;

    @Autowired
    private TableApplyFileService tableApplyFileService;

    @Autowired
    private TableVehicleModelInfoService tableVehicleModelInfoService;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private IDingTalkService dingTalkService;

    @Autowired
    private IVehicleService vehicleService;

    @Override
    public VehicleReverseDisposalResponse queryAndCheckVehicleBasicInfo(QueryVehicleRequest request) {
        VehicleBasicResponse vehicleBasicResponse = null;
        if (StringUtils.isNotBlank(request.getVin())) {
            vehicleBasicResponse = vehicleService.getVehicleBasicInfo(request.getVin()).getData();
        }
        if (StringUtils.isNotBlank(request.getLicensePlate())) {
            vehicleBasicResponse = vehicleService.getVehicleBasicInfoByLicensePlate(request.getLicensePlate()).getData();
        }
        if (vehicleBasicResponse != null) {
            VehicleReverseDisposalResponse response = BeanUtil.copyProperties(vehicleBasicResponse, VehicleReverseDisposalResponse.class);
            if (!vehicleBasicResponse.getPropertyStatus().equals(PropertyStatusEnum.DISPOSAL_APPROVAL.getCode()) &&
                    !vehicleBasicResponse.getPropertyStatus().equals(PropertyStatusEnum.DISPOSED.getCode()) &&
                    !vehicleBasicResponse.getPropertyStatus().equals(PropertyStatusEnum.SCRAPPED.getCode())) {
                throw new ServiceException("车架号【" + request.getVin() + "】车辆状态不是处置审批中或已处置或已报废，请检查！");
            }
            if (vehicleBasicResponse.getPropertyStatus().equals(PropertyStatusEnum.DISPOSAL_APPROVAL.getCode())) {
                List<VehicleDisposalDetailListDto> vehicleDisposalDetailListDtoList = tableVehicleDisposalService.queryVehicleDisposalList(vehicleBasicResponse.getVin());
                vehicleDisposalDetailListDtoList.stream()
                        .filter(record -> null != record.getSubmitDate()).max(Comparator.comparing(VehicleDisposalDetailListDto::getSubmitDate)).ifPresent(latestRecord -> response.setDisposalDocumentNo(latestRecord.getDocumentNo()));
            }
            return response;
        }
        return null;
    }

    @Override
    public PageResponse<VehicleReverseDisposalListDto> queryPageResponse(SearchVehicleDisposalListRequest request, TokenUserInfo tokenUserInfo) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleReverseDisposalListDto> vehicleDisposalListDtoList = tableVehicleReverseDisposalService.queryVehicleReverseDisposalList(request, tokenUserInfo);
        PageInfo<VehicleReverseDisposalListDto> pageInfo = new PageInfo<>(vehicleDisposalListDtoList);

        return new PageResponse<>(pageInfo.getTotal(), vehicleDisposalListDtoList);
    }

    @Override
    public ResultResponse<Long> saveApplication(SaveReverseDisposalApplicationRequest request, TokenUserInfo tokenUserInfo) {
        List<String> vinList = request.getVehicleReverseDisposalDetailList().stream().map(VehicleReverseDisposalDetailDto::getVin).collect(Collectors.toList());
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        for (VehicleInfo vehicleInfo : vehicleInfoList) {
            if (!vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.DISPOSAL_APPROVAL.getCode()) &&
                    !vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.DISPOSED.getCode()) &&
                    !vehicleInfo.getPropertyStatus().equals(PropertyStatusEnum.SCRAPPED.getCode())) {
                throw new ServiceException("车架号【" + vehicleInfo.getVin() + "】车辆状态不是处置审批中或已处置或已报废，请检查！");
            }
        }

        // 1、保存车辆处置申请单
        VehicleReverseDisposal vehicleReverseDisposal = BeanUtil.copyProperties(request, VehicleReverseDisposal.class);
        if (null == request.getId()) {
            String documentNo = "NCZ" + new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.CHINESE).format(new Date());
            vehicleReverseDisposal.setDocumentNo(documentNo);
            tableVehicleReverseDisposalService.insert(vehicleReverseDisposal, tokenUserInfo);
        } else {
            vehicleReverseDisposal.setCreateOperId(tokenUserInfo.getUserId());
            vehicleReverseDisposal.setCreateOperName(tokenUserInfo.getName());
            tableVehicleReverseDisposalService.updateSelectiveById(vehicleReverseDisposal, tokenUserInfo);
        }

        // 2、保存车辆处置申请单
        tableVehicleReverseDisposalDetailService.deleteByReverseDisposalId(vehicleReverseDisposal.getId());
        List<VehicleReverseDisposalDetail> vehicleDisposalDetailList = BeanUtil.copyToList(request.getVehicleReverseDisposalDetailList(), VehicleReverseDisposalDetail.class);
        for (VehicleReverseDisposalDetail vehicleReverseDisposalDetail : vehicleDisposalDetailList) {
            vehicleReverseDisposalDetail.setReverseDisposalId(vehicleReverseDisposal.getId());
        }
        tableVehicleReverseDisposalDetailService.batchInsert(vehicleDisposalDetailList, tokenUserInfo);


        // 3、添加附件
        if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
            tableApplyFileService.deleteApplyFile(vehicleReverseDisposal.getId(), FileBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode());
            if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
                for (VehicleApplyFileDto vehicleApplyFileDto : request.getVehicleApplyFileList()) {
                    VehicleApplyFile vehicleApplyFile = new VehicleApplyFile();
                    BeanUtil.copyProperties(vehicleApplyFileDto, vehicleApplyFile);
                    if (vehicleApplyFile.getFileUrl().contains(Global.instance.mfsUrl)) {
                        vehicleApplyFile.setFileUrl(vehicleApplyFile.getFileUrl().replace(Global.instance.mfsUrl + "/", ""));
                    }
                    vehicleApplyFile.setForeignId(vehicleReverseDisposal.getId());
                    vehicleApplyFile.setBusinessType(FileBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode());
                    tableApplyFileService.insert(vehicleApplyFile, tokenUserInfo);
                }
            }
        }

        return ResultResponse.success(vehicleReverseDisposal.getId());
    }

    @Override
    public ResultResponse<Long> submitApplication(SaveReverseDisposalApplicationRequest request, TokenUserInfo tokenUserInfo) {
        // 保存处置申请单
        ResultResponse<Long> longResultResponse = saveApplication(request, tokenUserInfo);
        // 提交钉钉审批流
        CreateDingTalkWorkFlowRequest createDingTalkWorkFlowRequest = new CreateDingTalkWorkFlowRequest();
        createDingTalkWorkFlowRequest.setProcessCode(dingTalkConfig.getVehicleReverseDisposalProcessCode());
        createDingTalkWorkFlowRequest.setProcessComponentValues(request.convertToDingTalkData());
        createDingTalkWorkFlowRequest.setOriginatorUserId(tokenUserInfo.getDingTalkNum());
        createDingTalkWorkFlowRequest.setOriginatorDeptId(request.getOriginatorDeptId());
        if (CollectionUtil.isNotEmpty(request.getVehicleApplyFileList())) {
            CommitDingFlowAttachmentDto attachmentDto = new CommitDingFlowAttachmentDto();
            attachmentDto.setName("附件");
            attachmentDto.setAttachmentFileList(new ArrayList<>());
            String mfsUrl = Global.instance.mfsUrl;
            String mfsRootPath = Global.instance.mfsRootPath;
            for (VehicleApplyFileDto fileDto : request.getVehicleApplyFileList()) {
                if (fileDto.getFileUrl().contains(mfsUrl)) {
                    fileDto.setFileUrl(fileDto.getFileUrl().replace(Global.instance.mfsUrl + "/", ""));
                }
                if (!fileDto.getFileUrl().contains(mfsRootPath)) {
                    fileDto.setFileUrl(mfsRootPath + "/" + fileDto.getFileUrl());
                }
                DingFlowAttachmentFileInfoDto dingFlowAttachmentFileInfoDto = new DingFlowAttachmentFileInfoDto();
                dingFlowAttachmentFileInfoDto.setFileName(fileDto.getFileName());
                dingFlowAttachmentFileInfoDto.setFile(new File(fileDto.getFileUrl()));
                attachmentDto.getAttachmentFileList().add(dingFlowAttachmentFileInfoDto);
            }
            createDingTalkWorkFlowRequest.setAttachmentInfo(attachmentDto);
        }
        try {
            String dingTalkNo = dingTalkService.createDingTalkWorkFlow(createDingTalkWorkFlowRequest);
            // 更新钉钉审批单号
            VehicleReverseDisposal vehicleReverseDisposal = new VehicleReverseDisposal();
            vehicleReverseDisposal.setId(longResultResponse.getData());
            vehicleReverseDisposal.setDingTalkNo(dingTalkNo);
            vehicleReverseDisposal.setSubmitDate(new Date());
            vehicleReverseDisposal.setDocumentStatus(DocumentStatusEnum.UNDER_REVIEW.getCode());
            tableVehicleReverseDisposalService.updateSelectiveById(vehicleReverseDisposal, tokenUserInfo);

            // 操作日志
            tableOperateLogService.insertLog(longResultResponse.getData(), OperateLogBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode(), null, "提交申请", tokenUserInfo);
        } catch (Exception e) {
            log.error("提交钉钉审批单异常", e);
            throw new ServiceException("提交至钉钉审批单异常！");
        }

        return longResultResponse;
    }

    @Override
    public ResultResponse<Void> cancelApplication(Long id, TokenUserInfo tokenUserInfo) {
        VehicleReverseDisposal vehicleReverseDisposal = tableVehicleReverseDisposalService.selectById(id);
        if (null == vehicleReverseDisposal) {
            throw new ServiceException("逆处置申请单不存在！");
        }
        if (!vehicleReverseDisposal.getDocumentStatus().equals(DocumentStatusEnum.NOT_SUBMITTED.getCode()) &&
                !vehicleReverseDisposal.getDocumentStatus().equals(DocumentStatusEnum.REVIEW_REJECTED.getCode())) {
            throw new ServiceException("逆处置申请单现状态无法作废！");
        }

        // 更新状态为已作废
        VehicleReverseDisposal updateVehicleReverseDisposal = new VehicleReverseDisposal();
        updateVehicleReverseDisposal.setId(id);
        updateVehicleReverseDisposal.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleReverseDisposalService.updateSelectiveById(updateVehicleReverseDisposal, tokenUserInfo);

        // 操作日志
        tableOperateLogService.insertLog(vehicleReverseDisposal.getId(), OperateLogBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode(), null, "审批作废", tokenUserInfo);

        return ResultResponse.success();
    }

    @Override
    public ResultResponse<Void> withdrawApplication(Long id, TokenUserInfo tokenUserInfo) {
        VehicleReverseDisposal vehicleReverseDisposal = tableVehicleReverseDisposalService.selectById(id);
        if (null == vehicleReverseDisposal) {
            throw new ServiceException("处置申请单不存在！");
        }
        if (!vehicleReverseDisposal.getDocumentStatus().equals(DocumentStatusEnum.UNDER_REVIEW.getCode())) {
            throw new ServiceException("处置申请单现状态无法撤回！");
        }

        // 更新状态为已作废
        VehicleReverseDisposal updateVehicleReverseDisposal = new VehicleReverseDisposal();
        updateVehicleReverseDisposal.setId(id);
        updateVehicleReverseDisposal.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleReverseDisposalService.updateSelectiveById(updateVehicleReverseDisposal, tokenUserInfo);

        // 操作日志
        tableOperateLogService.insertLog(vehicleReverseDisposal.getId(), OperateLogBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode(), null, "审批撤回", tokenUserInfo);

        List<VehicleReverseDisposalDetail> vehicleReverseDisposalDetailList = tableVehicleReverseDisposalDetailService.getVehicleReverseDisposalDetailListByReverseDisposalId(vehicleReverseDisposal.getId());
        for (VehicleReverseDisposalDetail vehicleReverseDisposalDetail : vehicleReverseDisposalDetailList) {
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vehicleReverseDisposalDetail.getVin());
            if (null != vehicleInfo) {
                VehicleInfo updateVehicleInfo = new VehicleInfo();
                updateVehicleInfo.setId(vehicleInfo.getId());
                updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
                tableVehicleService.updateVehicle(updateVehicleInfo, tokenUserInfo);

                // 操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode(), null, "撤回处置申请单，资产状态改为【固定资产】", tokenUserInfo);
            }
        }

        // 调用钉钉撤回服务
        TerminateDingTalkProcessInstanceRequest processInstanceRequest = new TerminateDingTalkProcessInstanceRequest();
        processInstanceRequest.setSystem(true);
        processInstanceRequest.setProcessInstanceId(vehicleReverseDisposal.getDingTalkNo());
        processInstanceRequest.setRemark("处置申请撤回");
        dingTalkService.terminateProcessInstance(processInstanceRequest);

        return ResultResponse.success();
    }

    @Override
    public ReverseDisposalApplicationDetailResponse queryReverseDisposalDetail(Long id) {
        VehicleReverseDisposal vehicleReverseDisposal = tableVehicleReverseDisposalService.selectById(id);
        ReverseDisposalApplicationDetailResponse result = BeanUtil.copyProperties(vehicleReverseDisposal, ReverseDisposalApplicationDetailResponse.class);

        // 明细数据
        List<VehicleReverseDisposalDetail> vehicleReverseDisposalDetailList = tableVehicleReverseDisposalDetailService.getVehicleReverseDisposalDetailListByReverseDisposalId(vehicleReverseDisposal.getId());
        result.setVehicleReverseDisposalDetailList(BeanUtil.copyToList(vehicleReverseDisposalDetailList, VehicleReverseDisposalDetailDto.class));
        result.setVehicleNumber(result.getVehicleReverseDisposalDetailList().size());
        for (VehicleReverseDisposalDetailDto vehicleReverseDisposalDetailDto : result.getVehicleReverseDisposalDetailList()) {
            VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(vehicleReverseDisposalDetailDto.getVehicleModelId());
            if (null != vehicleModelInfo) {
                vehicleReverseDisposalDetailDto.setVehicleModelName(vehicleModelInfo.getVehicleModelName());
            }
        }

        // 附件
        List<VehicleApplyFileDto> applyFileDtoList = new ArrayList<>();
        List<VehicleApplyFile> vehicleApplyFileList = tableApplyFileService.queryFileList(id, FileBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode());
        vehicleApplyFileList.forEach(record -> {
            VehicleApplyFileDto fileDto = BeanUtil.copyProperties(record, VehicleApplyFileDto.class);
            String fileUrl = fileDto.getFileUrl().replace(Global.instance.mfsRootPath + "/", "");
            fileDto.setFileUrl(StrUtil.format("{}/{}", Global.instance.mfsUrl, fileUrl));
            applyFileDtoList.add(fileDto);
        });
        result.setVehicleApplyFileList(applyFileDtoList);

        return result;
    }

    @Override
    public void dingTalkResultProcess(String dingTalkNo, String dingTalkResult) {
        if (dingTalkResult.equals("agree")) {
            dingTalkResultAgree(dingTalkNo);
        } else if (dingTalkResult.equals("refuse")) {
            dingTalkResultRefuse(dingTalkNo);
        } else if (dingTalkResult.equals("terminate")) {
            dingTalkResultTerminate(dingTalkNo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultAgree(String dingTalkNo) {
        VehicleReverseDisposal vehicleReverseDisposal = tableVehicleReverseDisposalService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleReverseDisposal) {
            throw new ServiceException("车辆逆处置申请单不存在！");
        }
        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");

        // 变更申请单状态-审批通过
        VehicleReverseDisposal updateVehicleReverseDisposal = new VehicleReverseDisposal();
        updateVehicleReverseDisposal.setId(vehicleReverseDisposal.getId());
        updateVehicleReverseDisposal.setDocumentStatus(DocumentStatusEnum.REVIEW_APPROVED.getCode());
        tableVehicleReverseDisposalService.updateSelectiveById(updateVehicleReverseDisposal);

        // 操作日志
        tableOperateLogService.insertLog(vehicleReverseDisposal.getId(), OperateLogBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode(), null, "审批通过", dingTalkUserInfo);

        GetDingTalkWorkFlowRequest queryFormInstanceRequest = new GetDingTalkWorkFlowRequest();
        queryFormInstanceRequest.setInstanceId(vehicleReverseDisposal.getDingTalkNo());
        GetDingTalkDetailResponse getDingTalkDetailResponse = dingTalkService.getDingTalkDetailFlow(queryFormInstanceRequest);
        List<RowDto> rowDtoList = getDingTalkDetailResponse.getDetailData();
        for (RowDto rowDto : rowDtoList) {
            VehicleDisposalDetailDto vehicleDisposalDetailDto = rowDto.convertToVehicleDisposalDetailDto();
            VehicleDisposalDetail updateVehicleDisposalDetail = BeanUtil.copyProperties(vehicleDisposalDetailDto, VehicleDisposalDetail.class);
            updateVehicleDisposalDetail.setDisposalId(vehicleReverseDisposal.getId());

            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(vehicleDisposalDetailDto.getVin());
            VehicleInfo updateVehicleInfo = new VehicleInfo();
            updateVehicleInfo.setVin(vehicleDisposalDetailDto.getVin());

            // 逆处置-变为固定资产
            updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
            updateVehicleInfo.setRealRetirementDate(new Date());
            // 操作日志
            tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "逆处置申请单通过，资产状态改为【固定资产】", dingTalkUserInfo);

            tableVehicleService.updateVehicleByVinSelective(updateVehicleInfo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultRefuse(String dingTalkNo) {
        VehicleReverseDisposal vehicleReverseDisposal = tableVehicleReverseDisposalService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleReverseDisposal) {
            throw new ServiceException("车辆主数据申请单不存在！");
        }
        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");
        // 变更申请单状态-审批拒绝
        VehicleReverseDisposal updateVehicleDisposal = new VehicleReverseDisposal();
        updateVehicleDisposal.setId(vehicleReverseDisposal.getId());
        updateVehicleDisposal.setDocumentStatus(DocumentStatusEnum.REVIEW_REJECTED.getCode());
        tableVehicleReverseDisposalService.updateSelectiveById(updateVehicleDisposal);

        // 操作日志
        tableOperateLogService.insertLog(vehicleReverseDisposal.getId(), OperateLogBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode(), null, "审批拒绝", dingTalkUserInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void dingTalkResultTerminate(String dingTalkNo) {
        VehicleReverseDisposal vehicleReverseDisposal = tableVehicleReverseDisposalService.selectByDingTalkNo(dingTalkNo);
        if (null == vehicleReverseDisposal) {
            throw new ServiceException("车辆逆处置申请单不存在！");
        }
        
        TokenUserInfo dingTalkUserInfo = new TokenUserInfo();
        dingTalkUserInfo.setUserId(-1L);
        dingTalkUserInfo.setName("钉钉审批回调");
        
        // 变更申请单状态-已作废
        VehicleReverseDisposal updateVehicleReverseDisposal = new VehicleReverseDisposal();
        updateVehicleReverseDisposal.setId(vehicleReverseDisposal.getId());
        updateVehicleReverseDisposal.setDocumentStatus(DocumentStatusEnum.CANCELLED.getCode());
        tableVehicleReverseDisposalService.updateSelectiveById(updateVehicleReverseDisposal, dingTalkUserInfo);
        
        // 操作日志
        tableOperateLogService.insertLog(vehicleReverseDisposal.getId(), OperateLogBusinessTypeEnum.REVERSE_DISPOSAL_APPLICATION.getCode(), null, "钉钉审批流终止，申请单状态改为【已作废】", dingTalkUserInfo);
        
        // 获取逆处置明细
        List<VehicleReverseDisposalDetail> detailList = tableVehicleReverseDisposalDetailService.getVehicleReverseDisposalDetailListByReverseDisposalId(vehicleReverseDisposal.getId());
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        
        // 将车辆状态恢复为已处置
        for (VehicleReverseDisposalDetail detailDto : detailList) {
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(detailDto.getVin());
            if (vehicleInfo != null) {
                VehicleInfo updateVehicleInfo = new VehicleInfo();
                updateVehicleInfo.setId(vehicleInfo.getId());
                updateVehicleInfo.setPropertyStatus(PropertyStatusEnum.FIXED_ASSET.getCode());
                tableVehicleService.updateVehicle(updateVehicleInfo, dingTalkUserInfo);
                
                // 操作日志
                tableOperateLogService.insertLog(vehicleInfo.getId(), OperateLogBusinessTypeEnum.VEHICLE_MASTER_DATA.getCode(), null, "钉钉审批流终止，资产状态恢复为【固定资产】", dingTalkUserInfo);
            }
        }
    }

    @Override
    public List<ImportReverseDisposalVehicleDetail> getReverseDisposalVehicleDetailList(BaseImportFileUrlRequest request) {
        List<ImportReverseDisposalVehicleDetail> readList = ExcelUtil.read(request.getFilePath(), 0, ImportReverseDisposalVehicleDetail.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("上传文件不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }
        Iterator<ImportReverseDisposalVehicleDetail> iterator = readList.iterator();
        while (iterator.hasNext()) {
            ImportReverseDisposalVehicleDetail importReverseDisposalVehicleDetail = iterator.next();
            ValidationUtils.validate(importReverseDisposalVehicleDetail);
            if (StringUtils.isBlank(importReverseDisposalVehicleDetail.getVin()) && StringUtils.isBlank(importReverseDisposalVehicleDetail.getLicensePlate())) {
                continue;
            }

            QueryVehicleRequest queryVehicleRequest = new QueryVehicleRequest(importReverseDisposalVehicleDetail.getVin(), importReverseDisposalVehicleDetail.getLicensePlate());
            VehicleReverseDisposalResponse vehicleReverseDisposalResponse = this.queryAndCheckVehicleBasicInfo(queryVehicleRequest);

            if (vehicleReverseDisposalResponse == null) {
                throw new ServiceException("导入失败，请检查导入数据");
            }

            BeanUtil.copyProperties(queryVehicleRequest, importReverseDisposalVehicleDetail);
        }
        return readList;
    }
}
