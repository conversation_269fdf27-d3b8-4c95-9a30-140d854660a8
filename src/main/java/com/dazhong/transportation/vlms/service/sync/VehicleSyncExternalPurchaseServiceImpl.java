package com.dazhong.transportation.vlms.service.sync;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.database.TablePurchaseIntentionService;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalPurchaseDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.VehiclePurchaseIntention;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class VehicleSyncExternalPurchaseServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TablePurchaseIntentionService tablePurchaseIntentionService;

    @Autowired
    private RedisUtils redisUtils;


    @Override
    public ResultResponse handleSyncData(SyncDataRequest request) {
        String bizRequest = request.getBizRequest();
        log.info("同步采购意向信息-{}", JSONUtil.toJsonStr(bizRequest));
        try {
            JSONObject jsonObject = JSONUtil.parseObj(bizRequest);
            JSONArray jsonArray = jsonObject.getJSONArray("vehiclePurchaseIntention");
            if (ObjectUtil.isEmpty(jsonArray)) {
                return ResultResponse.businessFailed("同步采购意向数据异常");
            }
            List<VehicleSyncExternalPurchaseDto> list = jsonArray.toList(VehicleSyncExternalPurchaseDto.class);
            for (VehicleSyncExternalPurchaseDto dto : list) {
                VehiclePurchaseIntention purchaseIntention = tablePurchaseIntentionService.queryByContractNo(dto.getContractNo());
                if (purchaseIntention != null) {
                    VehiclePurchaseIntention intention = BeanUtil.copyProperties(dto, VehiclePurchaseIntention.class);
                    intention.setId(purchaseIntention.getId());
                    tablePurchaseIntentionService.updatePurchaseIntention(intention);
                } else {
                    String intentionNo = redisUtils.generateUniqueId("YX");
                    VehiclePurchaseIntention intention = BeanUtil.copyProperties(dto, VehiclePurchaseIntention.class);
                    intention.setIntentionNo(intentionNo);
                    tablePurchaseIntentionService.insertPurchaseIntention(intention);
                }

            }
            return ResultResponse.success();
        } catch (Exception e) {
            log.error("批量同步采购意向信息数据异常-{}", e.getMessage());
        }
        return ResultResponse.businessFailed("同步采购意向数据异常");
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        return null;
    }
}
