package com.dazhong.transportation.vlms.service.sync;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.database.TableJdyDataInfoService;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.JdyDataInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class SyncExternalJianDaoYunDataServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableJdyDataInfoService tableJdyDataInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse handleSyncData(SyncDataRequest dataRequest) {
        /*表单查询参数*/
        Map<String, Object> params = new HashMap<>();
        params.put("app_id", Global.instance.jdyAppId);
        params.put("entry_id", Global.instance.jdyEntryId);
        OkHttpClient client = new OkHttpClient().newBuilder().build();
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");
        okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, JSONUtil.toJsonStr(params));

        /*获取表单字段信息*/
        Request request = new Request.Builder()
                .url(Global.instance.jdyEntryWidgetUrl)
                .method("POST", body)
                .addHeader("Authorization", StrUtil.format("Bearer {}", Global.instance.jdyAppKey))
                .addHeader("Content-Type", "application/json")
                .build();
        List<String> fieldNameList = getFieldNameList(client, request);
        if (fieldNameList.isEmpty()) {
            log.error("未查询到表单字段信息");
            return ResultResponse.businessFailed("查询字段信息失败");
        }
        // 获取最后一条数据ID
        JdyDataInfo lastOne = tableJdyDataInfoService.queryLastOne();
        if (lastOne != null) {
            params.put("data_id", lastOne.getDataId());
        }
        params.put("limit", "100");
        // 查询已完成的状态
        Integer[] flowStateArray = new Integer[]{1};
        // 创建条件数组
        JSONArray conditions = JSONUtil.createArray();
        // 添加flowState条件
        JSON flowStateQuery = JSONUtil.createObj()
                .set("field", "flowState")
                .set("type", "flowstate")
                .set("method", "eq")
                .set("value", flowStateArray);
        conditions.add(flowStateQuery);
        // 构建完整查询参数
        JSON queryParams = JSONUtil.createObj()
                .set("rel", "and")
                .set("cond", conditions);
        params.put("filter", queryParams);
        getData(client, params, mediaType, fieldNameList);
        return ResultResponse.success();
    }

    /**
     * 获取表单字段信息
     * @param client
     * @param request
     * @return
     */
    private List<String> getFieldNameList(OkHttpClient client, Request request) {
        List<String> fieldNameList = new ArrayList<>();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSONUtil.parseObj(responseBody);
                JSONArray dataArray = jsonObject.getJSONArray("widgets");
                for (Object o : dataArray) {
                    JSONObject dataObject = (JSONObject) o;
                    String fieldName = dataObject.getStr("name");
                    fieldNameList.add(fieldName);
                }
            }
        } catch (IOException e) {
            log.error("查询简道云字段信息请求失败: {}", e.getMessage());
        }
        return fieldNameList;
    }

    /**
     * 获取数据
     * @param client
     * @param params
     * @param mediaType
     * @param fieldNameList
     */
    private void getData(OkHttpClient client, Map<String, Object> params, okhttp3.MediaType mediaType, List<String> fieldNameList) {
        okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, JSONUtil.toJsonStr(params));
        Request request = new Request.Builder()
                .url(Global.instance.jdyEntryDataUrl)
                .method("POST", body)
                .addHeader("Authorization", StrUtil.format("Bearer {}", Global.instance.jdyAppKey))
                .addHeader("Content-Type", "application/json")
                .build();

        List<Map<String, Object>> dataList = new ArrayList<>();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                // 解析响应体
                JSONObject jsonObject = JSONUtil.parseObj(responseBody);
                JSONArray dataArray = jsonObject.getJSONArray("data");
                int size = dataArray.size();
                for (int i = 0; i < size; i++) {
                    JSONObject dataObject = dataArray.getJSONObject(i);
                    // 根据字段名称获取字段值
                    Map<String, Object> dataMap = new HashMap<>();
                    for (String fieldName : fieldNameList) {
                        String fieldValue = dataObject.getStr(fieldName);
                        dataMap.put(fieldName, fieldValue);
                    }
                    dataMap.put("dataId", dataObject.getStr("_id"));
                    dataMap.put("entryId", dataObject.getStr("entryId"));
                    dataMap.put("flowState", dataObject.getStr("flowState"));
                    dataList.add(dataMap);
                }
            }
        } catch (IOException e) {
            log.error("查询简道云数据请求失败: {}", e.getMessage());
        }
        for (Map<String, Object> map : dataList) {
            JdyDataInfo jdyDataInfo = convert(map);
            if (jdyDataInfo != null) {
                jdyDataInfo.setDataId(map.get("dataId").toString());
                jdyDataInfo.setEntryId(map.get("entryId").toString());
                jdyDataInfo.setFlowState(map.get("flowState") == null ? -1 : Integer.parseInt(map.get("flowState").toString()));
                tableJdyDataInfoService.insert(jdyDataInfo);
            }
        }
        // 如果dataList不为空 递归调用
        if (!dataList.isEmpty()) {
            params.put("data_id", dataList.get(dataList.size() - 1).get("dataId").toString());
            getData(client, params, mediaType, fieldNameList);
        }
    }

    /**
     * 将Map转换为JdyDataInfo对象
     * @param map
     * @return
     */
    public static JdyDataInfo convert(Map<String, Object> map) {
        JdyDataInfo obj = new JdyDataInfo();
        Class<?> clazz = obj.getClass();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String fieldName = entry.getKey().replace("_", "");
            Object value = entry.getValue();
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                // 类型转换
                if (value != null) {
                    if (field.getType() == Integer.class && value instanceof String) {
                        value = Integer.parseInt((String) value);
                    }
                    if (field.getType() == BigDecimal.class && value instanceof String) {
                        value = new BigDecimal((String) value);
                    }
                    if (field.getType() == Boolean.class && value instanceof String) {
                        value = Boolean.parseBoolean((String) value);
                    }
                    if (field.getType() == Date.class && value instanceof String) {
                        value = DateUtil.parse((String) value);
                    }
                    field.set(obj, value);
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                // 处理字段不存在或访问权限问题
                log.error("Error setting field " + fieldName + ": " + e.getMessage());
            } catch (NumberFormatException e) {
                log.error("Error converting value for field " + fieldName + ": " + e.getMessage());
            }
        }
        return obj;
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        return null;
    }
}
