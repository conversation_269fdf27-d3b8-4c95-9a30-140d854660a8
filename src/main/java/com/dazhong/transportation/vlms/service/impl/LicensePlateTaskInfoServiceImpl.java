package com.dazhong.transportation.vlms.service.impl;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableDataOwnerInfoService;
import com.dazhong.transportation.vlms.database.TableLicensePlateQuotaTransactionRecordService;
import com.dazhong.transportation.vlms.database.TableLicensePlateTaskInfoService;
import com.dazhong.transportation.vlms.database.TableLicensePlateTaskQuotaDetailService;
import com.dazhong.transportation.vlms.database.TableLicensePlateTaskVehicleDetailService;
import com.dazhong.transportation.vlms.database.TableOperateLogService;
import com.dazhong.transportation.vlms.database.TableOrgInfoService;
import com.dazhong.transportation.vlms.database.TableVehicleModelInfoService;
import com.dazhong.transportation.vlms.database.TableVehicleService;
import com.dazhong.transportation.vlms.dto.ExportLicensePlateTaskRecordDTO;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskQuotaDetailDto;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.dto.PurchaseQuotaDto;
import com.dazhong.transportation.vlms.dto.QuotaInfoDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.UpdateQuotaDto;
import com.dazhong.transportation.vlms.dto.request.SaveLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.ImportRevocationVehicleResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateTaskDetailResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateTaskInfoResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleBasicResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.DataDictEnum;
import com.dazhong.transportation.vlms.enums.ExportFileTypeEnum;
import com.dazhong.transportation.vlms.enums.OperateLogBusinessTypeEnum;
import com.dazhong.transportation.vlms.enums.QuotaAdjustTypeEnum;
import com.dazhong.transportation.vlms.enums.QuotaAdjustWayEnum;
import com.dazhong.transportation.vlms.enums.QuotaTypeEnum;
import com.dazhong.transportation.vlms.enums.ReturnQuotaTypeEnum;
import com.dazhong.transportation.vlms.excel.ImportLicensePlateQuota;
import com.dazhong.transportation.vlms.excel.ImportLicensePlateTaskVehicle;
import com.dazhong.transportation.vlms.excel.ImportRevocationVehicleDetail;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.DataOwnerInfo;
import com.dazhong.transportation.vlms.model.LicensePlateQuotaTransactionRecord;
import com.dazhong.transportation.vlms.model.LicensePlateTaskInfo;
import com.dazhong.transportation.vlms.model.LicensePlateTaskQuotaDetail;
import com.dazhong.transportation.vlms.model.LicensePlateTaskVehicleDetail;
import com.dazhong.transportation.vlms.model.OrgInfo;
import com.dazhong.transportation.vlms.model.VehicleInfo;
import com.dazhong.transportation.vlms.model.VehicleModelInfo;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.service.IFileService;
import com.dazhong.transportation.vlms.service.ILicensePlateQuotaService;
import com.dazhong.transportation.vlms.service.ILicensePlateTaskInfoService;
import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.service.IVehicleDisposalService;
import com.dazhong.transportation.vlms.service.IVehiclePurchaseService;
import com.dazhong.transportation.vlms.service.IVehicleService;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

@Service
public class LicensePlateTaskInfoServiceImpl implements ILicensePlateTaskInfoService {

    @Autowired
    private TableLicensePlateTaskInfoService tableLicensePlateTaskInfoService;

    @Autowired
    private TableLicensePlateTaskVehicleDetailService tableLicensePlateTaskVehicleDetailService;

    @Autowired
    private TableLicensePlateTaskQuotaDetailService tableLicensePlateTaskQuotaDetailService;

    @Autowired
    private TableLicensePlateQuotaTransactionRecordService tableLicensePlateQuotaTransactionRecordService;

    @Autowired
    private TableVehicleService tableVehicleService;

    @Autowired
    private TableVehicleModelInfoService tableVehicleModelInfoService;

    @Autowired
    private TableDataOwnerInfoService tableDataOwnerInfoService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private ILicensePlateQuotaService licensePlateQuotaService;

    @Autowired
    private IVehicleDisposalService vehicleDisposalService;

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private IDataDictService dataDictService;

    @Autowired
    private IVehiclePurchaseService vehiclePurchaseService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IFileService fileService;

    @Override
    public PageResponse<LicensePlateTaskInfoResponse> queryPageResponse(SearchLicensePlateTaskInfoRequest request) {
        // 分页查询
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<LicensePlateTaskVehicleDetailDto> licensePlateTaskVehicleDetailDtoList = tableLicensePlateTaskInfoService.queryList(request);
        PageInfo<LicensePlateTaskVehicleDetailDto> pageInfo = new PageInfo<>(licensePlateTaskVehicleDetailDtoList);

        // 转换成响应对象
        List<LicensePlateTaskInfoResponse> taskInfoResponseList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(licensePlateTaskVehicleDetailDtoList)) {
            for (LicensePlateTaskVehicleDetailDto licensePlateTaskVehicleDetailDTO : licensePlateTaskVehicleDetailDtoList) {
                LicensePlateTaskInfoResponse licensePlateTaskInfoResponse = BeanUtil.copyProperties(licensePlateTaskVehicleDetailDTO, LicensePlateTaskInfoResponse.class);
                if (null != licensePlateTaskInfoResponse.getAssetCompanyId()) {
                    OrgInfo assetOrgInfo = tableOrgInfoService.queryOrgInfoById(licensePlateTaskInfoResponse.getAssetCompanyId());
                    licensePlateTaskInfoResponse.setAssetCompanyName(assetOrgInfo != null ? assetOrgInfo.getCompanyName() : null);
                }

                if (null != licensePlateTaskInfoResponse.getOwnOrganizationId()) {
                    OrgInfo ownOrgInfo = tableOrgInfoService.queryOrgInfoById(licensePlateTaskInfoResponse.getOwnOrganizationId());
                    licensePlateTaskInfoResponse.setOwnOrganizationName(ownOrgInfo != null ? ownOrgInfo.getCompanyName() : null);
                }
                if (null != licensePlateTaskInfoResponse.getUsageOrganizationId()) {
                    OrgInfo usageOrgInfo = tableOrgInfoService.queryOrgInfoById(licensePlateTaskInfoResponse.getUsageOrganizationId());
                    licensePlateTaskInfoResponse.setUsageOrganizationName(usageOrgInfo != null ? usageOrgInfo.getCompanyName() : null);
                }

                if (null != licensePlateTaskInfoResponse.getVehicleModelId()) {
                    VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(licensePlateTaskInfoResponse.getVehicleModelId());
                    licensePlateTaskInfoResponse.setVehicleModelName(vehicleModelInfo != null ? vehicleModelInfo.getVehicleModelName() : null);
                }

                taskInfoResponseList.add(licensePlateTaskInfoResponse);
            }
        }

        // 返回分页结果
        return new PageResponse<>(pageInfo.getTotal(), taskInfoResponseList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Void> saveAssignmentTask(SaveLicensePlateTaskInfoRequest saveLicensePlateTaskInfoRequest, TokenUserInfo tokenUserInfo) {
        // 1.新增任务信息
        LicensePlateTaskInfo taskInfo = new LicensePlateTaskInfo();
        BeanUtils.copyProperties(saveLicensePlateTaskInfoRequest, taskInfo);
        // 生成任务编号
        String taskNumber = "SP" + new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.CHINESE).format(new Date());
        taskInfo.setTaskNumber(taskNumber);
        tableLicensePlateTaskInfoService.insert(taskInfo, tokenUserInfo);

        // 2.批量添加车辆详情
        for (LicensePlateTaskVehicleDetailDto licensePlateTaskVehicleDetailDto : saveLicensePlateTaskInfoRequest.getVehicleDetailList()) {
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(licensePlateTaskVehicleDetailDto.getVin());
            if (null == vehicleInfo) {
                throw new ServiceException("车架号【" + licensePlateTaskVehicleDetailDto.getVin() + "】车辆信息不存在");
            }
            if (null == vehicleInfo.getAssetCompanyId()) {
                throw new ServiceException("车架号【" + licensePlateTaskVehicleDetailDto.getVin() + "】车辆资产所属公司为空");
            }

            VehicleInfo usedLicensePlate = tableVehicleService
                    .queryVehicleByLicensePlate(licensePlateTaskVehicleDetailDto.getLicensePlate());
            if (null != usedLicensePlate) {
                throw new ServiceException("车牌号【" + licensePlateTaskVehicleDetailDto.getLicensePlate() + "】已上牌！");
            }

            licensePlateTaskVehicleDetailDto.setTaskNumber(taskNumber);
            licensePlateTaskVehicleDetailDto.setQuotaType(saveLicensePlateTaskInfoRequest.getQuotaType());
            licensePlateTaskVehicleDetailDto.setQuotaAssetCompanyId(saveLicensePlateTaskInfoRequest.getAssetCompanyId());
            licensePlateTaskVehicleDetailDto.setQuotaAssetCompanyName(saveLicensePlateTaskInfoRequest.getAssetCompanyName());
            licensePlateTaskVehicleDetailDto.setVehicleModelId(vehicleInfo.getVehicleModelId());
            VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(vehicleInfo.getVehicleModelId());
            if (null != vehicleModelInfo) {
                licensePlateTaskVehicleDetailDto.setVehicleModelName(vehicleModelInfo.getVehicleModelName());
            }
            licensePlateTaskVehicleDetailDto.setAssetCompanyId(vehicleInfo.getAssetCompanyId());
            licensePlateTaskVehicleDetailDto.setOwnOrganizationId(vehicleInfo.getOwnOrganizationId());
            licensePlateTaskVehicleDetailDto.setUsageOrganizationId(vehicleInfo.getUsageOrganizationId());
            licensePlateTaskVehicleDetailDto.setVehicleId(vehicleInfo.getId());
        }
        tableLicensePlateTaskVehicleDetailService.insertMultiple(saveLicensePlateTaskInfoRequest.getVehicleDetailList(), tokenUserInfo);

        // 3.占用额度逻辑
        if (taskInfo.getUseQuotaType() == 1) {
            int checkCount = 0;
            for (LicensePlateTaskVehicleDetailDto licensePlateTaskVehicleDetailDto : saveLicensePlateTaskInfoRequest.getVehicleDetailList()) {
                // 更新采购单额度
                PurchaseQuotaDto purchaseQuotaDto = vehiclePurchaseService.updatePurchaseQuota(licensePlateTaskVehicleDetailDto.getVin(), tokenUserInfo);
                if (null != purchaseQuotaDto && purchaseQuotaDto.getIsChangePreOccupied() &&
                        Objects.equals(purchaseQuotaDto.getQuotaAssetOwnership(), saveLicensePlateTaskInfoRequest.getAssetCompanyId()) && Objects.equals(purchaseQuotaDto.getQuotaType(), saveLicensePlateTaskInfoRequest.getQuotaType())) {
                    // 不校验-更新额度
                    UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
                    updateQuotaDTO.setQuotaType(saveLicensePlateTaskInfoRequest.getQuotaType());
                    updateQuotaDTO.setAssetCompanyId(saveLicensePlateTaskInfoRequest.getAssetCompanyId());
                    updateQuotaDTO.setAdjustNum(1);
                    updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.INCREASE.getCode());
                    updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.OCCUPY.getCode());
                    licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, false);
                } else {
                    checkCount++;
                }
            }
            // 校验-更新额度
            if (checkCount > 0) {
                // 更新额度
                UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
                updateQuotaDTO.setQuotaType(saveLicensePlateTaskInfoRequest.getQuotaType());
                updateQuotaDTO.setAssetCompanyId(saveLicensePlateTaskInfoRequest.getAssetCompanyId());
                updateQuotaDTO.setAdjustNum(checkCount);
                updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.INCREASE.getCode());
                updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.OCCUPY.getCode());
                licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);
            }

            List<QuotaInfoDto> quotaInfoDtoList = new ArrayList<>();
            // 明确占用额度
            if (taskInfo.getMatchType() == 1) {
                quotaInfoDtoList = saveLicensePlateTaskInfoRequest.getVehicleDetailList().stream().map(vehicleDetailDto -> new QuotaInfoDto(vehicleDetailDto.getQuotaNumber(), vehicleDetailDto.getQuotaPrintDate(), vehicleDetailDto.getLicensePlate())).collect(Collectors.toList());
            }
            // 非明确占用额度
            if (taskInfo.getMatchType() == 2) {
                if (saveLicensePlateTaskInfoRequest.getVehicleDetailList().size() != saveLicensePlateTaskInfoRequest.getQuotaDetailList().size()) {
                    throw new ServiceException("车辆详情和额度详情数量不一致");
                }
                quotaInfoDtoList = saveLicensePlateTaskInfoRequest.getQuotaDetailList().stream().map(quotaDetailDto -> new QuotaInfoDto(quotaDetailDto.getQuotaNumber(), quotaDetailDto.getQuotaPrintDate())).collect(Collectors.toList());
                // 批量添加额度详情
                for (LicensePlateTaskQuotaDetailDto licensePlateTaskQuotaDetailDto : saveLicensePlateTaskInfoRequest.getQuotaDetailList()) {
                    licensePlateTaskQuotaDetailDto.setTaskNumber(taskNumber);
                    licensePlateTaskQuotaDetailDto.setQuotaType(saveLicensePlateTaskInfoRequest.getQuotaType());
                    licensePlateTaskQuotaDetailDto.setQuotaAssetCompanyId(saveLicensePlateTaskInfoRequest.getAssetCompanyId());
                    licensePlateTaskQuotaDetailDto.setQuotaAssetCompanyName(saveLicensePlateTaskInfoRequest.getAssetCompanyName());
                }
                tableLicensePlateTaskQuotaDetailService.insertMultiple(saveLicensePlateTaskInfoRequest.getQuotaDetailList(), tokenUserInfo);
            }

            // 找到对应额度流水并登记上牌任务编号
            if (CollectionUtil.isNotEmpty(quotaInfoDtoList)) {
                // 判断quotaInfoDtoList是否有重复的数据，判断依据quotaNumber和quotaPrintDate相同
                Map<String, Long> quotaMap = new HashMap<>();
                List<String> duplicateQuotas = new ArrayList<>();
                // 建议定义一个SimpleDateFormat实例并设置时区，以供重用或确保一致性
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                // 例如，如果您的业务日期应以中国上海时区为准
                sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); 

                for (QuotaInfoDto quotaInfoDto : quotaInfoDtoList) {
                    LicensePlateQuotaTransactionRecord licensePlateQuotaTransactionRecord = tableLicensePlateQuotaTransactionRecordService.selectByQuotaNumberAndQuotaPrintDate(quotaInfoDto.getQuotaNumber(), quotaInfoDto.getQuotaPrintDate());
                    if (null== licensePlateQuotaTransactionRecord) {
                        throw new ServiceException("额度单号[" + quotaInfoDto.getQuotaNumber() + "]打印日期[" +
                                (quotaInfoDto.getQuotaPrintDate()!= null?
                                sdf.format(quotaInfoDto.getQuotaPrintDate()) : "") + "]的额度流水不存在");
                    }

                    String key = quotaInfoDto.getQuotaNumber() + "_" + (quotaInfoDto.getQuotaPrintDate() != null ?
                            sdf.format(quotaInfoDto.getQuotaPrintDate()) : "");
                    if (quotaMap.containsKey(key)) {
                        duplicateQuotas.add("额度单号[" + quotaInfoDto.getQuotaNumber() + "]打印日期[" +
                                (quotaInfoDto.getQuotaPrintDate() != null ?
                                sdf.format(quotaInfoDto.getQuotaPrintDate()) : "") + "]");
                    } else {
                        quotaMap.put(key, 1L);
                    }
                }
                if (CollectionUtil.isNotEmpty(duplicateQuotas)) {
                    throw new ServiceException("存在重复的额度信息：" + String.join("，", duplicateQuotas));
                }
                for (QuotaInfoDto quotaInfoDto : quotaInfoDtoList) {
                    LicensePlateQuotaTransactionRecord updateTransactionRecord = BeanUtil.copyProperties(quotaInfoDto, LicensePlateQuotaTransactionRecord.class);
                    updateTransactionRecord.setTaskNumber(taskNumber);
                    tableLicensePlateQuotaTransactionRecordService.updateTaskNumber(updateTransactionRecord, tokenUserInfo);
                }
            }
        }

        // 4. 更新车辆主数据表
        for (LicensePlateTaskVehicleDetailDto licensePlateTaskVehicleDetailDto : saveLicensePlateTaskInfoRequest.getVehicleDetailList()) {
            VehicleInfo updateLicensePlateVehicleInfo = BeanUtil.copyProperties(licensePlateTaskVehicleDetailDto, VehicleInfo.class);
            updateLicensePlateVehicleInfo.setUseQuotaType(saveLicensePlateTaskInfoRequest.getUseQuotaType());
            tableVehicleService.updateLicensePlateByVin(updateLicensePlateVehicleInfo, tokenUserInfo);

            // 操作日志
            tableOperateLogService.insertLog(licensePlateTaskVehicleDetailDto.getVehicleId(), BizConstant.BusinessType.businessType_5, null, "批量上传车牌信息", tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Void> saveRevocationTask(SaveLicensePlateTaskInfoRequest saveLicensePlateTaskInfoRequest, TokenUserInfo tokenUserInfo) {
        // 1.新增任务信息
        LicensePlateTaskInfo taskInfo = BeanUtil.copyProperties(saveLicensePlateTaskInfoRequest, LicensePlateTaskInfo.class);
        // 生成任务编号
        String taskNumber = "TP" + new SimpleDateFormat("yyyyMMddHHmmssSSS", Locale.CHINESE).format(new Date());
        taskInfo.setTaskNumber(taskNumber);
        tableLicensePlateTaskInfoService.insert(taskInfo, tokenUserInfo);

        // 2.批量添加车辆详情
        for (LicensePlateTaskVehicleDetailDto licensePlateTaskVehicleDetailDto : saveLicensePlateTaskInfoRequest.getVehicleDetailList()) {
            VehicleInfo vehicleInfo = tableVehicleService.queryVehicleByVin(licensePlateTaskVehicleDetailDto.getVin());
            if (StringUtils.isBlank(vehicleInfo.getLicensePlate())) {
                throw new ServiceException("车架号【" + licensePlateTaskVehicleDetailDto.getVin() + "】已退牌！");
            }
            if (!vehicleInfo.getLicensePlate().equals(licensePlateTaskVehicleDetailDto.getLicensePlate())) {
                throw new ServiceException("车牌号【" + licensePlateTaskVehicleDetailDto.getLicensePlate() + "】与车辆主数据不一致");
            }
            if (licensePlateTaskVehicleDetailDto.getReturnQuotaType() == 1) {
                saveLicensePlateTaskInfoRequest.getQuotaDetailList().stream()
                        .filter(quotaDetailDto -> quotaDetailDto.getLicensePlate().equals(licensePlateTaskVehicleDetailDto.getLicensePlate()))
                        .findFirst()
                        .orElseThrow(() -> new ServiceException("缺失与车牌号【" + licensePlateTaskVehicleDetailDto.getLicensePlate() + "】匹配的额度详情"));
            }
            licensePlateTaskVehicleDetailDto.setTaskNumber(taskNumber);
            licensePlateTaskVehicleDetailDto.setVehicleModelId(vehicleInfo.getVehicleModelId());
            VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(vehicleInfo.getVehicleModelId());
            if (null != vehicleModelInfo) {
                licensePlateTaskVehicleDetailDto.setVehicleModelName(vehicleModelInfo.getVehicleModelName());
            }
            licensePlateTaskVehicleDetailDto.setQuotaType(vehicleInfo.getQuotaType());
            licensePlateTaskVehicleDetailDto.setQuotaAssetCompanyId(vehicleInfo.getQuotaAssetCompanyId());
            licensePlateTaskVehicleDetailDto.setAssetCompanyId(vehicleInfo.getAssetCompanyId());
            licensePlateTaskVehicleDetailDto.setOwnOrganizationId(vehicleInfo.getOwnOrganizationId());
            licensePlateTaskVehicleDetailDto.setUsageOrganizationId(vehicleInfo.getUsageOrganizationId());
            licensePlateTaskVehicleDetailDto.setVehicleId(vehicleInfo.getId());
            if (licensePlateTaskVehicleDetailDto.getReturnQuotaType() == 1) {
                LicensePlateTaskQuotaDetailDto quotaDetailDto = saveLicensePlateTaskInfoRequest.getQuotaDetailList().stream().filter(dto -> dto.getLicensePlate().equals(licensePlateTaskVehicleDetailDto.getLicensePlate())).findFirst().orElse(null);
                if (null != quotaDetailDto) {
                    licensePlateTaskVehicleDetailDto.setQuotaNumber(quotaDetailDto.getQuotaNumber());
                    licensePlateTaskVehicleDetailDto.setQuotaPrintDate(quotaDetailDto.getQuotaPrintDate());
                }
            }
        }
        tableLicensePlateTaskVehicleDetailService.insertMultiple(saveLicensePlateTaskInfoRequest.getVehicleDetailList(), tokenUserInfo);

        // 3.批量添加退还信息
        if (CollectionUtil.isNotEmpty(saveLicensePlateTaskInfoRequest.getQuotaDetailList())) {
            for (LicensePlateTaskQuotaDetailDto licensePlateTaskQuotaDetailDto : saveLicensePlateTaskInfoRequest.getQuotaDetailList()) {
                licensePlateTaskQuotaDetailDto.setTaskNumber(taskNumber);
            }
            tableLicensePlateTaskQuotaDetailService.insertMultiple(saveLicensePlateTaskInfoRequest.getQuotaDetailList(), tokenUserInfo);
        }

        // 4.退还额度明细逻辑
        // 退还额度逻辑
        List<LicensePlateTaskVehicleDetailDto> returnQuotaList = saveLicensePlateTaskInfoRequest.getVehicleDetailList().stream().filter(vehicleDetailDto -> vehicleDetailDto.getReturnQuotaType() == 1).collect(Collectors.toList());
        if (returnQuotaList.size() > 0) {
            for (LicensePlateTaskVehicleDetailDto vehicleDetail : returnQuotaList) {
                // 更新额度
                UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
                updateQuotaDTO.setQuotaType(vehicleDetail.getQuotaType());
                updateQuotaDTO.setAssetCompanyId(vehicleDetail.getQuotaAssetCompanyId());
                updateQuotaDTO.setAdjustNum(1);
                updateQuotaDTO.setAdjustType(QuotaAdjustTypeEnum.DECREASE.getCode());
                updateQuotaDTO.setAdjustWay(QuotaAdjustWayEnum.OCCUPY.getCode());
                licensePlateQuotaService.updateQuota(updateQuotaDTO, tokenUserInfo, true);

                // 新增额度单流水
                LicensePlateQuotaTransactionRecord newRecord = new LicensePlateQuotaTransactionRecord();
                LicensePlateTaskQuotaDetailDto quotaDetailDto = saveLicensePlateTaskInfoRequest.getQuotaDetailList().stream().filter(quotaDetail -> quotaDetail.getLicensePlate().equals(vehicleDetail.getLicensePlate())).findFirst().orElse(null);
                if (null != quotaDetailDto) {
                    newRecord.setQuotaNumber(quotaDetailDto.getQuotaNumber());
                    newRecord.setQuotaPrintDate(quotaDetailDto.getQuotaPrintDate());
                    newRecord.setLicensePlateWithdrawalDate(quotaDetailDto.getLicensePlateWithdrawalDate());
                    newRecord.setQuotaType(vehicleDetail.getQuotaType());
                    newRecord.setAssetCompanyId(vehicleDetail.getQuotaAssetCompanyId());
                    newRecord.setAssetCompanyName(vehicleDetail.getQuotaAssetCompanyName());
                }

                tableLicensePlateQuotaTransactionRecordService.insert(newRecord, tokenUserInfo);
            }
        }
        // 不退还额度逻辑
        List<LicensePlateTaskVehicleDetailDto> notReturnList = saveLicensePlateTaskInfoRequest.getVehicleDetailList().stream().filter(vehicleDetailDto -> vehicleDetailDto.getReturnQuotaType() == 2).collect(Collectors.toList());
        if (notReturnList.size() > 0) {
            for (LicensePlateTaskVehicleDetailDto vehicleDetail : notReturnList) {
                // 不退还额度
                UpdateQuotaDto updateQuotaDTO = new UpdateQuotaDto();
                updateQuotaDTO.setQuotaType(vehicleDetail.getQuotaType());
                updateQuotaDTO.setAssetCompanyId(vehicleDetail.getQuotaAssetCompanyId());
                licensePlateQuotaService.notReturnQuota(updateQuotaDTO, tokenUserInfo);
            }
        }

        // 5. 清空车辆车牌号
        for (LicensePlateTaskVehicleDetailDto licensePlateTaskVehicleDetailDto : saveLicensePlateTaskInfoRequest.getVehicleDetailList()) {
            VehicleInfo updateLicensePlateVehicleInfo = new VehicleInfo();
            updateLicensePlateVehicleInfo.setVin(licensePlateTaskVehicleDetailDto.getVin());
            updateLicensePlateVehicleInfo.setLicensePlate(StringUtils.EMPTY);
            updateLicensePlateVehicleInfo.setQuotaType(0);
            updateLicensePlateVehicleInfo.setQuotaAssetCompanyId(0);
            tableVehicleService.updateLicensePlateByVin(updateLicensePlateVehicleInfo, tokenUserInfo);

            // 操作日志
            tableOperateLogService.insertLog(licensePlateTaskVehicleDetailDto.getVehicleId(), BizConstant.BusinessType.businessType_5, null, "退牌，任务编号【" + taskNumber + "】", tokenUserInfo);
        }

        // 6. 退牌任务关联处置申请单
        if (null != saveLicensePlateTaskInfoRequest.getDisposalId()) {
            // 关联退牌任务
            vehicleDisposalService.relateReturnLicenseTask(saveLicensePlateTaskInfoRequest.getDisposalId(), taskNumber, tokenUserInfo, false);
            // 操作日志
            tableOperateLogService.insertLog(saveLicensePlateTaskInfoRequest.getDisposalId(), OperateLogBusinessTypeEnum.DISPOSAL_APPLICATION.getCode(), null, "新增退牌任务编号" + taskNumber, tokenUserInfo);
        }

        return ResultResponse.success();
    }

    @Override
    public LicensePlateTaskDetailResponse getDetailByTaskNumber(String taskNumber) {
        LicensePlateTaskInfo licensePlateTaskInfo = tableLicensePlateTaskInfoService.selectByTaskNumber(taskNumber);
        if (null == licensePlateTaskInfo) {
            throw new ServiceException("任务不存在！");
        }
        LicensePlateTaskDetailResponse response = BeanUtil.copyProperties(licensePlateTaskInfo, LicensePlateTaskDetailResponse.class);
        // 查询车辆详情列表
        List<LicensePlateTaskVehicleDetail> taskVehicleDetailList = tableLicensePlateTaskVehicleDetailService.selectByTaskNumber(taskNumber);
        response.setVehicleDetailList(BeanUtil.copyToList(taskVehicleDetailList, LicensePlateTaskVehicleDetailDto.class));
        for (LicensePlateTaskVehicleDetailDto vehicleDetailDto : response.getVehicleDetailList()) {
            if (null != vehicleDetailDto.getAssetCompanyId()) {
                DataOwnerInfo dataOwnerInfo = tableDataOwnerInfoService.queryOwnerInfoById(vehicleDetailDto.getAssetCompanyId());
                vehicleDetailDto.setAssetCompanyName(dataOwnerInfo != null ? dataOwnerInfo.getName() : null);
            }
            if (null != vehicleDetailDto.getOwnOrganizationId()) {
                OrgInfo ownOrgInfo = tableOrgInfoService.queryOrgInfoById(vehicleDetailDto.getOwnOrganizationId());
                vehicleDetailDto.setOwnOrganizationName(ownOrgInfo != null ? ownOrgInfo.getCompanyName() : null);
            }
            if (null != vehicleDetailDto.getUsageOrganizationId()) {
                OrgInfo usageOrgInfo = tableOrgInfoService.queryOrgInfoById(vehicleDetailDto.getUsageOrganizationId());
                vehicleDetailDto.setUsageOrganizationName(usageOrgInfo != null ? usageOrgInfo.getCompanyName() : null);
            }
        }
        // 查询额度详情列表
        List<LicensePlateTaskQuotaDetail> quotaDetailList = tableLicensePlateTaskQuotaDetailService.selectByTaskNumber(taskNumber);
        response.setQuotaDetailList(BeanUtil.copyToList(quotaDetailList, LicensePlateTaskQuotaDetailDto.class));

        return response;
    }

    @Override
    public List<String> getLicensePlateListByTaskNumber(String taskNumber) {
        // 查询车辆详情列表
        List<LicensePlateTaskVehicleDetail> taskVehicleDetailList = tableLicensePlateTaskVehicleDetailService.selectByTaskNumber(taskNumber);
        // 返回车牌号列表
        return taskVehicleDetailList.stream().map(LicensePlateTaskVehicleDetail::getLicensePlate).collect(Collectors.toList());
    }

    @Override
    public List<LicensePlateTaskQuotaDetailDto> getLicensePlateTaskQuotaDetailsByTaskNumber(String taskNumber) {
        List<LicensePlateTaskQuotaDetail> quotaDetailList = tableLicensePlateTaskQuotaDetailService.selectByTaskNumber(taskNumber);
        return BeanUtil.copyToList(quotaDetailList, LicensePlateTaskQuotaDetailDto.class);
    }

    @Override
    public List<ImportLicensePlateTaskVehicle> getImportLicensePlateTaskVehicleList(BaseImportFileUrlRequest request) {
        List<ImportLicensePlateTaskVehicle> readList = ExcelUtil.read(request.getFilePath(), 0, ImportLicensePlateTaskVehicle.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("上传文件不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }

        // 获取车架号列表
        List<String> vinList = readList.stream().map(ImportLicensePlateTaskVehicle::getVin).distinct().collect(Collectors.toList());
        if (vinList.size() != readList.size()) {
            throw new ServiceException("车架号不能重复");
        }
        List<VehicleInfo> vehicleInfoList = tableVehicleService.queryVehicleByVinList(vinList);
        for (VehicleInfo vehicleInfo : vehicleInfoList) {
            if (StringUtils.isNotBlank(vehicleInfo.getLicensePlate())) {
                throw new ServiceException("车架号【" + vehicleInfo.getVin() + "】已有车牌号！");
            }
        }

        Map<String, Map<Integer, String>> dictMap = dataDictService.getDataMaintainDictMap(Arrays.asList("usage", "vehicleType"));
        for (ImportLicensePlateTaskVehicle importLicensePlateTaskVehicle : readList) {
            // vehicleType中文转化为枚举值
            for (Map.Entry<Integer, String> entry : dictMap.get("vehicleType").entrySet()) {
                if (importLicensePlateTaskVehicle.getVehicleTypeRegistrationCardString().equals(entry.getValue())) {
                    importLicensePlateTaskVehicle.setVehicleTypeRegistrationCard(entry.getKey());
                    break;
                }
            }
            // usage中文转化为枚举值
            for (Map.Entry<Integer, String> entry : dictMap.get("usage").entrySet()) {
                if (importLicensePlateTaskVehicle.getUsageIdRegistrationCardString().equals(entry.getValue())) {
                    importLicensePlateTaskVehicle.setUsageIdRegistrationCard(entry.getKey());
                    break;
                }
            }
        }

        return readList;
    }

    @Override
    public List<ImportLicensePlateQuota> getImportLicensePlateQuotaList(BaseImportFileUrlRequest request) {
        List<ImportLicensePlateQuota> readList = ExcelUtil.read(request.getFilePath(), 0, ImportLicensePlateQuota.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("上传文件不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }

        ComboResponse<String, String> comboResponse = tableLicensePlateQuotaTransactionRecordService.queryQuotaNumberCombo(null);

        for (ImportLicensePlateQuota importLicensePlateQuota : readList) {
            ValidationUtils.validate(importLicensePlateQuota);

            comboResponse.getList().stream()
                    .filter(comboData -> comboData.getKey().equals(importLicensePlateQuota.getQuotaNumber()) && comboData.getValue().equals(importLicensePlateQuota.getQuotaPrintDate()))
                    .findFirst()
                    .orElseThrow(() -> new ServiceException("额度单号【" + importLicensePlateQuota.getQuotaNumber() + "】和打印日期【" + importLicensePlateQuota.getQuotaPrintDate() + "】非可用额度！"));
        }

        return readList;
    }

    @Override
    public ImportRevocationVehicleResponse getImportRevocationVehicleDetail(BaseImportFileUrlRequest request) {
        List<ImportRevocationVehicleDetail> readList = ExcelUtil.read(request.getFilePath(), 0, ImportRevocationVehicleDetail.class);
        if (ObjectUtil.isEmpty(readList)) {
            throw new ServiceException("上传文件不能为空");
        }
        if (readList.size() > 1000) {
            throw new ServiceException("最大行数1000行");
        }

        List<LicensePlateTaskVehicleDetailDto> vehicleDetailList = new ArrayList<>();
        List<LicensePlateTaskQuotaDetailDto> quotaDetailList = new ArrayList<>();
        for (ImportRevocationVehicleDetail importRevocationVehicleDetail : readList) {
            if (StringUtils.isBlank(importRevocationVehicleDetail.getLicensePlate())) {
                continue;
            }
            ResultResponse<VehicleBasicResponse> resultResponse = vehicleService
                    .getVehicleBasicInfoByLicensePlate(importRevocationVehicleDetail.getLicensePlate());
            if (resultResponse.getCode() == 0) {
                LicensePlateTaskVehicleDetailDto vehicleDetailDto = new LicensePlateTaskVehicleDetailDto();
                BeanUtils.copyProperties(resultResponse.getData(), vehicleDetailDto);
                vehicleDetailDto.setReturnQuotaType(
                        ReturnQuotaTypeEnum.getCodeByDesc(importRevocationVehicleDetail.getReturnQuotaTypeString()));
                if (vehicleDetailDto.getUseQuotaType() == 1 && vehicleDetailDto.getReturnQuotaType() == 3) {
                    vehicleDetailDto.setReturnQuotaType(null);
                }
                vehicleDetailList.add(vehicleDetailDto);

                if (vehicleDetailDto.getReturnQuotaType() != null && vehicleDetailDto.getReturnQuotaType() == 1) {
                    LicensePlateTaskQuotaDetailDto quotaDetailDto = new LicensePlateTaskQuotaDetailDto();
                    BeanUtils.copyProperties(importRevocationVehicleDetail, quotaDetailDto);
                    quotaDetailDto.setQuotaType(vehicleDetailDto.getQuotaType());
                    quotaDetailDto.setQuotaAssetCompanyId(vehicleDetailDto.getQuotaAssetCompanyId());
                    quotaDetailDto.setQuotaAssetCompanyName(vehicleDetailDto.getQuotaAssetCompanyName());
                    quotaDetailList.add(quotaDetailDto);
                }
            }
        }

        return new ImportRevocationVehicleResponse(vehicleDetailList, quotaDetailList);
    }

    @Override
    public List<LicensePlateTaskVehicleDetailDto> queryLicensePlateTaskList(String vin, Integer taskType) {
        SearchLicensePlateTaskInfoRequest request = new SearchLicensePlateTaskInfoRequest();
        request.setVin(vin);
        request.setTaskType(taskType);

        return tableLicensePlateTaskInfoService.queryList(request);
    }

    @Override
    public void exportTaskList(SearchLicensePlateTaskInfoRequest request, TokenUserInfo tokenUserInfo) {
        //导出文件类型
        ExportFileTypeEnum fileTypeEnum = ExportFileTypeEnum.EXPORT_LICENSE_PLATE_TASK_RECORD;
        //导出模板信息
        InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("template/导出牌照任务记录.xlsx");
        //查询机构映射信息

        //查询字典表相关信息
        List<String> systemCodeList = Arrays.asList(
                //车辆拥有公司
                DataDictEnum.OWNER.getValue()
        );
        Map<String, Map<Integer, String>> dataMaintainDictMap = dataDictService.getDataMaintainDictMap(systemCodeList);
        Map<Integer, String> dictMap = dataMaintainDictMap.get(DataDictEnum.OWNER.getValue());

        Map<Long, String> orgNameMap = orgService.getOrgNameMap();
        Map<Long, VehicleModelInfo> vehicleModelMap = tableVehicleModelInfoService.getAllVehicleModelMap();
        //开始导出文件
        fileService.export(fileTypeEnum, templateStream, tokenUserInfo,
                ((excelWriter, writeSheet) -> {
                    //是否查询到导出数据
                    boolean hasResult = false;
                    long index = 1;
                    //标识位
                    int pageNum = 1;
                    while (true) {
                        //分批查询填充导出数据
                        PageMethod.startPage(pageNum, 100);
                        List<LicensePlateTaskVehicleDetailDto> resultList = tableLicensePlateTaskInfoService.queryList(request);
                        if (resultList.isEmpty()) {
                            break;
                        }
                        pageNum++;
                        //存在导出数据
                        hasResult = true;
                        List<ExportLicensePlateTaskRecordDTO> exportDataList = CollectionUtil.newArrayList();
                        //遍历处理查询字段
                        for (LicensePlateTaskVehicleDetailDto dto : resultList) {
                            ExportLicensePlateTaskRecordDTO exportData = BeanUtil.copyProperties(dto, ExportLicensePlateTaskRecordDTO.class);
                            exportData.setId(index);
                            // 额度类型
                            exportData.setQuotaType(QuotaTypeEnum.getDesc(dto.getQuotaType()));
                            exportData.setReturnQuotaType("");
                            if (dto.getReturnQuotaType() != null ){
                                if (dto.getReturnQuotaType() == 1){
                                    exportData.setReturnQuotaType("退还");
                                } else if (dto.getReturnQuotaType() == 2){
                                    exportData.setReturnQuotaType("不退还");
                                } else if (dto.getReturnQuotaType() == 3){
                                    exportData.setReturnQuotaType("不涉及");
                                }
                            }
                            exportData.setTaskType(dto.getTaskType() == 1 ? "上牌任务" : "退牌任务");
                            // 发证日期（行驶证）
                            exportData.setIssuanceDateRegistrationCard(DateTimeUtils.dateToString(dto.getIssuanceDateRegistrationCard(), DateTimeUtils.DATE_TYPE3));
                            // 额度单打印日期
                            exportData.setCreateTime(DateTimeUtils.dateToString(dto.getCreateTime(), DateTimeUtils.DATE_TYPE1));

                            String assetCompanyName = dictMap.get(dto.getAssetCompanyId());
                            exportData.setAssetCompanyName(assetCompanyName);
                            exportData.setOwnOrganizationName(orgNameMap.get(dto.getOwnOrganizationId()));
                            exportData.setUsageOrganizationName(orgNameMap.get(dto.getUsageOrganizationId()));
                            exportData.setVehicleModelName(vehicleModelMap.get(dto.getVehicleModelId()) == null ? "" : vehicleModelMap.get(dto.getVehicleModelId()).getVehicleModelName());
                            index++;
                            exportDataList.add(exportData);
                        }
                        excelWriter.fill(exportDataList, writeSheet);
                    }
                    return hasResult;
                }));
    }
}
