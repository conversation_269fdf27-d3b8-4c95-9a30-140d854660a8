package com.dazhong.transportation.vlms.service.sync;

import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableVehicleSyncExternalBusinessInfoService;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalBusinessInfoDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalInsuranceDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalInsuranceDto;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleExternalInsuranceResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.model.VehicleSyncExternalBusinessInfo;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class VehicleSyncExternalInsuranceServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableVehicleSyncExternalBusinessInfoService tableVehicleSyncExternalBusinessInfoService;

    @Override
    public ResultResponse handleSyncData(SyncDataRequest request) {
        //校验参数
        ValidationUtils.validate(request);
        try {
            VehicleSyncExternalInsuranceDto params = JSONObject.parseObject(request.getBizRequest(), VehicleSyncExternalInsuranceDto.class);
            if (CollectionUtils.isEmpty(params.getVehicleInsuranceInfo())){
                return ResultResponse.businessFailed(ExceptionEnum.PARAM_ERR.getMessage());
            }
            List<VehicleSyncExternalBusinessInfoDto> rows = new ArrayList<>();
            params.getVehicleInsuranceInfo().forEach(insurance ->{
                VehicleSyncExternalBusinessInfoDto row = new VehicleSyncExternalBusinessInfoDto();
                row.setVin(insurance.getVin());
                row.setLicensePlate(insurance.getLicensePlate());
                row.setBusinessNo(insurance.getInsuranceNo());
                row.setBusinessTime(insurance.getInsuranceStartDate());
                row.setBusinessInfo(JSONObject.toJSONString(insurance));
                row.setBusinessType(BizConstant.SyncExternalBusinessType.insurance);
                rows.add(row);
            });
            int insert = tableVehicleSyncExternalBusinessInfoService.batchInsert(rows);
            if (insert > 0) {
                return ResultResponse.success();
            }
        }catch (Exception e){
            log.error("batchInsert insurance exception ",e);
        }
        return ResultResponse.businessFailed("同步保险信息数据异常");
    }


    /**
     * 根据车牌号获取车辆保险信息
     * @param request
     * @return
     */
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        try {
            List<VehicleSyncExternalBusinessInfo> insuranceInfoList = tableVehicleSyncExternalBusinessInfoService.selectByLicensePlateAndType(request.getLicensePlate(),
                    BizConstant.SyncExternalBusinessType.insurance);
            if (CollectionUtils.isEmpty(insuranceInfoList)){
                return ResultResponse.success();
            }
            List<VehicleExternalInsuranceResponse> responses = new ArrayList<>();

            insuranceInfoList.forEach(insuranceInfo ->{
                VehicleExternalInsuranceResponse insuranceResponse = new VehicleExternalInsuranceResponse();
                insuranceResponse.setLicensePlate(insuranceInfo.getLicensePlate());
                insuranceResponse.setSyncDateTime(DateTimeUtils.dateToString(insuranceInfo.getCreateTime(),DateTimeUtils.DATE_TYPE1));
                if (StringUtils.isNotBlank(insuranceInfo.getBusinessInfo())){
                    VehicleSyncExternalInsuranceDetailDto insuranceDetailDto = JSONObject.parseObject(insuranceInfo.getBusinessInfo(),VehicleSyncExternalInsuranceDetailDto.class);
                    if (insuranceDetailDto != null){
                        insuranceResponse.setInsuranceNo(insuranceDetailDto.getInsuranceNo());
                        insuranceResponse.setInsuranceStartDate(insuranceDetailDto.getInsuranceStartDate());
                        insuranceResponse.setInsuranceEndDate(insuranceDetailDto.getInsuranceEndDate());
                        insuranceResponse.setInsuranceAmount(insuranceDetailDto.getInsuranceAmount());
                        insuranceResponse.setInsurerName(insuranceDetailDto.getInsurerName());
                        insuranceResponse.setThirdPartAmount(insuranceDetailDto.getThirdPartAmount());
                        insuranceResponse.setInsuranceType(BizConstant.insuranceTypeMap.get(insuranceDetailDto.getInsuranceType()));
                    }
                }
                responses.add(insuranceResponse);
            });
            return ResultResponse.success(responses);
        }catch (Exception e){
            log.error("insurance getSyncExternalInfoByLicensePlate exception ",e);
        }
        return ResultResponse.businessFailed("获取车辆保险信息异常");
    }


}
