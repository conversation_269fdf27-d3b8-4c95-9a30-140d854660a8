package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleTransferFixedResponse;
import com.dazhong.transportation.vlms.model.VehicleTransferFixedApply;

import java.util.List;

/**
 * 车辆转固服务服务
 */
public interface ITransferFixedService {


    /**
     * 查询转固申请
     * @param request
     * @param tokenUserInfo
     * @return
     */
    PageResponse searchVehicleTransferFixedApply(SearchTransferFixedRequest request, TokenUserInfo tokenUserInfo);


    /**
     * 添加转固申请
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse addVehicleTransferFixed(AddFixedAssetVehicleRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 更新转固申请
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse updateVehicleTransferFixed(UpdateFixedAssetVehicleRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 修改转固申请状态
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse editTransferFixedStatus(EditTransferFixedStatusRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询转固申请详情
     * @param applyNo
     * @return
     */
    ResultResponse queryTransferFixedDetail(String applyNo);

    /**
     * 获取转固车辆信息
     * @param vinList
     * @return
     */
    List<VehicleTransferFixedResponse> getVehicleTransferFixedResponse(List<String> vinList);

    /**
     * 刷新转固审批结果
     * @param request
     * @return
     */
    ResultResponse refreshTransferFixed(RefreshDingTalkApprovalRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 转固审批结果
     * @param requestNo
     * @param dingTalkResult "agree"："同意" , "refuse":"拒绝"
     * @return
     */
    void dingTalkResultProcess(String requestNo, String dingTalkResult);
}
