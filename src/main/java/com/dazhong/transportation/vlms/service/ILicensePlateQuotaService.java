package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.LicensePlateQuotaDto;
import com.dazhong.transportation.vlms.dto.LicensePlateQuotaOperateLogDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.UpdateQuotaDto;
import com.dazhong.transportation.vlms.dto.request.AdjustTotalQuotaRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaOperateLogRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaRequest;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.DataOwnerInfo;

import java.util.List;

public interface ILicensePlateQuotaService {

    /**
     * 查询车牌额度一览分页列表
     *
     * @param searchLicensePlateQuotaRequest 查询车牌额度入参
     * @return 返回符合条件的车牌额度列表
     */
    PageResponse<LicensePlateQuotaDto> queryPageResponse(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest);

    /**
     * 查询车牌额度一览总数信息
     *
     * @param searchLicensePlateQuotaRequest 查询车牌额度入参
     * @return 返回符合条件的车牌额度列表
     */
    LicensePlateQuotaResponse queryTotalQuota(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest);

    /**
     * 额度调整
     *
     * @param adjustTotalQuotaRequest 修改车辆额度入参
     * @param tokenUserInfo           登录用户信息
     * @return 返回操作结果，成功或失败信息
     */
    ResultResponse<Void> adjustTotalQuota(AdjustTotalQuotaRequest adjustTotalQuotaRequest, TokenUserInfo tokenUserInfo);

    /**
     * 查询车牌额度操作日志分页列表
     *
     * @param searchLicensePlateQuotaOperateLogRequest 查询车牌额度操作日志入参
     * @return 返回符合条件的车牌额度列表
     */
    PageResponse<LicensePlateQuotaOperateLogDto> queryOperateLogPageResponse(SearchLicensePlateQuotaOperateLogRequest searchLicensePlateQuotaOperateLogRequest);

    /**
     * 根据额度类型查询公司信息
     *
     * @param quotaType 额度类型
     * @return 返回符合条件的公司信息列表
     */
    List<DataOwnerInfo> queryOwnerInfoListByQuotaType(Integer quotaType);

    /**
     * 更新额度
     *
     * @param updateQuotaDTO 更新额度入参
     * @param tokenUserInfo  登录用户信息
     * @param doCheck        是否校验
     */
    void updateQuota(UpdateQuotaDto updateQuotaDTO, TokenUserInfo tokenUserInfo, boolean doCheck);

    /**
     * 不退还额度
     *
     * @param updateQuotaDTO 更新额度入参
     * @param tokenUserInfo  登录用户信息
     */
    void notReturnQuota(UpdateQuotaDto updateQuotaDTO, TokenUserInfo tokenUserInfo);
}
