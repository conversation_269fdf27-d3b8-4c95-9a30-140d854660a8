package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dazhong.transportation.vlms.database.TableCompanyExtraInfoService;
import com.dazhong.transportation.vlms.dto.CompanyExtraInfoDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.UpdateCompanyExtraInfoRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.CompanyExtraInfo;
import com.dazhong.transportation.vlms.service.ICompanyExtraInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 公司额外信息服务实现类
 */
@Service
@Slf4j
public class CompanyExtraInfoServiceImpl implements ICompanyExtraInfoService {

    @Autowired
    private TableCompanyExtraInfoService tableCompanyExtraInfoService;

    @Override
    public ResultResponse<Void> updateCompanyExtraInfo(UpdateCompanyExtraInfoRequest updateCompanyExtraInfoRequest, TokenUserInfo tokenUserInfo) {
        // 查询记录是否存在
        CompanyExtraInfo companyExtraInfo = tableCompanyExtraInfoService.selectCompanyExtraInfoList(updateCompanyExtraInfoRequest.getForeignId(), updateCompanyExtraInfoRequest.getBusinessType());
        CompanyExtraInfo companyExtraInfoRequest = BeanUtil.copyProperties(updateCompanyExtraInfoRequest, CompanyExtraInfo.class);
        if (null == companyExtraInfo) {
            // 新增记录
            tableCompanyExtraInfoService.insert(companyExtraInfoRequest, tokenUserInfo);
        } else {
            // 修改记录
            companyExtraInfoRequest.setId(companyExtraInfo.getId());
            tableCompanyExtraInfoService.updateSelectiveById(companyExtraInfoRequest, tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    public CompanyExtraInfoDto selectCompanyExtraInfo(Long foreignId, Integer businessType) {
        CompanyExtraInfo companyExtraInfo = tableCompanyExtraInfoService.selectCompanyExtraInfoList(foreignId, businessType);
        return BeanUtil.copyProperties(companyExtraInfo, CompanyExtraInfoDto.class);
    }
}
