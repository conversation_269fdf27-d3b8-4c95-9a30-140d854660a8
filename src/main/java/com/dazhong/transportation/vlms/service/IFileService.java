package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.dto.response.DownloadFileInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.enums.ExportFileTypeEnum;
import com.dazhong.transportation.vlms.utils.export.Exportable;

import java.io.InputStream;

public interface IFileService {


    /**
     * 导出文件
     * @param exportFileTypeEnum  导出文件类型
     * @param templateStream 模板文件流
     * @param tokenUserInfo 用户信息
     * @param exportable 具体导出处理逻辑
     */
    void export(ExportFileTypeEnum exportFileTypeEnum, InputStream templateStream, TokenUserInfo tokenUserInfo, Exportable exportable);

    /**
     * 查询下载文件列表
     * @param request 查询条件
     * @param tokenUserInfo 用户信息
     * @return 用户下载文件列表
     */
    PageResponse<DownloadFileInfoResponse> searchDownloadFileList(PageRequest request, TokenUserInfo tokenUserInfo);
}
