package com.dazhong.transportation.vlms.service.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.database.TableVehicleDeviceInfoService;
import com.dazhong.transportation.vlms.database.TableVehicleLocationCanService;
import com.dazhong.transportation.vlms.dto.VehicleLocationCanInfoDto;
import com.dazhong.transportation.vlms.dto.VehicleTerminalInfoDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.VehicleDeviceInfo;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class VehicleLocationCanServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableVehicleDeviceInfoService tableVehicleDeviceInfoService;


    @Override
    public ResultResponse handleSyncData(SyncDataRequest request) {
        //请求内容
        String requestBody = request.getBizRequest();
        if(StringUtils.isBlank(requestBody)){
            return ResultResponse.businessFailed("同步车辆位置、CAN数据参数异常，消息体内容缺失");
        }

        //解析请求内容
        List<VehicleLocationCanInfoDto> vehicleLocationCanInfoDtoList;
        try{
            vehicleLocationCanInfoDtoList = JSONObject.parseArray(requestBody, VehicleLocationCanInfoDto.class);
        }catch (Exception e){
            log.error("同步车辆位置、CAN数据参数异常，解析消息体内容失败:{}，内容：{}",e.getLocalizedMessage(), requestBody , e);
            return ResultResponse.businessFailed("同步车辆位置、CAN数据参数异常，解析消息体内容失败");
        }

        //遍历循环处理请求信息
        for (VehicleLocationCanInfoDto vehicleLocationCanInfoDto : vehicleLocationCanInfoDtoList) {
            //校验参数内容
            if(!checkParamsPass(vehicleLocationCanInfoDto)){
                log.error("同步车辆位置、CAN数据参数异常，略过同步，内容：{}", JSON.toJSONString(vehicleLocationCanInfoDto));
                continue;
            }

            //判断设备信息是否存在
            VehicleDeviceInfo deviceInfo = tableVehicleDeviceInfoService.selectByDeviceSeq(vehicleLocationCanInfoDto.getTerminalNo());
            if(deviceInfo == null){
                log.error("同步车辆位置、CAN数据参数异常，未查询到终端信息：{}", vehicleLocationCanInfoDto.getTerminalNo());
            }
            //更新终端最新信息
            updateDeviceCanData(deviceInfo, vehicleLocationCanInfoDto);

        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        return null;
    }

    /**
     * 更新设备CAN信息
     * @param deviceInfo 设备信息
     * @param vehicleLocationCanInfoDto
     */
    private void updateDeviceCanData(VehicleDeviceInfo deviceInfo, VehicleLocationCanInfoDto vehicleLocationCanInfoDto){
        try{
            VehicleDeviceInfo updateVehicleDeviceInfo = new VehicleDeviceInfo();
            updateVehicleDeviceInfo.setId(deviceInfo.getId());
            //维度
            String latitude = vehicleLocationCanInfoDto.getLatitude();
            updateVehicleDeviceInfo.setLatitude(new BigDecimal(latitude));
            //经度
            String longitude = vehicleLocationCanInfoDto.getLongitude();
            updateVehicleDeviceInfo.setLongitude(new BigDecimal(longitude));
            //里程数
            updateVehicleDeviceInfo.setTotalMileage(vehicleLocationCanInfoDto.getMileage());
            //TODO 油量百分比  等具体联调商定
            //TODO 电量百分比 等具体联调商定
            //采集时间
            updateVehicleDeviceInfo.setLatestLocationTime(parseDate(vehicleLocationCanInfoDto.getCollectionTime()));
            tableVehicleDeviceInfoService.updateSelectiveById(updateVehicleDeviceInfo, "system");

        }catch (Exception e){
            //预防可能出现的字段转换错误
            log.error("同步车辆位置、CAN数据参数异常：{}，内容：{}", e.getMessage(), JSON.toJSONString(vehicleLocationCanInfoDto), e );
        }
    }

    /**
     * 检查参数是否通过
     * @param vehicleLocationCanInfoDto 请求参数信息
     * @return true:校验通过 false:校验不通过
     */
    public boolean checkParamsPass(VehicleLocationCanInfoDto vehicleLocationCanInfoDto){
        if(StringUtils.isBlank(vehicleLocationCanInfoDto.getTerminalNo())){
            return false;
        }
        if(StringUtils.isBlank(vehicleLocationCanInfoDto.getLatitude())){
            return false;
        }
        if(StringUtils.isBlank(vehicleLocationCanInfoDto.getLongitude())){
            return false;
        }

        return true;
    }

    /**
     * 将字符串类型转换成时间格式
     * @param value 字符串类型
     * @return 转换后的时间格式
     */
    private Date parseDate(String value){
        if(StringUtils.isBlank(value)){
            return null;
        }
        Date result = null;
        try{
            result = DateTimeUtils.stringToDate(value, DateTimeUtils.DATE_TYPE1);
        }catch (Exception e){
            e.printStackTrace();
        }

        return result;
    }
}
