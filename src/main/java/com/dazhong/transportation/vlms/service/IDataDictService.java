package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.DataOwnerDto;
import com.dazhong.transportation.vlms.dto.DataSupplierDto;
import com.dazhong.transportation.vlms.dto.DatabaseDictSyncDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.DeleteDataDictRequest;
import com.dazhong.transportation.vlms.dto.request.EditDataDictRequest;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.dto.response.DataDictResponse;
import com.dazhong.transportation.vlms.dto.response.DataMaintainDictResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.DataDictInfo;
import com.dazhong.transportation.vlms.model.DataOwnerInfo;
import com.dazhong.transportation.vlms.model.OrgInfo;

import java.util.List;
import java.util.Map;

/**
 * 数据字典服务
 * <AUTHOR>
 * @date 2024-12-27 16:26
 */
public interface IDataDictService {

    /**
     * 查询列表
     * @param request
     * @return
     */
    List<DataDictInfo> queryDataDictList(PageRequest request);


    /**
     * 根据code查询明细
     * @param dataCode
     * @return
     */
    DataDictResponse queryDataDictByCode(String dataCode);


    /**
     * 新增数据字典
     * @param request
     * @return
     */
    ResultResponse addDataDict(EditDataDictRequest<String> request);

    /**
     * 修改数据字典
     * @param request
     * @return
     */
    ResultResponse updateDataDict(EditDataDictRequest<String> request);

    /**
     * 删除字典
     * @param request
     * @return
     */
    ResultResponse deleteDataDict(DeleteDataDictRequest request);

    /**
     * 查询字典维护数据列表
     * @param systemCode 系统编码
     * @return 数据字典配置信息
     */
    DataDictResponse<Integer> queryDataMaintainDict(String systemCode);

    /**
     * 新增字典维护数据
     * @param request 数据字典配置信息
     * @param tokenUserInfo 用户信息
     */
    void addDataMaintainDict(EditDataDictRequest<Integer> request, TokenUserInfo tokenUserInfo);

    /**
     * 修改字典维护数据
     * @param request 数据字典配置信息
     * @param tokenUserInfo 用户信息
     */
    void updateDataMaintainDict(EditDataDictRequest<Integer> request, TokenUserInfo tokenUserInfo);


    /**
     * 根据系统编码批量查询字典信息
     * @param systemCodeList 系统编码列表
     * @return 字典信息 KEY:系统编码   VALUE: key: 值 value：名称
     */
    Map<String, Map<Integer, String>> getDataMaintainDictMap(List<String> systemCodeList);

    /**
     * 根据系统编码批量查询字典value key
     * @param systemCodeList 系统编码列表
     * @return 字典信息 KEY:系统编码   VALUE: key: 值 value：名称
     */
    Map<String, Map<String, Integer>> getDataMaintainDictValueMap(List<String> systemCodeList);

    /**
     * 查询供应商列表
     * @return 供应商列表
     */
    DataMaintainDictResponse<DataSupplierDto> querySupplierList();

    /**
     * 新增供应商信息
     * @param request 供应商信息
     * @param tokenUserInfo 用户信息
     */
    void createSupplierInfo(DataMaintainDictResponse<DataSupplierDto> request, TokenUserInfo tokenUserInfo);

    /**
     * 修改供应商信息
     * @param request 供应商信息
     * @param tokenUserInfo 用户信息
     */
    void updateSupplierInfo(DataMaintainDictResponse<DataSupplierDto> request, TokenUserInfo tokenUserInfo);

    /**
     * 查询资产车辆拥有公司Map
     * @return key:ID value:资产车辆拥有公司信息
     */
    Map<Integer, DataOwnerInfo> getOwnerMap();

    /**
     * 查询机构所有人Map
     * @return key:ID value:资产所有人信息
     */
    Map<Long, OrgInfo> getOrgMap();

    /**
     * 查询数据车辆拥有公司列表
     * @return 数据车辆拥有公司列表
     */
    DataMaintainDictResponse<DataOwnerDto> queryOwnerList();

    /**
     * 新增数据车辆拥有公司信息
     * @param request 车辆拥有公司信息
     * @param tokenUserInfo 用户信息
     */
    void createOwnerInfo(DataMaintainDictResponse<DataOwnerDto> request, TokenUserInfo tokenUserInfo);

    /**
     * 修改数据车辆拥有公司信息
     * @param request 车辆拥有公司信息
     * @param tokenUserInfo 用户信息
     */
    void updateOwnerInfo(DataMaintainDictResponse<DataOwnerDto> request, TokenUserInfo tokenUserInfo);

    /**
     * 查询数据库表同步信息
     * @param systemCode 系统编码
     * @return 字典表同步信息
     */
    List<DatabaseDictSyncDto> queryMaintainDictSyncData(String systemCode);

    /**
     * 查询资产车辆拥有公司同步数据
     * @return 车辆拥有公司同步数据列表
     */
    List<DatabaseDictSyncDto> queryOwnerSyncData();

    /**
     * 查询供应商同步数据
     * @return 供应商同步数据列表
     */
    List<DatabaseDictSyncDto> querySupplierSyncData();

    /**
     * 查询机构同步数据
     * @return 机构同步数据
     */
    List<DatabaseDictSyncDto> queryOrgSyncData();

    /**
     * 查询区域同步数据
     * @return 区域同步数据
     */
    List<DatabaseDictSyncDto> queryAreaSyncData();
}
