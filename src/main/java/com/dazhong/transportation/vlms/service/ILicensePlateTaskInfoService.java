package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.LicensePlateTaskQuotaDetailDto;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateTaskInfoRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.ImportRevocationVehicleResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateTaskDetailResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateTaskInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportLicensePlateQuota;
import com.dazhong.transportation.vlms.excel.ImportLicensePlateTaskVehicle;

import java.util.List;

public interface ILicensePlateTaskInfoService {

    /**
     * 查询车牌任务分页列表
     *
     * @param searchLicensePlateTaskInfoRequest 查询车牌任务请求
     * @return 返回符合条件的车牌任务分页列表
     */
    PageResponse<LicensePlateTaskInfoResponse> queryPageResponse(SearchLicensePlateTaskInfoRequest searchLicensePlateTaskInfoRequest);

    /**
     * 创建上牌任务
     *
     * @param saveLicensePlateTaskInfoRequest 创建上牌任务请求
     * @param tokenUserInfo                   用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> saveAssignmentTask(SaveLicensePlateTaskInfoRequest saveLicensePlateTaskInfoRequest, TokenUserInfo tokenUserInfo);

    /**
     * 创建退牌任务
     *
     * @param saveLicensePlateTaskInfoRequest 创建退牌任务请求
     * @param tokenUserInfo                   用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> saveRevocationTask(SaveLicensePlateTaskInfoRequest saveLicensePlateTaskInfoRequest, TokenUserInfo tokenUserInfo);

    /**
     * 根据任务编号查询任务详情
     *
     * @param taskNumber 任务编号
     * @return 返回符合条件的车牌任务列表
     */
    LicensePlateTaskDetailResponse getDetailByTaskNumber(String taskNumber);

    /**
     * 根据任务编号查询车牌号列表
     *
     * @param taskNumber 任务编号
     * @return 返回符合条件的车牌号列表
     */
    List<String> getLicensePlateListByTaskNumber(String taskNumber);

    /**
     * 根据任务编号查询车牌号列表
     *
     * @param taskNumber 任务编号
     * @return 返回符合条件的车牌号列表
     */
    List<LicensePlateTaskQuotaDetailDto> getLicensePlateTaskQuotaDetailsByTaskNumber(String taskNumber);

    /**
     * 获取批量导入上牌任务车辆信息
     *
     * @param request 导入文件信息入参
     * @return 返回符合条件的车牌号列表
     */
    List<ImportLicensePlateTaskVehicle> getImportLicensePlateTaskVehicleList(BaseImportFileUrlRequest request);

    /**
     * 获取批量导入额度单信息
     *
     * @param request 导入文件信息入参
     * @return 返回符合条件的车牌号列表
     */
    List<ImportLicensePlateQuota> getImportLicensePlateQuotaList(BaseImportFileUrlRequest request);
    
    /**
     * 获取批量导入退牌任务车辆信息
     *
     * @param request 导入文件信息入参
     * @return 返回符合条件的车牌号列表
     */
    ImportRevocationVehicleResponse getImportRevocationVehicleDetail(BaseImportFileUrlRequest request);

    /**
     * 查询车牌任务列表
     *
     * @param vin      车架号
     * @param taskType 任务类型 1-上牌任务 2-退牌任务
     * @return 返回符合条件的车牌任务列表
     */
    List<LicensePlateTaskVehicleDetailDto> queryLicensePlateTaskList(String vin, Integer taskType);

    /**
     * 导出车牌任务列表
     * @param request 查询条件
     * @param tokenUserInfo 用户信息
     */
    void exportTaskList(SearchLicensePlateTaskInfoRequest request, TokenUserInfo tokenUserInfo);

}
