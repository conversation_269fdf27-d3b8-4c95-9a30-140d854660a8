package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableOperateLogService;
import com.dazhong.transportation.vlms.database.TableOrgInfoService;
import com.dazhong.transportation.vlms.database.TableUserOrgService;
import com.dazhong.transportation.vlms.database.TableUserService;
import com.dazhong.transportation.vlms.dto.CompanyExtraInfoDto;
import com.dazhong.transportation.vlms.dto.DataDictDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveManagerRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateCompanyExtraInfoRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateOrgInfoRequest;
import com.dazhong.transportation.vlms.dto.response.DataDictResponse;
import com.dazhong.transportation.vlms.dto.response.OrgDetailsResponse;
import com.dazhong.transportation.vlms.dto.response.OrgListResponse;
import com.dazhong.transportation.vlms.dto.response.OrgTreeResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.DataDictEnum;
import com.dazhong.transportation.vlms.excel.ImportOrgInfo;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.OperateLog;
import com.dazhong.transportation.vlms.model.OrgInfo;
import com.dazhong.transportation.vlms.model.UserInfo;
import com.dazhong.transportation.vlms.model.UserOrgInfo;
import com.dazhong.transportation.vlms.service.ICompanyExtraInfoService;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.service.IOrgService;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Slf4j
@Service
public class OrgServiceImpl implements IOrgService {

    @Autowired
    private TableUserService tableUserService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableUserOrgService tableUserOrgService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Resource
    private IDataDictService dataDictService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ICompanyExtraInfoService companyExtraInfoService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;


    @Override
    public ResultResponse queryAllOrgTree() {
        List<OrgInfo> list = tableOrgInfoService.queryAllOrgInfo();
        if (CollectionUtil.isEmpty(list)) {
            return ResultResponse.success();
        }
        List<OrgTreeResponse> treeListResponse = CommonUtils.getOrgTree(list);
        return ResultResponse.success(treeListResponse);
    }

    @Override
    public ResultResponse queryEnableStatusOrgTree() {
        List<OrgInfo> list = tableOrgInfoService.queryOrgInfoByCheckedState(1);
        if (CollectionUtil.isEmpty(list)) {
            return ResultResponse.success();
        }
        List<OrgTreeResponse> treeListResponse = CommonUtils.getOrgTree(list);
        return ResultResponse.success(treeListResponse);
    }

    @Override
    public List<OrgListResponse> queryUserOrgList(Long userId) {
        UserInfo userInfo = tableUserService.selectById(userId);
        if (userInfo == null){
            throw new ServiceException("用户不存在");
        }
        // 是否超管 1-是 2-否
        int isSystemAdmin = userInfo.getIsSystemAdmin();
        if (isSystemAdmin == BizConstant.SystemAdminType.isAdministrator){
            List<OrgInfo> list = tableOrgInfoService.queryAllOrgInfo();
            List<OrgListResponse> orgList = BeanUtil.copyToList(list, OrgListResponse.class);
            return orgList;
        }
        /* 非系统管理员 */
        List<Long> orgIdList = tableUserOrgService.queryOrgIdList(userInfo.getId(),1);
        if (CollectionUtil.isEmpty(orgIdList)){
            return Collections.emptyList();
        }
        List<OrgInfo> list = tableOrgInfoService.queryOrgListByIds(orgIdList);
        List<OrgListResponse> orgList = BeanUtil.copyToList(list, OrgListResponse.class);
        return orgList;
    }

    @Override
    public ResultResponse queryUserOrgTree(Long userId) {
        UserInfo userInfo = tableUserService.selectById(userId);
        if (userInfo == null){
            throw new ServiceException("系统用户不存在");
        }
        // 是否超管 1-是 2-否
        int isSystemAdmin = userInfo.getIsSystemAdmin();
        if (isSystemAdmin == BizConstant.SystemAdminType.isAdministrator){
            List<OrgInfo> list = tableOrgInfoService.queryAllOrgInfo();
            List<OrgTreeResponse> treeListResponse = CommonUtils.getOrgTree(list);
            return ResultResponse.success(treeListResponse);
        }
        List<Long> orgIdList = tableUserOrgService.queryOrgIdList(userId,1);
        if (CollectionUtil.isEmpty(orgIdList)){
            return ResultResponse.success();
        }
        List<OrgInfo> orgList = tableOrgInfoService.queryOrgListByIds(orgIdList);
        if (CollectionUtil.isEmpty(orgList)) {
            return ResultResponse.success();
        }
        // 找到最小的 companyCode 长度
        int minLength = orgList.stream()
                .mapToInt(orgInfo -> orgInfo.getCompanyCode().length())
                .min().orElse(0);
        // 筛选出 orgCode 长度等于最小长度的对象
        List<OrgInfo> minLengthOrgList = orgList.stream()
                .filter(orgInfo -> orgInfo.getCompanyCode().length() == minLength)
                .collect(Collectors.toList());
        // 构建组织树
        List<OrgTreeResponse> orgTreeList = new ArrayList<>();
        for (OrgInfo orgInfo : minLengthOrgList) {
            String code = orgInfo.getCompanyCode();
            orgList.forEach(info -> {
                if (StringUtils.equals(info.getCompanyCode(), code)){
                    OrgTreeResponse treeResponse = new OrgTreeResponse();
                    treeResponse.setId(orgInfo.getId());
                    treeResponse.setCompanyName(orgInfo.getCompanyName());
                    orgTreeList.add(treeResponse);
                    treeResponse.setChildren(CommonUtils.buildOrgTree(orgList, orgInfo.getCompanyCode()));
                }
            });
        }
        return ResultResponse.success(orgTreeList);
    }

    @Override
    public DataDictResponse<Integer> queryUserOwnerList(Long userId) {
        UserInfo userInfo = tableUserService.selectById(userId);
        if (userInfo == null){
            throw new ServiceException("系统用户不存在");
        }
        DataDictResponse<Integer> ownerDict = dataDictService.queryDataMaintainDict(DataDictEnum.OWNER.getValue());
        List<DataDictDto<Integer>> dataDictList = ownerDict.getDataDictList();
        // 是否超管 1-是 2-否
        int isSystemAdmin = userInfo.getIsSystemAdmin();
        if (isSystemAdmin == BizConstant.SystemAdminType.nonAdministrator){
            List<Long> ownerIdList = tableUserOrgService.queryOrgIdList(userId,2);
            // long类型转换为int类型
            List<Integer> intList = ownerIdList.stream()
                    .map(Long::intValue)
                    .collect(Collectors.toList());
            // dataDictList 里筛选 intList数据
            dataDictList = dataDictList.stream()
                    .filter(dataDictDto -> intList.contains(dataDictDto.getValue()))
                    .collect(Collectors.toList());
            ownerDict.setDataDictList(dataDictList);
        }
        return ownerDict;
    }

    @Override
    public ResultResponse saveManagerInfo(SaveManagerRequest request, TokenUserInfo tokenUserInfo) {
        OrgInfo orgInfo = tableOrgInfoService.queryOrgInfoById(request.getId());
        if (orgInfo == null){
            throw new ServiceException("机构不存在");
        }
        UpdateCompanyExtraInfoRequest infoRequest = new UpdateCompanyExtraInfoRequest();
        infoRequest.setForeignId(orgInfo.getId());
        infoRequest.setBusinessType(1);
        infoRequest.setCeoName(request.getGeneralManager());
        infoRequest.setCeoPhone(request.getGeneralPhone());
        infoRequest.setDingTalkNo(request.getDingTalkNum());
        companyExtraInfoService.updateCompanyExtraInfo(infoRequest,tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    public ResultResponse queryOrgDetails(Long orgId) {
        OrgInfo orgInfo = tableOrgInfoService.queryOrgInfoById(orgId);
        if (orgInfo == null){
            throw new ServiceException("机构不存在");
        }
        // 查询机构管理者信息
        OrgDetailsResponse orgDetailsResponse = new OrgDetailsResponse();
        orgDetailsResponse.setId(orgInfo.getId());
        orgDetailsResponse.setCompanyName(orgInfo.getCompanyName());
        orgDetailsResponse.setDisabledState(orgInfo.getIsDeleted() == 1 ? 1 : 2);
        // 公司补充信息
        CompanyExtraInfoDto companyExtraInfo = companyExtraInfoService.selectCompanyExtraInfo(orgInfo.getId(), 1);
        if (companyExtraInfo != null){
            orgDetailsResponse.setGeneralManager(companyExtraInfo.getCeoName());
            orgDetailsResponse.setDingTalkNum(companyExtraInfo.getDingTalkNo());
            orgDetailsResponse.setGeneralPhone(companyExtraInfo.getCeoPhone());
        }
        return ResultResponse.success(orgDetailsResponse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse updateOrgInfo(UpdateOrgInfoRequest request, TokenUserInfo tokenUserInfo) {
        List<Long> orgIdList = request.getOrgIdList();
        List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();
        List<OperateLog> logs = new ArrayList<>();
        // 选中的机构信息
        List<OrgInfo> selectedOrgList = orgInfoList.stream()
                .filter(orgInfo -> orgIdList.contains(orgInfo.getId()))
                .collect(Collectors.toList());
        for (OrgInfo orgInfo : selectedOrgList) {
            // 是否选中 1-是 2-否
            int checkedState = orgInfo.getCheckedState();
            if (checkedState == 1){
                continue;
            }
            orgInfo.setCheckedState(1);
            tableOrgInfoService.updateOrgInfo(orgInfo);
            // 日志信息
            OperateLog log = new OperateLog();
            log.setForeignId(tokenUserInfo.getUserId());
            log.setBusinessType(BizConstant.BusinessType.businessType_2);
            log.setOperateType(BizConstant.CommonStatusStatus.enable);
            log.setOperateContent(StrUtil.format("启用组织架构-{}",orgInfo.getCompanyName()));
            log.setCreateOperId(tokenUserInfo.getUserId());
            log.setCreateOperName(tokenUserInfo.getName());
            logs.add(log);
        }

        // 未选中的机构信息
        List<OrgInfo> unselectedOrgList = orgInfoList.stream()
                .filter(orgInfo -> !orgIdList.contains(orgInfo.getId()))
                .collect(Collectors.toList());
        for (OrgInfo orgInfo : unselectedOrgList) {
            // 是否选中 1-是 2-否
            int checkedState = orgInfo.getCheckedState();
            if (checkedState == 2){
                continue;
            }
            orgInfo.setCheckedState(2);
            tableOrgInfoService.updateOrgInfo(orgInfo);
            // 日志信息
            OperateLog log = new OperateLog();
            log.setForeignId(tokenUserInfo.getUserId());
            log.setBusinessType(BizConstant.BusinessType.businessType_2);
            log.setOperateType(BizConstant.CommonStatusStatus.enable);
            log.setOperateContent(StrUtil.format("禁用组织架构-{}",orgInfo.getCompanyName()));
            logs.add(log);
        }
        tableOperateLogService.batchInsertLog(logs,tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse importOrgInfo() {
        List<ImportOrgInfo> readList = ExcelUtil.readLocal("C:\\Users\\<USER>\\Desktop\\大众交通\\组织结构.xlsx", 0, ImportOrgInfo.class);
        // readList 按照 orgType 进行过滤 获取公司列表数据
        List<ImportOrgInfo> companyList = readList.stream()
                .filter(importOrgInfo -> importOrgInfo.getOrgType() == 0)
                .collect(Collectors.toList());
        // companyList 获取id重复的数据
        List<ImportOrgInfo> repeatList = companyList.stream()
                .filter(importOrgInfo -> companyList.stream().filter(importOrgInfo1 -> importOrgInfo1.getId().equals(importOrgInfo.getId())).count() > 1)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(repeatList)){
            throw new ServiceException("导入数据有重复");
        }
        List<OrgInfo> list = new ArrayList<>();
        Map<String, ImportOrgInfo> orgMap = companyList.stream().collect(Collectors.toMap(ImportOrgInfo::getVgUid, importOrgInfo -> importOrgInfo));
        tableOrgInfoService.deleteOrgInfo();
        for (ImportOrgInfo importOrg : companyList) {
            OrgInfo orgInfo = new OrgInfo();
            orgInfo.setId(importOrg.getId());
            orgInfo.setCompanyCode(importOrg.getOrgCode());
            orgInfo.setCompanyName(importOrg.getOrgName());
            orgInfo.setVgUid(importOrg.getVgUid());
            orgInfo.setPgUid(importOrg.getPgUid());
            orgInfo.setOrgType("公司");
            // 状态 2-启用 1-停用
            orgInfo.setIsDeleted(1);
            if (importOrg.getVStatus() == 2){
                orgInfo.setIsDeleted(0);
                orgInfo.setCompanyName(importOrg.getOrgName());
            }
            orgInfo.setPosition(Math.toIntExact(importOrg.getId()));
            // 父节点id
            String pgUid = importOrg.getPgUid();
            if (StringUtils.isNotBlank(pgUid)){
                ImportOrgInfo parentOrg = orgMap.get(pgUid);
                if (parentOrg != null){
                    orgInfo.setParentId(parentOrg.getId());
                }
            }
            list.add(orgInfo);
        }
        tableOrgInfoService.batchInsertOrgInfo(list);
        return ResultResponse.success();
    }

    @Override
    public Map<Long, String> getOrgNameMap() {
        Map<Long, String> result = new HashMap<>();
        List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();
        if(orgInfoList == null || orgInfoList.isEmpty()){
            return result;
        }
        for (OrgInfo orgInfo : orgInfoList) {
            result.put(orgInfo.getId(), orgInfo.getCompanyName());
        }
        return result;
    }

    @Override
    public List<UserOrgInfo> queryUserOrgInfo(Long userId) {
        return tableUserOrgService.queryUserOrgInfoByUserId(userId);
    }

    @Override
    public void syncOrganization() {
        // 同步数据到维修saas系统
        taskExecutor.execute(() -> {
//            List<OrgInfo> orgInfos = tableOrgInfoService.queryAllOrgInfo();
            List<OrgInfo> orgInfos = tableOrgInfoService.queryOrgInfoByCheckedState(1);
            if (CollectionUtil.isNotEmpty(orgInfos)) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.set("Content-Type", "application/json");
                List<Map<String, Object>> mapList = orgInfos.stream()
                        .map(orgInfo -> {
                            Map<String, Object> map = new HashMap<>();
                            map.put("uniqueId", orgInfo.getId().toString());
                            map.put("orgId", orgInfo.getCompanyCode());
                            map.put("orgName", orgInfo.getCompanyName());
                            map.put("parentId", String.valueOf(orgInfo.getParentId()));
                            return map;
                        })
                        .collect(Collectors.toList());
                Map<String, Object> params = new HashMap<>();
                params.put("syncKey", Global.instance.saasSyncKey);
                params.put("batchData", mapList);
                System.out.println(JSONUtil.toJsonStr(params));
//                log.info("组织架构同步参数：{}", JSONUtil.toJsonStr(params));
                HttpEntity<String> httpEntity = new HttpEntity(JSONUtil.toJsonStr(params), headers);
                ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(Global.instance.saasOrgSyncUrl, httpEntity, JSONObject.class);
                log.info("组织架构同步结果：{}", JSONUtil.toJsonStr(responseEntity));
            }
        });
    }
}
