package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.dazhong.transportation.vlms.database.TableDataOwnerInfoService;
import com.dazhong.transportation.vlms.database.TableLicensePlateQuotaOperateLogService;
import com.dazhong.transportation.vlms.database.TableLicensePlateQuotaService;
import com.dazhong.transportation.vlms.database.TableOrgInfoService;
import com.dazhong.transportation.vlms.dto.LicensePlateQuotaDto;
import com.dazhong.transportation.vlms.dto.LicensePlateQuotaOperateLogDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.UpdateQuotaDto;
import com.dazhong.transportation.vlms.dto.request.AdjustTotalQuotaRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaOperateLogRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaRequest;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.QuotaTypeEnum;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.DataOwnerInfo;
import com.dazhong.transportation.vlms.model.LicensePlateQuota;
import com.dazhong.transportation.vlms.model.LicensePlateQuotaOperateLog;
import com.dazhong.transportation.vlms.service.ILicensePlateQuotaService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LicensePlateQuotaServiceImpl implements ILicensePlateQuotaService {

    @Autowired
    private TableLicensePlateQuotaService tableLicensePlateQuotaService;

    @Autowired
    private TableLicensePlateQuotaOperateLogService tableLicensePlateQuotaOperateLogService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableDataOwnerInfoService tableDataOwnerInfoService;



    @Override
    public PageResponse<LicensePlateQuotaDto> queryPageResponse(SearchLicensePlateQuotaRequest request) {
        // 查询车牌额度一览信息列表并创建分页信息对象
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        if (request.getIsCompanyShowQuota() == 1) {
            List<LicensePlateQuota> licensePlateQuotaList = tableLicensePlateQuotaService.queryList(request);
            PageInfo<LicensePlateQuota> pageInfo = new PageInfo<>(licensePlateQuotaList);

            // 将查询结果转换为响应对象列表
            List<LicensePlateQuotaDto> list = new LinkedList<>();
            licensePlateQuotaList.forEach(vehicleQuota -> {
                LicensePlateQuotaDto response = BeanUtil.copyProperties(vehicleQuota, LicensePlateQuotaDto.class);
                DataOwnerInfo dataOwnerInfo = tableDataOwnerInfoService.queryOwnerInfoById(vehicleQuota.getAssetCompanyId());
                if (dataOwnerInfo != null) {
                    response.setAssetCompanyName(dataOwnerInfo.getName());
                }
                list.add(response);
            });

            // 返回分页响应对象
            return new PageResponse<>(pageInfo.getTotal(), list);
        } else {
            List<LicensePlateQuota> licensePlateQuotaList = tableLicensePlateQuotaService.queryListGroupByQuotaType(request);
            PageInfo<LicensePlateQuota> pageInfo = new PageInfo<>(licensePlateQuotaList);
            List<LicensePlateQuotaDto> list = BeanUtil.copyToList(licensePlateQuotaList, LicensePlateQuotaDto.class);

            // 返回分页响应对象
            return new PageResponse<>(pageInfo.getTotal(), list);
        }
    }

    @Override
    public LicensePlateQuotaResponse queryTotalQuota(SearchLicensePlateQuotaRequest searchLicensePlateQuotaRequest) {
        return tableLicensePlateQuotaService.selectLicensePlateQuotaResponse(searchLicensePlateQuotaRequest);
    }


    @Override
    public ResultResponse<Void> adjustTotalQuota(AdjustTotalQuotaRequest adjustTotalQuotaRequest, TokenUserInfo tokenUserInfo) {
        // 总数增加 & 总数减少
        if (adjustTotalQuotaRequest.getAdjustType() == 1 || adjustTotalQuotaRequest.getAdjustType() == 2) {
            return updateTotalQuota(adjustTotalQuotaRequest, tokenUserInfo);
        }
        // 额度转移
        if (adjustTotalQuotaRequest.getAdjustType() == 3) {
            return transferQuota(adjustTotalQuotaRequest, tokenUserInfo);
        }
        return null;
    }

    @Override
    public PageResponse<LicensePlateQuotaOperateLogDto> queryOperateLogPageResponse(SearchLicensePlateQuotaOperateLogRequest request) {
        // 查询车牌额度操作日志信息列表并创建分页信息对象
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<LicensePlateQuotaOperateLog> licensePlateQuotaOperateLogList = tableLicensePlateQuotaOperateLogService.searchOperateLog(request);
        PageInfo<LicensePlateQuotaOperateLog> pageInfo = new PageInfo<>(licensePlateQuotaOperateLogList);

        // 将查询结果转换为响应对象列表
        List<LicensePlateQuotaOperateLogDto> licensePlateQuotaResponseList = new ArrayList<>();
        for (LicensePlateQuotaOperateLog licensePlateQuotaOperateLog : licensePlateQuotaOperateLogList) {
            LicensePlateQuotaOperateLogDto dto = BeanUtil.copyProperties(licensePlateQuotaOperateLog, LicensePlateQuotaOperateLogDto.class);
            dto.setAdjustType(licensePlateQuotaOperateLog.getAdjustType().toString());
            licensePlateQuotaResponseList.add(dto);
        }

        // 返回分页响应对象
        return new PageResponse<>(pageInfo.getTotal(), licensePlateQuotaResponseList);
    }

    @Override
    public List<DataOwnerInfo> queryOwnerInfoListByQuotaType(Integer quotaType) {
        List<DataOwnerInfo> result = new ArrayList<>();

        List<LicensePlateQuota> licensePlateQuotaList = tableLicensePlateQuotaService.selectByQuotaType(quotaType);
        if (CollectionUtil.isNotEmpty(licensePlateQuotaList)) {
            List<Long> assetCompanyIdList = licensePlateQuotaList.stream().map(licensePlateQuota -> licensePlateQuota.getAssetCompanyId().longValue()).collect(Collectors.toList());
            List<DataOwnerInfo> ownerInfoList = tableDataOwnerInfoService.queryOwnerInfoList(assetCompanyIdList);
            for (Long assetCompanyId : assetCompanyIdList) {
                ownerInfoList.stream().filter(ownerInfo -> ownerInfo.getId().equals(assetCompanyId)).findFirst().ifPresent(result::add);
            }
        }

        return result;
    }

    @Override
    public void updateQuota(UpdateQuotaDto updateQuotaDTO, TokenUserInfo tokenUserInfo, boolean doCheck) {
        if (updateQuotaDTO.getAssetCompanyId() == null ||
                updateQuotaDTO.getAdjustNum() == null){
            return;
        }
        LicensePlateQuota licensePlateQuota = tableLicensePlateQuotaService.selectByQuotaTypeAndAssetCompanyCode(updateQuotaDTO.getQuotaType(), updateQuotaDTO.getAssetCompanyId());
        if (null == licensePlateQuota) {
            throw new ServiceException("额度不存在");
        }

        LicensePlateQuota updateLicensePlateQuota = new LicensePlateQuota();
        updateLicensePlateQuota.setId(licensePlateQuota.getId());
        updateLicensePlateQuota.setUpdateOperId(tokenUserInfo.getUserId());
        updateLicensePlateQuota.setUpdateOperName(tokenUserInfo.getName());

        // 占用增加
        if (updateQuotaDTO.getAdjustType() == 1) {
            // 预占用 + adjustNum
            if (updateQuotaDTO.getAdjustWay() == 1) {
                updateLicensePlateQuota.setPreOccupied(licensePlateQuota.getPreOccupied() + updateQuotaDTO.getAdjustNum());
            }
            // 占用 + adjustNum
            if (updateQuotaDTO.getAdjustWay() == 2) {
                if (doCheck) {
                    if (licensePlateQuota.getOccupied() + updateQuotaDTO.getAdjustNum() < 0) {
                        throw new ServiceException("占用，减至负数，无法操作！");
                    }
                    if (licensePlateQuota.getRemaining() - updateQuotaDTO.getAdjustNum() < 0) {
                        throw new ServiceException("剩余可用，减至负数，无法操作！");
                    }
                }
                updateLicensePlateQuota.setOccupied(licensePlateQuota.getOccupied() + updateQuotaDTO.getAdjustNum());
            }
            // 剩余可用 - adjustNum
            updateLicensePlateQuota.setRemaining(licensePlateQuota.getRemaining() - updateQuotaDTO.getAdjustNum());
            tableLicensePlateQuotaService.updateByPrimaryKeySelective(updateLicensePlateQuota);
        }

        // 占用减少
        if (updateQuotaDTO.getAdjustType() == 2) {
            // 预占用 - adjustNum
            if (updateQuotaDTO.getAdjustWay() == 1) {
                updateLicensePlateQuota.setPreOccupied(licensePlateQuota.getPreOccupied() - updateQuotaDTO.getAdjustNum());
            }
            // 占用 - adjustNum
            if (updateQuotaDTO.getAdjustWay() == 2) {
                updateLicensePlateQuota.setOccupied(licensePlateQuota.getOccupied() - updateQuotaDTO.getAdjustNum());
            }
            // 剩余可用 + adjustNum
            updateLicensePlateQuota.setRemaining(licensePlateQuota.getRemaining() + updateQuotaDTO.getAdjustNum());
            tableLicensePlateQuotaService.updateByPrimaryKeySelective(updateLicensePlateQuota);
        }
    }

    @Override
    public void notReturnQuota(UpdateQuotaDto updateQuotaDTO, TokenUserInfo tokenUserInfo) {
        LicensePlateQuota licensePlateQuota = tableLicensePlateQuotaService.selectByQuotaTypeAndAssetCompanyCode(updateQuotaDTO.getQuotaType(), updateQuotaDTO.getAssetCompanyId());
        if (null == licensePlateQuota) {
            throw new ServiceException("额度不存在");
        }

        LicensePlateQuota updateLicensePlateQuota = new LicensePlateQuota();
        updateLicensePlateQuota.setId(licensePlateQuota.getId());
        updateLicensePlateQuota.setUpdateOperId(tokenUserInfo.getUserId());
        updateLicensePlateQuota.setUpdateOperName(tokenUserInfo.getName());
        updateLicensePlateQuota.setQuota(licensePlateQuota.getQuota() - 1);
        updateLicensePlateQuota.setOccupied(licensePlateQuota.getOccupied() - 1);
        tableLicensePlateQuotaService.updateByPrimaryKeySelective(updateLicensePlateQuota);
    }

    /**
     * 总数增加 & 总数减少
     *
     * @param adjustLicensePlateQuotaRequest 修改额度请求
     * @param tokenUserInfo                  登录用户信息
     * @return 返回错误信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Void> updateTotalQuota(AdjustTotalQuotaRequest adjustLicensePlateQuotaRequest, TokenUserInfo tokenUserInfo) {
        LicensePlateQuota licensePlateQuota = tableLicensePlateQuotaService.selectByQuotaTypeAndAssetCompanyCode(adjustLicensePlateQuotaRequest.getQuotaType(), adjustLicensePlateQuotaRequest.getAssetCompanyId());

        LicensePlateQuota updateLicensePlateQuota = new LicensePlateQuota();
        updateLicensePlateQuota.setUpdateOperId(tokenUserInfo.getUserId());
        updateLicensePlateQuota.setUpdateOperName(tokenUserInfo.getName());

        // 总数增加
        if (adjustLicensePlateQuotaRequest.getAdjustType() == 1) {
            if (null == licensePlateQuota) {
                // 如果没有目标额度数据，则创建
                LicensePlateQuota newQuota = new LicensePlateQuota();
                newQuota.setQuotaType(adjustLicensePlateQuotaRequest.getQuotaType());
                newQuota.setAssetCompanyId(adjustLicensePlateQuotaRequest.getAssetCompanyId());
                newQuota.setCreateOperId(tokenUserInfo.getUserId());
                newQuota.setCreateOperName(tokenUserInfo.getName());
                newQuota.setQuota(adjustLicensePlateQuotaRequest.getAdjustNum());
                newQuota.setRemaining(adjustLicensePlateQuotaRequest.getAdjustNum());
                tableLicensePlateQuotaService.saveLicensePlateQuota(newQuota);
            } else {
                updateLicensePlateQuota.setId(licensePlateQuota.getId());
                updateLicensePlateQuota.setQuota(licensePlateQuota.getQuota() + adjustLicensePlateQuotaRequest.getAdjustNum());
                updateLicensePlateQuota.setRemaining(licensePlateQuota.getRemaining() + adjustLicensePlateQuotaRequest.getAdjustNum());
                tableLicensePlateQuotaService.updateByPrimaryKeySelective(updateLicensePlateQuota);
            }

            // 增加日志
            tableLicensePlateQuotaOperateLogService.insertLog(
                    new LicensePlateQuotaOperateLogDto(
                            adjustLicensePlateQuotaRequest.getAdjustType(),
                            adjustLicensePlateQuotaRequest.getAssetCompanyCode(),
                            tableDataOwnerInfoService.queryOwnerInfoById(adjustLicensePlateQuotaRequest.getAssetCompanyId()).getName(),
                            adjustLicensePlateQuotaRequest.getAdjustReason(),
                            "【" + QuotaTypeEnum.getDesc(adjustLicensePlateQuotaRequest.getQuotaType()) + "】" + "额度类型增加：" + adjustLicensePlateQuotaRequest.getAdjustNum() + "个"),
                    tokenUserInfo
            );
        }

        // 总数减少
        if (adjustLicensePlateQuotaRequest.getAdjustType() == 2) {
            if (null == licensePlateQuota) {
                throw new ServiceException("该额度类型不存在！");
            }
            if (licensePlateQuota.getQuota() - adjustLicensePlateQuotaRequest.getAdjustNum() < 0) {
                throw new ServiceException("额度扣减至负数，无法操作！");
            }
            updateLicensePlateQuota.setId(licensePlateQuota.getId());
            updateLicensePlateQuota.setQuota(licensePlateQuota.getQuota() - adjustLicensePlateQuotaRequest.getAdjustNum());
            updateLicensePlateQuota.setRemaining(licensePlateQuota.getRemaining() - adjustLicensePlateQuotaRequest.getAdjustNum());
            tableLicensePlateQuotaService.updateByPrimaryKeySelective(updateLicensePlateQuota);
            // 增加日志
            tableLicensePlateQuotaOperateLogService.insertLog(
                    new LicensePlateQuotaOperateLogDto(
                            adjustLicensePlateQuotaRequest.getAdjustType(),
                            adjustLicensePlateQuotaRequest.getAssetCompanyCode(),
                            tableDataOwnerInfoService.queryOwnerInfoById(adjustLicensePlateQuotaRequest.getAssetCompanyId()).getName(),
                            adjustLicensePlateQuotaRequest.getAdjustReason(),
                            "【" + QuotaTypeEnum.getDesc(adjustLicensePlateQuotaRequest.getQuotaType()) + "】" + "额度类型减少：" + adjustLicensePlateQuotaRequest.getAdjustNum() + "个"),
                    tokenUserInfo
            );
        }
        return (ResultResponse<Void>) ResultResponse.success();
    }

    /**
     * 转移额度
     *
     * @param adjustLicensePlateQuotaRequest 修改车辆额度入参
     * @return 返回操作结果，成功或失败信息
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse<Void> transferQuota(AdjustTotalQuotaRequest adjustLicensePlateQuotaRequest, TokenUserInfo tokenUserInfo) {
        // 查询源额度数据
        LicensePlateQuota transferFromVehicleQuota = tableLicensePlateQuotaService.selectByQuotaTypeAndAssetCompanyCode(adjustLicensePlateQuotaRequest.getQuotaType(), adjustLicensePlateQuotaRequest.getAssetCompanyId());
        if (null == transferFromVehicleQuota) {
            throw new ServiceException("源额度数据异常，无法转移额度！");
        }

        // 查询目标额度数据
        LicensePlateQuota transferToVehicleQuota = tableLicensePlateQuotaService.selectByQuotaTypeAndAssetCompanyCode(adjustLicensePlateQuotaRequest.getAdjustQuotaType(), adjustLicensePlateQuotaRequest.getAssetCompanyId());
        if (null == transferToVehicleQuota) {
            // 如果没有目标额度数据，则创建
            transferToVehicleQuota = new LicensePlateQuota();
            transferToVehicleQuota.setQuotaType(adjustLicensePlateQuotaRequest.getAdjustQuotaType());
            transferToVehicleQuota.setAssetCompanyId(adjustLicensePlateQuotaRequest.getAssetCompanyId());
            transferToVehicleQuota.setCreateOperId(tokenUserInfo.getUserId());
            transferToVehicleQuota.setCreateOperName(tokenUserInfo.getName());
            transferToVehicleQuota.setQuota(0);
            transferToVehicleQuota.setRemaining(0);
            tableLicensePlateQuotaService.saveLicensePlateQuota(transferToVehicleQuota);
        }

        // 源额度数据-总数减少
        if (transferFromVehicleQuota.getQuota() - adjustLicensePlateQuotaRequest.getAdjustNum() < 0) {
            throw new ServiceException("源额度扣减至负数，无法操作！");
        }
        transferFromVehicleQuota.setQuota(transferFromVehicleQuota.getQuota() - adjustLicensePlateQuotaRequest.getAdjustNum());
        transferFromVehicleQuota.setRemaining(transferFromVehicleQuota.getRemaining() - adjustLicensePlateQuotaRequest.getAdjustNum());
        transferFromVehicleQuota.setUpdateOperId(tokenUserInfo.getUserId());
        transferFromVehicleQuota.setUpdateOperName(tokenUserInfo.getName());
        tableLicensePlateQuotaService.updateByPrimaryKeySelective(transferFromVehicleQuota);

        // 目标额度数据-总数增加
        transferToVehicleQuota.setQuota(transferToVehicleQuota.getQuota() + adjustLicensePlateQuotaRequest.getAdjustNum());
        transferToVehicleQuota.setRemaining(transferToVehicleQuota.getRemaining() + adjustLicensePlateQuotaRequest.getAdjustNum());
        transferToVehicleQuota.setUpdateOperId(tokenUserInfo.getUserId());
        transferToVehicleQuota.setUpdateOperName(tokenUserInfo.getName());
        tableLicensePlateQuotaService.updateByPrimaryKeySelective(transferToVehicleQuota);

        // 增加日志
        tableLicensePlateQuotaOperateLogService.insertLog(
                new LicensePlateQuotaOperateLogDto(
                        adjustLicensePlateQuotaRequest.getAdjustType(),
                        adjustLicensePlateQuotaRequest.getAssetCompanyCode(),
                        tableDataOwnerInfoService.queryOwnerInfoById(adjustLicensePlateQuotaRequest.getAssetCompanyId()).getName(),
                        adjustLicensePlateQuotaRequest.getAdjustReason(),
                        "【" + QuotaTypeEnum.getDesc(adjustLicensePlateQuotaRequest.getQuotaType()) + "】" + "额度类型增加：" + adjustLicensePlateQuotaRequest.getAdjustNum() + "个，" +
                                "【" + QuotaTypeEnum.getDesc(adjustLicensePlateQuotaRequest.getAdjustQuotaType()) + "】" + "额度类型减少：" + adjustLicensePlateQuotaRequest.getAdjustNum() + "个"),
                tokenUserInfo
        );

        return (ResultResponse<Void>) ResultResponse.success();
    }
}
