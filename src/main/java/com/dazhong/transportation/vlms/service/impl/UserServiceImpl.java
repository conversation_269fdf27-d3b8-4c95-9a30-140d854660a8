package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.DaZhongUserInfo;
import com.dazhong.transportation.vlms.dto.SyncSsoUserDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkUserResponse;
import com.dazhong.transportation.vlms.dto.response.UserInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.IUserService;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class UserServiceImpl implements IUserService {

    @Autowired
    private TableUserService tableUserService;

    @Autowired
    private TableSsoUserService tableSsoUserService;

    @Autowired
    private TableRoleService tableRoleService;

    @Autowired
    private TableUserRoleService tableUserRoleService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private TableUserOrgService tableUserOrgService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private IDataDictService dataDictService;

    @Autowired
    private IDingTalkService dingTalkService;

    @Autowired
    public RedisUtils redisUtils;


    @Override
    public PageResponse queryUserList(SearchUserRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        PageResponse pageResponse = new PageResponse<>();
        List<Long> userIdList = new ArrayList<>();
        Long roleId = request.getRoleId();
        if (roleId != null) {
            // 根据角色查询用户ID
            List<UserRoleInfo> userRoleList = tableUserRoleService.queryUserRoleByRoleId(roleId);
            if (CollectionUtil.isEmpty(userRoleList)) {
                pageResponse.setTotal(0L);
                pageResponse.setList(Collections.emptyList());
                return pageResponse;
            }
            List<Long> list = userRoleList.stream().map(userRoleInfo -> userRoleInfo.getUserId()).distinct().collect(Collectors.toList());
            userIdList.addAll(list);
        }
        Long orgId = request.getOrgId();
        if (orgId != null) {
            // 根据组织机构查询用户ID
            List<Long> list = tableUserOrgService.queryUserIdList(orgId,1);
            if (CollectionUtil.isEmpty(list)) {
                pageResponse.setTotal(0L);
                pageResponse.setList(Collections.emptyList());
                return pageResponse;
            }
            userIdList.addAll(list);
        }
        Long ownerId = request.getOwnerId();
        if (ownerId != null) {
            // 根据组织机构查询用户ID
            List<Long> list = tableUserOrgService.queryUserIdList(ownerId,2);
            if (CollectionUtil.isEmpty(list)) {
                pageResponse.setTotal(0L);
                pageResponse.setList(Collections.emptyList());
                return pageResponse;
            }
            userIdList.addAll(list);
        }
        // userIdList 去重
        if (CollectionUtil.isNotEmpty(userIdList)) {
            userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        }
        List<UserInfo> list = tableUserService.queryUserList(request, userIdList);
        PageInfo<UserInfo> pageInfo = new PageInfo<>(list);
        // 返回结果
        List<UserInfoResponse> responseList = new ArrayList<>();
        list.forEach(userInfo -> {
            UserInfoResponse response = BeanUtil.copyProperties(userInfo, UserInfoResponse.class);
            // 是否超管 1-是 2-否
            int isSystemAdmin = userInfo.getIsSystemAdmin();
            if (isSystemAdmin == BizConstant.SystemAdminType.isAdministrator) {
                response.setRoleName("系统超管");
            } else {
                List<RoleInfo> roleInfos = tableRoleService.queryAllRoleListByUserId(userInfo.getId());
                if (CollectionUtil.isNotEmpty(roleInfos)) {
                    response.setRoleName(roleInfos.stream().map(RoleInfo::getRoleName).collect(Collectors.joining(",")));
                }
            }
            responseList.add(response);
        });
        pageResponse.setTotal(pageInfo.getTotal());
        pageResponse.setList(responseList);
        return pageResponse;
    }


    @Override
    public UserInfo queryUserByAccount(String userAccount) {
        return tableUserService.queryUserByUserAccount(userAccount);
    }

    @Override
    public ResultResponse queryUserInfo(long userId) {
        UserInfo userInfo = tableUserService.selectById(userId);
        if (userInfo == null) {
            return ResultResponse.businessFailed("用户不存在");
        }
        UserInfoResponse response = BeanUtil.copyProperties(userInfo, UserInfoResponse.class);
        // 查询用户关联角色
        List<RoleInfo> roleInfos = tableRoleService.queryAllRoleListByUserId(userInfo.getId());
        if (CollectionUtil.isNotEmpty(roleInfos)) {
            response.setRoleName(roleInfos.stream().map(RoleInfo::getRoleName).collect(Collectors.joining(",")));
            response.setRoleIdList(roleInfos.stream().map(RoleInfo::getId).collect(Collectors.toList()));
        }
        // 查询用户关联组织机构
        List<Long> orgIdList = tableUserOrgService.queryOrgIdList(userId,1);
        response.setOrgIdList(orgIdList);
        if (CollectionUtil.isNotEmpty(orgIdList)) {
            List<OrgInfo> orgInfoList = tableOrgInfoService.queryOrgListByIds(orgIdList);
            if (CollectionUtil.isNotEmpty(orgInfoList)) {
                // 获取checkedState 等于 1 的组织机构id
                List<Long> checkedOrgIdList = orgInfoList.stream().filter(orgInfo -> orgInfo.getCheckedState() == 1).map(OrgInfo::getId).collect(Collectors.toList());
                response.setOrgIdList(checkedOrgIdList);
            }
        }
        // 查询关联owner公司
        List<Long> ownerIdList = tableUserOrgService.queryOrgIdList(userId,2);
        response.setOwnerIdList(ownerIdList);
        return ResultResponse.success(response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse addUser(SaveUserRequest request, TokenUserInfo tokenUserInfo) {
        // 判断用户信息是否存在
        List<DaZhongUserInfo> userList = request.getUserList();
        for (DaZhongUserInfo info : userList) {
            UserInfo userInfo = tableUserService.queryUserByUserAccount(info.getSsoUserId());
            if (userInfo != null) {
                return ResultResponse.businessFailed(StrUtil.format("用户【{}】已存在", info.getName()));
            }
        }
        // 查询全局角色
        List<RoleInfo> roleList = tableRoleService.queryAllRoleList();
        Map<Long, RoleInfo> roleMap = roleList.stream().collect(Collectors.toMap(RoleInfo::getId, roleInfo -> roleInfo));

        // 新增关联角色信息
        StringBuilder roleLogString = new StringBuilder();
        List<Long> roleIdList = request.getRoleIdList();
        for (Long roleId : roleIdList) {
            RoleInfo roleInfo = roleMap.get(roleId);
            if (roleInfo == null) {
                return ResultResponse.businessFailed("选择的角色不存在，请重新选择");
            }
            // 角色状态 1-启用 2-禁用
            int roleStatus = roleInfo.getRoleStatus();
            if (roleStatus == BizConstant.CommonStatusStatus.disabled) {
                return ResultResponse.businessFailed(StrUtil.format("角色【{}】已禁用", roleInfo.getRoleName()));
            }
            roleLogString.append(roleInfo.getRoleName()).append(" ");
        }

        // 新增关联组织架构
        StringBuilder orgLogString = new StringBuilder();
        List<Long> orgIdList = request.getOrgIdList();
        if (CollectionUtil.isNotEmpty(orgIdList)){
            // 查询全部机构信息
            List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();
            Map<Long, OrgInfo> orgMap = orgInfoList.stream().collect(Collectors.toMap(OrgInfo::getId, orgInfo -> orgInfo));
            for (Long orgId : orgIdList) {
                OrgInfo orgInfo = orgMap.get(orgId);
                if (orgInfo == null) {
                    return ResultResponse.businessFailed("选择的组织机构不存在，请重新选择");
                }
                if (orgInfo.getCheckedState() == BizConstant.CommonStatusStatus.disabled) {
                    return ResultResponse.businessFailed(StrUtil.format("组织机构【{}】已禁用", orgInfo.getCompanyName()));
                }
                orgLogString.append(orgInfo.getCompanyName()).append(" ");
            }
        }

        //用户关联资产所有公司ID
        List<Integer> ownerIdList = request.getOwnerIdList();
        StringBuilder ownerLogString = new StringBuilder();
        if (CollectionUtil.isNotEmpty(ownerIdList)){
            Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();
            for (Integer ownerId : ownerIdList) {
                DataOwnerInfo ownerInfo = ownerInfoMap.get(ownerId);
                if (ownerInfo == null) {
                    return ResultResponse.businessFailed("选择的owner不存在，请重新选择");
                }
                ownerLogString.append(ownerInfo.getName()).append(" ");
            }
        }

        List<UserRoleInfo> userRoleInfos = new ArrayList<>();
        List<UserOrgInfo> userOrgInfos = new ArrayList<>();
        List<OperateLog> logs = new ArrayList<>();

        // 新增用户信息
        userList.forEach(user -> {
            UserInfo userInfo = new UserInfo();
            userInfo.setUserAccount(user.getSsoUserId());
            userInfo.setName(user.getName());
            userInfo.setMobilePhone(user.getMobilePhone());
            userInfo.setOrgCode("00");
            userInfo.setSystemCode(BizConstant.system_code);
            tableUserService.saveUser(userInfo);

            for (Long roleId : roleIdList) {
                UserRoleInfo userRoleInfo = new UserRoleInfo();
                userRoleInfo.setUserId(userInfo.getId());
                userRoleInfo.setRoleId(roleId);
                userRoleInfos.add(userRoleInfo);
            }

            if (CollectionUtil.isNotEmpty(orgIdList)){
                for (Long orgId : orgIdList) {
                    UserOrgInfo userOrgInfo = new UserOrgInfo();
                    userOrgInfo.setUserId(userInfo.getId());
                    userOrgInfo.setOrgId(orgId);
                    userOrgInfo.setOrgType(1);
                    userOrgInfos.add(userOrgInfo);
                }
            }
            if (CollectionUtil.isNotEmpty(ownerIdList)){
                for (Integer ownerId : ownerIdList) {
                    UserOrgInfo userOrgInfo = new UserOrgInfo();
                    userOrgInfo.setUserId(userInfo.getId());
                    userOrgInfo.setOrgId(Long.valueOf(ownerId));
                    userOrgInfo.setOrgType(2);
                    userOrgInfos.add(userOrgInfo);
                }
            }

            // 日志信息
            OperateLog log = new OperateLog();
            log.setForeignId(tokenUserInfo.getUserId());
            log.setBusinessType(BizConstant.BusinessType.businessType_1);
            log.setOperateType(BizConstant.OperateType.OperateType_13);
            log.setOperateContent(StrUtil.format("创建用户-{}, 关联角色-{}, 关联组织架构-{} 关联资产公司-{}", user.getName(), roleLogString.toString(), orgLogString.toString(), ownerLogString.toString()));
            logs.add(log);
        });
        tableUserRoleService.batchInsert(userRoleInfos);
        tableUserOrgService.batchInsert(userOrgInfos);
        // 添加日志
        tableOperateLogService.batchInsertLog(logs, tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse updateUser(UpdateUserRequest request, TokenUserInfo tokenUserInfo) {
        UserInfo userInfo = tableUserService.selectById(request.getId());
        if (userInfo == null) {
            return ResultResponse.businessFailed("用户不存在");
        }
        int isSystemAdmin = userInfo.getIsSystemAdmin();
        if (isSystemAdmin == BizConstant.SystemAdminType.isAdministrator) {
            return ResultResponse.businessFailed("超级管理员不允许修改");
        }
        StringBuffer logBuffer = new StringBuffer()
                .append("修改【").append(userInfo.getName()).append("】用户信息 新角色为：");

        // 查询全局角色
        List<RoleInfo> roleList = tableRoleService.queryAllRoleList();
        Map<Long, RoleInfo> roleMap = roleList.stream().collect(Collectors.toMap(RoleInfo::getId, roleInfo -> roleInfo));
        // 判断角色是否存在
        List<Long> roleIdList = request.getRoleIdList();
        for (Long roleId : roleIdList) {
            RoleInfo roleInfo = roleMap.get(roleId);
            if (roleInfo == null) {
                return ResultResponse.businessFailed("选择的角色不存在，请重新选择");
            }
            // 角色状态 1-启用 2-禁用
            int roleStatus = roleInfo.getRoleStatus();
            if (roleStatus == BizConstant.CommonStatusStatus.disabled) {
                return ResultResponse.businessFailed(StrUtil.format("角色【{}】已禁用", roleInfo.getRoleName()));
            }
            logBuffer.append(roleInfo.getRoleName()).append(" ");
        }


        List<Long> orgIdList = request.getOrgIdList();
        if (CollectionUtil.isNotEmpty(orgIdList)){
            logBuffer.append("新的组织机构为：");
            // 查询全部机构信息
            List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();
            Map<Long, OrgInfo> orgMap = orgInfoList.stream().collect(Collectors.toMap(OrgInfo::getId, orgInfo -> orgInfo));
            for (Long orgId : orgIdList) {
                OrgInfo orgInfo = orgMap.get(orgId);
                if (orgInfo == null) {
                    return ResultResponse.businessFailed("选择的组织信息不存在，请重新选择");
                }
                if (orgInfo.getCheckedState() == BizConstant.CommonStatusStatus.disabled) {
                    return ResultResponse.businessFailed(StrUtil.format("组织机构【{}】已禁用", orgInfo.getCompanyName()));
                }
                logBuffer.append(orgInfo.getCompanyName()).append(",");
            }
        }

        // 用户关联owner公司ID
        List<Integer> ownerIdList = request.getOwnerIdList();
        StringBuilder ownerLogString = new StringBuilder();
        if (CollectionUtil.isNotEmpty(ownerIdList)){
            logBuffer.append("新的资产公司为：");
            Map<Integer, DataOwnerInfo> ownerInfoMap = dataDictService.getOwnerMap();
            for (Integer ownerId : ownerIdList) {
                DataOwnerInfo ownerInfo = ownerInfoMap.get(ownerId);
                if (ownerInfo == null) {
                    return ResultResponse.businessFailed("选择的owner不存在，请重新选择");
                }
                ownerLogString.append(ownerInfo.getName()).append(" ");
            }
        }
        // 删除用户原有角色
        tableUserRoleService.deleteByUserId(userInfo.getId());
        // 新增新的角色信息
        List<UserRoleInfo> userRoleInfos = new ArrayList<>();
        for (Long roleId : roleIdList) {
            UserRoleInfo userRoleInfo = new UserRoleInfo();
            userRoleInfo.setUserId(userInfo.getId());
            userRoleInfo.setRoleId(roleId);
            userRoleInfos.add(userRoleInfo);
        }
        tableUserRoleService.batchInsert(userRoleInfos);

        // 删除用户原有组织机构
        tableUserOrgService.deleteByUserId(userInfo.getId());
        // 重新关联用户关联组织机构信息
        List<UserOrgInfo> userOrgInfos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orgIdList)){
            for (Long orgId : orgIdList) {
                UserOrgInfo userOrgInfo = new UserOrgInfo();
                userOrgInfo.setUserId(userInfo.getId());
                userOrgInfo.setOrgId(orgId);
                userOrgInfo.setOrgType(1);
                userOrgInfos.add(userOrgInfo);
            }
        }
        // 重新关联用户关联资产所有公司
        if (CollectionUtil.isNotEmpty(ownerIdList)){
            for (Integer ownerId : ownerIdList) {
                UserOrgInfo userOrgInfo = new UserOrgInfo();
                userOrgInfo.setUserId(userInfo.getId());
                userOrgInfo.setOrgId(Long.valueOf(ownerId));
                userOrgInfo.setOrgType(2);
                userOrgInfos.add(userOrgInfo);
            }
        }
        tableUserOrgService.batchInsert(userOrgInfos);
        tableUserService.updateUser(userInfo);
        tableOperateLogService.insertLog(userInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_5, logBuffer.toString(), tokenUserInfo);

        // 修改后重新覆盖redis中的用户信息
        String loginUser = redisUtils.getStr(BizConstant.LOGIN_USER_KEY + userInfo.getId());
        if (StringUtils.isBlank(loginUser)) {
            return ResultResponse.success();
        }
        TokenUserInfo info = JSONUtil.toBean(loginUser, TokenUserInfo.class);
        info.setOrgIdList(null);
        info.setOwnerIdList(null);
        if (CollectionUtil.isNotEmpty(orgIdList)){
            info.setOrgIdList(orgIdList);
        }
        if (CollectionUtil.isNotEmpty(ownerIdList)){
            info.setOwnerIdList(ownerIdList);
        }
        if (CollectionUtil.isEmpty(ownerIdList) && CollectionUtil.isEmpty(orgIdList)) {
            info.setOrgIdList(Collections.singletonList(-999999L));
            info.setOwnerIdList(Collections.singletonList(-999999));
        }
        redisUtils.set(BizConstant.LOGIN_USER_KEY + userInfo.getId(), JSONUtil.toJsonStr(info), BizConstant.LOGIN_EXPIRES_IN);
        return ResultResponse.success();
    }

    @Override
    public List<SsoUserInfo> querySsoUserList() {
        List<SsoUserInfo> ssoUserInfos = tableSsoUserService.querySsoUserList();
        return ssoUserInfos;
    }

    @Override
    public ResultResponse syncSsoUser(SyncSsoUserDto dto) {
        if (StringUtils.isBlank(dto.getId())) {
            return ResultResponse.businessFailed("用户id不能为空");
        }
        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(-1L);
        tokenUserInfo.setName("账号同步");
        SsoUserInfo ssoUserInfo = tableSsoUserService.querySsoUserByUserId(dto.getId());
        if (dto.getType().equals("add") && ssoUserInfo == null) {
            ssoUserInfo = new SsoUserInfo();
            ssoUserInfo.setName(dto.getName());
            ssoUserInfo.setMobilePhone(dto.getPhone());
            ssoUserInfo.setUserId(dto.getId());
            ssoUserInfo.setCompanyId(dto.getCompanyId());
            tableSsoUserService.saveSsoUser(ssoUserInfo);
        }
        if (dto.getType().equals("update") && ssoUserInfo != null) {
            ssoUserInfo.setName(dto.getName());
            ssoUserInfo.setMobilePhone(dto.getPhone());
            ssoUserInfo.setUserId(dto.getId());
            ssoUserInfo.setCompanyId(dto.getCompanyId());
            tableSsoUserService.updateSsoUser(ssoUserInfo);
        }
        if (dto.getType().equals("delete") && ssoUserInfo != null) {
            tableSsoUserService.deleteSsoUser(ssoUserInfo.getId());
            // 删除用户信息，角色信息
            UserInfo userInfo = tableUserService.queryUserByUserAccount(ssoUserInfo.getUserId());
            if (userInfo != null) {
                tableUserService.deleteUser(userInfo.getId());
                tableUserRoleService.deleteByUserId(userInfo.getId());
                tableOperateLogService.insertLog(userInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_15, StrUtil.format("账号同步删除用户【{}】", userInfo.getName()), tokenUserInfo);
            }
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse bindDingTalkUser(BindDingTalkRequest request, TokenUserInfo tokenUserInfo) {
        UserInfo userInfo = tableUserService.selectById(request.getId());
        if (userInfo == null) {
            return ResultResponse.businessFailed("用户不存在");
        }
        GetDingTalkUserResponse dingTalkUserResponse = dingTalkService.getDingTalkUserByMobilePhone(request.getPhoneNumber());
        if (ObjectUtil.isEmpty(dingTalkUserResponse.getUserId())) {
            return ResultResponse.businessFailed("未获取到用户钉钉号");
        }
        userInfo.setMobilePhone(request.getPhoneNumber());
        userInfo.setDingTalkNum(dingTalkUserResponse.getUserId());
        tableUserService.updateUser(userInfo);
        tableOperateLogService.insertLog(userInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_14, StrUtil.format("绑定钉钉用户【{}】", userInfo.getName()), tokenUserInfo);
        // 绑定用户钉钉重新覆盖redis中的用户信息
        String loginUser = redisUtils.getStr(BizConstant.LOGIN_USER_KEY + userInfo.getId());
        if (StringUtils.isNotBlank(loginUser)) {
            TokenUserInfo info = JSONUtil.toBean(loginUser, TokenUserInfo.class);
            info.setDingTalkNum(dingTalkUserResponse.getUserId());
            redisUtils.set(BizConstant.LOGIN_USER_KEY + userInfo.getId(), JSONUtil.toJsonStr(info), BizConstant.LOGIN_EXPIRES_IN);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse deleteUser(BaseIdRequest request, TokenUserInfo tokenUserInfo) {
        UserInfo userInfo = tableUserService.selectById(request.getId());
        if (userInfo == null) {
            return ResultResponse.success();
        }
        int isSystemAdmin = userInfo.getIsSystemAdmin();
        if (isSystemAdmin == BizConstant.SystemAdminType.isAdministrator) {
            return ResultResponse.businessFailed("系统超管不能删除");
        }
        tableUserService.deleteUser(userInfo.getId());
        tableUserRoleService.deleteByUserId(userInfo.getId());
        tableUserOrgService.deleteByUserId(userInfo.getId());
        tableOperateLogService.insertLog(userInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_15, StrUtil.format("删除用户【{}】", userInfo.getName()), tokenUserInfo);
        // 删除用户后如果用户是登录状态 删除Redis中的用户信息
        if (redisUtils.exist(BizConstant.LOGIN_USER_KEY + userInfo.getId())){
            redisUtils.del(BizConstant.LOGIN_USER_KEY + userInfo.getId());
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse queryUserDingTalk(QueryDingTalkRequest request, TokenUserInfo tokenUserInfo) {
        GetDingTalkUserResponse dingTalkUserResponse = dingTalkService.getDingTalkUserByMobilePhone(request.getPhoneNumber());
        if (ObjectUtil.isEmpty(dingTalkUserResponse.getUserId())) {
            return ResultResponse.businessFailed("未获取到用户钉钉号");
        }
        Map<String, String> map = new HashMap<>();
        map.put("dingTalkNum", dingTalkUserResponse.getUserId());
        return ResultResponse.success(map);
    }
}
