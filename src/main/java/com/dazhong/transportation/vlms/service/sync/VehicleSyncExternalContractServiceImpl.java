package com.dazhong.transportation.vlms.service.sync;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.database.TableVehicleContractService;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalContractDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.VehicleContract;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class VehicleSyncExternalContractServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableVehicleContractService tableVehicleContractService;

    @Override
    public ResultResponse handleSyncData(SyncDataRequest request) {
        log.info("批量同步合同信息-{}", JSONUtil.toJsonStr(request));
        String bizRequest = request.getBizRequest();
        try {
            JSONObject jsonObject = JSONUtil.parseObj(bizRequest);
            JSONArray jsonArray = jsonObject.getJSONArray("contractInfo");
            if (ObjectUtil.isEmpty(jsonArray)) {
                return ResultResponse.businessFailed("同步合同信息数据为空");
            }
            List<VehicleSyncExternalContractDto> contractList = jsonArray.toList(VehicleSyncExternalContractDto.class);
            for (VehicleSyncExternalContractDto dto : contractList) {
                VehicleContract contract = BeanUtil.copyProperties(dto, VehicleContract.class);
                VehicleContract vehicleContract = tableVehicleContractService.queryByContractNo(dto.getContractNo());
                if (vehicleContract == null){
                    tableVehicleContractService.insert(contract);
                } else {
                    contract.setId(vehicleContract.getId());
                    tableVehicleContractService.updateSelectiveById(contract);
                }
            }
            return ResultResponse.success();
        } catch (Exception e) {
            log.error("批量同步合同信息数据异常-{}", e.getMessage());
        }
        return ResultResponse.businessFailed("同步合同信息数据异常");
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        return null;
    }
}
