package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.request.SaveRoleRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateRoleRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateRoleStatusRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.RoleInfo;

import java.util.List;

/**
 * 系统角色服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface IRoleService {

    /**
     * 查询登录用户角色信息
     * @return
     */
    List<RoleInfo> queryRoleList(TokenUserInfo tokenUserInfo);

    /**
     * 删除角色状态
     * @param request
     * @return
     */
    ResultResponse deleteRole(BaseIdRequest request, TokenUserInfo tokenUserInfo);


    /**
     * 更新角色状态
     * @param request
     * @return
     */
    ResultResponse updateRoleStatus(UpdateRoleStatusRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 新增角色
     * @param request
     * @return
     */
    ResultResponse addRole(SaveRoleRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 修改角色
     * @param request
     * @return
     */
    ResultResponse updateRole(UpdateRoleRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询角色资源树
     * @param roleId
     * @return
     */
    ResultResponse queryRoleResourceTree(Long roleId);

    /**
     * 查询角色明细
     * @param roleId
     * @return
     */
    ResultResponse queryRoleInfo(Long roleId);
}
