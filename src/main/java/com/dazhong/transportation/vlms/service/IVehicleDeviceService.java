package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.request.SearchVehicleDeviceListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;

public interface IVehicleDeviceService {

    /**
     * 查询车辆设备列表
     * @param request 查询条件
     * @return 设备列表 - 分页后
     */
    PageResponse<VehicleDeviceInfoResponse> searchVehicleDeviceList(SearchVehicleDeviceListRequest request);
}
