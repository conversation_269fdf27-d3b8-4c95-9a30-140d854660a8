package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleReverseDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.QueryVehicleRequest;
import com.dazhong.transportation.vlms.dto.request.SaveReverseDisposalApplicationRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.ReverseDisposalApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleReverseDisposalResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportReverseDisposalVehicleDetail;

import java.util.List;

public interface IVehicleReverseDisposalService {


    /**
     * 查询逆处置车辆信息
     *
     * @param queryVehicleRequest 查询车辆信息入参
     * @return 逆处置车辆信息
     */
    VehicleReverseDisposalResponse queryAndCheckVehicleBasicInfo(QueryVehicleRequest queryVehicleRequest);

    /**
     * 查询车辆处置列表
     *
     * @param searchVehicleDisposalListRequest 查询车辆处置列表入参
     * @param tokenUserInfo                    用户登录信息
     * @return 返回车辆处置列表
     */
    PageResponse<VehicleReverseDisposalListDto> queryPageResponse(SearchVehicleDisposalListRequest searchVehicleDisposalListRequest, TokenUserInfo tokenUserInfo);

    /**
     * 保存逆处置申请单
     *
     * @param saveReverseDisposalApplicationRequest 保存逆处置申请单入参
     * @param tokenUserInfo                         用户登录信息
     * @return 主键id
     */
    ResultResponse<Long> saveApplication(SaveReverseDisposalApplicationRequest saveReverseDisposalApplicationRequest, TokenUserInfo tokenUserInfo);

    /**
     * 提交逆处置申请单
     *
     * @param saveReverseDisposalApplicationRequest 提交逆处置申请单入参
     * @param tokenUserInfo                         用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Long> submitApplication(SaveReverseDisposalApplicationRequest saveReverseDisposalApplicationRequest, TokenUserInfo tokenUserInfo);

    /**
     * 作废逆处置申请单
     *
     * @param id            申请单id
     * @param tokenUserInfo 用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> cancelApplication(Long id, TokenUserInfo tokenUserInfo);

    /**
     * 撤回逆处置申请单
     *
     * @param id            申请单id
     * @param tokenUserInfo 用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> withdrawApplication(Long id, TokenUserInfo tokenUserInfo);

    /**
     * 查询详情
     *
     * @param id 主键id
     * @return 返回处置申请单详情
     */
    ReverseDisposalApplicationDetailResponse queryReverseDisposalDetail(Long id);

    /**
     * 钉钉回调处理
     *
     * @param dingTalkNo     钉钉审批单号
     * @param dingTalkResult 钉钉审批回调结果
     */
    void dingTalkResultProcess(String dingTalkNo, String dingTalkResult);

    /**
     * 获取批量导入处置任务车辆明细
     *
     * @param request 导入文件信息入参
     * @return 返回符合条件的车牌号列表
     */
    List<ImportReverseDisposalVehicleDetail> getReverseDisposalVehicleDetailList(BaseImportFileUrlRequest request);
}
