package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.CompanyExtraInfoDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.UpdateCompanyExtraInfoRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;

/**
 * 公司额外信息服务
 *
 * <AUTHOR>
 * @date 2025-04-16 15:26
 */
public interface ICompanyExtraInfoService {

    /**
     * 更新公司额外信息
     *
     * @param updateCompanyExtraInfoRequest
     * @return
     */
    ResultResponse<Void> updateCompanyExtraInfo(UpdateCompanyExtraInfoRequest updateCompanyExtraInfoRequest, TokenUserInfo tokenUserInfo);

    /**
     * 查询公司额外信息
     *
     * @param foreignId    外键id
     * @param businessType 业务类型 1-组织架构 2-资产所有者
     * @return
     */
    CompanyExtraInfoDto selectCompanyExtraInfo(Long foreignId, Integer businessType);

}
