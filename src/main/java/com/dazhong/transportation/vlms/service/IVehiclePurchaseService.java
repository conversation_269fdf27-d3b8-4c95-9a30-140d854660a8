package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.PurchaseQuotaDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.PurchaseApplyDetailResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;

/**
 * 车辆采购服务
 * <AUTHOR>
 * @date 2025-01-03 10:37
 */
public interface IVehiclePurchaseService {


    /**
     * 查询采购意向列表
     * @param request
     * @param tokenUserInfo
     * @return
     */
    PageResponse searchPurchaseIntention(SearchPurchaseIntentionRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 查询采购意向详情
     * @param intentionNo
     * @return
     */
    ResultResponse queryPurchaseIntention(String intentionNo);

    /**
     * 导入采购意向
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse importPurchaseIntention(BaseImportFileUrlRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 查询采购申请列表
     * @param request
     * @param tokenUserInfo
     * @return
     */
    PageResponse searchPurchaseApply(SearchPurchaseApplyRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询采购申请详情
     * @param applyNo
     * @return
     */
    PurchaseApplyDetailResponse queryPurchaseApplyDetail(String applyNo);


    /**
     * 查询采购申请额度占用列表
     * @param applyNo
     * @return
     */
    ResultResponse queryPurchaseOccupiedList(String applyNo);


    /**
     * 释放采购详情额度占用
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse releasePurchaseOccupied(ReleasePurchaseOccupiedRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 获取采购申请明细行号列表
     * @param applyNo
     * @return
     */
    ResultResponse queryApplyDetailsNoList(String applyNo);

    /**
     * 新增采购申请
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse addPurchaseApply(AddPurchaseApplyRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 编辑采购申请
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse updatePurchaseApply(UpdatePurchaseApplyRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 修改采购申请状态
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse editPurchaseApplyStatus(EditPurchaseApplyStatusRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 关闭采购申请
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse closePurchaseApply(ClosePurchaseApplyRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 采购收货
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse purchasingReceiving(PurchaseReceivingRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 批量采购收货
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse batchPurchasingReceiving(BaseImportFileUrlRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 更新采购单额度
     *
     * @param vin 车架号
     * @param tokenUserInfo  登录用户信息
     */
    PurchaseQuotaDto updatePurchaseQuota(String vin, TokenUserInfo tokenUserInfo);

    /**
     * 刷新采购审批结果
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse refreshPurchasing(RefreshDingTalkApprovalRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 采购审批结果
     * @param requestNo
     * @param dingTalkResult "agree"："同意" , "refuse":"拒绝"
     * @return
     */
    void dingTalkResultProcess(String requestNo, String dingTalkResult);
}
