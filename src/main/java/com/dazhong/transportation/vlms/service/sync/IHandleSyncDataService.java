package com.dazhong.transportation.vlms.service.sync;

import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;

/**
 * 同步数据处理服务
 * <AUTHOR>
 * @date 2025-01-07 17:55
 */
public interface IHandleSyncDataService {

    /**
     * 同步数据
     * @param request
     * @return
     */
    ResultResponse handleSyncData(SyncDataRequest request);


    /**
     * 获取同步信息用于页面展示
     * @param request
     * @return
     */
    ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request);
}
