package com.dazhong.transportation.vlms.service.sync;

import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableVehicleSyncExternalBusinessInfoService;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalAccidentDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalAccidentDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalBusinessInfoDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalIllegalDetailDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleExternalAccidentResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleExternalIllegalResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.model.VehicleSyncExternalBusinessInfo;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class VehicleSyncExternalAccidentServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableVehicleSyncExternalBusinessInfoService tableVehicleSyncExternalBusinessInfoService;

    @Override
    public ResultResponse handleSyncData(SyncDataRequest request) {
        //校验参数
        ValidationUtils.validate(request);
        try {
            VehicleSyncExternalAccidentDto params = JSONObject.parseObject(request.getBizRequest(), VehicleSyncExternalAccidentDto.class);
            if (CollectionUtils.isEmpty(params.getVehicleAccidentInfo())){
                return ResultResponse.businessFailed(ExceptionEnum.PARAM_ERR.getMessage());
            }
            List<VehicleSyncExternalBusinessInfoDto> rows = new ArrayList<>();
            params.getVehicleAccidentInfo().forEach(accident ->{
                VehicleSyncExternalBusinessInfoDto row = new VehicleSyncExternalBusinessInfoDto();
                row.setVin(accident.getVin());
                row.setLicensePlate(accident.getLicensePlate());
                row.setBusinessNo(accident.getAccidentNo());
                row.setBusinessTime(accident.getAccidentTime());
                row.setBusinessInfo(JSONObject.toJSONString(accident));
                row.setBusinessType(BizConstant.SyncExternalBusinessType.accident);
                rows.add(row);
            });
            int insert = tableVehicleSyncExternalBusinessInfoService.batchInsert(rows);
            if (insert > 0) {
                return ResultResponse.success();
            }
        }catch (Exception e){
            log.error("batchInsert accident exception ",e);
        }
        return ResultResponse.businessFailed("同步事故信息数据异常");
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        try {
            List<VehicleSyncExternalBusinessInfo> accidentInfoList = tableVehicleSyncExternalBusinessInfoService.selectByLicensePlateAndType(request.getLicensePlate(),
                    BizConstant.SyncExternalBusinessType.accident);
            if (CollectionUtils.isEmpty(accidentInfoList)){
                return ResultResponse.success();
            }
            List<VehicleExternalAccidentResponse> responses = new ArrayList<>();

            accidentInfoList.forEach(accidentInfo ->{
                VehicleExternalAccidentResponse accidentResponse = new VehicleExternalAccidentResponse();
                accidentResponse.setLicensePlate(accidentInfo.getLicensePlate());
                accidentResponse.setSyncDateTime(DateTimeUtils.dateToString(accidentInfo.getCreateTime(),DateTimeUtils.DATE_TYPE1));
                if (StringUtils.isNotBlank(accidentInfo.getBusinessInfo())){
                    VehicleSyncExternalAccidentDetailDto accidentDetailDto = JSONObject.parseObject(accidentInfo.getBusinessInfo(),VehicleSyncExternalAccidentDetailDto.class);
                    if (accidentDetailDto != null){
                        accidentResponse.setAccidentNo(accidentDetailDto.getAccidentNo());
                        accidentResponse.setAccidentStatus(accidentDetailDto.getAccidentStatus());
                        accidentResponse.setAccidentResponseType(accidentDetailDto.getAccidentResponseType());
                        accidentResponse.setAccidentLevel(accidentDetailDto.getAccidentLevel());
                        accidentResponse.setAccidentTime(accidentDetailDto.getAccidentTime());
                        accidentResponse.setAccidentType(accidentDetailDto.getAccidentType());
                        accidentResponse.setAccidentDealUser(accidentDetailDto.getAccidentDealUser());
                    }
                }
                responses.add(accidentResponse);
            });
            return ResultResponse.success(responses);
        }catch (Exception e){
            log.error("accident getSyncExternalInfoByLicensePlate exception ",e);
        }
        return ResultResponse.businessFailed("获取车辆事故信息异常");
    }


}
