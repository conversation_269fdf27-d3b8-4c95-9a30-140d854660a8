package com.dazhong.transportation.vlms.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.database.TableDownloadFileInfoService;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.dto.response.DownloadFileInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.enums.DownloadFileStatusEnum;
import com.dazhong.transportation.vlms.enums.ExportFileTypeEnum;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.DownloadFileInfo;
import com.dazhong.transportation.vlms.service.IFileService;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.export.Exportable;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FileServiceImpl implements IFileService {

    @Autowired
    private TableDownloadFileInfoService tableDownloadFileInfoService;

    @Override
    public void export(ExportFileTypeEnum exportFileTypeEnum, InputStream templateStream, TokenUserInfo tokenUserInfo, Exportable exportable) {
        //导出文件名
        String fileName = exportFileTypeEnum.getTitle() + "_" + DateTimeUtils.dateToString(new Date(), DateTimeUtils.DATE_TYPE2) + ".xlsx";
        //生成文件中间路径
        String exportFilePath = generateFilePath(exportFileTypeEnum.isTemp());
        //下载路径
        String downloadFilePath = Global.instance.mfsUrl +  exportFilePath + fileName;
        //文件全路径
        String fullExportFilePath = Global.instance.mfsRootPath + exportFilePath + fileName;


        //如果目录不存在，则创建目录
        File file = new File(fullExportFilePath);
        if(!file.getParentFile().exists()){
            file.getParentFile().mkdirs();
        }

        //开始导出文件（导出中）
        Long exportFileId = startExport(exportFileTypeEnum.getFileSource(), fileName, downloadFilePath, tokenUserInfo);
        if(exportFileId == null){
            throw new ServiceException("创建导出文件失败");
        }

        try(
            // 创建文件输出流，用于写入导出文件
            FileOutputStream fileOutputStream = new FileOutputStream(fullExportFilePath);
            // 使用 EasyExcel 创建 ExcelWriter，基于模板流
            ExcelWriter excelWriter = EasyExcelFactory.write(fileOutputStream).withTemplate(templateStream).build();
        ){
            // 创建一个写入工作表
            WriteSheet writeSheet = EasyExcelFactory.writerSheet().build();
            boolean hasResult = exportable.export(excelWriter, writeSheet);
            //如果导出数据为空，则填充一个空列表
            if(!hasResult){
                excelWriter.fill(new ArrayList<>(), writeSheet);
            }
            //完成Excel写入
            excelWriter.finish();

            //导出成功
            exportSuccess(exportFileId, tokenUserInfo);
        }catch (Exception e){
            log.error(e.getLocalizedMessage(),e);
            //导出失败
            exportFail(exportFileId, e.getLocalizedMessage(), tokenUserInfo);
        }

    }

    @Override
    public PageResponse<DownloadFileInfoResponse> searchDownloadFileList(PageRequest request, TokenUserInfo tokenUserInfo) {
        //采用PageHelper自动分页
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<DownloadFileInfo> downloadFileInfoList = tableDownloadFileInfoService.searchDownloadFileList(request, tokenUserInfo);
        PageInfo<DownloadFileInfo> pageInfo = new PageInfo<>(downloadFileInfoList);

        List<DownloadFileInfoResponse> list = new ArrayList<>();
        for (DownloadFileInfo downloadFileInfo : downloadFileInfoList) {
            DownloadFileInfoResponse response = new DownloadFileInfoResponse();
            BeanUtils.copyProperties(downloadFileInfo, response);
            //如果状态为导出中且导出时间超过5分钟，则状态展示为导出失败，失败原因为导出超时
            if(downloadFileInfo.getFileStatus().equals(DownloadFileStatusEnum.EXPORTING.getValue()) 
                && ((System.currentTimeMillis() - downloadFileInfo.getCreateTime().getTime()) > 5 * 60 * 1000)){
                response.setFileStatus(DownloadFileStatusEnum.EXPORT_FAILED.getValue());
                response.setRemark("导出超时");
            }
            list.add(response);
        }
        return new PageResponse<>(pageInfo.getTotal(), list);
    }

    /**
     * 开始导出文件 - 默认有效期30天
     * @param fileSource 文件来源 1:车辆信息
     * @param fileName 导出文件名称（最终用户看到的名称）
     * @param filePath 导出文件下载路径
     * @param tokenUserInfo 用户信息
     * @return Long 导出文件任务ID
     */
    private Long startExport(Integer fileSource, String fileName, String filePath, TokenUserInfo tokenUserInfo) {
        DownloadFileInfo createFileInfo = new DownloadFileInfo();
        createFileInfo.setFileSource(fileSource);
        createFileInfo.setFileName(fileName);
        createFileInfo.setFilePath(filePath);
        Date now = new Date();
        createFileInfo.setExpireTime(new Date(now.getTime() + TimeUnit.DAYS.toMillis(7)));
        createFileInfo.setFileStatus(DownloadFileStatusEnum.EXPORTING.getValue());
        tableDownloadFileInfoService.insert(createFileInfo, tokenUserInfo);
        return createFileInfo.getId();
    }

    /**
     * 导出文件成功
     * @param fileId 导出任务ID
     * @param tokenUserInfo 用户信息
     */
    private void exportSuccess(Long fileId, TokenUserInfo tokenUserInfo) {
        DownloadFileInfo updateFileInfo = new DownloadFileInfo();
        updateFileInfo.setId(fileId);
        updateFileInfo.setFileStatus(DownloadFileStatusEnum.EXPORT_SUCCESS.getValue());
        tableDownloadFileInfoService.updateSelectiveById(updateFileInfo, tokenUserInfo);
    }

    /**
     * 导出文件失败
     * @param fileId 导出任务ID
     * @param reason 失败原因
     * @param tokenUserInfo 用户信息
     */
    private void exportFail(Long fileId, String reason, TokenUserInfo tokenUserInfo) {
        DownloadFileInfo updateFileInfo = new DownloadFileInfo();
        updateFileInfo.setId(fileId);
        updateFileInfo.setFileStatus(DownloadFileStatusEnum.EXPORT_FAILED.getValue());
        updateFileInfo.setRemark(reason);
        tableDownloadFileInfoService.updateSelectiveById(updateFileInfo, tokenUserInfo);
    }


    /**
     * 生成导出文件目录
     * @param isTemp 是否是临时文件
     * @return 生成的目录 ex:/exportFile/20250215/
     */
    private String generateFilePath( boolean isTemp){
        // 文件目录 - 其中临时目录数据会被定期清理
        String fileCategoryPath = isTemp ? "tempFile" : "exportFile";
        //日期
        String dateStr = DateTimeUtils.dateToString(new Date(), DateTimeUtils.DATE_TYPE4);
        //文件目录： /exportFile/20250215/
        //临时目录： /tempFile/20250215/
        return File.separator + fileCategoryPath + File.separator + dateStr + File.separator;
    }
}
