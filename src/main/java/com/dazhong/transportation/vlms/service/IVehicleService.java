package com.dazhong.transportation.vlms.service;

import java.util.List;

import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleAssetDto;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.*;
import com.dazhong.transportation.vlms.dto.response.*;
import com.dazhong.transportation.vlms.dto.request.BatchSaveVehicleAttachmentRequest;
import com.dazhong.transportation.vlms.dto.request.ImportVehicleInfoRequest;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleRequest;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchInboundVehicleRequest;
import com.dazhong.transportation.vlms.dto.request.VehicleAttachmentRequest;
import com.dazhong.transportation.vlms.dto.request.VehicleDecorationRequest;
import com.dazhong.transportation.vlms.dto.request.VehicleOtherInfoRequest;
import com.dazhong.transportation.vlms.dto.request.VinQueryRequest;
import com.dazhong.transportation.vlms.dto.response.GetCarInfoResponse;
import com.dazhong.transportation.vlms.dto.response.SearchAssetVehicleSyncDataResponse;
import com.dazhong.transportation.vlms.dto.response.SearchDatabaseTableSyncDataResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleBasicResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleDetailsResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportAdminVehicleOtherInfo;
import com.dazhong.transportation.vlms.excel.ImportAnnualInspectionExpiryDate;
import com.dazhong.transportation.vlms.excel.ImportVehicleDecoration;
import com.dazhong.transportation.vlms.excel.ImportVehicleDepreciationInfo;
import com.dazhong.transportation.vlms.excel.ImportVehicleMasterData;
import com.dazhong.transportation.vlms.excel.ImportVehicleOtherInfo;

/**
 * 车辆信息服务服务
 */
public interface IVehicleService {

    /**
     * 查询入库车辆列表
     * @return
     */
    PageResponse searchInboundVehicleList(SearchInboundVehicleRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询入库车辆详情
     * @param vin
     * @return
     */
    ResultResponse getInboundVehicleDetails(String vin);

    /**
     * 查询车辆列表
     * @return
     */
    PageResponse searchAssetVehicleList(SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 查询车辆详情
     * @param vin
     * @return
     */
    ResultResponse<VehicleDetailsResponse> getVehicleDetails(String vin);

    /**
     * 查询车辆基础信息
     * @param vin
     * @return
     */
    ResultResponse<VehicleBasicResponse> getVehicleBasicInfo(String vin);

    /**
     * 查询车辆列表信息
     *
     * @param vinList
     * @return
     */
    List<VehicleListResponse> getVehicleListResponse(List<String> vinList);

    /**
     * 查询车辆基础信息
     *
     * @param licensePlate
     * @return
     */
    ResultResponse<VehicleBasicResponse> getVehicleBasicInfoByLicensePlate(String licensePlate);

    /**
     * 批量导入车辆装潢信息
     * @param list
     * @return
     */
    ResultResponse importVehicleDecoration(List<ImportVehicleDecoration> list, TokenUserInfo tokenUserInfo);

    /**
     * 批量导入车辆其他信息
     * @param list
     * @return
     */
    ResultResponse importVehicleOtherInfo(List<ImportVehicleOtherInfo> list, TokenUserInfo tokenUserInfo);

    /**
     * 批量导入车辆折旧信息
     * @param list
     * @return
     */
    ResultResponse importVehicleDepreciationInfo(List<ImportVehicleDepreciationInfo> list, TokenUserInfo tokenUserInfo);

    /**
     * 批量登记年检到期日
     * @param list
     * @return
     */
    ResultResponse importAnnualInspectionExpiryDate(List<ImportAnnualInspectionExpiryDate> list, TokenUserInfo tokenUserInfo);

    /**
     * 导入车辆主数据信息
     * @param list
     * @return
     */
    ResultResponse importVehicleMasterDate(List<ImportVehicleMasterData> list, TokenUserInfo tokenUserInfo);

    /**
     * 管理员批量导入车辆其他信息
     * @param list
     * @return
     */
    ResultResponse importVehicleDataInfo(List<ImportAdminVehicleOtherInfo> list, TokenUserInfo tokenUserInfo);

    /**
     * 导入车辆附件信息
     * @param request
     * @return
     */
    ResultResponse importVehicleFill(ImportVehicleInfoRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 证照流水记录查询
     * @param request
     * @return
     */
    ResultResponse queryVehicleFileRecord(VinQueryRequest request);

    /**
     * 装潢流水记录查询
     * @param request
     * @return
     */
    ResultResponse queryVehicleDecorationRecord(VinQueryRequest request);


    /**
     * 折旧记录查询
     * @param request
     * @return
     */
    ResultResponse queryVehicleDepreciationRecord(VinQueryRequest request);

    /**
     * 合同记录查询
     * @param request
     * @return
     */
    ResultResponse queryVehicleContractRecord(VinQueryRequest request);

    /**
     * 查询车辆信息
     * @param vin
     * @return
     */
    ResultResponse getComBoxVehicleInfo(String vin,String licensePlate);

    /**
     * 保存车辆装潢信息
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse saveVehicleDecoration(VehicleDecorationRequest request, TokenUserInfo tokenUserInfo);


    /**
     * 保存车辆其他信息
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse saveVehicleOtherInfo(VehicleOtherInfoRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 保存车辆附件信息
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse saveVehicleAttachment(VehicleAttachmentRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 批量保存车辆附件信息
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse batchSaveVehicleAttachment(BatchSaveVehicleAttachmentRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 导出车辆列表
     * @param request 查询条件
     * @param tokenUserInfo 用户信息
     */
    void exportAssetVehicleInfo(SearchAssetVehicleRequest request, TokenUserInfo tokenUserInfo);


    /**
     * 查询车辆中间表同步信息
     * @param request 查询条件
     * @return 车辆中间表同步数据
     */
    SearchAssetVehicleSyncDataResponse searchAssetVehicleSyncData(SearchAssetVehicleSyncDataRequest request);


    /**
     * 查询车辆表数据库同步信息
     * @param request 查询条件
     * @return 车辆表同步数据
     */
    SearchDatabaseTableSyncDataResponse<SyncDatabaseVehicleDto> searchVehicleDatabaseSyncData(SearchDatabaseTableSyncDataRequest request);

    /**
     * 查询车辆资产表数据库同步信息
     * @param request 查询条件
     * @return 车辆资产表同步数据
     */
    SearchDatabaseTableSyncDataResponse<SyncDatabaseVehicleAssetDto> searchVehicleAssetDatabaseSyncData(SearchDatabaseTableSyncDataRequest request);

    /**
     * 根据车架号获取核心系统车辆信息
     * @param vin 车架号
     * @return 车辆信息
     */
    GetCarInfoResponse.CarInfo getCarInfo(String vin);

    /**
     * 根据公司id获取核心系统车辆信息
     * @param companyId 公司id
     * @return 车辆信息
     */
    List<GetCarInfoResponse.CarInfo> getCarInfoList(Long companyId);

    /**
     * 从大众交通核心系统同步车辆信息
     * @return
     */
    ResultResponse<Void> syncAllVehicleInfoFromDaZhong();

    /**
     * 手动触发同步车辆信息到SAAS系统
     * @param isFullSync 是否全量同步（true:全量同步，false:增量同步）
     * @return 同步结果
     */
    ResultResponse<Void> syncVehicleToSaasManually(boolean isFullSync);

    /**
     * 手动触发同步车型信息到SAAS系统
     * @param isFullSync 是否全量同步（true:全量同步，false:增量同步）
     * @return 同步结果
     */
    ResultResponse<Void> syncVehicleModelToSaasManually(boolean isFullSync);
}
