package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.CreateDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.request.DealDingTalkWorkFlowResultRequest;
import com.dazhong.transportation.vlms.dto.request.GetDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.request.TerminateDingTalkProcessInstanceRequest;
import com.dazhong.transportation.vlms.dto.response.DingTalkDepartmentTreeResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkDetailResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkUserResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkWorkFlowResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.exception.ServiceException;

import java.util.List;

/**
 * 钉钉相关接口
 */
public interface IDingTalkService {

     /**
      * 创建钉钉审批流
      * @param request
      * @return
      * @throws Exception
      */
     String createDingTalkWorkFlow(CreateDingTalkWorkFlowRequest request) throws ServiceException;


     /**
      * 获取审批流详情
      * @return
      * @throws Exception
      */
     GetDingTalkWorkFlowResponse getDingTalkWorkFlow(GetDingTalkWorkFlowRequest request) throws ServiceException;


     /**
      * 根据手机号查询用户信息
      * @param mobilePhone
      */
     GetDingTalkUserResponse getDingTalkUserByMobilePhone(String mobilePhone) throws ServiceException;

     /**
      * 获取钉钉组织架构树
      * @return
      */
     List<DingTalkDepartmentTreeResponse> queryDepartmentTree();

     /**
      * 获取钉钉组织下拉列表
      *
      * @return
      */
     ComboResponse<Long, String> queryDepartmentCombo(TokenUserInfo tokenUserInfo);


     /**
      * 终止钉钉审批流
      * @param request
      * @throws ServiceException
      */
     void terminateProcessInstance(TerminateDingTalkProcessInstanceRequest request) throws ServiceException;


     /**
      * 根据钉钉UserId获取用户信息
      * @param userId
      * @return
      * @throws ServiceException
      */
     public GetDingTalkUserResponse getDingTalkUserByDingId(String userId) throws ServiceException;

     /**
      * 根据钉钉UserId获取用户信息
      *
      * @return
      * @throws ServiceException
      */
     GetDingTalkDetailResponse getDingTalkDetailFlow(GetDingTalkWorkFlowRequest request) throws ServiceException;


     /**
      * 根据钉钉审批流里面的附件ID获取附件地址
      * @param processInstanceId 流程ID
      * @param fileId 附件唯一ID
      * @return
      */
     public String getInstanceDownloadFile(String processInstanceId,String fileId,String fileName) throws ServiceException ;


     /**
      * 手动执行钉钉审批流
      * @param request
      */
     public void dealDingFlowResult(DealDingTalkWorkFlowResultRequest request);

     /**
      * 同步钉钉单据详情
      * @param dingTalkNo 钉钉单据号
      */
     void syncDingTalkOrderDetail(String dingTalkNo);
}
