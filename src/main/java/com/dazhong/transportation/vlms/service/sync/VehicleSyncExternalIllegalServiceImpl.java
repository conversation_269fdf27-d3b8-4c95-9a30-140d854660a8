package com.dazhong.transportation.vlms.service.sync;

import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.TableVehicleSyncExternalBusinessInfoService;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalBusinessInfoDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalIllegalDetailDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalIllegalDto;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalInsuranceDetailDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleExternalIllegalResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleExternalInsuranceResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.model.VehicleSyncExternalBusinessInfo;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class VehicleSyncExternalIllegalServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableVehicleSyncExternalBusinessInfoService tableVehicleSyncExternalBusinessInfoService;

    @Override
    public ResultResponse handleSyncData(SyncDataRequest request) {
        //校验参数
        ValidationUtils.validate(request);
        try {
            VehicleSyncExternalIllegalDto params = JSONObject.parseObject(request.getBizRequest(), VehicleSyncExternalIllegalDto.class);
            if (CollectionUtils.isEmpty(params.getVehicleIllegalInfo())){
                return ResultResponse.businessFailed(ExceptionEnum.PARAM_ERR.getMessage());
            }
            List<VehicleSyncExternalBusinessInfoDto> rows = new ArrayList<>();
            params.getVehicleIllegalInfo().forEach(illegal ->{
                VehicleSyncExternalBusinessInfoDto row = new VehicleSyncExternalBusinessInfoDto();
                row.setVin(illegal.getVin());
                row.setLicensePlate(illegal.getLicensePlate());
                row.setBusinessNo(illegal.getIllegalCode());
                row.setBusinessTime(illegal.getIllegalTime());
                row.setBusinessInfo(JSONObject.toJSONString(illegal));
                row.setBusinessType(BizConstant.SyncExternalBusinessType.illegal);
                rows.add(row);
            });
            int insert = tableVehicleSyncExternalBusinessInfoService.batchInsert(rows);
            if (insert > 0) {
                return ResultResponse.success();
            }
        }catch (Exception e){
            log.error("batchInsert illegal exception ",e);
        }
        return ResultResponse.businessFailed("同步违章信息数据异常");
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        try {
            List<VehicleSyncExternalBusinessInfo> illegalInfoList = tableVehicleSyncExternalBusinessInfoService.selectByLicensePlateAndType(request.getLicensePlate(),
                    BizConstant.SyncExternalBusinessType.illegal);
            if (CollectionUtils.isEmpty(illegalInfoList)){
                return ResultResponse.success();
            }
            List<VehicleExternalIllegalResponse> responses = new ArrayList<>();

            illegalInfoList.forEach(illegalInfo ->{
                VehicleExternalIllegalResponse illegalResponse = new VehicleExternalIllegalResponse();
                illegalResponse.setLicensePlate(illegalInfo.getLicensePlate());
                illegalResponse.setSyncDateTime(DateTimeUtils.dateToString(illegalInfo.getCreateTime(),DateTimeUtils.DATE_TYPE1));
                if (StringUtils.isNotBlank(illegalInfo.getBusinessInfo())){
                    VehicleSyncExternalIllegalDetailDto illegalDetailDto = JSONObject.parseObject(illegalInfo.getBusinessInfo(),VehicleSyncExternalIllegalDetailDto.class);
                    if (illegalDetailDto != null){
                        illegalResponse.setIllegalTime(illegalDetailDto.getIllegalTime());
                        illegalResponse.setIllegalAddress(illegalDetailDto.getIllegalAddress());
                        illegalResponse.setIllegalAmount(illegalDetailDto.getIllegalAmount());
                        illegalResponse.setIllegalCode(illegalDetailDto.getIllegalCode());
                        illegalResponse.setIllegalScore(illegalDetailDto.getIllegalScore());
                        illegalResponse.setIllegalDealStatus(illegalDetailDto.getIllegalDealStatus());
                        illegalResponse.setIllegalDegress(illegalDetailDto.getIllegalDegress());
                        illegalResponse.setIllegalAction(illegalDetailDto.getIllegalAction());
                     }
                }
                responses.add(illegalResponse);
            });
            return ResultResponse.success(responses);
        }catch (Exception e){
            log.error("illegal getSyncExternalInfoByLicensePlate exception ",e);
        }
        return ResultResponse.businessFailed("获取车辆违章信息异常");
    }


}
