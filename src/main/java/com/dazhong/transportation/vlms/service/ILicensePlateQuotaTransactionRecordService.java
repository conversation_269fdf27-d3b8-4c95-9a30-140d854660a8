package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.ComboQuotaNumberRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLicensePlateQuotaTransactionRecordRequest;
import com.dazhong.transportation.vlms.dto.request.SearchLogRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateQuotaTransactionRecordRequest;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaTransactionOperateLogResponse;
import com.dazhong.transportation.vlms.dto.response.LicensePlateQuotaTransactionRecordResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;

public interface ILicensePlateQuotaTransactionRecordService {

    /**
     * 查询额度但流水记录
     *
     * @param searchLicensePlateQuotaTransactionRecordRequest 查询参数
     * @return 返回查询结果
     */
    PageResponse<LicensePlateQuotaTransactionRecordResponse> queryPageResponse(SearchLicensePlateQuotaTransactionRecordRequest searchLicensePlateQuotaTransactionRecordRequest);

    /**
     * 导出额度流水记录
     *
     * @param searchLicensePlateQuotaTransactionRecordRequest  查询参数
     * @param tokenUserInfo                                    token用户信息
     */
    void exportTransactionRecord(SearchLicensePlateQuotaTransactionRecordRequest searchLicensePlateQuotaTransactionRecordRequest, TokenUserInfo tokenUserInfo);

    /**
     * 更新额度流水记录
     *
     * @param updateQuotaTransactionRecordRequest 更新参数
     * @param tokenUserInfo                       token用户信息
     * @return 返回更新结果
     */
    ResultResponse<Void> updateQuotaTransactionRecord(UpdateQuotaTransactionRecordRequest updateQuotaTransactionRecordRequest, TokenUserInfo tokenUserInfo);

    /**
     * 查询额度但流水操作日志分页列表
     *
     * @param searchLogRequest 查询参数
     * @return 返回查询结果
     */
    PageResponse<LicensePlateQuotaTransactionOperateLogResponse> queryOperateLogPageResponse(SearchLogRequest searchLogRequest);

    /**
     * 获取额度编号下拉列表
     *
     * @return
     */
    ComboResponse<String, String> queryQuotaNumberCombo(ComboQuotaNumberRequest comboQuotaNumberRequest);
}
