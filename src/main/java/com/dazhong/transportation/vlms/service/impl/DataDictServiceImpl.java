package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.*;
import com.dazhong.transportation.vlms.dto.request.DeleteDataDictRequest;
import com.dazhong.transportation.vlms.dto.request.EditDataDictRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateCompanyExtraInfoRequest;
import com.dazhong.transportation.vlms.dto.request.base.PageRequest;
import com.dazhong.transportation.vlms.dto.response.DataDictResponse;
import com.dazhong.transportation.vlms.dto.response.DataMaintainDictResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.enums.DataDictEnum;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.ICompanyExtraInfoService;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-27 16:32
 */
@Slf4j
@Service
public class DataDictServiceImpl implements IDataDictService {

    @Autowired
    private TableDataAreaInfoService tableDataAreaInfoService;

    @Autowired
    private TableDataDictService tableDataDictService;

    @Autowired
    private TableDataMaintainDictInfoService tableDataMaintainDictInfoService;

    @Autowired
    private TableOrgInfoService tableOrgInfoService;

    @Autowired
    private TableDataSupplierInfoService tableDataSupplierInfoService;

    @Autowired
    private TableDataOwnerInfoService tableDataOwnerInfoService;

    @Autowired
    private ICompanyExtraInfoService companyExtraInfoService;


    @Override
    public List<DataDictInfo> queryDataDictList(PageRequest request) {
        return tableDataDictService.searchDataDictList();
    }

    @Override
    public DataDictResponse queryDataDictByCode(String dataCode) {
        DataDictInfo dataDictInfo = tableDataDictService.queryDataDictByCode(dataCode);
        if (dataDictInfo == null) {
            throw new ServiceException("数据字典不存在");
        }
        DataDictResponse response = new DataDictResponse();
        response.setDataCode(dataDictInfo.getDataCode());
        response.setDataName(dataDictInfo.getDataName());
        response.setCodeType(dataDictInfo.getCodeType());
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 数据字段value值类型 1-数字 2-字符串
            int codeType = dataDictInfo.getCodeType();
            if (codeType == 1){
                List<DataDictDto<Integer>> dataDictList = objectMapper.readValue(dataDictInfo.getDataValue(), new TypeReference<List<DataDictDto<Integer>>>(){});
                response.setDataDictList(dataDictList);
            } else {
                List<DataDictDto<String>> dataDictList = objectMapper.readValue(dataDictInfo.getDataValue(), new TypeReference<List<DataDictDto<String>>>(){});
                response.setDataDictList(dataDictList);
            }
        } catch (JsonProcessingException e) {
            throw new ServiceException("数据字典解析失败");
        }
        return response;
    }

    @Override
    public ResultResponse addDataDict(EditDataDictRequest<String> request) {
        DataDictInfo dictInfo = tableDataDictService.queryDataDictByCode(request.getDataCode());
        if (dictInfo != null) {
            return ResultResponse.businessFailed("数据字典已存在");
        }
        // dataDictList 按照value去重
        List<DataDictDto<String>> dataDictList = request.getDataDictList().stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DataDictDto::getValue))), ArrayList::new)
        );


        if (dataDictList.size() != request.getDataDictList().size()) {
            return ResultResponse.businessFailed("数据字典值不能重复");
        }
        DataDictInfo dataDictInfo = new DataDictInfo();
        dataDictInfo.setDataCode(request.getDataCode());
        dataDictInfo.setDataName(request.getDataName());
        dataDictInfo.setDataValue(JSONUtil.toJsonStr(request.getDataDictList()));
        dataDictInfo.setCodeType(request.getCodeType());
        dataDictInfo.setSystemCode(BizConstant.system_code);
        tableDataDictService.saveDataDict(dataDictInfo);
        return ResultResponse.success();
    }

    @Override
    public ResultResponse updateDataDict(EditDataDictRequest<String> request) {
        DataDictInfo dictInfo = tableDataDictService.queryDataDictByCode(request.getDataCode());
        if (dictInfo == null) {
            return ResultResponse.businessFailed("数据字典已存在");
        }
        // dataDictList 按照value去重
        List<DataDictDto<String>> dataDictList = request.getDataDictList()
                .stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DataDictDto::getValue))), ArrayList::new)
        );

        if (dataDictList.size() != request.getDataDictList().size()) {
            return ResultResponse.businessFailed("数据字典值不能重复");
        }
        dictInfo.setDataCode(request.getDataCode());
        dictInfo.setDataName(request.getDataName());
        dictInfo.setDataValue(JSONUtil.toJsonStr(request.getDataDictList()));
        dictInfo.setCodeType(request.getCodeType());
        dictInfo.setSystemCode(BizConstant.system_code);
        tableDataDictService.updateDataDict(dictInfo);
        return ResultResponse.success();
    }

    @Override
    public ResultResponse deleteDataDict(DeleteDataDictRequest request) {
        DataDictInfo dictInfo = tableDataDictService.queryDataDictByCode(request.getDataCode());
        if (dictInfo == null) {
            return ResultResponse.success();
        }
        tableDataDictService.deleteDataDict(dictInfo.getId());
        return ResultResponse.success();
    }

    @Override
    public DataDictResponse<Integer> queryDataMaintainDict(String systemCode) {
        List<DataDictDto<Integer>> result;

        //针对供应商、资产车辆拥有公司等字典进行特殊处理
        switch (systemCode){
            case "supplier":
                result = querySupplierDict();
                break;
            case "owner":
                result = queryOwnerDict();
                break;
            case "area":
                result = queryAreaDict();
                break;
            case "orgInfo":
                result = queryOrgDict();
                break;
            default:
                result = queryNormalDict(systemCode);
                break;
        }
        return new DataDictResponse(result);
    }

    /**
     * 查询资产车辆拥有公司字典
     * @return 资产车辆拥有公司字典
     */
    private List<DataDictDto<Integer>> queryOwnerDict(){
        List<DataDictDto<Integer>> result = new ArrayList<>();

        //查询资产车辆拥有公司信息
        List<DataOwnerInfo> dataOwnerInfoList = tableDataOwnerInfoService.queryAllOwner();
        for (DataOwnerInfo dataOwnerInfo : dataOwnerInfoList) {
            DataDictDto<Integer> dataDictDto = new DataDictDto<>();
            dataDictDto.setId(dataOwnerInfo.getId());
            dataDictDto.setValue(dataOwnerInfo.getId().intValue());
            dataDictDto.setName(dataOwnerInfo.getName());
            result.add(dataDictDto);
        }
        return result;
    }

    /**
     * 查询供应商字典
     * @return 供应商字典
     */
    private List<DataDictDto<Integer>> querySupplierDict(){
        List<DataDictDto<Integer>> result = new ArrayList<>();

        //查询供应商信息
        List<DataSupplierInfo> dataSupplierInfoList = tableDataSupplierInfoService.queryAllSupplier();
        //对返回结果进行转换
        for (DataSupplierInfo dataSupplierInfo : dataSupplierInfoList) {
            DataDictDto<Integer> dataDictDto = new DataDictDto<>();
            dataDictDto.setId(dataSupplierInfo.getId());
            dataDictDto.setValue(dataSupplierInfo.getId().intValue());
            dataDictDto.setName(dataSupplierInfo.getName());
            result.add(dataDictDto);
        }
        return result;
    }

    /**
     * 查询区域字典表
     * @return 区域字典
     */
    private List<DataDictDto<Integer>> queryAreaDict(){
        List<DataDictDto<Integer>> result = new ArrayList<>();
        //查询区域信息
        List<DataAreaInfo> dataAreaInfoList = tableDataAreaInfoService.queryAllArea();
        //对返回结果进行转换
        for (DataAreaInfo dataAreaInfo : dataAreaInfoList) {
            DataDictDto<Integer> dataDictDto = new DataDictDto<>();
            dataDictDto.setId(dataAreaInfo.getId());
            dataDictDto.setValue(dataAreaInfo.getId().intValue());
            dataDictDto.setName(dataAreaInfo.getName());
            result.add(dataDictDto);
        }
        return result;
    }

    /**
     * 查询字典表
     * @return 区域字典
     */
    private List<DataDictDto<Integer>> queryOrgDict(){
        List<DataDictDto<Integer>> result = new ArrayList<>();
        //查询区域信息
        List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();
        //对返回结果进行转换
        for (OrgInfo orgInfo : orgInfoList) {
            DataDictDto<Integer> dataDictDto = new DataDictDto<>();
            dataDictDto.setId(orgInfo.getId());
            dataDictDto.setValue(orgInfo.getId().intValue());
            dataDictDto.setName(orgInfo.getCompanyName());
            result.add(dataDictDto);
        }
        return result;
    }


    /**
     * 查询普通字段
     * @param systemCode 系统编码
     * @return 普通可维护字典
     */
    private List<DataDictDto<Integer>> queryNormalDict(String systemCode){
        List<DataDictDto<Integer>> result = new ArrayList<>();
        //根据系统编码查询数据字典
        List<DataMaintainDictInfo> dataMaintainDictInfoList = tableDataMaintainDictInfoService.queryBySystemCode(systemCode);
        //对返回结果进行转换
        for (DataMaintainDictInfo dataMaintainDictInfo : dataMaintainDictInfoList) {
            DataDictDto<Integer> dataDictDto = new DataDictDto<>();
            dataDictDto.setId(dataMaintainDictInfo.getId());
            dataDictDto.setValue(Integer.parseInt(dataMaintainDictInfo.getDataCode()));
            dataDictDto.setName(dataMaintainDictInfo.getDataValue());
            result.add(dataDictDto);
        }
        return result;
    }


    @Override
    public void addDataMaintainDict(EditDataDictRequest<Integer> request, TokenUserInfo tokenUserInfo) {
        //获取数据字典的值和名称 - 此处默认单个新增
        DataDictDto dataDictDto = request.getDataDictList().get(0);

        //校验code是否存在重复
        boolean hasDuplicateCode = tableDataMaintainDictInfoService.hasDuplicateMaintainDictDataCode(request.getDataCode(), null, String.valueOf(dataDictDto.getValue()));
        if(hasDuplicateCode){
            throw new ServiceException("数据字典值不能重复");
        }

        DataMaintainDictInfo saveDataMaintainDictInfo = new DataMaintainDictInfo();
        saveDataMaintainDictInfo.setDataName(request.getDataName());
        saveDataMaintainDictInfo.setSystemCode(request.getDataCode());

        saveDataMaintainDictInfo.setDataCode(String.valueOf(dataDictDto.getValue()));
        saveDataMaintainDictInfo.setDataValue(dataDictDto.getName());
        tableDataMaintainDictInfoService.insert(saveDataMaintainDictInfo, tokenUserInfo);
    }

    @Override
    public void updateDataMaintainDict(EditDataDictRequest<Integer> request, TokenUserInfo tokenUserInfo) {
        DataDictDto dataDictDto = request.getDataDictList().get(0);

        //校验code是否存在重复
        boolean hasDuplicateCode = tableDataMaintainDictInfoService.hasDuplicateMaintainDictDataCode(request.getDataCode(), dataDictDto.getId(), String.valueOf(dataDictDto.getValue()));
        if(hasDuplicateCode){
            throw new ServiceException("数据字典值不能重复");
        }

        DataMaintainDictInfo updateDataMaintainDictInfo = new DataMaintainDictInfo();
        //根据ID更新字典表内容
        updateDataMaintainDictInfo.setId(dataDictDto.getId());
        updateDataMaintainDictInfo.setDataCode(String.valueOf(dataDictDto.getValue()));
        updateDataMaintainDictInfo.setDataValue(dataDictDto.getName());
        tableDataMaintainDictInfoService.updateSelectiveById(updateDataMaintainDictInfo, tokenUserInfo);
    }



    @Override
    public Map<String, Map<Integer, String>> getDataMaintainDictMap(List<String> systemCodeList) {
        Map<String, Map<Integer, String>> result = new HashMap<>();
        if(systemCodeList == null || systemCodeList.isEmpty()){
            return result;
        }

        //根据系统编码查询数据字典
        List<DataMaintainDictInfo> dictInfoList =  tableDataMaintainDictInfoService.selectBySystemCode(systemCodeList);
        for (DataMaintainDictInfo dataMaintainDictInfo : dictInfoList) {
            String systemCode = dataMaintainDictInfo.getSystemCode();
            Map<Integer, String> dictMap;
            if(result.containsKey(systemCode)) {
                //获取现有Map
               dictMap = result.get(systemCode);
            }
            else{
                //新建一个Map
                dictMap = new HashMap<>();
            }
            dictMap.put(Integer.valueOf(dataMaintainDictInfo.getDataCode()), dataMaintainDictInfo.getDataValue());
            result.put(systemCode, dictMap);
        }

        //查询供应商字典表
        if(systemCodeList.contains(DataDictEnum.SUPPLIER.getValue())){
            List<DataSupplierInfo> dataSupplierInfoList = tableDataSupplierInfoService.queryAllSupplier();
            for (DataSupplierInfo dataSupplierInfo : dataSupplierInfoList) {
                Map<Integer, String> dictMap;
                if(result.containsKey(DataDictEnum.SUPPLIER.getValue())) {
                    //获取现有Map
                    dictMap = result.get(DataDictEnum.SUPPLIER.getValue());
                }
                else{
                    //新建一个Map
                    dictMap = new HashMap<>();
                }

                dictMap.put(dataSupplierInfo.getId().intValue() , dataSupplierInfo.getName());
                result.put(DataDictEnum.SUPPLIER.getValue(), dictMap);
            }
        }

        //查询车辆拥有公司字典表
        if(systemCodeList.contains(DataDictEnum.OWNER.getValue())){
            List<DataOwnerInfo> dataOwnerInfoList = tableDataOwnerInfoService.queryAllOwner();
            for (DataOwnerInfo dataOwnerInfo : dataOwnerInfoList) {
                Map<Integer, String> dictMap;
                if(result.containsKey(DataDictEnum.OWNER.getValue())) {
                    //获取现有Map
                    dictMap = result.get(DataDictEnum.OWNER.getValue());
                }
                else{
                    //新建一个Map
                    dictMap = new HashMap<>();
                }
                dictMap.put(dataOwnerInfo.getId().intValue() , dataOwnerInfo.getName());
                result.put(DataDictEnum.OWNER.getValue(), dictMap);
            }
        }
        //查询组织架构
        if(systemCodeList.contains(DataDictEnum.ORGANIZATION.getValue())){
            List<OrgInfo> dataOrgInfoList = tableOrgInfoService.queryAllOrgInfo();
            for (OrgInfo orgInfo : dataOrgInfoList) {
                Map<Integer, String> dictMap;
                if(result.containsKey(DataDictEnum.ORGANIZATION.getValue())) {
                    //获取现有Map
                    dictMap = result.get(DataDictEnum.ORGANIZATION.getValue());
                }
                else{
                    //新建一个Map
                    dictMap = new HashMap<>();
                }
                dictMap.put(orgInfo.getId().intValue() , orgInfo.getCompanyName());
                result.put(DataDictEnum.ORGANIZATION.getValue(), dictMap);
            }
        }
        return result;
    }

    @Override
    public Map<String, Map<String, Integer>> getDataMaintainDictValueMap(List<String> systemCodeList) {
        Map<String, Map<String, Integer>> result = new HashMap<>();
        if(systemCodeList == null || systemCodeList.isEmpty()){
            return result;
        }

        //根据系统编码查询数据字典
        List<DataMaintainDictInfo> dictInfoList =  tableDataMaintainDictInfoService.selectBySystemCode(systemCodeList);
        for (DataMaintainDictInfo dataMaintainDictInfo : dictInfoList) {
            String systemCode = dataMaintainDictInfo.getSystemCode();
            Map<String, Integer> dictMap;
            if(result.containsKey(systemCode)) {
                //获取现有Map
                dictMap = result.get(systemCode);
            }
            else{
                //新建一个Map
                dictMap = new HashMap<>();
            }
            dictMap.put(dataMaintainDictInfo.getDataValue(),Integer.valueOf(dataMaintainDictInfo.getDataCode()));
            result.put(systemCode, dictMap);
        }

        //查询供应商字典表
        if(systemCodeList.contains(DataDictEnum.SUPPLIER.getValue())){
            List<DataSupplierInfo> dataSupplierInfoList = tableDataSupplierInfoService.queryAllSupplier();
            for (DataSupplierInfo dataSupplierInfo : dataSupplierInfoList) {
                Map<String, Integer> dictMap;
                if(result.containsKey(DataDictEnum.SUPPLIER.getValue())) {
                    //获取现有Map
                    dictMap = result.get(DataDictEnum.SUPPLIER.getValue());
                }
                else{
                    //新建一个Map
                    dictMap = new HashMap<>();
                }

                dictMap.put(dataSupplierInfo.getName(),dataSupplierInfo.getId().intValue());
                result.put(DataDictEnum.SUPPLIER.getValue(), dictMap);
            }
        }

        //查询车辆拥有公司字典表
        if(systemCodeList.contains(DataDictEnum.OWNER.getValue())){
            List<DataOwnerInfo> dataOwnerInfoList = tableDataOwnerInfoService.queryAllOwner();
            for (DataOwnerInfo dataOwnerInfo : dataOwnerInfoList) {
                Map<String, Integer> dictMap;
                if(result.containsKey(DataDictEnum.OWNER.getValue())) {
                    //获取现有Map
                    dictMap = result.get(DataDictEnum.OWNER.getValue());
                }
                else{
                    //新建一个Map
                    dictMap = new HashMap<>();
                }
                dictMap.put(dataOwnerInfo.getName(),dataOwnerInfo.getId().intValue());
                result.put(DataDictEnum.OWNER.getValue(), dictMap);
            }
        }
        //查询车辆拥有公司字典表
        if(systemCodeList.contains(DataDictEnum.ORGANIZATION.getValue())){
            List<OrgInfo> dataOrgInfoList = tableOrgInfoService.queryAllOrgInfo();
            for (OrgInfo dataOrgInfo : dataOrgInfoList) {
                Map<String, Integer> dictMap;
                if(result.containsKey(DataDictEnum.ORGANIZATION.getValue())) {
                    //获取现有Map
                    dictMap = result.get(DataDictEnum.ORGANIZATION.getValue());
                }
                else{
                    //新建一个Map
                    dictMap = new HashMap<>();
                }
                dictMap.put(dataOrgInfo.getCompanyName(),dataOrgInfo.getId().intValue());
                result.put(DataDictEnum.ORGANIZATION.getValue(), dictMap);
            }
        }
        //查询车辆拥有公司字典表-系统选中的公司信息
        if(systemCodeList.contains(DataDictEnum.CHECK_ORGANIZATION.getValue())){
            List<OrgInfo> dataOrgInfoList = tableOrgInfoService.queryOrgInfoByCheckedState(1);
            for (OrgInfo dataOrgInfo : dataOrgInfoList) {
                Map<String, Integer> dictMap;
                if(result.containsKey(DataDictEnum.CHECK_ORGANIZATION.getValue())) {
                    //获取现有Map
                    dictMap = result.get(DataDictEnum.CHECK_ORGANIZATION.getValue());
                }
                else{
                    //新建一个Map
                    dictMap = new HashMap<>();
                }
                dictMap.put(dataOrgInfo.getCompanyName(),dataOrgInfo.getId().intValue());
                result.put(DataDictEnum.CHECK_ORGANIZATION.getValue(), dictMap);
            }
        }
        return result;
    }

    @Override
    public DataMaintainDictResponse<DataSupplierDto> querySupplierList() {
        List<DataSupplierDto>  result = new ArrayList<>();
        //遍历转换供应商信息
        List<DataSupplierInfo> dataSupplierInfoList = tableDataSupplierInfoService.queryAllSupplier();
        for (DataSupplierInfo dataSupplierInfo : dataSupplierInfoList) {
            DataSupplierDto dataSupplierDto = new DataSupplierDto();
            BeanUtils.copyProperties(dataSupplierInfo, dataSupplierDto);
            result.add(dataSupplierDto);
        }
        return new DataMaintainDictResponse<>(result);
    }

    @Override
    public void createSupplierInfo(DataMaintainDictResponse<DataSupplierDto> request, TokenUserInfo tokenUserInfo) {
        for (DataSupplierDto dataSupplierDto : request.getDataDictList()) {
            //参数校验
            boolean hasDuplicateSupplierName = tableDataSupplierInfoService.hasDuplicateSupplierName(dataSupplierDto.getName(), null);
            if(hasDuplicateSupplierName){
                throw new ServiceException("供应商名称不能重复");
            }

            DataSupplierInfo saveSupplierInfo = new DataSupplierInfo();
            saveSupplierInfo.setName(dataSupplierDto.getName());
            saveSupplierInfo.setAddress(dataSupplierDto.getAddress());
            saveSupplierInfo.setPhone(dataSupplierDto.getPhone());
            tableDataSupplierInfoService.insert(saveSupplierInfo, tokenUserInfo);
        }
    }

    @Override
    public void updateSupplierInfo(DataMaintainDictResponse<DataSupplierDto> request, TokenUserInfo tokenUserInfo) {
        for (DataSupplierDto dataSupplierDto : request.getDataDictList()) {
            //参数校验
            boolean hasDuplicateSupplierName = tableDataSupplierInfoService.hasDuplicateSupplierName(dataSupplierDto.getName(), dataSupplierDto.getId());
            if(hasDuplicateSupplierName){
                throw new ServiceException("供应商名称不能重复");
            }

            DataSupplierInfo updateSupplierInfo = new DataSupplierInfo();
            updateSupplierInfo.setId(dataSupplierDto.getId());
            updateSupplierInfo.setName(dataSupplierDto.getName());
            updateSupplierInfo.setAddress(dataSupplierDto.getAddress());
            updateSupplierInfo.setPhone(dataSupplierDto.getPhone());
            tableDataSupplierInfoService.updateSelectiveById(updateSupplierInfo, tokenUserInfo);
        }
    }

    @Override
    public List<DatabaseDictSyncDto> querySupplierSyncData() {
        List<DatabaseDictSyncDto> result = new ArrayList<>();
        List<DataSupplierInfo> dataSupplierInfoList = tableDataSupplierInfoService.queryAllSupplier();
        for (DataSupplierInfo dataSupplierInfo : dataSupplierInfoList) {
            DatabaseDictSyncDto syncData = new DatabaseDictSyncDto();
            syncData.setId(dataSupplierInfo.getId());
            syncData.setName(dataSupplierInfo.getName());
            syncData.setAddress(dataSupplierInfo.getAddress());
            syncData.setPhone(dataSupplierInfo.getPhone());
            result.add(syncData);
        }
        return result;
    }

    @Override
    public List<DatabaseDictSyncDto> queryOrgSyncData() {
        List<DatabaseDictSyncDto> result = new ArrayList<>();
        List<OrgInfo> orgInfoList = tableOrgInfoService.queryAllOrgInfo();
        for (OrgInfo orgInfo : orgInfoList) {
            DatabaseDictSyncDto syncData = new DatabaseDictSyncDto();
            syncData.setId(orgInfo.getId());
            syncData.setName(orgInfo.getCompanyName());
            syncData.setLevelCode(orgInfo.getCompanyCode());
            syncData.setParentId(orgInfo.getParentId());
            result.add(syncData);
        }
        return result;
    }

    @Override
    public Map<Integer, DataOwnerInfo> getOwnerMap() {
        Map<Integer, DataOwnerInfo> result = new HashMap<>();
        List<DataOwnerInfo> dataOwnerInfoList = tableDataOwnerInfoService.queryAllOwner();
        for (DataOwnerInfo dataOwnerInfo : dataOwnerInfoList) {
            result.put(dataOwnerInfo.getId().intValue(), dataOwnerInfo);
        }
        return result;
    }

    @Override
    public Map<Long, OrgInfo> getOrgMap() {
        Map<Long, OrgInfo> result = new HashMap<>();
        List<OrgInfo> orgList = tableOrgInfoService.queryAllOrgInfo();
        for (OrgInfo orgInfo : orgList) {
            result.put(orgInfo.getId(), orgInfo);
        }
        return result;
    }

    @Override
    public List<DatabaseDictSyncDto> queryOwnerSyncData() {
        List<DatabaseDictSyncDto> result = new ArrayList<>();
        List<DataOwnerInfo> dataOwnerInfoList = tableDataOwnerInfoService.queryAllOwner();
        for (DataOwnerInfo dataOwnerInfo : dataOwnerInfoList) {
            DatabaseDictSyncDto syncData = new DatabaseDictSyncDto();
            syncData.setId(dataOwnerInfo.getId());
            syncData.setName(dataOwnerInfo.getName());
            syncData.setAddress(dataOwnerInfo.getAddress());
            syncData.setPhone(dataOwnerInfo.getPhone());
            result.add(syncData);
        }
        return result;
    }

    @Override
    public DataMaintainDictResponse<DataOwnerDto> queryOwnerList() {
        List<DataOwnerDto> dataOwnerInfoList = tableDataOwnerInfoService.queryAllOwnerList();
        return new DataMaintainDictResponse<>(dataOwnerInfoList);
    }

    @Override
    public void createOwnerInfo(DataMaintainDictResponse<DataOwnerDto> request, TokenUserInfo tokenUserInfo) {
        // 参数校验：确保请求对象和数据列表不为空
        if (request == null || request.getDataDictList() == null || request.getDataDictList().isEmpty()) {
            throw new ServiceException("请求数据不能为空");
        }

        // 遍历处理每个车辆拥有公司信息
        for (DataOwnerDto dataOwnerDto : request.getDataDictList()) {
            // 基础数据校验：公司名称不能为空
            if (dataOwnerDto == null || dataOwnerDto.getName() == null || dataOwnerDto.getName().trim().isEmpty()) {
                throw new ServiceException("车辆拥有公司名称不能为空");
            }

            // 1. 检查用户是否指定了自定义ID
            if (dataOwnerDto.getId() != null) {
                // 用户指定了自定义ID，需要检查该ID是否已被占用
                DataOwnerInfo existingOwnerInfo = tableDataOwnerInfoService.queryOwnerInfoById(dataOwnerDto.getId());
                if (existingOwnerInfo != null) {
                    // ID已被占用，抛出明确的业务异常
                    throw new ServiceException(String.format("指定的ID [%d] 已被占用，请选择其他ID或留空让系统自动生成", dataOwnerDto.getId()));
                }
            }

            // 2. 检查车辆拥有公司名称是否重复（排除当前记录）
            boolean hasDuplicateOwnerName = tableDataOwnerInfoService.hasDuplicateOwnerName(dataOwnerDto.getName().trim(), null);
            if (hasDuplicateOwnerName) {
                throw new ServiceException(String.format("车辆拥有公司名称 [%s] 已存在，不能重复", dataOwnerDto.getName().trim()));
            }

            // 3. 构建保存对象
            DataOwnerInfo saveOwnerInfo = new DataOwnerInfo();

            // 如果用户指定了ID，则使用指定的ID；否则让数据库自动生成
            if (dataOwnerDto.getId() != null) {
                saveOwnerInfo.setId(dataOwnerDto.getId());
            }

            // 设置基本信息（去除首尾空格）
            saveOwnerInfo.setName(dataOwnerDto.getName().trim());
            saveOwnerInfo.setAddress(dataOwnerDto.getAddress() != null ? dataOwnerDto.getAddress().trim() : null);
            saveOwnerInfo.setPhone(dataOwnerDto.getPhone() != null ? dataOwnerDto.getPhone().trim() : null);

            // 4. 保存数据到数据库
            try {
                // 根据是否指定了自定义ID选择不同的保存方法
                if (dataOwnerDto.getId() != null) {
                    // 用户指定了自定义ID，使用支持ID保存的方法
                    tableDataOwnerInfoService.insertById(saveOwnerInfo, tokenUserInfo);
                    log.info("成功新增车辆拥有公司信息（自定义ID）：ID={}, 名称={}, 操作人={}",
                        saveOwnerInfo.getId(), saveOwnerInfo.getName(), tokenUserInfo.getName());
                } else {
                    // 用户未指定ID，使用系统自动生成ID的方法
                    tableDataOwnerInfoService.insert(saveOwnerInfo, tokenUserInfo);
                    log.info("成功新增车辆拥有公司信息（自动生成ID）：ID={}, 名称={}, 操作人={}",
                        saveOwnerInfo.getId(), saveOwnerInfo.getName(), tokenUserInfo.getName());
                }

            } catch (Exception e) {
                // 数据库操作异常处理
                log.error("新增车辆拥有公司信息失败：名称={}, 指定ID={}, 操作人={}, 错误信息={}",
                    dataOwnerDto.getName(), dataOwnerDto.getId(), tokenUserInfo.getName(), e.getMessage(), e);
                throw new ServiceException(String.format("保存车辆拥有公司信息失败：%s", e.getMessage()));
            }

            // TODO: 后续可能需要处理公司额外信息（总经理、钉钉号等）
            // 目前该功能已注释，如需启用请取消以下注释并完善相关逻辑
//            UpdateCompanyExtraInfoRequest infoRequest = new UpdateCompanyExtraInfoRequest();
//            infoRequest.setForeignId(saveOwnerInfo.getId());
//            infoRequest.setBusinessType(2);
//            infoRequest.setCeoName(dataOwnerDto.getCeoName());
//            infoRequest.setCeoPhone(dataOwnerDto.getCeoPhone());
//            infoRequest.setDingTalkNo(dataOwnerDto.getDingTalkNo());
//            companyExtraInfoService.updateCompanyExtraInfo(infoRequest,tokenUserInfo);
        }

        // 批量操作完成日志
        log.info("批量新增车辆拥有公司信息完成，共处理 {} 条记录，操作人：{}",
            request.getDataDictList().size(), tokenUserInfo.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOwnerInfo(DataMaintainDictResponse<DataOwnerDto> request, TokenUserInfo tokenUserInfo) {
        for (DataOwnerDto dataOwnerDto : request.getDataDictList()) {
            //判断车辆拥有公司名称是否重复
            boolean hasDuplicateOwnerName = tableDataOwnerInfoService.hasDuplicateOwnerName(dataOwnerDto.getName(), dataOwnerDto.getId());
            if(hasDuplicateOwnerName){
                throw new ServiceException("车辆拥有公司名称不能重复");
            }

            DataOwnerInfo updateOwnerInfo = new DataOwnerInfo();
            updateOwnerInfo.setId(dataOwnerDto.getId());
            updateOwnerInfo.setName(dataOwnerDto.getName());
            updateOwnerInfo.setAddress(dataOwnerDto.getAddress());
            updateOwnerInfo.setPhone(dataOwnerDto.getPhone());
            tableDataOwnerInfoService.updateSelectiveById(updateOwnerInfo, tokenUserInfo);
//            UpdateCompanyExtraInfoRequest infoRequest = new UpdateCompanyExtraInfoRequest();
//            infoRequest.setForeignId(dataOwnerDto.getId());
//            infoRequest.setBusinessType(2);
//            infoRequest.setCeoName(dataOwnerDto.getCeoName());
//            infoRequest.setCeoPhone(dataOwnerDto.getCeoPhone());
//            infoRequest.setDingTalkNo(dataOwnerDto.getDingTalkNo());
//            companyExtraInfoService.updateCompanyExtraInfo(infoRequest,tokenUserInfo);
        }
    }

    @Override
    public List<DatabaseDictSyncDto> queryMaintainDictSyncData(String systemCode) {
        List<DatabaseDictSyncDto> result = new ArrayList<>();
        if(StringUtils.isBlank(systemCode)){
            return result;
        }

        //根据系统编码查询数据字典
        List<DataMaintainDictInfo> dataMaintainDictInfoList = tableDataMaintainDictInfoService.queryBySystemCode(systemCode);
        for (DataMaintainDictInfo dataMaintainDictInfo : dataMaintainDictInfoList) {
            DatabaseDictSyncDto syncData = new DatabaseDictSyncDto();
            syncData.setId(Long.valueOf(dataMaintainDictInfo.getDataCode()));
            syncData.setName(dataMaintainDictInfo.getDataValue());
            result.add(syncData);
        }
        return result;
    }

    @Override
    public List<DatabaseDictSyncDto> queryAreaSyncData() {
        List<DatabaseDictSyncDto> result = new ArrayList<>();
        List<DataAreaInfo> areaInfoList = tableDataAreaInfoService.queryAllArea();
        for (DataAreaInfo dataAreaInfo : areaInfoList) {
            DatabaseDictSyncDto syncData = new DatabaseDictSyncDto();
            syncData.setId(dataAreaInfo.getId());
            syncData.setName(dataAreaInfo.getName());
            result.add(syncData);
        }
        return result;
    }
}
