package com.dazhong.transportation.vlms.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.database.TableVehicleModelInfoService;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleModelDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleModelListRequest;
import com.dazhong.transportation.vlms.dto.request.UpsertVehicleModelRequest;
import com.dazhong.transportation.vlms.dto.response.GetVehicleBaseInfoResponse;
import com.dazhong.transportation.vlms.dto.response.QueryVehicleBaseListResponse;
import com.dazhong.transportation.vlms.dto.response.SearchDatabaseTableSyncDataResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleModelInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.VehicleModelInfo;
import com.dazhong.transportation.vlms.service.IVehicleModelService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;

@Service
public class VehicleModelServiceImpl implements IVehicleModelService {

    @Autowired
    private TableVehicleModelInfoService tableVehicleModelInfoService;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public VehicleModelInfoResponse getVehicleModelDetail(Long modelId) {
        VehicleModelInfo vehicleModelInfo = tableVehicleModelInfoService.selectById(modelId);
        if (vehicleModelInfo == null) {
            throw new ServiceException("未查询到相关车型信息");
        }
        VehicleModelInfoResponse response = new VehicleModelInfoResponse();
        BeanUtils.copyProperties(vehicleModelInfo, response);
        return response;
    }

    @Override
    public PageResponse<VehicleModelInfoResponse> searchVehicleModelList(SearchVehicleModelListRequest request) {
        //采用PageHelper自动分页
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleModelInfo> vehicleModelInfoList = tableVehicleModelInfoService.searchVehicleModelList(request);
        PageInfo<VehicleModelInfo> pageInfo = new PageInfo<>(vehicleModelInfoList);

        List<VehicleModelInfoResponse> list = new ArrayList<>();
        for (VehicleModelInfo vehicleModelInfo : vehicleModelInfoList) {
            VehicleModelInfoResponse response = new VehicleModelInfoResponse();
            BeanUtils.copyProperties(vehicleModelInfo, response);
            list.add(response);
        }
        return new PageResponse<>(pageInfo.getTotal(), list);
    }

    @Override
    public void createVehicleModel(UpsertVehicleModelRequest request, TokenUserInfo tokenUserInfo) {
        //校验汽车之家车型是否已经被占用
        boolean autohomeDuplicate = tableVehicleModelInfoService.isAutohomeDuplicate(request.getAutohomeVehicleModelId());
        if(autohomeDuplicate){
            throw new ServiceException("汽车之家车型ID已经被占用");
        }
        //校验车型名称是否重复
        boolean modelNameDuplicate = tableVehicleModelInfoService.isModelNameDuplicate(request.getVehicleModelName());
        if(modelNameDuplicate){
            throw new ServiceException("车型名称重复");
        }

        request.setId(null);
        VehicleModelInfo insertVehicleModel = new VehicleModelInfo();
        BeanUtils.copyProperties(request, insertVehicleModel);
        tableVehicleModelInfoService.insert(insertVehicleModel, tokenUserInfo);
    }

    @Override
    public void updateVehicleModel(UpsertVehicleModelRequest request, TokenUserInfo tokenUserInfo) {
        //判断车型是否存在
        VehicleModelInfo modelInfo = tableVehicleModelInfoService.selectById(request.getId());
        if(modelInfo == null){
            throw new ServiceException("车型不存在");
        }
        //校验汽车之家车型是否已经被占用
        boolean autohomeDuplicate = tableVehicleModelInfoService.isAutohomeDuplicate(request.getAutohomeVehicleModelId(), request.getId());
        if(autohomeDuplicate){
            throw new ServiceException("汽车之家车型ID已经被占用");
        }
        //校验车型名称是否重复
        boolean modelNameDuplicate = tableVehicleModelInfoService.isModelNameDuplicate(request.getVehicleModelName(), request.getId());
        if(modelNameDuplicate){
            throw new ServiceException("车型名称重复");
        }
        VehicleModelInfo updateVehicleModel = new VehicleModelInfo();
        BeanUtils.copyProperties(request, updateVehicleModel);
        updateVehicleModel.setCreateTime(modelInfo.getCreateTime());
        updateVehicleModel.setCreateOperId(modelInfo.getCreateOperId());
        updateVehicleModel.setCreateOperName(modelInfo.getCreateOperName());
        tableVehicleModelInfoService.updateCoverById(updateVehicleModel, tokenUserInfo);
        
    }

    @Override
    public ComboResponse<Long, String> comboVehicleModel() {
        ComboResponse<Long, String> result = new ComboResponse<>();
        List<VehicleModelInfo> allVehicleModel = tableVehicleModelInfoService.getAllVehicleModel();
        if(allVehicleModel == null || allVehicleModel.isEmpty()){
            return result;
        }

        for (VehicleModelInfo vehicleModelInfo : allVehicleModel) {
            Long key = vehicleModelInfo.getId();
            String value = vehicleModelInfo.getVehicleModelName();
            ComboResponse.ComboData<Long, String> comboData = new ComboResponse.ComboData<>(key, value);
            result.getList().add(comboData);
        }
        return result;
    }

    @Override
    public ComboResponse<Long, String> comboVehicleModelNo() {
        // 创建返回结果对象
        ComboResponse<Long, String> result = new ComboResponse<>();

        // 获取所有有效的车型信息
        List<VehicleModelInfo> allVehicleModel = tableVehicleModelInfoService.getAllVehicleModel();
        if(allVehicleModel == null || allVehicleModel.isEmpty()){
            // 如果没有数据，返回空列表
            return result;
        }

        // 使用LinkedHashMap保持插入顺序，同时实现按车型编号去重
        // key: 车型编号(vehicleModelNo), value: 车型信息对象
        Map<String, VehicleModelInfo> uniqueVehicleModelMap = new LinkedHashMap<>();

        // 遍历车型信息，进行去重处理
        for (VehicleModelInfo vehicleModelInfo : allVehicleModel) {
            // 检查车型编号是否为空，只有非空的车型编号才加入下拉列表
            if(vehicleModelInfo.getVehicleModelNo() != null &&
               !vehicleModelInfo.getVehicleModelNo().trim().isEmpty()) {

                String vehicleModelNo = vehicleModelInfo.getVehicleModelNo().trim();

                // 去重逻辑：如果该车型编号尚未存在，则添加到Map中
                // 由于使用LinkedHashMap，会保留第一个匹配的记录（按当前排序规则）
                if (!uniqueVehicleModelMap.containsKey(vehicleModelNo)) {
                    uniqueVehicleModelMap.put(vehicleModelNo, vehicleModelInfo);
                }
            }
        }

        // 构建去重后的下拉列表数据
        for (VehicleModelInfo vehicleModelInfo : uniqueVehicleModelMap.values()) {
            Long key = vehicleModelInfo.getId();  // 车型ID作为key
            String value = vehicleModelInfo.getVehicleModelNo().trim();  // 车型编号作为value

            // 创建下拉列表数据项
            ComboResponse.ComboData<Long, String> comboData = new ComboResponse.ComboData<>(key, value);
            result.getList().add(comboData);
        }

        return result;
    }

    @Override
    public ComboResponse<Long, String> comboAutohomeVehicleModel(String autohomeModelName) {
        ComboResponse<Long, String> result = new ComboResponse<>();

        //调用HTTP接口查询
        String url = Global.instance.queryVehicleBaseListUrl;
        Map<String, Object> params = new HashMap<>();
        params.put("field", autohomeModelName);
        ResponseEntity<QueryVehicleBaseListResponse> response = restTemplate.postForEntity(url, params, QueryVehicleBaseListResponse.class);

        //组装返回结果
        if(
            Objects.requireNonNull(response.getBody()).getData() != null &&
            Objects.requireNonNull(response.getBody()).getData().getInfo() != null){
            for (QueryVehicleBaseListResponse.VehicleBaseInfo vehicleBaseInfo : response.getBody().getData().getInfo()) {
                Long key = vehicleBaseInfo.getId();
                String value = vehicleBaseInfo.getFullModelName();
                ComboResponse.ComboData<Long, String> comboData = new ComboResponse.ComboData<>(key, value);
                result.getList().add(comboData);
            }
        }
        return result;
    }

    @Override
    public VehicleModelInfoResponse getVehicleModelDetailByAutohome(Long autohomeModelId) {
        VehicleModelInfoResponse result = new VehicleModelInfoResponse();

        //调用HTTP接口查询
        String url = Global.instance.getVehicleBaseInfoUrl;
        Map<String, Object> params = new HashMap<>();
        params.put("id", autohomeModelId);
        ResponseEntity<GetVehicleBaseInfoResponse> response = restTemplate.postForEntity(url, params, GetVehicleBaseInfoResponse.class);
        if(
                Objects.requireNonNull(response.getBody()).getData() != null &&
                Objects.requireNonNull(response.getBody()).getData().getInfo() != null){
            GetVehicleBaseInfoResponse.VehicleBaseInfo vehicleBaseInfo = response.getBody().getData().getInfo();

            //转换汽车之家字段至大众出行字段
            //汽车之家ID
            result.setAutohomeVehicleModelId(vehicleBaseInfo.getId());
            //车型名称
            result.setVehicleModelName(vehicleBaseInfo.getFullModelName());
            //品牌
            result.setVehicleBrandName(transAutohomeModelValue(vehicleBaseInfo.getSerialTypeName()));
            //车系
            result.setVehicleSeriesName(transAutohomeModelValue(vehicleBaseInfo.getVehicleSeriesName()));
            //座位数
            result.setAssessPassenger(transAutohomeModelValue(vehicleBaseInfo.getSeatCount()));
            //发动机型号
            result.setEngineModelNo(transAutohomeModelValue(vehicleBaseInfo.getEngineType()));
            //燃料类型
            result.setGasTypeId(transGasType(vehicleBaseInfo.getFuelTypeName()));
            //上市时间
            result.setTtmMonth(transAutohomeModelValue(vehicleBaseInfo.getTtmMonth()));
            //车身长度
            result.setOutLength(transAutohomeModelValue(vehicleBaseInfo.getBodyLength()));
            //车身宽度
            result.setOutWidth(transAutohomeModelValue(vehicleBaseInfo.getBodyWidth()));
            //车身高度
            result.setOutHeight(transAutohomeModelValue(vehicleBaseInfo.getBodyHeight()));
            //车身轴距
            result.setWheelBase1(transAutohomeModelValue(vehicleBaseInfo.getWheelBase()));
            //总质量
            result.setTotalMass(transAutohomeModelValue(vehicleBaseInfo.getCurbWeight()));
            //油箱容积 L
            result.setFuelTankCapacity(transAutohomeModelValue(vehicleBaseInfo.getTankage()));
            //燃油标号
            result.setFuelLabelName(transAutohomeModelValue(vehicleBaseInfo.getFuelLabelName()));
            //发动机排量 ml
            result.setCapacity(transAutohomeModelBigDecimalValue(vehicleBaseInfo.getEngineDisplacementMl()));
            //电池容量
            result.setBatteryCapacity(transAutohomeModelBigDecimalValue(vehicleBaseInfo.getBatteryPower()));
            //车辆续驶里程
            result.setVehicleRange((transAutohomeModelValue(vehicleBaseInfo.getActualTotal())));
            if(result.getVehicleRange() == null){
                result.setVehicleRange(transAutohomeModelValue(vehicleBaseInfo.getCltcTotal()));
            }
            
            //百公里油耗
            result.setFuelEconomyMiit(transAutohomeModelValue(vehicleBaseInfo.getWltcCost()));
            //前轮距
            result.setTreadFront(transAutohomeModelValue(vehicleBaseInfo.getWheelFront()));
            //后轮距
            result.setTreadRear(transAutohomeModelValue(vehicleBaseInfo.getWheelBack()));
            //车门数
            result.setDoorQuantity(transAutohomeModelValue(vehicleBaseInfo.getDoorCount()));
            //0-100加速
            result.setAcceleration(transAutohomeModelBigDecimalValue(vehicleBaseInfo.getHundredKilometerAcceleration()));
            //高速度
            result.setSpeed(transAutohomeModelIntValue(vehicleBaseInfo.getTopSpeed()));
            //扭矩
            result.setTorque(transAutohomeModelIntValue(vehicleBaseInfo.getTorque()));
            //轮胎规格
            result.setWheelParam(transAutohomeModelValue(vehicleBaseInfo.getWheelSize()));
            //厂商指导价
            result.setPriceOnAutohome(transAutohomeModelIntValue(vehicleBaseInfo.getMrsp()));
        }
        return result;
    }

    @Override
    public SearchDatabaseTableSyncDataResponse<SyncDatabaseVehicleModelDto> searchVehicleModelDatabaseSyncData(SearchDatabaseTableSyncDataRequest request) {
        List<SyncDatabaseVehicleModelDto> list = tableVehicleModelInfoService.searchVehicleModelDatabaseSyncInfo(request);

        //记录标识位，用于下一次基于该标识继续查询
        Long lastIndex = null;
        if(!list.isEmpty()){
            lastIndex = list.get(list.size() - 1).getId();
        }
        return new SearchDatabaseTableSyncDataResponse<>("VehicleModel", list, lastIndex);
    }

    /**
     * 1:汽油
     * 2:柴油
     * 3:LPG
     * 4:电力
     * 5:油气混合
     * 6:油电混合
     * @param fuelType 汽车之家燃料类型
     * @return
     */
    private Integer transGasType(String fuelType){
        switch (fuelType){
            case "汽油":
                return 1;
            case "增程式":
            case "插电式混合动力":
            case "油电混合":
                return 6;
            case "纯电动":
                return 4;
            case "柴油":
                return 2;
            default:
                return null;
        }
    }


    /**
     * 转换汽车之家字段至大众出行字段
     * @param value String值
     * @return 转换后的值
     */
    private BigDecimal transAutohomeModelBigDecimalValue(String value) {
        if(StringUtils.isBlank(value)){
            return null;
        }
        try{
            return new BigDecimal(value);
        }catch (Exception e){
            return null;
        }
    }

    private Integer transAutohomeModelIntValue(String value){
        if(StringUtils.isBlank(value)){
            return null;
        }
        try{
            return Integer.valueOf(value);
        }catch (Exception e){
            return null;
        }
    }

    private BigDecimal transAutohomeModelBigDecimalValue(Integer value){
        if(value == null || value == 0){
            return null;
        }
        return BigDecimal.valueOf(value);
    }

    /**
     * 转换汽车之家字段至大众出行字段
     * @param value Int值
     * @return 转换后的值
     */
    private Integer transAutohomeModelValue(Integer value){
        //由于通过krpc获取数据，部分空值会被转换为默认值0，需要特殊处理
        if(value == null || value == 0){
            return null;
        }
        return value;
    }

    /**
     * 转换汽车之家字段至大众出行字段
     * @param value String值
     * @return 转换后的值
     */
    private String transAutohomeModelValue(String value){
        //由于通过krpc获取数据，部分空值会被转换为默认值空字符串，需要特殊处理
        if(StringUtils.isBlank(value)){
            return null;
        }
        return value;
    }

    /**
     * 转换汽车之家字段至大众出行字段
     * @param value BigDecimal值
     * @return 转换后的值
     */
    private BigDecimal transAutohomeModelValue(BigDecimal value){
        if(value == null){
            return null;
        }
        if(BigDecimal.ZERO.compareTo(value) == 0) {
            return null;
        }
        return value;
    }

}
