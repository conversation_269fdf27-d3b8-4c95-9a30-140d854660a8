package com.dazhong.transportation.vlms.service.sync;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.database.TableVehicleService;
import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleStatusDTO;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalStatusDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class VehicleSyncExternalStatusServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableVehicleService tableVehicleService;

    @Override
    @Transactional
    public ResultResponse handleSyncData(SyncDataRequest request) {
        //校验参数
        ValidationUtils.validate(request);
        try {
            VehicleSyncExternalStatusDto params = JSONObject.parseObject(request.getBizRequest(), VehicleSyncExternalStatusDto.class);
            if (CollectionUtil.isEmpty(params.getSyncDatabaseVehicleStatusList())) {
                return ResultResponse.businessFailed(ExceptionEnum.PARAM_ERR.getMessage());
            }

            // 按运营状态和所属车队分组，以便批量更新
            Map<Integer, List<String>> operatingStatusMap = new HashMap<>();
            Map<String, List<String>> belongingTeamMap = new HashMap<>();

            // 验证参数并分组
            for (SyncDatabaseVehicleStatusDTO dto : params.getSyncDatabaseVehicleStatusList()) {
                if (StringUtils.isBlank(dto.getVin()) && StringUtils.isBlank(dto.getVehicleAssetId())) {
                    return ResultResponse.businessFailed("参数错误，车辆信息缺失");
                }

                // 只处理有车架号的记录
                if (StringUtils.isBlank(dto.getVin())) {
                    continue;
                }

                // 按运营状态分组
                if (dto.getOperatingStatus() != null) {
                    operatingStatusMap.computeIfAbsent(dto.getOperatingStatus(), k -> new ArrayList<>())
                        .add(dto.getVin());
                }

                // 按所属车队分组
                if (StringUtils.isNotBlank(dto.getBelongingTeam())) {
                    belongingTeamMap.computeIfAbsent(dto.getBelongingTeam(), k -> new ArrayList<>())
                        .add(dto.getVin());
                }
            }

            // 批量更新运营状态
            for (Map.Entry<Integer, List<String>> entry : operatingStatusMap.entrySet()) {
                Integer operatingStatus = entry.getKey();
                List<String> vinList = entry.getValue();
                if (CollectionUtil.isNotEmpty(vinList)) {
                    int updatedCount = tableVehicleService.batchUpdateVehicleOperatingStatus(vinList, operatingStatus);
                    log.info("批量更新运营状态 {} 的车辆 {} 辆，成功更新 {} 条记录", operatingStatus, vinList.size(), updatedCount);
                }
            }

            // 批量更新所属车队
            for (Map.Entry<String, List<String>> entry : belongingTeamMap.entrySet()) {
                String belongingTeam = entry.getKey();
                List<String> vinList = entry.getValue();
                if (CollectionUtil.isNotEmpty(vinList)) {
                    int updatedCount = tableVehicleService.batchUpdateVehicleBelongingTeam(vinList, belongingTeam);
                    log.info("批量更新所属车队 {} 的车辆 {} 辆，成功更新 {} 条记录", belongingTeam, vinList.size(), updatedCount);
                }
            }

        } catch (Exception e) {
            log.error("syncVehicleStatus exception ", e);
            return ResultResponse.businessFailed("同步所属车队、运营状态异常");
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        return null;
    }
}
