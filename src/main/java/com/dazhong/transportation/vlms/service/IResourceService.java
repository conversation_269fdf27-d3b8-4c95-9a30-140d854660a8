package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveResourceRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateResourceRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;

/**
 * 资源服务
 * <AUTHOR>
 * @date 2024-12-20 16:37
 */
public interface IResourceService {


    /**
     * 新增资源
     * @param request
     * @param tokenUserInfo
     * @return
     */
    ResultResponse addResource(SaveResourceRequest request,TokenUserInfo tokenUserInfo);

    /**
     * 删除资源
     * @param id
     * @return
     */
    ResultResponse deleteResource(long id,TokenUserInfo tokenUserInfo);

    /**
     * 修改资源
     * @param request
     * @return
     */
    ResultResponse updateResource(UpdateResourceRequest request,TokenUserInfo tokenUserInfo);


    /**
     * 查询用户权限资源树
     * @param tokenUserInfo
     * @return
     */
    ResultResponse queryUserResourceTree(TokenUserInfo tokenUserInfo);

}
