package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveRoleRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateRoleRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateRoleStatusRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseIdRequest;
import com.dazhong.transportation.vlms.dto.response.ResourceTreeResponse;
import com.dazhong.transportation.vlms.dto.response.RoleResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.ResourceInfo;
import com.dazhong.transportation.vlms.model.RoleInfo;
import com.dazhong.transportation.vlms.model.RoleResourceInfo;
import com.dazhong.transportation.vlms.service.IRoleService;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class RoleServiceImpl implements IRoleService {

    @Autowired
    private TableRoleService tableRoleService;

    @Autowired
    private TableResourceService tableResourceService;

    @Autowired
    private TableRoleResourceService tableRoleResourceService;

    @Autowired
    private TableUserRoleService tableUserRoleService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Override
    public List<RoleInfo> queryRoleList(TokenUserInfo tokenUserInfo) {
        return tableRoleService.queryAllRoleList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse deleteRole(BaseIdRequest request, TokenUserInfo tokenUserInfo) {
        // 验证角色名称是否存在
        RoleInfo roleInfo = tableRoleService.selectById(request.getId());
        if (roleInfo == null) {
            return ResultResponse.success();
        }
        tableRoleService.deleteRoleInfo(roleInfo.getId());
        // 删除角色资源关联信息
        tableRoleResourceService.deleteByRoleId(roleInfo.getId());
        // 删除角色用户关联信息
        tableUserRoleService.deleteByRoleId(roleInfo.getId());
        String content = StrUtil.format("删除了角色【{}】", roleInfo.getRoleName());
        tableOperateLogService.insertLog(roleInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_2, content, tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse updateRoleStatus(UpdateRoleStatusRequest request, TokenUserInfo tokenUserInfo) {
        RoleInfo roleInfo = tableRoleService.selectById(request.getId());
        if (roleInfo == null) {
            return ResultResponse.businessFailed("角色不存在");
        }
        // 角色状态 1-启用 2-禁用
        int roleStatus = request.getRoleStatus();
        int compareRoleStatus = roleInfo.getRoleStatus();
        if (roleStatus != compareRoleStatus) {
            roleInfo.setRoleStatus(roleStatus);
            tableRoleService.updateRoleInfo(roleInfo);
        }
        if (roleStatus == 2) {
            String content = StrUtil.format("禁用了角色【{}】", roleInfo.getRoleName());
            tableOperateLogService.insertLog(roleInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_4, content, tokenUserInfo);
        } else {
            String content = StrUtil.format("启用了角色【{}】", roleInfo.getRoleName());
            tableOperateLogService.insertLog(roleInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_3, content, tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse addRole(SaveRoleRequest request, TokenUserInfo tokenUserInfo) {
        // 验证角色名称是否存在
        RoleInfo roleInfo = tableRoleService.queryRoleInfoByName(request.getRoleName());
        if (roleInfo != null) {
            return ResultResponse.businessFailed("角色名已经被占用");
        }
        // 判断选择的资源是否存在
        List<RoleResourceInfo> resourceInfoList = new ArrayList<>();
        RoleResourceInfo roleResourceInfo = null;
        List<Long> resourceIdList = request.getResourceIdList();
        for (Long resourceId : resourceIdList) {
            // 验证资源是否存在
            ResourceInfo resourceInfo = tableResourceService.selectById(resourceId);
            if (resourceInfo == null) {
                return ResultResponse.businessFailed(StrUtil.format("{}-{}", resourceId, "资源点不存在"));
            }
            roleResourceInfo = new RoleResourceInfo();
            roleResourceInfo.setResourceId(resourceId);
            resourceInfoList.add(roleResourceInfo);
        }
        roleInfo = new RoleInfo();
        roleInfo.setRoleName(request.getRoleName());
        roleInfo.setRoleStatus(request.getRoleStatus());
        roleInfo.setSystemCode(BizConstant.system_code);
        tableRoleService.addRoleInfo(roleInfo);
        for (RoleResourceInfo resourceInfo : resourceInfoList) {
            resourceInfo.setRoleId(roleInfo.getId());
        }
        tableRoleResourceService.batchInsert(resourceInfoList);
        // 记录日志
        String content = StrUtil.format("新增角色，角色名称为【{}】，", request.getRoleName());
        tableOperateLogService.insertLog(roleInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_1, content, tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse updateRole(UpdateRoleRequest request, TokenUserInfo tokenUserInfo) {
        // 判断角色是否存在
        RoleInfo roleInfo = tableRoleService.selectById(request.getId());
        if (roleInfo == null) {
            return ResultResponse.businessFailed("角色信息不存在");
        }
        StringBuffer logBuffer = new StringBuffer();
        // 判断角色名是否已经被占用
        if (!roleInfo.getRoleName().equals(request.getRoleName())) {
            RoleInfo roleInfoByName = tableRoleService.queryRoleInfoByName(request.getRoleName());
            if (roleInfoByName != null) {
                return ResultResponse.businessFailed("角色名已经被占用");
            }
            roleInfo.setRoleName(request.getRoleName());
            String content = StrUtil.format("修改角色，角色名称由【{}】，改为：【{}】", roleInfo.getRoleName(), request.getRoleName());
            logBuffer.append(content);
        }
        if (roleInfo.getRoleStatus() != request.getRoleStatus()) {
            roleInfo.setRoleStatus(request.getRoleStatus());
            if (request.getRoleStatus() == 2) {
                String content = StrUtil.format(" 禁用了角色【{}】", roleInfo.getRoleName());
                logBuffer.append(content);
            } else {
                String content = StrUtil.format(" 启用了角色【{}】", roleInfo.getRoleName());
                logBuffer.append(content);
            }
        }
        tableRoleService.updateRoleInfo(roleInfo);

        List<Long> resourceIdList = request.getResourceIdList();
        if (CollectionUtil.isNotEmpty(resourceIdList)){
            // 删除修改角色下所有资源，重新添加新的资源信息
            List<RoleResourceInfo> resourceInfoList = new ArrayList<>();
            RoleResourceInfo roleResourceInfo = null;
            for (Long resourceId : resourceIdList) {
                // 验证资源是否存在
                ResourceInfo resourceInfo = tableResourceService.selectById(resourceId);
                if (resourceInfo == null) {
                    return ResultResponse.businessFailed(StrUtil.format("{}-{}", resourceId, "资源点不存在"));
                }
                roleResourceInfo = new RoleResourceInfo();
                roleResourceInfo.setResourceId(resourceId);
                roleResourceInfo.setRoleId(roleInfo.getId());
                resourceInfoList.add(roleResourceInfo);
            }
            // 删除角色资源信息
            tableRoleResourceService.deleteByRoleId(roleInfo.getId());
            // 重新添加角色资源
            tableRoleResourceService.batchInsert(resourceInfoList);
        }

        // 记录日志
        if (StringUtils.isNotBlank(logBuffer.toString())){
            tableOperateLogService.insertLog(roleInfo.getId(), BizConstant.BusinessType.businessType_1, BizConstant.OperateType.OperateType_2, logBuffer.toString(), tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse queryRoleResourceTree(Long roleId) {
        RoleInfo roleInfo = tableRoleService.selectById(roleId);
        if (roleInfo == null) {
            return ResultResponse.businessFailed("角色信息不存在");
        }
        List<Long> roleIdList = Arrays.asList(roleId);
        List<ResourceInfo> list = tableResourceService.queryhRoleResourceList(roleIdList);
        if (CollectionUtil.isEmpty(list)) {
            return ResultResponse.success();
        }
        ResourceTreeResponse treeResponse = CommonUtils.getResourceTree(list);
        return ResultResponse.success(treeResponse);
    }

    @Override
    public ResultResponse queryRoleInfo(Long roleId) {
        RoleInfo roleInfo = tableRoleService.selectById(roleId);
        if (roleInfo == null) {
            return ResultResponse.businessFailed("角色信息不存在");
        }
        RoleResponse roleResponse = new RoleResponse();
        roleResponse.setId(roleInfo.getId());
        roleResponse.setRoleName(roleInfo.getRoleName());
        roleResponse.setRoleStatus(roleInfo.getRoleStatus());
        List<Long> roleIdList = Arrays.asList(roleId);
        List<ResourceInfo> list = tableResourceService.queryhRoleResourceList(roleIdList);
        if (CollectionUtil.isNotEmpty(list)) {
            roleResponse.setResourceIdList(list.stream().map(ResourceInfo::getId).collect(Collectors.toList()));
        }
        return ResultResponse.success(roleResponse);
    }
}
