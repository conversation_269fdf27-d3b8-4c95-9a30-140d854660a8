package com.dazhong.transportation.vlms.service.sync;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dazhong.transportation.vlms.database.TableOrgInfoService;
import com.dazhong.transportation.vlms.dto.VehicleSyncExternalOrgDto;
import com.dazhong.transportation.vlms.dto.request.GetVehicleSyncExternalRequest;
import com.dazhong.transportation.vlms.dto.request.SyncDataRequest;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.model.OrgInfo;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VehicleSyncExternalCompanyServiceImpl implements IHandleSyncDataService {

    @Autowired
    private TableOrgInfoService tableOrgInfoService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse handleSyncData(SyncDataRequest request) {
        log.info("同步组织架构数据:{}", request);
        String bizRequest = request.getBizRequest();
        JSONObject jsonObject = JSONUtil.parseObj(bizRequest);
        JSONArray jsonArray = jsonObject.getJSONArray("companyInfo");
        if (ObjectUtil.isEmpty(jsonArray)) {
            return ResultResponse.businessFailed("同步组织架构数据为空");
        }

        // 校验参数是否为空
        List<VehicleSyncExternalOrgDto> syncExternalOrgList = jsonArray.toList(VehicleSyncExternalOrgDto.class);
        for (VehicleSyncExternalOrgDto dto : syncExternalOrgList) {
            ValidationUtils.validate(dto);
        }

        // syncExternalOrgList 按照 orgType 进行过滤 获取公司列表数据
        List<VehicleSyncExternalOrgDto> companyList = syncExternalOrgList.stream()
                .filter(orgInfo -> orgInfo.getOrgType() == 0)
                .collect(Collectors.toList());

        // companyList 获取id重复的数据
        List<VehicleSyncExternalOrgDto> repeatList = companyList.stream()
                .filter(vehicleSyncExternalOrgDto -> companyList.stream().filter(vehicleSyncExternalOrgDto1 -> vehicleSyncExternalOrgDto1.getId().equals(vehicleSyncExternalOrgDto.getId())).count() > 1)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(repeatList)){
            throw new ServiceException("导入数据有重复");
        }

        // 获取选中的机构
        List<OrgInfo> checkedList = tableOrgInfoService.queryOrgInfoByCheckedState(1);
        Map<String, OrgInfo> checkedOrgMap = checkedList.stream().collect(Collectors.toMap(OrgInfo::getVgUid, orgInfo -> orgInfo));

        List<OrgInfo> list = new ArrayList<>();
        Map<String, VehicleSyncExternalOrgDto> orgMap = companyList.stream().collect(Collectors.toMap(VehicleSyncExternalOrgDto::getVgUid, orgInfo -> orgInfo));
        tableOrgInfoService.deleteOrgInfo();
        for (VehicleSyncExternalOrgDto dto : companyList) {
            OrgInfo orgInfo = new OrgInfo();
            orgInfo.setOrgType("公司");
            orgInfo.setId(dto.getId());
            orgInfo.setCompanyCode(dto.getCompanyCode());
            orgInfo.setCompanyName(dto.getCompanyName());
            orgInfo.setVgUid(dto.getVgUid());
            orgInfo.setPgUid(dto.getPgUid());
            orgInfo.setPosition(Math.toIntExact(dto.getId()));
            OrgInfo checkedOrg = checkedOrgMap.get(dto.getVgUid());
            orgInfo.setCheckedState(2);
            if (checkedOrg != null){
                orgInfo.setCheckedState(1);
            }
            // 状态 2-启用 1-停用
            orgInfo.setIsDeleted(1);
            if (dto.getVStatus() == 2){
                orgInfo.setIsDeleted(0);
                orgInfo.setCompanyName(dto.getCompanyName());
            }
            // 父节点id
            String pgUid = dto.getPgUid();
            if (StringUtils.isNotBlank(pgUid)){
                VehicleSyncExternalOrgDto parentOrg = orgMap.get(pgUid);
                if (parentOrg != null){
                    orgInfo.setParentId(parentOrg.getId());
                }
            }
            list.add(orgInfo);
        }
        tableOrgInfoService.batchInsertOrgInfo(list);
        return ResultResponse.success();
    }

    @Override
    public ResultResponse getSyncExternalInfoByLicensePlate(GetVehicleSyncExternalRequest request) {
        return null;
    }
}
