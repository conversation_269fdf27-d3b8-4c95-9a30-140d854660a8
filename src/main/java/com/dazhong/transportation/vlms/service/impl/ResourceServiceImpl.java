package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.dazhong.transportation.vlms.constant.BizConstant;
import com.dazhong.transportation.vlms.database.*;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SaveResourceRequest;
import com.dazhong.transportation.vlms.dto.request.UpdateResourceRequest;
import com.dazhong.transportation.vlms.dto.response.ResourceTreeResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.model.OperateLog;
import com.dazhong.transportation.vlms.model.ResourceInfo;
import com.dazhong.transportation.vlms.model.UserInfo;
import com.dazhong.transportation.vlms.model.UserRoleInfo;
import com.dazhong.transportation.vlms.service.IResourceService;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-12-20 16:38
 */
@Service
public class ResourceServiceImpl implements IResourceService {

    @Autowired
    private TableResourceService tableResourceService;

    @Autowired
    private TableUserRoleService tableUserRoleService;

    @Autowired
    private TableUserService tableUserService;

    @Autowired
    private TableOperateLogService tableOperateLogService;

    @Autowired
    private TableRoleService tableRoleService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse addResource(SaveResourceRequest request, TokenUserInfo tokenUserInfo) {
        // 判断父节点菜单是否存在
        ResourceInfo parentResource = tableResourceService.selectById(request.getParentResourceId());
        if (parentResource == null) {
            return ResultResponse.businessFailed("父节点菜单不存在");
        }
        // 判断资源key是否存在
        ResourceInfo resource = tableResourceService.queryResourceByKey(request.getResourceKey());
        if (resource != null) {
            return ResultResponse.businessFailed("资源key已存在");
        }
        ResourceInfo resourceInfo = BeanUtil.copyProperties(request,ResourceInfo.class);
        resourceInfo.setSystemCode(BizConstant.system_code);
        int num = tableResourceService.saveResource(resourceInfo);
        if (num == 0) {
            return ResultResponse.businessFailed("资源信息新增失败");
        }
        // 记录日志
        OperateLog log = new OperateLog();
        log.setForeignId(resourceInfo.getId());
        log.setBusinessType(BizConstant.BusinessType.businessType_1);
        log.setOperateType(BizConstant.OperateType.OperateType_6);
        log.setOperateContent(StrUtil.format("新增资源-{}", resourceInfo.getResourceName()));
        log.setCreateOperName(tokenUserInfo.getName());
        log.setCreateOperId(tokenUserInfo.getUserId());
        return ResultResponse.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultResponse deleteResource(long id,TokenUserInfo tokenUserInfo) {
        ResourceInfo info = tableResourceService.selectById(id);
        if (info == null) {
            return ResultResponse.businessFailed("资源信息不存在");
        }
        if (info.getParentResourceId() == -1) {
            return ResultResponse.businessFailed("根资源不可删除");
        }
        List<ResourceInfo> list = tableResourceService.queryResourceByParentId(id);
        if (CollectionUtil.isNotEmpty(list)) {
            return ResultResponse.businessFailed("请先删除全部子资源");
        }
        tableResourceService.deleteResource(id);
        tableOperateLogService.insertLog(id,BizConstant.BusinessType.businessType_1,
                BizConstant.OperateType.OperateType_8,StrUtil.format("删除资源-{}",info.getResourceName()),tokenUserInfo);
        return ResultResponse.success();
    }

    @Override
    public ResultResponse updateResource(UpdateResourceRequest request,TokenUserInfo tokenUserInfo) {
        // 判断资源是否存在
        ResourceInfo info = tableResourceService.selectById(request.getId());
        if (info == null) {
            return ResultResponse.businessFailed("资源信息不存在");
        }
        String resourceName = info.getResourceName();
        // 判断资源key是否重复
        if (StrUtil.isNotBlank(request.getResourceKey()) && !info.getResourceKey().equals(request.getResourceKey())) {
            ResourceInfo resource = tableResourceService.queryResourceByKey(request.getResourceKey());
            if (resource != null) {
                return ResultResponse.businessFailed("资源key已存在");
            }
        }
        ResourceInfo resourceInfo = BeanUtil.copyProperties(request,ResourceInfo.class);
        resourceInfo.setId(info.getId());
        tableResourceService.updateResource(resourceInfo);
        if (!request.getResourceName().equals(resourceName)){
            tableOperateLogService.insertLog(info.getId(), BizConstant.BusinessType.businessType_1,
                    BizConstant.OperateType.OperateType_7,StrUtil.format("修改资源名称:{}->{}",info.getResourceName(),request.getResourceName()),tokenUserInfo);
        }
        return ResultResponse.success();
    }

    @Override
    public ResultResponse queryUserResourceTree(TokenUserInfo tokenUserInfo) {
        UserInfo userInfo = tableUserService.selectById(tokenUserInfo.getUserId());
        if (userInfo == null){
            return ResultResponse.businessFailed("用户不存在");
        }
        // 是否超管 1-是 2-否
        int isSystemAdmin = userInfo.getIsSystemAdmin();
        if (isSystemAdmin == BizConstant.SystemAdminType.isAdministrator){
            List<ResourceInfo> list = tableResourceService.qeuryAllResourceList();
            if (CollectionUtil.isEmpty(list)) {
                return ResultResponse.success();
            }
            ResourceTreeResponse treeResponse = CommonUtils.getResourceTree(list);
            return ResultResponse.success(treeResponse);
        }
        // 查询用户关联角色列表
        List<UserRoleInfo> userRoleInfos = tableUserRoleService.queryUserRoleByUserId(tokenUserInfo.getUserId());
        if (CollectionUtil.isEmpty(userRoleInfos)){
            return ResultResponse.success();
        }
        List<Long> roleIdList = userRoleInfos.stream().map(userRole->userRole.getRoleId()).distinct().collect(Collectors.toList());
        List<ResourceInfo> list = tableResourceService.queryhRoleResourceList(roleIdList);
        if (CollectionUtil.isEmpty(list)){
            return ResultResponse.businessFailed("用户角色未配置资源信息");
        }
        ResourceTreeResponse treeResponse = CommonUtils.getResourceTree(list);
        return ResultResponse.success(treeResponse);
    }
}
