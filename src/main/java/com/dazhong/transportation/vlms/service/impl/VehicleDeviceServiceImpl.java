package com.dazhong.transportation.vlms.service.impl;

import com.dazhong.transportation.vlms.database.TableVehicleDeviceInfoService;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDeviceListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.service.IVehicleDeviceService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VehicleDeviceServiceImpl implements IVehicleDeviceService {

    @Autowired
    private TableVehicleDeviceInfoService tableVehicleDeviceInfoService;

    @Override
    public PageResponse<VehicleDeviceInfoResponse> searchVehicleDeviceList(SearchVehicleDeviceListRequest request) {
        //采用PageHelper自动分页
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<VehicleDeviceInfoResponse> list = tableVehicleDeviceInfoService.searchVehicleDeviceList(request);
        PageInfo<VehicleDeviceInfoResponse> pageInfo = new PageInfo<>(list);
        return new PageResponse<>(pageInfo.getTotal(), list);
    }
}
