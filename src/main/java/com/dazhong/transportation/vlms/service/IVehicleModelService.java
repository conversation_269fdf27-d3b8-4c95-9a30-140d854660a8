package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.SyncDatabaseVehicleModelDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchDatabaseTableSyncDataRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleModelListRequest;
import com.dazhong.transportation.vlms.dto.request.UpsertVehicleModelRequest;
import com.dazhong.transportation.vlms.dto.response.SearchDatabaseTableSyncDataResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleModelInfoResponse;

public interface IVehicleModelService {

    /**
     * 创建车型信息
     * @param request 车型信息
     * @param tokenUserInfo 操作人信息
     */
    void createVehicleModel(UpsertVehicleModelRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 获取车型详情
     * @param modelId 车型id
     * @return 车型信息
     */
    VehicleModelInfoResponse getVehicleModelDetail(Long modelId);

    /**
     * 搜索车型列表
     * @param request 搜索条件
     * @return 车型列表
     */
    PageResponse<VehicleModelInfoResponse> searchVehicleModelList(SearchVehicleModelListRequest request);

    /**
     * 更新车型信息
     * @param request 车型信息
     * @param tokenUserInfo 操作人信息
     */
    void updateVehicleModel(UpsertVehicleModelRequest request, TokenUserInfo tokenUserInfo);

    /**
     * 获取全量车型下拉列表
     * @return 车型下拉列表
     */
    ComboResponse<Long, String> comboVehicleModel();

    /**
     * 获取车型编号下拉列表
     * 用于前端下拉选择组件，提供车型ID和对应的车型编号
     * @return 车型编号下拉列表，key为车型ID，value为车型编号(vehicleModelNo)
     */
    ComboResponse<Long, String> comboVehicleModelNo();

    /**
     * 获取汽车之家车型下拉列表
     * @param autohomeModelName 汽车之家车型名称
     * @return 获取汽车之家车型下拉列表
     */
    ComboResponse<Long, String> comboAutohomeVehicleModel(String autohomeModelName);

    /**
     * 根据汽车之家车型id获取车型详情
     * @param autohomeModelId 汽车之家车型id
     * @return 车型详情
     */
    VehicleModelInfoResponse getVehicleModelDetailByAutohome(Long autohomeModelId);

    /**
     * 查询车型数据库同步数据
     * @param request 查询条件
     * @return 车型数据库同步数据
     */
    SearchDatabaseTableSyncDataResponse<SyncDatabaseVehicleModelDto> searchVehicleModelDatabaseSyncData(SearchDatabaseTableSyncDataRequest request);
}
