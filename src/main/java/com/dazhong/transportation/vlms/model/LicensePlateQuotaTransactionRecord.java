package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   额度单流水记录
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_license_plate_quota_transaction_record
 */
public class LicensePlateQuotaTransactionRecord implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   退牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.license_plate_withdrawal_date")
    private Date licensePlateWithdrawalDate;

    /**
     * Database Column Remarks:
     *   额度单打印日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_print_date")
    private Date quotaPrintDate;

    /**
     * Database Column Remarks:
     *   额度单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_number")
    private String quotaNumber;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_type")
    private Integer quotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.asset_company_id")
    private Integer assetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.asset_company_name")
    private String assetCompanyName;

    /**
     * Database Column Remarks:
     *   上牌任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.task_number")
    private String taskNumber;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.license_plate")
    private String licensePlate;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_transaction_record")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.license_plate_withdrawal_date")
    public Date getLicensePlateWithdrawalDate() {
        return licensePlateWithdrawalDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.license_plate_withdrawal_date")
    public void setLicensePlateWithdrawalDate(Date licensePlateWithdrawalDate) {
        this.licensePlateWithdrawalDate = licensePlateWithdrawalDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_print_date")
    public Date getQuotaPrintDate() {
        return quotaPrintDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_print_date")
    public void setQuotaPrintDate(Date quotaPrintDate) {
        this.quotaPrintDate = quotaPrintDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_number")
    public String getQuotaNumber() {
        return quotaNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_number")
    public void setQuotaNumber(String quotaNumber) {
        this.quotaNumber = quotaNumber == null ? null : quotaNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_type")
    public Integer getQuotaType() {
        return quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.quota_type")
    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.asset_company_id")
    public Integer getAssetCompanyId() {
        return assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.asset_company_id")
    public void setAssetCompanyId(Integer assetCompanyId) {
        this.assetCompanyId = assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.asset_company_name")
    public String getAssetCompanyName() {
        return assetCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.asset_company_name")
    public void setAssetCompanyName(String assetCompanyName) {
        this.assetCompanyName = assetCompanyName == null ? null : assetCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.task_number")
    public String getTaskNumber() {
        return taskNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.task_number")
    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber == null ? null : taskNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.license_plate")
    public String getLicensePlate() {
        return licensePlate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.license_plate")
    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_transaction_record.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}