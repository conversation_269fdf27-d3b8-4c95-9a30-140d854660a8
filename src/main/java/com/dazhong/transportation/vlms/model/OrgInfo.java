package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   组织机构表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_org_info
 */
public class OrgInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   组织机构code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_code")
    private String companyCode;

    /**
     * Database Column Remarks:
     *   组织机构名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_name")
    private String companyName;

    /**
     * Database Column Remarks:
     *   是否选中 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.checked_state")
    private Integer checkedState;

    /**
     * Database Column Remarks:
     *   父资源id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.parent_id")
    private Long parentId;

    /**
     * Database Column Remarks:
     *   位置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.position")
    private Integer position;

    /**
     * Database Column Remarks:
     *   类型（公司、部门）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.org_type")
    private String orgType;

    /**
     * Database Column Remarks:
     *   唯一标识
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.vg_uid")
    private String vgUid;

    /**
     * Database Column Remarks:
     *   父节点标识
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.pg_uid")
    private String pgUid;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_org_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_code")
    public String getCompanyCode() {
        return companyCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_code")
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_name")
    public String getCompanyName() {
        return companyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.company_name")
    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.checked_state")
    public Integer getCheckedState() {
        return checkedState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.checked_state")
    public void setCheckedState(Integer checkedState) {
        this.checkedState = checkedState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.parent_id")
    public Long getParentId() {
        return parentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.parent_id")
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.position")
    public Integer getPosition() {
        return position;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.position")
    public void setPosition(Integer position) {
        this.position = position;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.org_type")
    public String getOrgType() {
        return orgType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.org_type")
    public void setOrgType(String orgType) {
        this.orgType = orgType == null ? null : orgType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.vg_uid")
    public String getVgUid() {
        return vgUid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.vg_uid")
    public void setVgUid(String vgUid) {
        this.vgUid = vgUid == null ? null : vgUid.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.pg_uid")
    public String getPgUid() {
        return pgUid;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.pg_uid")
    public void setPgUid(String pgUid) {
        this.pgUid = pgUid == null ? null : pgUid.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_org_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}