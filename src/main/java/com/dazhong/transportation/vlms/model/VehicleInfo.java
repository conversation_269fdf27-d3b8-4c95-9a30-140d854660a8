package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车辆资产信息表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_info
 */
public class VehicleInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   资产编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_asset_id")
    private String vehicleAssetId;

    /**
     * Database Column Remarks:
     *   关联资产ID（仅用于历史数据同步）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.relate_asset_id")
    private Long relateAssetId;

    /**
     * Database Column Remarks:
     *   发动机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_no")
    private String engineNo;

    /**
     * Database Column Remarks:
     *   发动机型号(车辆)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_model")
    private String engineModel;

    /**
     * Database Column Remarks:
     *   车型id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_model_id")
    private Long vehicleModelId;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate")
    private String licensePlate;

    /**
     * Database Column Remarks:
     *   是否使用额度（上牌） 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.use_quota_type")
    private Integer useQuotaType;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_type")
    private Integer quotaType;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_color_id")
    private Integer vehicleColorId;

    /**
     * Database Column Remarks:
     *   内饰颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.interior_color")
    private String interiorColor;

    /**
     * Database Column Remarks:
     *   供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.supplier_id")
    private Integer supplierId;

    /**
     * Database Column Remarks:
     *   是否回购 1-是 2-否 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_repurchase")
    private Integer isRepurchase;

    /**
     * Database Column Remarks:
     *   回购时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_date")
    private Date repurchaseDate;

    /**
     * Database Column Remarks:
     *   回购要求
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_requirements")
    private String repurchaseRequirements;

    /**
     * Database Column Remarks:
     *   使用年限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_age_limit")
    private Integer usageAgeLimit;

    /**
     * Database Column Remarks:
     *   折旧年限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_age_limit")
    private Integer depreciationAgeLimit;

    /**
     * Database Column Remarks:
     *   实际报废日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.real_retirement_date")
    private Date realRetirementDate;

    /**
     * Database Column Remarks:
     *   裸车价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_price")
    private BigDecimal purchasePrice;

    /**
     * Database Column Remarks:
     *   购置税
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax")
    private BigDecimal purchaseTax;

    /**
     * Database Column Remarks:
     *   牌照费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_price")
    private BigDecimal licensePlatePrice;

    /**
     * Database Column Remarks:
     *   上牌杂费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_other_price")
    private BigDecimal licensePlateOtherPrice;

    /**
     * Database Column Remarks:
     *   装潢费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.upholster_price")
    private BigDecimal upholsterPrice;

    /**
     * Database Column Remarks:
     *   购置总价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.total_price")
    private BigDecimal totalPrice;

    /**
     * Database Column Remarks:
     *   账面净值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.remain_price")
    private BigDecimal remainPrice;

    /**
     * Database Column Remarks:
     *   旧车销售价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.second_hand_price")
    private BigDecimal secondHandPrice;

    /**
     * Database Column Remarks:
     *   资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.property_status")
    private Integer propertyStatus;

    /**
     * Database Column Remarks:
     *   产品线 1-巡网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_line")
    private Integer productLine;

    /**
     * Database Column Remarks:
     *   业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.business_line")
    private Integer businessLine;

    /**
     * Database Column Remarks:
     *   运营状态 1-待运 2-租出
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_status")
    private Integer operatingStatus;

    /**
     * Database Column Remarks:
     *   车辆申购公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_code")
    private String subscriptionCompanyCode;

    /**
     * Database Column Remarks:
     *   申购公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_name")
    private String subscriptionCompanyName;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_asset_company_id")
    private Integer quotaAssetCompanyId;

    /**
     * Database Column Remarks:
     *   资产所属公司ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.asset_company_id")
    private Integer assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产机构
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.own_organization_id")
    private Long ownOrganizationId;

    /**
     * Database Column Remarks:
     *   使用机构
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_organization_id")
    private Long usageOrganizationId;

    /**
     * Database Column Remarks:
     *   所属车队
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.belonging_team")
    private String belongingTeam;

    /**
     * Database Column Remarks:
     *   获得方式 数据字典详细描叙
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.obtain_way_id")
    private Integer obtainWayId;

    /**
     * Database Column Remarks:
     *   管辖区县
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.area_id")
    private Integer areaId;

    /**
     * Database Column Remarks:
     *   折旧数据ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_data_id")
    private Long depreciationDataId;

    /**
     * Database Column Remarks:
     *   最新定位地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_position")
    private String latestPosition;

    /**
     * Database Column Remarks:
     *   最新总里程 (车机)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_total_mileage")
    private String latestTotalMileage;

    /**
     * Database Column Remarks:
     *   使用性质 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_id_registration_card")
    private Integer usageIdRegistrationCard;

    /**
     * Database Column Remarks:
     *   车辆类型 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_type_registration_card")
    private Integer vehicleTypeRegistrationCard;

    /**
     * Database Column Remarks:
     *   注册日期(行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.registration_date_registration_card")
    private Date registrationDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   发证日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date_registration_card")
    private Date issuanceDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   强制报废日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.retirement_date_registration_card")
    private Date retirementDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   年检到期日 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.annual_inspection_due_date_registration_card")
    private Date annualInspectionDueDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   档案编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.file_number")
    private String fileNumber;

    /**
     * Database Column Remarks:
     *   车辆出厂日期 (产证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_date")
    private Date productDate;

    /**
     * Database Column Remarks:
     *   发证日期 (产证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date")
    private Date issuanceDate;

    /**
     * Database Column Remarks:
     *   产证编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_number")
    private String certificateNumber;

    /**
     * Database Column Remarks:
     *   行驶证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_license_url")
    private String vehicleLicenseUrl;

    /**
     * Database Column Remarks:
     *   产证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_ownership_url")
    private String certificateOwnershipUrl;

    /**
     * Database Column Remarks:
     *   合格证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_conformity_url")
    private String certificateConformityUrl;

    /**
     * Database Column Remarks:
     *   车辆发票文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_invoice_url")
    private String vehicleInvoiceUrl;

    /**
     * Database Column Remarks:
     *   购置税文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax_url")
    private String purchaseTaxUrl;

    /**
     * Database Column Remarks:
     *   营运证文件地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_permit_url")
    private String operatingPermitUrl;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_asset_id")
    public String getVehicleAssetId() {
        return vehicleAssetId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_asset_id")
    public void setVehicleAssetId(String vehicleAssetId) {
        this.vehicleAssetId = vehicleAssetId == null ? null : vehicleAssetId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.relate_asset_id")
    public Long getRelateAssetId() {
        return relateAssetId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.relate_asset_id")
    public void setRelateAssetId(Long relateAssetId) {
        this.relateAssetId = relateAssetId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_no")
    public String getEngineNo() {
        return engineNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_no")
    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo == null ? null : engineNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_model")
    public String getEngineModel() {
        return engineModel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.engine_model")
    public void setEngineModel(String engineModel) {
        this.engineModel = engineModel == null ? null : engineModel.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_model_id")
    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_model_id")
    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate")
    public String getLicensePlate() {
        return licensePlate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate")
    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.use_quota_type")
    public Integer getUseQuotaType() {
        return useQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.use_quota_type")
    public void setUseQuotaType(Integer useQuotaType) {
        this.useQuotaType = useQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_type")
    public Integer getQuotaType() {
        return quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_type")
    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_color_id")
    public Integer getVehicleColorId() {
        return vehicleColorId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_color_id")
    public void setVehicleColorId(Integer vehicleColorId) {
        this.vehicleColorId = vehicleColorId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.interior_color")
    public String getInteriorColor() {
        return interiorColor;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.interior_color")
    public void setInteriorColor(String interiorColor) {
        this.interiorColor = interiorColor == null ? null : interiorColor.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.supplier_id")
    public Integer getSupplierId() {
        return supplierId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.supplier_id")
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_repurchase")
    public Integer getIsRepurchase() {
        return isRepurchase;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_repurchase")
    public void setIsRepurchase(Integer isRepurchase) {
        this.isRepurchase = isRepurchase;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_date")
    public Date getRepurchaseDate() {
        return repurchaseDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_date")
    public void setRepurchaseDate(Date repurchaseDate) {
        this.repurchaseDate = repurchaseDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_requirements")
    public String getRepurchaseRequirements() {
        return repurchaseRequirements;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.repurchase_requirements")
    public void setRepurchaseRequirements(String repurchaseRequirements) {
        this.repurchaseRequirements = repurchaseRequirements == null ? null : repurchaseRequirements.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_age_limit")
    public Integer getUsageAgeLimit() {
        return usageAgeLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_age_limit")
    public void setUsageAgeLimit(Integer usageAgeLimit) {
        this.usageAgeLimit = usageAgeLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_age_limit")
    public Integer getDepreciationAgeLimit() {
        return depreciationAgeLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_age_limit")
    public void setDepreciationAgeLimit(Integer depreciationAgeLimit) {
        this.depreciationAgeLimit = depreciationAgeLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.real_retirement_date")
    public Date getRealRetirementDate() {
        return realRetirementDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.real_retirement_date")
    public void setRealRetirementDate(Date realRetirementDate) {
        this.realRetirementDate = realRetirementDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_price")
    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_price")
    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax")
    public BigDecimal getPurchaseTax() {
        return purchaseTax;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax")
    public void setPurchaseTax(BigDecimal purchaseTax) {
        this.purchaseTax = purchaseTax;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_price")
    public BigDecimal getLicensePlatePrice() {
        return licensePlatePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_price")
    public void setLicensePlatePrice(BigDecimal licensePlatePrice) {
        this.licensePlatePrice = licensePlatePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_other_price")
    public BigDecimal getLicensePlateOtherPrice() {
        return licensePlateOtherPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.license_plate_other_price")
    public void setLicensePlateOtherPrice(BigDecimal licensePlateOtherPrice) {
        this.licensePlateOtherPrice = licensePlateOtherPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.upholster_price")
    public BigDecimal getUpholsterPrice() {
        return upholsterPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.upholster_price")
    public void setUpholsterPrice(BigDecimal upholsterPrice) {
        this.upholsterPrice = upholsterPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.total_price")
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.total_price")
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.remain_price")
    public BigDecimal getRemainPrice() {
        return remainPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.remain_price")
    public void setRemainPrice(BigDecimal remainPrice) {
        this.remainPrice = remainPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.second_hand_price")
    public BigDecimal getSecondHandPrice() {
        return secondHandPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.second_hand_price")
    public void setSecondHandPrice(BigDecimal secondHandPrice) {
        this.secondHandPrice = secondHandPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.property_status")
    public Integer getPropertyStatus() {
        return propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.property_status")
    public void setPropertyStatus(Integer propertyStatus) {
        this.propertyStatus = propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_line")
    public Integer getProductLine() {
        return productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_line")
    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.business_line")
    public Integer getBusinessLine() {
        return businessLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.business_line")
    public void setBusinessLine(Integer businessLine) {
        this.businessLine = businessLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_status")
    public Integer getOperatingStatus() {
        return operatingStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_status")
    public void setOperatingStatus(Integer operatingStatus) {
        this.operatingStatus = operatingStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_code")
    public String getSubscriptionCompanyCode() {
        return subscriptionCompanyCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_code")
    public void setSubscriptionCompanyCode(String subscriptionCompanyCode) {
        this.subscriptionCompanyCode = subscriptionCompanyCode == null ? null : subscriptionCompanyCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_name")
    public String getSubscriptionCompanyName() {
        return subscriptionCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.subscription_company_name")
    public void setSubscriptionCompanyName(String subscriptionCompanyName) {
        this.subscriptionCompanyName = subscriptionCompanyName == null ? null : subscriptionCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_asset_company_id")
    public Integer getQuotaAssetCompanyId() {
        return quotaAssetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.quota_asset_company_id")
    public void setQuotaAssetCompanyId(Integer quotaAssetCompanyId) {
        this.quotaAssetCompanyId = quotaAssetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.asset_company_id")
    public Integer getAssetCompanyId() {
        return assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.asset_company_id")
    public void setAssetCompanyId(Integer assetCompanyId) {
        this.assetCompanyId = assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.own_organization_id")
    public Long getOwnOrganizationId() {
        return ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.own_organization_id")
    public void setOwnOrganizationId(Long ownOrganizationId) {
        this.ownOrganizationId = ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_organization_id")
    public Long getUsageOrganizationId() {
        return usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_organization_id")
    public void setUsageOrganizationId(Long usageOrganizationId) {
        this.usageOrganizationId = usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.belonging_team")
    public String getBelongingTeam() {
        return belongingTeam;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.belonging_team")
    public void setBelongingTeam(String belongingTeam) {
        this.belongingTeam = belongingTeam == null ? null : belongingTeam.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.obtain_way_id")
    public Integer getObtainWayId() {
        return obtainWayId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.obtain_way_id")
    public void setObtainWayId(Integer obtainWayId) {
        this.obtainWayId = obtainWayId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.area_id")
    public Integer getAreaId() {
        return areaId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.area_id")
    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_data_id")
    public Long getDepreciationDataId() {
        return depreciationDataId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.depreciation_data_id")
    public void setDepreciationDataId(Long depreciationDataId) {
        this.depreciationDataId = depreciationDataId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_position")
    public String getLatestPosition() {
        return latestPosition;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_position")
    public void setLatestPosition(String latestPosition) {
        this.latestPosition = latestPosition == null ? null : latestPosition.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_total_mileage")
    public String getLatestTotalMileage() {
        return latestTotalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.latest_total_mileage")
    public void setLatestTotalMileage(String latestTotalMileage) {
        this.latestTotalMileage = latestTotalMileage == null ? null : latestTotalMileage.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_id_registration_card")
    public Integer getUsageIdRegistrationCard() {
        return usageIdRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.usage_id_registration_card")
    public void setUsageIdRegistrationCard(Integer usageIdRegistrationCard) {
        this.usageIdRegistrationCard = usageIdRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_type_registration_card")
    public Integer getVehicleTypeRegistrationCard() {
        return vehicleTypeRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_type_registration_card")
    public void setVehicleTypeRegistrationCard(Integer vehicleTypeRegistrationCard) {
        this.vehicleTypeRegistrationCard = vehicleTypeRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.registration_date_registration_card")
    public Date getRegistrationDateRegistrationCard() {
        return registrationDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.registration_date_registration_card")
    public void setRegistrationDateRegistrationCard(Date registrationDateRegistrationCard) {
        this.registrationDateRegistrationCard = registrationDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date_registration_card")
    public Date getIssuanceDateRegistrationCard() {
        return issuanceDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date_registration_card")
    public void setIssuanceDateRegistrationCard(Date issuanceDateRegistrationCard) {
        this.issuanceDateRegistrationCard = issuanceDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.retirement_date_registration_card")
    public Date getRetirementDateRegistrationCard() {
        return retirementDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.retirement_date_registration_card")
    public void setRetirementDateRegistrationCard(Date retirementDateRegistrationCard) {
        this.retirementDateRegistrationCard = retirementDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.annual_inspection_due_date_registration_card")
    public Date getAnnualInspectionDueDateRegistrationCard() {
        return annualInspectionDueDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.annual_inspection_due_date_registration_card")
    public void setAnnualInspectionDueDateRegistrationCard(Date annualInspectionDueDateRegistrationCard) {
        this.annualInspectionDueDateRegistrationCard = annualInspectionDueDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.file_number")
    public String getFileNumber() {
        return fileNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.file_number")
    public void setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber == null ? null : fileNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_date")
    public Date getProductDate() {
        return productDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.product_date")
    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date")
    public Date getIssuanceDate() {
        return issuanceDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.issuance_date")
    public void setIssuanceDate(Date issuanceDate) {
        this.issuanceDate = issuanceDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_number")
    public String getCertificateNumber() {
        return certificateNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_number")
    public void setCertificateNumber(String certificateNumber) {
        this.certificateNumber = certificateNumber == null ? null : certificateNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_license_url")
    public String getVehicleLicenseUrl() {
        return vehicleLicenseUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_license_url")
    public void setVehicleLicenseUrl(String vehicleLicenseUrl) {
        this.vehicleLicenseUrl = vehicleLicenseUrl == null ? null : vehicleLicenseUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_ownership_url")
    public String getCertificateOwnershipUrl() {
        return certificateOwnershipUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_ownership_url")
    public void setCertificateOwnershipUrl(String certificateOwnershipUrl) {
        this.certificateOwnershipUrl = certificateOwnershipUrl == null ? null : certificateOwnershipUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_conformity_url")
    public String getCertificateConformityUrl() {
        return certificateConformityUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.certificate_conformity_url")
    public void setCertificateConformityUrl(String certificateConformityUrl) {
        this.certificateConformityUrl = certificateConformityUrl == null ? null : certificateConformityUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_invoice_url")
    public String getVehicleInvoiceUrl() {
        return vehicleInvoiceUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.vehicle_invoice_url")
    public void setVehicleInvoiceUrl(String vehicleInvoiceUrl) {
        this.vehicleInvoiceUrl = vehicleInvoiceUrl == null ? null : vehicleInvoiceUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax_url")
    public String getPurchaseTaxUrl() {
        return purchaseTaxUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.purchase_tax_url")
    public void setPurchaseTaxUrl(String purchaseTaxUrl) {
        this.purchaseTaxUrl = purchaseTaxUrl == null ? null : purchaseTaxUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_permit_url")
    public String getOperatingPermitUrl() {
        return operatingPermitUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.operating_permit_url")
    public void setOperatingPermitUrl(String operatingPermitUrl) {
        this.operatingPermitUrl = operatingPermitUrl == null ? null : operatingPermitUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}