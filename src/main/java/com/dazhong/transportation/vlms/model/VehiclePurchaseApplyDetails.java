package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车辆采购申请明细
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_purchase_apply_details
 */
public class VehiclePurchaseApplyDetails implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   采购申id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_apply_id")
    private Long purchaseApplyId;

    /**
     * Database Column Remarks:
     *   采购申请明细编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.apply_details_no")
    private String applyDetailsNo;

    /**
     * Database Column Remarks:
     *   预算情况枚举值 1-预算内 2-预算外
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.budget_status")
    private Integer budgetStatus;

    /**
     * Database Column Remarks:
     *   采购类型枚举值 1-新车 2-二手车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_type")
    private Integer purchaseType;

    /**
     * Database Column Remarks:
     *   采购车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_model_id")
    private Long purchaseModelId;

    /**
     * Database Column Remarks:
     *   单价（巡网）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.unit_price")
    private BigDecimal unitPrice;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quantity")
    private Integer quantity;

    /**
     * Database Column Remarks:
     *   已收货车辆数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.received_quantity")
    private Integer receivedQuantity;

    /**
     * Database Column Remarks:
     *   其他费用（巡网）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.other_costs")
    private BigDecimal otherCosts;

    /**
     * Database Column Remarks:
     *   总价（巡网）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.total_price")
    private BigDecimal totalPrice;

    /**
     * Database Column Remarks:
     *   业务类型枚举值 1-巡讯 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.business_type")
    private Integer businessType;

    /**
     * Database Column Remarks:
     *   收货所属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.owner_id")
    private Integer ownerId;

    /**
     * Database Column Remarks:
     *   制造商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.manufacturer_id")
    private Integer manufacturerId;

    /**
     * Database Column Remarks:
     *   供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.supplier_id")
    private Integer supplierId;

    /**
     * Database Column Remarks:
     *   已使用年限（二手车）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_years")
    private String usedYears;

    /**
     * Database Column Remarks:
     *   已使用公里数（二手车）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_km")
    private String usedKm;

    /**
     * Database Column Remarks:
     *   车辆预计退运时间（商务）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_return_date")
    private Date expectedReturnDate;

    /**
     * Database Column Remarks:
     *   期望到货日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_delivery_date")
    private Date expectedDeliveryDate;

    /**
     * Database Column Remarks:
     *   是否占用管控额度 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_quota_occupied")
    private Integer isQuotaOccupied;

    /**
     * Database Column Remarks:
     *   额度类型枚举值  1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_type")
    private Integer quotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_asset_ownership")
    private Integer quotaAssetOwnership;

    /**
     * Database Column Remarks:
     *   预占用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.pre_occupied")
    private Integer preOccupied;

    /**
     * Database Column Remarks:
     *   投资回报率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.return_on_investment")
    private String returnOnInvestment;

    /**
     * Database Column Remarks:
     *   月单车收益
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.monthly_bicycle_revenue")
    private String monthlyBicycleRevenue;

    /**
     * Database Column Remarks:
     *   车辆残值率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.vehicle_residual_value_rate")
    private String vehicleResidualValueRate;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply_details")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_apply_id")
    public Long getPurchaseApplyId() {
        return purchaseApplyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_apply_id")
    public void setPurchaseApplyId(Long purchaseApplyId) {
        this.purchaseApplyId = purchaseApplyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.apply_details_no")
    public String getApplyDetailsNo() {
        return applyDetailsNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.apply_details_no")
    public void setApplyDetailsNo(String applyDetailsNo) {
        this.applyDetailsNo = applyDetailsNo == null ? null : applyDetailsNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.budget_status")
    public Integer getBudgetStatus() {
        return budgetStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.budget_status")
    public void setBudgetStatus(Integer budgetStatus) {
        this.budgetStatus = budgetStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_type")
    public Integer getPurchaseType() {
        return purchaseType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_type")
    public void setPurchaseType(Integer purchaseType) {
        this.purchaseType = purchaseType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_model_id")
    public Long getPurchaseModelId() {
        return purchaseModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.purchase_model_id")
    public void setPurchaseModelId(Long purchaseModelId) {
        this.purchaseModelId = purchaseModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.unit_price")
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.unit_price")
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.received_quantity")
    public Integer getReceivedQuantity() {
        return receivedQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.received_quantity")
    public void setReceivedQuantity(Integer receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.other_costs")
    public BigDecimal getOtherCosts() {
        return otherCosts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.other_costs")
    public void setOtherCosts(BigDecimal otherCosts) {
        this.otherCosts = otherCosts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.total_price")
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.total_price")
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.business_type")
    public Integer getBusinessType() {
        return businessType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.business_type")
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.owner_id")
    public Integer getOwnerId() {
        return ownerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.owner_id")
    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.manufacturer_id")
    public Integer getManufacturerId() {
        return manufacturerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.manufacturer_id")
    public void setManufacturerId(Integer manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.supplier_id")
    public Integer getSupplierId() {
        return supplierId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.supplier_id")
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_years")
    public String getUsedYears() {
        return usedYears;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_years")
    public void setUsedYears(String usedYears) {
        this.usedYears = usedYears == null ? null : usedYears.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_km")
    public String getUsedKm() {
        return usedKm;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.used_km")
    public void setUsedKm(String usedKm) {
        this.usedKm = usedKm == null ? null : usedKm.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_return_date")
    public Date getExpectedReturnDate() {
        return expectedReturnDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_return_date")
    public void setExpectedReturnDate(Date expectedReturnDate) {
        this.expectedReturnDate = expectedReturnDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_delivery_date")
    public Date getExpectedDeliveryDate() {
        return expectedDeliveryDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.expected_delivery_date")
    public void setExpectedDeliveryDate(Date expectedDeliveryDate) {
        this.expectedDeliveryDate = expectedDeliveryDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_quota_occupied")
    public Integer getIsQuotaOccupied() {
        return isQuotaOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_quota_occupied")
    public void setIsQuotaOccupied(Integer isQuotaOccupied) {
        this.isQuotaOccupied = isQuotaOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_type")
    public Integer getQuotaType() {
        return quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_type")
    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_asset_ownership")
    public Integer getQuotaAssetOwnership() {
        return quotaAssetOwnership;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.quota_asset_ownership")
    public void setQuotaAssetOwnership(Integer quotaAssetOwnership) {
        this.quotaAssetOwnership = quotaAssetOwnership;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.pre_occupied")
    public Integer getPreOccupied() {
        return preOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.pre_occupied")
    public void setPreOccupied(Integer preOccupied) {
        this.preOccupied = preOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.return_on_investment")
    public String getReturnOnInvestment() {
        return returnOnInvestment;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.return_on_investment")
    public void setReturnOnInvestment(String returnOnInvestment) {
        this.returnOnInvestment = returnOnInvestment == null ? null : returnOnInvestment.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.monthly_bicycle_revenue")
    public String getMonthlyBicycleRevenue() {
        return monthlyBicycleRevenue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.monthly_bicycle_revenue")
    public void setMonthlyBicycleRevenue(String monthlyBicycleRevenue) {
        this.monthlyBicycleRevenue = monthlyBicycleRevenue == null ? null : monthlyBicycleRevenue.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.vehicle_residual_value_rate")
    public String getVehicleResidualValueRate() {
        return vehicleResidualValueRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.vehicle_residual_value_rate")
    public void setVehicleResidualValueRate(String vehicleResidualValueRate) {
        this.vehicleResidualValueRate = vehicleResidualValueRate == null ? null : vehicleResidualValueRate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply_details.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}