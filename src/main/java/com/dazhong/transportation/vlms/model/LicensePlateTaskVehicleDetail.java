package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车牌任务车辆详情表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_license_plate_task_vehicle_detail
 */
public class LicensePlateTaskVehicleDetail implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.task_number")
    private String taskNumber;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.license_plate")
    private String licensePlate;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   车型id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_id")
    private Long vehicleModelId;

    /**
     * Database Column Remarks:
     *   车型名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_name")
    private String vehicleModelName;

    /**
     * Database Column Remarks:
     *   资产所属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.asset_company_id")
    private Integer assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产机构id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.own_organization_id")
    private Long ownOrganizationId;

    /**
     * Database Column Remarks:
     *   使用机构id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_organization_id")
    private Long usageOrganizationId;

    /**
     * Database Column Remarks:
     *   退还额度类型 1-退还 2-不退还 3-不涉及
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.return_quota_type")
    private Integer returnQuotaType;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_type")
    private Integer quotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_id")
    private Integer quotaAssetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_name")
    private String quotaAssetCompanyName;

    /**
     * Database Column Remarks:
     *   额度编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_number")
    private String quotaNumber;

    /**
     * Database Column Remarks:
     *   额度单打印时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_print_date")
    private Date quotaPrintDate;

    /**
     * Database Column Remarks:
     *   车辆类型 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_type_registration_card")
    private Integer vehicleTypeRegistrationCard;

    /**
     * Database Column Remarks:
     *   使用性质 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_id_registration_card")
    private Integer usageIdRegistrationCard;

    /**
     * Database Column Remarks:
     *   注册日期(行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.registration_date_registration_card")
    private Date registrationDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   发证日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.issuance_date_registration_card")
    private Date issuanceDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   档案编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.file_number")
    private String fileNumber;

    /**
     * Database Column Remarks:
     *   强制报废日期 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.retirement_date_registration_card")
    private Date retirementDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   年检到期日 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.annual_inspection_due_date_registration_card")
    private Date annualInspectionDueDateRegistrationCard;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_vehicle_detail")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.task_number")
    public String getTaskNumber() {
        return taskNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.task_number")
    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber == null ? null : taskNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.license_plate")
    public String getLicensePlate() {
        return licensePlate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.license_plate")
    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_id")
    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_id")
    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_name")
    public String getVehicleModelName() {
        return vehicleModelName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_model_name")
    public void setVehicleModelName(String vehicleModelName) {
        this.vehicleModelName = vehicleModelName == null ? null : vehicleModelName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.asset_company_id")
    public Integer getAssetCompanyId() {
        return assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.asset_company_id")
    public void setAssetCompanyId(Integer assetCompanyId) {
        this.assetCompanyId = assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.own_organization_id")
    public Long getOwnOrganizationId() {
        return ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.own_organization_id")
    public void setOwnOrganizationId(Long ownOrganizationId) {
        this.ownOrganizationId = ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_organization_id")
    public Long getUsageOrganizationId() {
        return usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_organization_id")
    public void setUsageOrganizationId(Long usageOrganizationId) {
        this.usageOrganizationId = usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.return_quota_type")
    public Integer getReturnQuotaType() {
        return returnQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.return_quota_type")
    public void setReturnQuotaType(Integer returnQuotaType) {
        this.returnQuotaType = returnQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_type")
    public Integer getQuotaType() {
        return quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_type")
    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_id")
    public Integer getQuotaAssetCompanyId() {
        return quotaAssetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_id")
    public void setQuotaAssetCompanyId(Integer quotaAssetCompanyId) {
        this.quotaAssetCompanyId = quotaAssetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_name")
    public String getQuotaAssetCompanyName() {
        return quotaAssetCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_asset_company_name")
    public void setQuotaAssetCompanyName(String quotaAssetCompanyName) {
        this.quotaAssetCompanyName = quotaAssetCompanyName == null ? null : quotaAssetCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_number")
    public String getQuotaNumber() {
        return quotaNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_number")
    public void setQuotaNumber(String quotaNumber) {
        this.quotaNumber = quotaNumber == null ? null : quotaNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_print_date")
    public Date getQuotaPrintDate() {
        return quotaPrintDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.quota_print_date")
    public void setQuotaPrintDate(Date quotaPrintDate) {
        this.quotaPrintDate = quotaPrintDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_type_registration_card")
    public Integer getVehicleTypeRegistrationCard() {
        return vehicleTypeRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.vehicle_type_registration_card")
    public void setVehicleTypeRegistrationCard(Integer vehicleTypeRegistrationCard) {
        this.vehicleTypeRegistrationCard = vehicleTypeRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_id_registration_card")
    public Integer getUsageIdRegistrationCard() {
        return usageIdRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.usage_id_registration_card")
    public void setUsageIdRegistrationCard(Integer usageIdRegistrationCard) {
        this.usageIdRegistrationCard = usageIdRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.registration_date_registration_card")
    public Date getRegistrationDateRegistrationCard() {
        return registrationDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.registration_date_registration_card")
    public void setRegistrationDateRegistrationCard(Date registrationDateRegistrationCard) {
        this.registrationDateRegistrationCard = registrationDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.issuance_date_registration_card")
    public Date getIssuanceDateRegistrationCard() {
        return issuanceDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.issuance_date_registration_card")
    public void setIssuanceDateRegistrationCard(Date issuanceDateRegistrationCard) {
        this.issuanceDateRegistrationCard = issuanceDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.file_number")
    public String getFileNumber() {
        return fileNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.file_number")
    public void setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber == null ? null : fileNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.retirement_date_registration_card")
    public Date getRetirementDateRegistrationCard() {
        return retirementDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.retirement_date_registration_card")
    public void setRetirementDateRegistrationCard(Date retirementDateRegistrationCard) {
        this.retirementDateRegistrationCard = retirementDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.annual_inspection_due_date_registration_card")
    public Date getAnnualInspectionDueDateRegistrationCard() {
        return annualInspectionDueDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.annual_inspection_due_date_registration_card")
    public void setAnnualInspectionDueDateRegistrationCard(Date annualInspectionDueDateRegistrationCard) {
        this.annualInspectionDueDateRegistrationCard = annualInspectionDueDateRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_vehicle_detail.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}