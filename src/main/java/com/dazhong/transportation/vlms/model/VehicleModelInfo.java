package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_model_info
 */
public class VehicleModelInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   汽车之家车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.autohome_vehicle_model_id")
    private Long autohomeVehicleModelId;

    /**
     * Database Column Remarks:
     *   品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_name")
    private String vehicleBrandName;

    /**
     * Database Column Remarks:
     *   车系
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_series_name")
    private String vehicleSeriesName;

    /**
     * Database Column Remarks:
     *   车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_name")
    private String vehicleModelName;

    /**
     * Database Column Remarks:
     *   财务车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.financial_model_name")
    private String financialModelName;

    /**
     * Database Column Remarks:
     *   商品车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_abbreviation_id")
    private Integer vehicleAbbreviationId;

    /**
     * Database Column Remarks:
     *   座位数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_passenger")
    private Integer assessPassenger;

    /**
     * Database Column Remarks:
     *   大巴实际座位数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.bus_assess_passenger")
    private Integer busAssessPassenger;

    /**
     * Database Column Remarks:
     *   发动机型号（车型）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_model_no")
    private String engineModelNo;

    /**
     * Database Column Remarks:
     *   能源类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gas_type_id")
    private Integer gasTypeId;

    /**
     * Database Column Remarks:
     *   车辆级别 (1-经济轿车, 2-舒适轿车, 3-豪华轿车, 4-SUV, 5-商务车, 6-轻客, 7-跑车, 8-皮卡, 9-大巴)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_level")
    private Integer vehicleLevel;

    /**
     * Database Column Remarks:
     *   轮胎规格
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_param")
    private String wheelParam;

    /**
     * Database Column Remarks:
     *   环保标准
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.exhaust_id")
    private Integer exhaustId;

    /**
     * Database Column Remarks:
     *   上市时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.ttm_month")
    private String ttmMonth;

    /**
     * Database Column Remarks:
     *   车身-长度（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_length")
    private Integer outLength;

    /**
     * Database Column Remarks:
     *   车身-宽度（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_width")
    private Integer outWidth;

    /**
     * Database Column Remarks:
     *   车身-高度（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_height")
    private Integer outHeight;

    /**
     * Database Column Remarks:
     *   车身-轴距（mm）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base1")
    private Integer wheelBase1;

    /**
     * Database Column Remarks:
     *   整备质量(kg)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.total_mass")
    private Integer totalMass;

    /**
     * Database Column Remarks:
     *   油箱容积（升）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_tank_capacity")
    private Integer fuelTankCapacity;

    /**
     * Database Column Remarks:
     *   燃油标号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_label_name")
    private String fuelLabelName;

    /**
     * Database Column Remarks:
     *   发动机-排量(mL)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.capacity")
    private BigDecimal capacity;

    /**
     * Database Column Remarks:
     *   电池容量（kWh）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.battery_capacity")
    private BigDecimal batteryCapacity;

    /**
     * Database Column Remarks:
     *   车辆续航里程（km）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_range")
    private Integer vehicleRange;

    /**
     * Database Column Remarks:
     *   快速充能时间（h）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fast_charging_time")
    private BigDecimal fastChargingTime;

    /**
     * Database Column Remarks:
     *   慢充能时间（h）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.slow_charging_time")
    private BigDecimal slowChargingTime;

    /**
     * Database Column Remarks:
     *   百公里油耗（L/100km）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy_miit")
    private BigDecimal fuelEconomyMiit;

    /**
     * Database Column Remarks:
     *   售价（汽车之家）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.price_on_autohome")
    private Integer priceOnAutohome;

    /**
     * Database Column Remarks:
     *   车辆型号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_no")
    private String vehicleModelNo;

    /**
     * Database Column Remarks:
     *   功率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.power")
    private Integer power;

    /**
     * Database Column Remarks:
     *   前轮距
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_front")
    private Integer treadFront;

    /**
     * Database Column Remarks:
     *   后轮距
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_rear")
    private Integer treadRear;

    /**
     * Database Column Remarks:
     *   轮胎数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_quantity")
    private Integer wheelQuantity;

    /**
     * Database Column Remarks:
     *   后轴钢板弹簧数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.spring_lamination")
    private Integer springLamination;

    /**
     * Database Column Remarks:
     *   轴距2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base2")
    private Integer wheelBase2;

    /**
     * Database Column Remarks:
     *   轴距3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base3")
    private Integer wheelBase3;

    /**
     * Database Column Remarks:
     *   轴数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.axle_quantity")
    private Integer axleQuantity;

    /**
     * Database Column Remarks:
     *   货箱内部尺寸长
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_length")
    private Integer containerLength;

    /**
     * Database Column Remarks:
     *   货箱内部尺寸宽
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_width")
    private Integer containerWidth;

    /**
     * Database Column Remarks:
     *   货箱内部尺寸高
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_height")
    private Integer containerHeight;

    /**
     * Database Column Remarks:
     *   核定载质量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_mass")
    private Integer assessMass;

    /**
     * Database Column Remarks:
     *   准牵引总质量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.traction_mass")
    private Integer tractionMass;

    /**
     * Database Column Remarks:
     *   驾驶室载客
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.cab_passenger")
    private Integer cabPassenger;

    /**
     * Database Column Remarks:
     *   国产/进口 (0: 国产, 1: 进口)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacture_location")
    private Integer manufactureLocation;

    /**
     * Database Column Remarks:
     *   车门数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.door_quantity")
    private Integer doorQuantity;

    /**
     * Database Column Remarks:
     *   档位数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_quantity")
    private Integer gearQuantity;

    /**
     * Database Column Remarks:
     *   0-100加速
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.acceleration")
    private BigDecimal acceleration;

    /**
     * Database Column Remarks:
     *   最高车速
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.speed")
    private Integer speed;

    /**
     * Database Column Remarks:
     *   最小转弯半径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turning_radius")
    private BigDecimal turningRadius;

    /**
     * Database Column Remarks:
     *   最小离地间隙
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.road_clearance")
    private Integer roadClearance;

    /**
     * Database Column Remarks:
     *   最大爬坡度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gradient")
    private Integer gradient;

    /**
     * Database Column Remarks:
     *   等速油耗
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy")
    private BigDecimal fuelEconomy;

    /**
     * Database Column Remarks:
     *   扭矩
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.torque")
    private Integer torque;

    /**
     * Database Column Remarks:
     *   压缩比
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.compression_ratio")
    private String compressionRatio;

    /**
     * Database Column Remarks:
     *   制造商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacturer_id")
    private Integer manufacturerId;

    /**
     * Database Column Remarks:
     *   车辆品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_id")
    private Integer vehicleBrandId;

    /**
     * Database Column Remarks:
     *   车辆类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_type_id")
    private Integer vehicleTypeId;

    /**
     * Database Column Remarks:
     *   驱动类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_drive_id")
    private Integer wheelDriveId;

    /**
     * Database Column Remarks:
     *   制动形式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.break_mode_id")
    private Integer breakModeId;

    /**
     * Database Column Remarks:
     *   转向方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turn_mode_id")
    private Integer turnModeId;

    /**
     * Database Column Remarks:
     *   驾驶位置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.drive_position_id")
    private Integer drivePositionId;

    /**
     * Database Column Remarks:
     *   发动机位置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_position_id")
    private Integer enginePositionId;

    /**
     * Database Column Remarks:
     *   变速箱形式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_box_type_id")
    private Integer gearBoxTypeId;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_model_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.autohome_vehicle_model_id")
    public Long getAutohomeVehicleModelId() {
        return autohomeVehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.autohome_vehicle_model_id")
    public void setAutohomeVehicleModelId(Long autohomeVehicleModelId) {
        this.autohomeVehicleModelId = autohomeVehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_name")
    public String getVehicleBrandName() {
        return vehicleBrandName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_name")
    public void setVehicleBrandName(String vehicleBrandName) {
        this.vehicleBrandName = vehicleBrandName == null ? null : vehicleBrandName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_series_name")
    public String getVehicleSeriesName() {
        return vehicleSeriesName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_series_name")
    public void setVehicleSeriesName(String vehicleSeriesName) {
        this.vehicleSeriesName = vehicleSeriesName == null ? null : vehicleSeriesName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_name")
    public String getVehicleModelName() {
        return vehicleModelName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_name")
    public void setVehicleModelName(String vehicleModelName) {
        this.vehicleModelName = vehicleModelName == null ? null : vehicleModelName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.financial_model_name")
    public String getFinancialModelName() {
        return financialModelName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.financial_model_name")
    public void setFinancialModelName(String financialModelName) {
        this.financialModelName = financialModelName == null ? null : financialModelName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_abbreviation_id")
    public Integer getVehicleAbbreviationId() {
        return vehicleAbbreviationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_abbreviation_id")
    public void setVehicleAbbreviationId(Integer vehicleAbbreviationId) {
        this.vehicleAbbreviationId = vehicleAbbreviationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_passenger")
    public Integer getAssessPassenger() {
        return assessPassenger;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_passenger")
    public void setAssessPassenger(Integer assessPassenger) {
        this.assessPassenger = assessPassenger;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.bus_assess_passenger")
    public Integer getBusAssessPassenger() {
        return busAssessPassenger;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.bus_assess_passenger")
    public void setBusAssessPassenger(Integer busAssessPassenger) {
        this.busAssessPassenger = busAssessPassenger;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_model_no")
    public String getEngineModelNo() {
        return engineModelNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_model_no")
    public void setEngineModelNo(String engineModelNo) {
        this.engineModelNo = engineModelNo == null ? null : engineModelNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gas_type_id")
    public Integer getGasTypeId() {
        return gasTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gas_type_id")
    public void setGasTypeId(Integer gasTypeId) {
        this.gasTypeId = gasTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_level")
    public Integer getVehicleLevel() {
        return vehicleLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_level")
    public void setVehicleLevel(Integer vehicleLevel) {
        this.vehicleLevel = vehicleLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_param")
    public String getWheelParam() {
        return wheelParam;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_param")
    public void setWheelParam(String wheelParam) {
        this.wheelParam = wheelParam == null ? null : wheelParam.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.exhaust_id")
    public Integer getExhaustId() {
        return exhaustId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.exhaust_id")
    public void setExhaustId(Integer exhaustId) {
        this.exhaustId = exhaustId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.ttm_month")
    public String getTtmMonth() {
        return ttmMonth;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.ttm_month")
    public void setTtmMonth(String ttmMonth) {
        this.ttmMonth = ttmMonth == null ? null : ttmMonth.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_length")
    public Integer getOutLength() {
        return outLength;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_length")
    public void setOutLength(Integer outLength) {
        this.outLength = outLength;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_width")
    public Integer getOutWidth() {
        return outWidth;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_width")
    public void setOutWidth(Integer outWidth) {
        this.outWidth = outWidth;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_height")
    public Integer getOutHeight() {
        return outHeight;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.out_height")
    public void setOutHeight(Integer outHeight) {
        this.outHeight = outHeight;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base1")
    public Integer getWheelBase1() {
        return wheelBase1;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base1")
    public void setWheelBase1(Integer wheelBase1) {
        this.wheelBase1 = wheelBase1;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.total_mass")
    public Integer getTotalMass() {
        return totalMass;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.total_mass")
    public void setTotalMass(Integer totalMass) {
        this.totalMass = totalMass;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_tank_capacity")
    public Integer getFuelTankCapacity() {
        return fuelTankCapacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_tank_capacity")
    public void setFuelTankCapacity(Integer fuelTankCapacity) {
        this.fuelTankCapacity = fuelTankCapacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_label_name")
    public String getFuelLabelName() {
        return fuelLabelName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_label_name")
    public void setFuelLabelName(String fuelLabelName) {
        this.fuelLabelName = fuelLabelName == null ? null : fuelLabelName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.capacity")
    public BigDecimal getCapacity() {
        return capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.capacity")
    public void setCapacity(BigDecimal capacity) {
        this.capacity = capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.battery_capacity")
    public BigDecimal getBatteryCapacity() {
        return batteryCapacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.battery_capacity")
    public void setBatteryCapacity(BigDecimal batteryCapacity) {
        this.batteryCapacity = batteryCapacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_range")
    public Integer getVehicleRange() {
        return vehicleRange;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_range")
    public void setVehicleRange(Integer vehicleRange) {
        this.vehicleRange = vehicleRange;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fast_charging_time")
    public BigDecimal getFastChargingTime() {
        return fastChargingTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fast_charging_time")
    public void setFastChargingTime(BigDecimal fastChargingTime) {
        this.fastChargingTime = fastChargingTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.slow_charging_time")
    public BigDecimal getSlowChargingTime() {
        return slowChargingTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.slow_charging_time")
    public void setSlowChargingTime(BigDecimal slowChargingTime) {
        this.slowChargingTime = slowChargingTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy_miit")
    public BigDecimal getFuelEconomyMiit() {
        return fuelEconomyMiit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy_miit")
    public void setFuelEconomyMiit(BigDecimal fuelEconomyMiit) {
        this.fuelEconomyMiit = fuelEconomyMiit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.price_on_autohome")
    public Integer getPriceOnAutohome() {
        return priceOnAutohome;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.price_on_autohome")
    public void setPriceOnAutohome(Integer priceOnAutohome) {
        this.priceOnAutohome = priceOnAutohome;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_no")
    public String getVehicleModelNo() {
        return vehicleModelNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_model_no")
    public void setVehicleModelNo(String vehicleModelNo) {
        this.vehicleModelNo = vehicleModelNo == null ? null : vehicleModelNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.power")
    public Integer getPower() {
        return power;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.power")
    public void setPower(Integer power) {
        this.power = power;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_front")
    public Integer getTreadFront() {
        return treadFront;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_front")
    public void setTreadFront(Integer treadFront) {
        this.treadFront = treadFront;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_rear")
    public Integer getTreadRear() {
        return treadRear;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.tread_rear")
    public void setTreadRear(Integer treadRear) {
        this.treadRear = treadRear;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_quantity")
    public Integer getWheelQuantity() {
        return wheelQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_quantity")
    public void setWheelQuantity(Integer wheelQuantity) {
        this.wheelQuantity = wheelQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.spring_lamination")
    public Integer getSpringLamination() {
        return springLamination;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.spring_lamination")
    public void setSpringLamination(Integer springLamination) {
        this.springLamination = springLamination;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base2")
    public Integer getWheelBase2() {
        return wheelBase2;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base2")
    public void setWheelBase2(Integer wheelBase2) {
        this.wheelBase2 = wheelBase2;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base3")
    public Integer getWheelBase3() {
        return wheelBase3;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_base3")
    public void setWheelBase3(Integer wheelBase3) {
        this.wheelBase3 = wheelBase3;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.axle_quantity")
    public Integer getAxleQuantity() {
        return axleQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.axle_quantity")
    public void setAxleQuantity(Integer axleQuantity) {
        this.axleQuantity = axleQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_length")
    public Integer getContainerLength() {
        return containerLength;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_length")
    public void setContainerLength(Integer containerLength) {
        this.containerLength = containerLength;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_width")
    public Integer getContainerWidth() {
        return containerWidth;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_width")
    public void setContainerWidth(Integer containerWidth) {
        this.containerWidth = containerWidth;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_height")
    public Integer getContainerHeight() {
        return containerHeight;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.container_height")
    public void setContainerHeight(Integer containerHeight) {
        this.containerHeight = containerHeight;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_mass")
    public Integer getAssessMass() {
        return assessMass;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.assess_mass")
    public void setAssessMass(Integer assessMass) {
        this.assessMass = assessMass;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.traction_mass")
    public Integer getTractionMass() {
        return tractionMass;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.traction_mass")
    public void setTractionMass(Integer tractionMass) {
        this.tractionMass = tractionMass;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.cab_passenger")
    public Integer getCabPassenger() {
        return cabPassenger;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.cab_passenger")
    public void setCabPassenger(Integer cabPassenger) {
        this.cabPassenger = cabPassenger;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacture_location")
    public Integer getManufactureLocation() {
        return manufactureLocation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacture_location")
    public void setManufactureLocation(Integer manufactureLocation) {
        this.manufactureLocation = manufactureLocation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.door_quantity")
    public Integer getDoorQuantity() {
        return doorQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.door_quantity")
    public void setDoorQuantity(Integer doorQuantity) {
        this.doorQuantity = doorQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_quantity")
    public Integer getGearQuantity() {
        return gearQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_quantity")
    public void setGearQuantity(Integer gearQuantity) {
        this.gearQuantity = gearQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.acceleration")
    public BigDecimal getAcceleration() {
        return acceleration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.acceleration")
    public void setAcceleration(BigDecimal acceleration) {
        this.acceleration = acceleration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.speed")
    public Integer getSpeed() {
        return speed;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.speed")
    public void setSpeed(Integer speed) {
        this.speed = speed;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turning_radius")
    public BigDecimal getTurningRadius() {
        return turningRadius;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turning_radius")
    public void setTurningRadius(BigDecimal turningRadius) {
        this.turningRadius = turningRadius;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.road_clearance")
    public Integer getRoadClearance() {
        return roadClearance;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.road_clearance")
    public void setRoadClearance(Integer roadClearance) {
        this.roadClearance = roadClearance;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gradient")
    public Integer getGradient() {
        return gradient;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gradient")
    public void setGradient(Integer gradient) {
        this.gradient = gradient;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy")
    public BigDecimal getFuelEconomy() {
        return fuelEconomy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.fuel_economy")
    public void setFuelEconomy(BigDecimal fuelEconomy) {
        this.fuelEconomy = fuelEconomy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.torque")
    public Integer getTorque() {
        return torque;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.torque")
    public void setTorque(Integer torque) {
        this.torque = torque;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.compression_ratio")
    public String getCompressionRatio() {
        return compressionRatio;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.compression_ratio")
    public void setCompressionRatio(String compressionRatio) {
        this.compressionRatio = compressionRatio == null ? null : compressionRatio.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacturer_id")
    public Integer getManufacturerId() {
        return manufacturerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.manufacturer_id")
    public void setManufacturerId(Integer manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_id")
    public Integer getVehicleBrandId() {
        return vehicleBrandId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_brand_id")
    public void setVehicleBrandId(Integer vehicleBrandId) {
        this.vehicleBrandId = vehicleBrandId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_type_id")
    public Integer getVehicleTypeId() {
        return vehicleTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.vehicle_type_id")
    public void setVehicleTypeId(Integer vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_drive_id")
    public Integer getWheelDriveId() {
        return wheelDriveId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.wheel_drive_id")
    public void setWheelDriveId(Integer wheelDriveId) {
        this.wheelDriveId = wheelDriveId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.break_mode_id")
    public Integer getBreakModeId() {
        return breakModeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.break_mode_id")
    public void setBreakModeId(Integer breakModeId) {
        this.breakModeId = breakModeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turn_mode_id")
    public Integer getTurnModeId() {
        return turnModeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.turn_mode_id")
    public void setTurnModeId(Integer turnModeId) {
        this.turnModeId = turnModeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.drive_position_id")
    public Integer getDrivePositionId() {
        return drivePositionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.drive_position_id")
    public void setDrivePositionId(Integer drivePositionId) {
        this.drivePositionId = drivePositionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_position_id")
    public Integer getEnginePositionId() {
        return enginePositionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.engine_position_id")
    public void setEnginePositionId(Integer enginePositionId) {
        this.enginePositionId = enginePositionId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_box_type_id")
    public Integer getGearBoxTypeId() {
        return gearBoxTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.gear_box_type_id")
    public void setGearBoxTypeId(Integer gearBoxTypeId) {
        this.gearBoxTypeId = gearBoxTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_model_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}