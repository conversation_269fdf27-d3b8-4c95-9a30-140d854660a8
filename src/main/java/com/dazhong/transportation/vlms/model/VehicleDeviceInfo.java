package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_device_info
 */
public class VehicleDeviceInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   设备ID，系统自动生成的唯一编码流水号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   设备类型 设备类型 有源  无源  原厂ETC TBOX
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_type")
    private String deviceType;

    /**
     * Database Column Remarks:
     *   设备的编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_seq")
    private String deviceSeq;

    /**
     * Database Column Remarks:
     *   设备的品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_brand")
    private String deviceBrand;

    /**
     * Database Column Remarks:
     *   设备的型号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_model")
    private String deviceModel;

    /**
     * Database Column Remarks:
     *   设备关联的SIM卡号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.sim_card_number")
    private String simCardNumber;

    /**
     * Database Column Remarks:
     *   设备的安装时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_time")
    private Date installTime;

    /**
     * Database Column Remarks:
     *   设备的安装地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_location")
    private String installLocation;

    /**
     * Database Column Remarks:
     *   设备关联的车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   设备的激活时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.activation_time")
    private Date activationTime;

    /**
     * Database Column Remarks:
     *   设备的到期时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.expiration_time")
    private Date expirationTime;

    /**
     * Database Column Remarks:
     *   GPS最新的定位时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location_time")
    private Date latestLocationTime;

    /**
     * Database Column Remarks:
     *   GPS最新的定位经度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.longitude")
    private BigDecimal longitude;

    /**
     * Database Column Remarks:
     *   GPS最新的定位纬度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latitude")
    private BigDecimal latitude;

    /**
     * Database Column Remarks:
     *   GPS最新的定位地点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location")
    private String latestLocation;

    /**
     * Database Column Remarks:
     *   设备读取到最新里程数据的时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_mileage_update_time")
    private Date latestMileageUpdateTime;

    /**
     * Database Column Remarks:
     *   设备读取到最新里程数据
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.total_mileage")
    private BigDecimal totalMileage;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_device_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_type")
    public String getDeviceType() {
        return deviceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_type")
    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType == null ? null : deviceType.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_seq")
    public String getDeviceSeq() {
        return deviceSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_seq")
    public void setDeviceSeq(String deviceSeq) {
        this.deviceSeq = deviceSeq == null ? null : deviceSeq.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_brand")
    public String getDeviceBrand() {
        return deviceBrand;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_brand")
    public void setDeviceBrand(String deviceBrand) {
        this.deviceBrand = deviceBrand == null ? null : deviceBrand.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_model")
    public String getDeviceModel() {
        return deviceModel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.device_model")
    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel == null ? null : deviceModel.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.sim_card_number")
    public String getSimCardNumber() {
        return simCardNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.sim_card_number")
    public void setSimCardNumber(String simCardNumber) {
        this.simCardNumber = simCardNumber == null ? null : simCardNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_time")
    public Date getInstallTime() {
        return installTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_time")
    public void setInstallTime(Date installTime) {
        this.installTime = installTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_location")
    public String getInstallLocation() {
        return installLocation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.install_location")
    public void setInstallLocation(String installLocation) {
        this.installLocation = installLocation == null ? null : installLocation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.activation_time")
    public Date getActivationTime() {
        return activationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.activation_time")
    public void setActivationTime(Date activationTime) {
        this.activationTime = activationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.expiration_time")
    public Date getExpirationTime() {
        return expirationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.expiration_time")
    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location_time")
    public Date getLatestLocationTime() {
        return latestLocationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location_time")
    public void setLatestLocationTime(Date latestLocationTime) {
        this.latestLocationTime = latestLocationTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.longitude")
    public BigDecimal getLongitude() {
        return longitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.longitude")
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latitude")
    public BigDecimal getLatitude() {
        return latitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latitude")
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location")
    public String getLatestLocation() {
        return latestLocation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_location")
    public void setLatestLocation(String latestLocation) {
        this.latestLocation = latestLocation == null ? null : latestLocation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_mileage_update_time")
    public Date getLatestMileageUpdateTime() {
        return latestMileageUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.latest_mileage_update_time")
    public void setLatestMileageUpdateTime(Date latestMileageUpdateTime) {
        this.latestMileageUpdateTime = latestMileageUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.total_mileage")
    public BigDecimal getTotalMileage() {
        return totalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.total_mileage")
    public void setTotalMileage(BigDecimal totalMileage) {
        this.totalMileage = totalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_device_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}