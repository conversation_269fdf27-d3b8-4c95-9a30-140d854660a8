package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车辆采购意向表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_purchase_intention
 */
public class VehiclePurchaseIntention implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   采购意向单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.intention_no")
    private String intentionNo;

    /**
     * Database Column Remarks:
     *   合同号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_no")
    private String contractNo;

    /**
     * Database Column Remarks:
     *   合同开始日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_start_date")
    private Date contractStartDate;

    /**
     * Database Column Remarks:
     *   合同结束日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_end_date")
    private Date contractEndDate;

    /**
     * Database Column Remarks:
     *   客户信息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.customer_user")
    private String customerUser;

    /**
     * Database Column Remarks:
     *   客户信息
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.sale_name")
    private String saleName;

    /**
     * Database Column Remarks:
     *   车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_name")
    private String vehicleModelName;

    /**
     * Database Column Remarks:
     *   车型id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_id")
    private Long vehicleModelId;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.quantity")
    private Integer quantity;

    /**
     * Database Column Remarks:
     *   指导价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.guide_price")
    private BigDecimal guidePrice;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_body_color")
    private String vehicleBodyColor;

    /**
     * Database Column Remarks:
     *   内饰颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_interior_color")
    private String vehicleInteriorColor;

    /**
     * Database Column Remarks:
     *   期望到车日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_date")
    private Date expectedArrivalDate;

    /**
     * Database Column Remarks:
     *   期望到车最后日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_last_date")
    private Date expectedArrivalLastDate;

    /**
     * Database Column Remarks:
     *   合同约定牌照属性
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_attribute")
    private String licensePlateAttribute;

    /**
     * Database Column Remarks:
     *   合同约定牌照所属地
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_location")
    private String licensePlateLocation;

    /**
     * Database Column Remarks:
     *   装潢需求
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.decoration_demand")
    private String decorationDemand;

    /**
     * Database Column Remarks:
     *   数据同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.data_sync_time")
    private Date dataSyncTime;

    /**
     * Database Column Remarks:
     *   是否有单号 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.apply_status")
    private Integer applyStatus;

    /**
     * Database Column Remarks:
     *   关联采购申请id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_apply_id")
    private Long purchaseApplyId;

    /**
     * Database Column Remarks:
     *   所属公司code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_code")
    private String orgCode;

    /**
     * Database Column Remarks:
     *   所属公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_name")
    private String orgName;

    /**
     * Database Column Remarks:
     *   所属公司ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_id")
    private Long orgId;

    /**
     * Database Column Remarks:
     *   采购备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_remark")
    private String purchaseRemark;

    /**
     * Database Column Remarks:
     *   钉钉号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.ding_talk_id")
    private String dingTalkId;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_intention")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.intention_no")
    public String getIntentionNo() {
        return intentionNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.intention_no")
    public void setIntentionNo(String intentionNo) {
        this.intentionNo = intentionNo == null ? null : intentionNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_no")
    public String getContractNo() {
        return contractNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_no")
    public void setContractNo(String contractNo) {
        this.contractNo = contractNo == null ? null : contractNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_start_date")
    public Date getContractStartDate() {
        return contractStartDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_start_date")
    public void setContractStartDate(Date contractStartDate) {
        this.contractStartDate = contractStartDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_end_date")
    public Date getContractEndDate() {
        return contractEndDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.contract_end_date")
    public void setContractEndDate(Date contractEndDate) {
        this.contractEndDate = contractEndDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.customer_user")
    public String getCustomerUser() {
        return customerUser;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.customer_user")
    public void setCustomerUser(String customerUser) {
        this.customerUser = customerUser == null ? null : customerUser.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.sale_name")
    public String getSaleName() {
        return saleName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.sale_name")
    public void setSaleName(String saleName) {
        this.saleName = saleName == null ? null : saleName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_name")
    public String getVehicleModelName() {
        return vehicleModelName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_name")
    public void setVehicleModelName(String vehicleModelName) {
        this.vehicleModelName = vehicleModelName == null ? null : vehicleModelName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_id")
    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_model_id")
    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.guide_price")
    public BigDecimal getGuidePrice() {
        return guidePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.guide_price")
    public void setGuidePrice(BigDecimal guidePrice) {
        this.guidePrice = guidePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_body_color")
    public String getVehicleBodyColor() {
        return vehicleBodyColor;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_body_color")
    public void setVehicleBodyColor(String vehicleBodyColor) {
        this.vehicleBodyColor = vehicleBodyColor == null ? null : vehicleBodyColor.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_interior_color")
    public String getVehicleInteriorColor() {
        return vehicleInteriorColor;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.vehicle_interior_color")
    public void setVehicleInteriorColor(String vehicleInteriorColor) {
        this.vehicleInteriorColor = vehicleInteriorColor == null ? null : vehicleInteriorColor.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_date")
    public Date getExpectedArrivalDate() {
        return expectedArrivalDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_date")
    public void setExpectedArrivalDate(Date expectedArrivalDate) {
        this.expectedArrivalDate = expectedArrivalDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_last_date")
    public Date getExpectedArrivalLastDate() {
        return expectedArrivalLastDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.expected_arrival_last_date")
    public void setExpectedArrivalLastDate(Date expectedArrivalLastDate) {
        this.expectedArrivalLastDate = expectedArrivalLastDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_attribute")
    public String getLicensePlateAttribute() {
        return licensePlateAttribute;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_attribute")
    public void setLicensePlateAttribute(String licensePlateAttribute) {
        this.licensePlateAttribute = licensePlateAttribute == null ? null : licensePlateAttribute.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_location")
    public String getLicensePlateLocation() {
        return licensePlateLocation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.license_plate_location")
    public void setLicensePlateLocation(String licensePlateLocation) {
        this.licensePlateLocation = licensePlateLocation == null ? null : licensePlateLocation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.decoration_demand")
    public String getDecorationDemand() {
        return decorationDemand;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.decoration_demand")
    public void setDecorationDemand(String decorationDemand) {
        this.decorationDemand = decorationDemand == null ? null : decorationDemand.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.data_sync_time")
    public Date getDataSyncTime() {
        return dataSyncTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.data_sync_time")
    public void setDataSyncTime(Date dataSyncTime) {
        this.dataSyncTime = dataSyncTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.apply_status")
    public Integer getApplyStatus() {
        return applyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.apply_status")
    public void setApplyStatus(Integer applyStatus) {
        this.applyStatus = applyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_apply_id")
    public Long getPurchaseApplyId() {
        return purchaseApplyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_apply_id")
    public void setPurchaseApplyId(Long purchaseApplyId) {
        this.purchaseApplyId = purchaseApplyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_code")
    public String getOrgCode() {
        return orgCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_code")
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_name")
    public String getOrgName() {
        return orgName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_name")
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_id")
    public Long getOrgId() {
        return orgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.org_id")
    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_remark")
    public String getPurchaseRemark() {
        return purchaseRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.purchase_remark")
    public void setPurchaseRemark(String purchaseRemark) {
        this.purchaseRemark = purchaseRemark == null ? null : purchaseRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.ding_talk_id")
    public String getDingTalkId() {
        return dingTalkId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.ding_talk_id")
    public void setDingTalkId(String dingTalkId) {
        this.dingTalkId = dingTalkId == null ? null : dingTalkId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_intention.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}