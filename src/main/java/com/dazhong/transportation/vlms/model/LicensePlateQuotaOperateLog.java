package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车牌额度操作日志
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_license_plate_quota_operate_log
 */
public class LicensePlateQuotaOperateLog implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   调整类型 1-总数增加 2-总数减少 3-类型调整
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_type")
    private Integer adjustType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_id")
    private Integer assetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_name")
    private String assetCompanyName;

    /**
     * Database Column Remarks:
     *   调整原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_reason")
    private String adjustReason;

    /**
     * Database Column Remarks:
     *   操作内容
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.operate_content")
    private String operateContent;

    /**
     * Database Column Remarks:
     *   删除标记（0：正常 1：删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_quota_operate_log")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_type")
    public Integer getAdjustType() {
        return adjustType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_type")
    public void setAdjustType(Integer adjustType) {
        this.adjustType = adjustType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_id")
    public Integer getAssetCompanyId() {
        return assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_id")
    public void setAssetCompanyId(Integer assetCompanyId) {
        this.assetCompanyId = assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_name")
    public String getAssetCompanyName() {
        return assetCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.asset_company_name")
    public void setAssetCompanyName(String assetCompanyName) {
        this.assetCompanyName = assetCompanyName == null ? null : assetCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_reason")
    public String getAdjustReason() {
        return adjustReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.adjust_reason")
    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason == null ? null : adjustReason.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.operate_content")
    public String getOperateContent() {
        return operateContent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.operate_content")
    public void setOperateContent(String operateContent) {
        this.operateContent = operateContent == null ? null : operateContent.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_quota_operate_log.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}