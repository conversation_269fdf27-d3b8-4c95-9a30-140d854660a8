package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车辆处置明细表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_disposal_ding_talk_result
 */
public class VehicleDisposalDingTalkResult implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   处置单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.disposal_id")
    private Long disposalId;

    /**
     * Database Column Remarks:
     *   原值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.original_value")
    private BigDecimal originalValue;

    /**
     * Database Column Remarks:
     *   已提折旧（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.depreciation_taken")
    private BigDecimal depreciationTaken;

    /**
     * Database Column Remarks:
     *   净值/残值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.net_value_or_salvage")
    private BigDecimal netValueOrSalvage;

    /**
     * Database Column Remarks:
     *   预计收益/损失（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.estimated_profit_loss")
    private BigDecimal estimatedProfitLoss;

    /**
     * Database Column Remarks:
     *   实际出售车辆数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vehicles_sold")
    private Integer actualVehiclesSold;

    /**
     * Database Column Remarks:
     *   实际总盈亏（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_total_profit_loss")
    private BigDecimal actualTotalProfitLoss;

    /**
     * Database Column Remarks:
     *   实际单车盈亏（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_per_vehicle_profit_loss")
    private BigDecimal actualPerVehicleProfitLoss;

    /**
     * Database Column Remarks:
     *   实预1差额/扣除撤拍车辆预计总盈亏1（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_one")
    private BigDecimal actualVsEstimatedDiffOne;

    /**
     * Database Column Remarks:
     *   实预2差额/扣除撤拍车辆预计总盈亏2（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_two")
    private BigDecimal actualVsEstimatedDiffTwo;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_ding_talk_result")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.disposal_id")
    public Long getDisposalId() {
        return disposalId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.disposal_id")
    public void setDisposalId(Long disposalId) {
        this.disposalId = disposalId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.original_value")
    public BigDecimal getOriginalValue() {
        return originalValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.original_value")
    public void setOriginalValue(BigDecimal originalValue) {
        this.originalValue = originalValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.depreciation_taken")
    public BigDecimal getDepreciationTaken() {
        return depreciationTaken;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.depreciation_taken")
    public void setDepreciationTaken(BigDecimal depreciationTaken) {
        this.depreciationTaken = depreciationTaken;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.net_value_or_salvage")
    public BigDecimal getNetValueOrSalvage() {
        return netValueOrSalvage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.net_value_or_salvage")
    public void setNetValueOrSalvage(BigDecimal netValueOrSalvage) {
        this.netValueOrSalvage = netValueOrSalvage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.estimated_profit_loss")
    public BigDecimal getEstimatedProfitLoss() {
        return estimatedProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.estimated_profit_loss")
    public void setEstimatedProfitLoss(BigDecimal estimatedProfitLoss) {
        this.estimatedProfitLoss = estimatedProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vehicles_sold")
    public Integer getActualVehiclesSold() {
        return actualVehiclesSold;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vehicles_sold")
    public void setActualVehiclesSold(Integer actualVehiclesSold) {
        this.actualVehiclesSold = actualVehiclesSold;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_total_profit_loss")
    public BigDecimal getActualTotalProfitLoss() {
        return actualTotalProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_total_profit_loss")
    public void setActualTotalProfitLoss(BigDecimal actualTotalProfitLoss) {
        this.actualTotalProfitLoss = actualTotalProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_per_vehicle_profit_loss")
    public BigDecimal getActualPerVehicleProfitLoss() {
        return actualPerVehicleProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_per_vehicle_profit_loss")
    public void setActualPerVehicleProfitLoss(BigDecimal actualPerVehicleProfitLoss) {
        this.actualPerVehicleProfitLoss = actualPerVehicleProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_one")
    public BigDecimal getActualVsEstimatedDiffOne() {
        return actualVsEstimatedDiffOne;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_one")
    public void setActualVsEstimatedDiffOne(BigDecimal actualVsEstimatedDiffOne) {
        this.actualVsEstimatedDiffOne = actualVsEstimatedDiffOne;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_two")
    public BigDecimal getActualVsEstimatedDiffTwo() {
        return actualVsEstimatedDiffTwo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.actual_vs_estimated_diff_two")
    public void setActualVsEstimatedDiffTwo(BigDecimal actualVsEstimatedDiffTwo) {
        this.actualVsEstimatedDiffTwo = actualVsEstimatedDiffTwo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_ding_talk_result.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}