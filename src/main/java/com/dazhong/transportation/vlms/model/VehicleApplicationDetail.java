package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_application_detail
 */
public class VehicleApplicationDetail implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   申请单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.application_id")
    private Long applicationId;

    /**
     * Database Column Remarks:
     *   资产编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_asset_id")
    private String vehicleAssetId;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.license_plate")
    private String licensePlate;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_model_id")
    private Long vehicleModelId;

    /**
     * Database Column Remarks:
     *   资产所属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id")
    private Integer assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产所属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name")
    private String assetCompanyName;

    /**
     * Database Column Remarks:
     *   资产所属公司id修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id_update")
    private Integer assetCompanyIdUpdate;

    /**
     * Database Column Remarks:
     *   资产所属公司名修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name_update")
    private String assetCompanyNameUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id")
    private Long ownOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name")
    private String ownOrganizationName;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）id修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id_update")
    private Long ownOrganizationIdUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）名修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name_update")
    private String ownOrganizationNameUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id")
    private Long usageOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name")
    private String usageOrganizationName;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）id修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id_update")
    private Long usageOrganizationIdUpdate;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）名修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name_update")
    private String usageOrganizationNameUpdate;

    /**
     * Database Column Remarks:
     *   产品线 1-巡网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line")
    private Integer productLine;

    /**
     * Database Column Remarks:
     *   产品线修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line_update")
    private Integer productLineUpdate;

    /**
     * Database Column Remarks:
     *   业务线 1-巡网 2-长包 3-临租 4-大巴 5-公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line")
    private Integer businessLine;

    /**
     * Database Column Remarks:
     *   业务线修改为
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line_update")
    private Integer businessLineUpdate;

    /**
     * Database Column Remarks:
     *   运营状态 1-待运 2-租出
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.operating_status")
    private Integer operatingStatus;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_application_detail")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.application_id")
    public Long getApplicationId() {
        return applicationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.application_id")
    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_asset_id")
    public String getVehicleAssetId() {
        return vehicleAssetId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_asset_id")
    public void setVehicleAssetId(String vehicleAssetId) {
        this.vehicleAssetId = vehicleAssetId == null ? null : vehicleAssetId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.license_plate")
    public String getLicensePlate() {
        return licensePlate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.license_plate")
    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_model_id")
    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.vehicle_model_id")
    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id")
    public Integer getAssetCompanyId() {
        return assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id")
    public void setAssetCompanyId(Integer assetCompanyId) {
        this.assetCompanyId = assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name")
    public String getAssetCompanyName() {
        return assetCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name")
    public void setAssetCompanyName(String assetCompanyName) {
        this.assetCompanyName = assetCompanyName == null ? null : assetCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id_update")
    public Integer getAssetCompanyIdUpdate() {
        return assetCompanyIdUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_id_update")
    public void setAssetCompanyIdUpdate(Integer assetCompanyIdUpdate) {
        this.assetCompanyIdUpdate = assetCompanyIdUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name_update")
    public String getAssetCompanyNameUpdate() {
        return assetCompanyNameUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.asset_company_name_update")
    public void setAssetCompanyNameUpdate(String assetCompanyNameUpdate) {
        this.assetCompanyNameUpdate = assetCompanyNameUpdate == null ? null : assetCompanyNameUpdate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id")
    public Long getOwnOrganizationId() {
        return ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id")
    public void setOwnOrganizationId(Long ownOrganizationId) {
        this.ownOrganizationId = ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name")
    public String getOwnOrganizationName() {
        return ownOrganizationName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name")
    public void setOwnOrganizationName(String ownOrganizationName) {
        this.ownOrganizationName = ownOrganizationName == null ? null : ownOrganizationName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id_update")
    public Long getOwnOrganizationIdUpdate() {
        return ownOrganizationIdUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_id_update")
    public void setOwnOrganizationIdUpdate(Long ownOrganizationIdUpdate) {
        this.ownOrganizationIdUpdate = ownOrganizationIdUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name_update")
    public String getOwnOrganizationNameUpdate() {
        return ownOrganizationNameUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.own_organization_name_update")
    public void setOwnOrganizationNameUpdate(String ownOrganizationNameUpdate) {
        this.ownOrganizationNameUpdate = ownOrganizationNameUpdate == null ? null : ownOrganizationNameUpdate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id")
    public Long getUsageOrganizationId() {
        return usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id")
    public void setUsageOrganizationId(Long usageOrganizationId) {
        this.usageOrganizationId = usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name")
    public String getUsageOrganizationName() {
        return usageOrganizationName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name")
    public void setUsageOrganizationName(String usageOrganizationName) {
        this.usageOrganizationName = usageOrganizationName == null ? null : usageOrganizationName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id_update")
    public Long getUsageOrganizationIdUpdate() {
        return usageOrganizationIdUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_id_update")
    public void setUsageOrganizationIdUpdate(Long usageOrganizationIdUpdate) {
        this.usageOrganizationIdUpdate = usageOrganizationIdUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name_update")
    public String getUsageOrganizationNameUpdate() {
        return usageOrganizationNameUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.usage_organization_name_update")
    public void setUsageOrganizationNameUpdate(String usageOrganizationNameUpdate) {
        this.usageOrganizationNameUpdate = usageOrganizationNameUpdate == null ? null : usageOrganizationNameUpdate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line")
    public Integer getProductLine() {
        return productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line")
    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line_update")
    public Integer getProductLineUpdate() {
        return productLineUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.product_line_update")
    public void setProductLineUpdate(Integer productLineUpdate) {
        this.productLineUpdate = productLineUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line")
    public Integer getBusinessLine() {
        return businessLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line")
    public void setBusinessLine(Integer businessLine) {
        this.businessLine = businessLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line_update")
    public Integer getBusinessLineUpdate() {
        return businessLineUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.business_line_update")
    public void setBusinessLineUpdate(Integer businessLineUpdate) {
        this.businessLineUpdate = businessLineUpdate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.operating_status")
    public Integer getOperatingStatus() {
        return operatingStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.operating_status")
    public void setOperatingStatus(Integer operatingStatus) {
        this.operatingStatus = operatingStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_application_detail.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}