package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   通用文件下载表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_download_file_info
 */
public class DownloadFileInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   主键自增
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   文件来源 1=车辆主数据
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_source")
    private Integer fileSource;

    /**
     * Database Column Remarks:
     *   模块来源 1=车辆详情
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.model_source")
    private Integer modelSource;

    /**
     * Database Column Remarks:
     *   文件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_name")
    private String fileName;

    /**
     * Database Column Remarks:
     *   文件路径
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_path")
    private String filePath;

    /**
     * Database Column Remarks:
     *   文件状态 1=生成中 2=生成成功 3=生成失败
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_status")
    private Integer fileStatus;

    /**
     * Database Column Remarks:
     *   文件过期时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.expire_time")
    private Date expireTime;

    /**
     * Database Column Remarks:
     *   失败原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_download_file_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_source")
    public Integer getFileSource() {
        return fileSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_source")
    public void setFileSource(Integer fileSource) {
        this.fileSource = fileSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.model_source")
    public Integer getModelSource() {
        return modelSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.model_source")
    public void setModelSource(Integer modelSource) {
        this.modelSource = modelSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_name")
    public String getFileName() {
        return fileName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_name")
    public void setFileName(String fileName) {
        this.fileName = fileName == null ? null : fileName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_path")
    public String getFilePath() {
        return filePath;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_path")
    public void setFilePath(String filePath) {
        this.filePath = filePath == null ? null : filePath.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_status")
    public Integer getFileStatus() {
        return fileStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.file_status")
    public void setFileStatus(Integer fileStatus) {
        this.fileStatus = fileStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.expire_time")
    public Date getExpireTime() {
        return expireTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.expire_time")
    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_download_file_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}