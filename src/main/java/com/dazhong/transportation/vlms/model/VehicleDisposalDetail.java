package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车辆处置明细表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_disposal_detail
 */
public class VehicleDisposalDetail implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   处置单id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_id")
    private Long disposalId;

    /**
     * Database Column Remarks:
     *   资产编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_asset_id")
    private String vehicleAssetId;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_plate")
    private String licensePlate;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_model_id")
    private Long vehicleModelId;

    /**
     * Database Column Remarks:
     *   资产所属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_id")
    private Integer assetCompanyId;

    /**
     * Database Column Remarks:
     *   资产所属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_name")
    private String assetCompanyName;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_id")
    private Long ownOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（资产）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_name")
    private String ownOrganizationName;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_id")
    private Long usageOrganizationId;

    /**
     * Database Column Remarks:
     *   实际运营公司（使用）名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_name")
    private String usageOrganizationName;

    /**
     * Database Column Remarks:
     *   所属车队
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.belonging_team")
    private String belongingTeam;

    /**
     * Database Column Remarks:
     *   投产日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.start_date")
    private Date startDate;

    /**
     * Database Column Remarks:
     *   退役日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.real_retirement_date")
    private Date realRetirementDate;

    /**
     * Database Column Remarks:
     *   上牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_date")
    private Date licenseDate;

    /**
     * Database Column Remarks:
     *   使用期限（月）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_month_limit")
    private Integer usageMonthLimit;

    /**
     * Database Column Remarks:
     *   使用性质 (行驶证)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_id_registration_card")
    private Integer usageIdRegistrationCard;

    /**
     * Database Column Remarks:
     *   到填表日已用月数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.used_months")
    private Integer usedMonths;

    /**
     * Database Column Remarks:
     *   市场预估价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_market_price")
    private BigDecimal estimatedMarketPrice;

    /**
     * Database Column Remarks:
     *   起拍价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.starting_price")
    private BigDecimal startingPrice;

    /**
     * Database Column Remarks:
     *   预估净售值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_net_sale_value")
    private BigDecimal estimatedNetSaleValue;

    /**
     * Database Column Remarks:
     *   最低成交价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_acceptable_price")
    private BigDecimal minimumAcceptablePrice;

    /**
     * Database Column Remarks:
     *   手续费（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.handling_fee")
    private BigDecimal handlingFee;

    /**
     * Database Column Remarks:
     *   出售原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_reason")
    private String saleReason;

    /**
     * Database Column Remarks:
     *   实际出售原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_reason")
    private String actualSaleReason;

    /**
     * Database Column Remarks:
     *   使用性质
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_nature")
    private String usageNature;

    /**
     * Database Column Remarks:
     *   报废类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_type")
    private Integer disposalType;

    /**
     * Database Column Remarks:
     *   预估车辆损失（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_vehicle_loss")
    private BigDecimal estimatedVehicleLoss;

    /**
     * Database Column Remarks:
     *   政府补贴金额（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.government_subsidy_amount")
    private BigDecimal governmentSubsidyAmount;

    /**
     * Database Column Remarks:
     *   报废原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_reason")
    private String disposalReason;

    /**
     * Database Column Remarks:
     *   原值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.original_value")
    private BigDecimal originalValue;

    /**
     * Database Column Remarks:
     *   已提折旧（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.accumulated_depreciation")
    private String accumulatedDepreciation;

    /**
     * Database Column Remarks:
     *   净值/残值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_value")
    private BigDecimal netValue;

    /**
     * Database Column Remarks:
     *   财务评估
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.financial_evaluation")
    private String financialEvaluation;

    /**
     * Database Column Remarks:
     *   实际售价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_selling_price")
    private BigDecimal actualSellingPrice;

    /**
     * Database Column Remarks:
     *   实际净售价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_net_price")
    private BigDecimal actualNetPrice;

    /**
     * Database Column Remarks:
     *   实际净售价差值（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_price_diff")
    private BigDecimal netPriceDiff;

    /**
     * Database Column Remarks:
     *   实际出售损益
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_gain_loss")
    private BigDecimal saleGainLoss;

    /**
     * Database Column Remarks:
     *   实际出售数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sold_quantity")
    private Integer actualSoldQuantity;

    /**
     * Database Column Remarks:
     *   计划数量与实际数量差值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quantity_diff")
    private Integer quantityDiff;

    /**
     * Database Column Remarks:
     *   实际出售说明
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_desc")
    private String actualSaleDesc;

    /**
     * Database Column Remarks:
     *   预估报废损失金额（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_scrap_loss_amount")
    private BigDecimal estimatedScrapLossAmount;

    /**
     * Database Column Remarks:
     *   是否保险公司拍卖 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_insurance_auction")
    private Integer isInsuranceAuction;

    /**
     * Database Column Remarks:
     *   实际拍卖金额（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_auction_amount")
    private BigDecimal actualAuctionAmount;

    /**
     * Database Column Remarks:
     *   实际报废损益（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_scrap_profit_loss")
    private BigDecimal actualScrapProfitLoss;

    /**
     * Database Column Remarks:
     *   牌照性质（手工）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_type_manual")
    private String licenseTypeManual;

    /**
     * Database Column Remarks:
     *   额度类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quota_type")
    private Integer quotaType;

    /**
     * Database Column Remarks:
     *   商品车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_abbreviation_id")
    private Integer vehicleAbbreviationId;

    /**
     * Database Column Remarks:
     *   车身颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_color_id")
    private Integer vehicleColorId;

    /**
     * Database Column Remarks:
     *   msrp
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.msrp")
    private String msrp;

    /**
     * Database Column Remarks:
     *   公里数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.mileage")
    private BigDecimal mileage;

    /**
     * Database Column Remarks:
     *   车况
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_condition")
    private String vehicleCondition;

    /**
     * Database Column Remarks:
     *   最低出售价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_selling_price")
    private BigDecimal minimumSellingPrice;

    /**
     * Database Column Remarks:
     *   拍卖保留价（元）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.auction_reserve_price")
    private BigDecimal auctionReservePrice;

    /**
     * Database Column Remarks:
     *   预期盈亏1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_one")
    private BigDecimal expectedProfitLossOne;

    /**
     * Database Column Remarks:
     *   预期盈亏2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_two")
    private BigDecimal expectedProfitLossTwo;

    /**
     * Database Column Remarks:
     *   当月资产净值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.current_month_net_value")
    private BigDecimal currentMonthNetValue;

    /**
     * Database Column Remarks:
     *   二手车发票URL
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.secondhand_car_invoice_url")
    private String secondhandCarInvoiceUrl;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_name")
    private String updateOperName;

    /**
     * Database Column Remarks:
     *   附件url
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.file_url")
    private String fileUrl;

    /**
     * Database Column Remarks:
     *   实际售价附件
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_file_url")
    private String actualSaleFileUrl;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_disposal_detail")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_id")
    public Long getDisposalId() {
        return disposalId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_id")
    public void setDisposalId(Long disposalId) {
        this.disposalId = disposalId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_asset_id")
    public String getVehicleAssetId() {
        return vehicleAssetId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_asset_id")
    public void setVehicleAssetId(String vehicleAssetId) {
        this.vehicleAssetId = vehicleAssetId == null ? null : vehicleAssetId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_plate")
    public String getLicensePlate() {
        return licensePlate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_plate")
    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate == null ? null : licensePlate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_model_id")
    public Long getVehicleModelId() {
        return vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_model_id")
    public void setVehicleModelId(Long vehicleModelId) {
        this.vehicleModelId = vehicleModelId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_id")
    public Integer getAssetCompanyId() {
        return assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_id")
    public void setAssetCompanyId(Integer assetCompanyId) {
        this.assetCompanyId = assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_name")
    public String getAssetCompanyName() {
        return assetCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.asset_company_name")
    public void setAssetCompanyName(String assetCompanyName) {
        this.assetCompanyName = assetCompanyName == null ? null : assetCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_id")
    public Long getOwnOrganizationId() {
        return ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_id")
    public void setOwnOrganizationId(Long ownOrganizationId) {
        this.ownOrganizationId = ownOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_name")
    public String getOwnOrganizationName() {
        return ownOrganizationName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.own_organization_name")
    public void setOwnOrganizationName(String ownOrganizationName) {
        this.ownOrganizationName = ownOrganizationName == null ? null : ownOrganizationName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_id")
    public Long getUsageOrganizationId() {
        return usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_id")
    public void setUsageOrganizationId(Long usageOrganizationId) {
        this.usageOrganizationId = usageOrganizationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_name")
    public String getUsageOrganizationName() {
        return usageOrganizationName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_organization_name")
    public void setUsageOrganizationName(String usageOrganizationName) {
        this.usageOrganizationName = usageOrganizationName == null ? null : usageOrganizationName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.belonging_team")
    public String getBelongingTeam() {
        return belongingTeam;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.belonging_team")
    public void setBelongingTeam(String belongingTeam) {
        this.belongingTeam = belongingTeam == null ? null : belongingTeam.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.start_date")
    public Date getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.start_date")
    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.real_retirement_date")
    public Date getRealRetirementDate() {
        return realRetirementDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.real_retirement_date")
    public void setRealRetirementDate(Date realRetirementDate) {
        this.realRetirementDate = realRetirementDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_date")
    public Date getLicenseDate() {
        return licenseDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_date")
    public void setLicenseDate(Date licenseDate) {
        this.licenseDate = licenseDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_month_limit")
    public Integer getUsageMonthLimit() {
        return usageMonthLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_month_limit")
    public void setUsageMonthLimit(Integer usageMonthLimit) {
        this.usageMonthLimit = usageMonthLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_id_registration_card")
    public Integer getUsageIdRegistrationCard() {
        return usageIdRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_id_registration_card")
    public void setUsageIdRegistrationCard(Integer usageIdRegistrationCard) {
        this.usageIdRegistrationCard = usageIdRegistrationCard;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.used_months")
    public Integer getUsedMonths() {
        return usedMonths;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.used_months")
    public void setUsedMonths(Integer usedMonths) {
        this.usedMonths = usedMonths;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_market_price")
    public BigDecimal getEstimatedMarketPrice() {
        return estimatedMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_market_price")
    public void setEstimatedMarketPrice(BigDecimal estimatedMarketPrice) {
        this.estimatedMarketPrice = estimatedMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.starting_price")
    public BigDecimal getStartingPrice() {
        return startingPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.starting_price")
    public void setStartingPrice(BigDecimal startingPrice) {
        this.startingPrice = startingPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_net_sale_value")
    public BigDecimal getEstimatedNetSaleValue() {
        return estimatedNetSaleValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_net_sale_value")
    public void setEstimatedNetSaleValue(BigDecimal estimatedNetSaleValue) {
        this.estimatedNetSaleValue = estimatedNetSaleValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_acceptable_price")
    public BigDecimal getMinimumAcceptablePrice() {
        return minimumAcceptablePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_acceptable_price")
    public void setMinimumAcceptablePrice(BigDecimal minimumAcceptablePrice) {
        this.minimumAcceptablePrice = minimumAcceptablePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.handling_fee")
    public BigDecimal getHandlingFee() {
        return handlingFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.handling_fee")
    public void setHandlingFee(BigDecimal handlingFee) {
        this.handlingFee = handlingFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_reason")
    public String getSaleReason() {
        return saleReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_reason")
    public void setSaleReason(String saleReason) {
        this.saleReason = saleReason == null ? null : saleReason.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_reason")
    public String getActualSaleReason() {
        return actualSaleReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_reason")
    public void setActualSaleReason(String actualSaleReason) {
        this.actualSaleReason = actualSaleReason == null ? null : actualSaleReason.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_nature")
    public String getUsageNature() {
        return usageNature;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.usage_nature")
    public void setUsageNature(String usageNature) {
        this.usageNature = usageNature == null ? null : usageNature.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_type")
    public Integer getDisposalType() {
        return disposalType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_type")
    public void setDisposalType(Integer disposalType) {
        this.disposalType = disposalType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_vehicle_loss")
    public BigDecimal getEstimatedVehicleLoss() {
        return estimatedVehicleLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_vehicle_loss")
    public void setEstimatedVehicleLoss(BigDecimal estimatedVehicleLoss) {
        this.estimatedVehicleLoss = estimatedVehicleLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.government_subsidy_amount")
    public BigDecimal getGovernmentSubsidyAmount() {
        return governmentSubsidyAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.government_subsidy_amount")
    public void setGovernmentSubsidyAmount(BigDecimal governmentSubsidyAmount) {
        this.governmentSubsidyAmount = governmentSubsidyAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_reason")
    public String getDisposalReason() {
        return disposalReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.disposal_reason")
    public void setDisposalReason(String disposalReason) {
        this.disposalReason = disposalReason == null ? null : disposalReason.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.original_value")
    public BigDecimal getOriginalValue() {
        return originalValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.original_value")
    public void setOriginalValue(BigDecimal originalValue) {
        this.originalValue = originalValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.accumulated_depreciation")
    public String getAccumulatedDepreciation() {
        return accumulatedDepreciation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.accumulated_depreciation")
    public void setAccumulatedDepreciation(String accumulatedDepreciation) {
        this.accumulatedDepreciation = accumulatedDepreciation == null ? null : accumulatedDepreciation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_value")
    public BigDecimal getNetValue() {
        return netValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_value")
    public void setNetValue(BigDecimal netValue) {
        this.netValue = netValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.financial_evaluation")
    public String getFinancialEvaluation() {
        return financialEvaluation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.financial_evaluation")
    public void setFinancialEvaluation(String financialEvaluation) {
        this.financialEvaluation = financialEvaluation == null ? null : financialEvaluation.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_selling_price")
    public BigDecimal getActualSellingPrice() {
        return actualSellingPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_selling_price")
    public void setActualSellingPrice(BigDecimal actualSellingPrice) {
        this.actualSellingPrice = actualSellingPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_net_price")
    public BigDecimal getActualNetPrice() {
        return actualNetPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_net_price")
    public void setActualNetPrice(BigDecimal actualNetPrice) {
        this.actualNetPrice = actualNetPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_price_diff")
    public BigDecimal getNetPriceDiff() {
        return netPriceDiff;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.net_price_diff")
    public void setNetPriceDiff(BigDecimal netPriceDiff) {
        this.netPriceDiff = netPriceDiff;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_gain_loss")
    public BigDecimal getSaleGainLoss() {
        return saleGainLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.sale_gain_loss")
    public void setSaleGainLoss(BigDecimal saleGainLoss) {
        this.saleGainLoss = saleGainLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sold_quantity")
    public Integer getActualSoldQuantity() {
        return actualSoldQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sold_quantity")
    public void setActualSoldQuantity(Integer actualSoldQuantity) {
        this.actualSoldQuantity = actualSoldQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quantity_diff")
    public Integer getQuantityDiff() {
        return quantityDiff;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quantity_diff")
    public void setQuantityDiff(Integer quantityDiff) {
        this.quantityDiff = quantityDiff;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_desc")
    public String getActualSaleDesc() {
        return actualSaleDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_desc")
    public void setActualSaleDesc(String actualSaleDesc) {
        this.actualSaleDesc = actualSaleDesc == null ? null : actualSaleDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_scrap_loss_amount")
    public BigDecimal getEstimatedScrapLossAmount() {
        return estimatedScrapLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.estimated_scrap_loss_amount")
    public void setEstimatedScrapLossAmount(BigDecimal estimatedScrapLossAmount) {
        this.estimatedScrapLossAmount = estimatedScrapLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_insurance_auction")
    public Integer getIsInsuranceAuction() {
        return isInsuranceAuction;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_insurance_auction")
    public void setIsInsuranceAuction(Integer isInsuranceAuction) {
        this.isInsuranceAuction = isInsuranceAuction;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_auction_amount")
    public BigDecimal getActualAuctionAmount() {
        return actualAuctionAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_auction_amount")
    public void setActualAuctionAmount(BigDecimal actualAuctionAmount) {
        this.actualAuctionAmount = actualAuctionAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_scrap_profit_loss")
    public BigDecimal getActualScrapProfitLoss() {
        return actualScrapProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_scrap_profit_loss")
    public void setActualScrapProfitLoss(BigDecimal actualScrapProfitLoss) {
        this.actualScrapProfitLoss = actualScrapProfitLoss;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_type_manual")
    public String getLicenseTypeManual() {
        return licenseTypeManual;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.license_type_manual")
    public void setLicenseTypeManual(String licenseTypeManual) {
        this.licenseTypeManual = licenseTypeManual == null ? null : licenseTypeManual.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quota_type")
    public Integer getQuotaType() {
        return quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.quota_type")
    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_abbreviation_id")
    public Integer getVehicleAbbreviationId() {
        return vehicleAbbreviationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_abbreviation_id")
    public void setVehicleAbbreviationId(Integer vehicleAbbreviationId) {
        this.vehicleAbbreviationId = vehicleAbbreviationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_color_id")
    public Integer getVehicleColorId() {
        return vehicleColorId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_color_id")
    public void setVehicleColorId(Integer vehicleColorId) {
        this.vehicleColorId = vehicleColorId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.msrp")
    public String getMsrp() {
        return msrp;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.msrp")
    public void setMsrp(String msrp) {
        this.msrp = msrp == null ? null : msrp.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.mileage")
    public BigDecimal getMileage() {
        return mileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.mileage")
    public void setMileage(BigDecimal mileage) {
        this.mileage = mileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_condition")
    public String getVehicleCondition() {
        return vehicleCondition;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.vehicle_condition")
    public void setVehicleCondition(String vehicleCondition) {
        this.vehicleCondition = vehicleCondition == null ? null : vehicleCondition.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_selling_price")
    public BigDecimal getMinimumSellingPrice() {
        return minimumSellingPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.minimum_selling_price")
    public void setMinimumSellingPrice(BigDecimal minimumSellingPrice) {
        this.minimumSellingPrice = minimumSellingPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.auction_reserve_price")
    public BigDecimal getAuctionReservePrice() {
        return auctionReservePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.auction_reserve_price")
    public void setAuctionReservePrice(BigDecimal auctionReservePrice) {
        this.auctionReservePrice = auctionReservePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_one")
    public BigDecimal getExpectedProfitLossOne() {
        return expectedProfitLossOne;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_one")
    public void setExpectedProfitLossOne(BigDecimal expectedProfitLossOne) {
        this.expectedProfitLossOne = expectedProfitLossOne;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_two")
    public BigDecimal getExpectedProfitLossTwo() {
        return expectedProfitLossTwo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.expected_profit_loss_two")
    public void setExpectedProfitLossTwo(BigDecimal expectedProfitLossTwo) {
        this.expectedProfitLossTwo = expectedProfitLossTwo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.current_month_net_value")
    public BigDecimal getCurrentMonthNetValue() {
        return currentMonthNetValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.current_month_net_value")
    public void setCurrentMonthNetValue(BigDecimal currentMonthNetValue) {
        this.currentMonthNetValue = currentMonthNetValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.secondhand_car_invoice_url")
    public String getSecondhandCarInvoiceUrl() {
        return secondhandCarInvoiceUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.secondhand_car_invoice_url")
    public void setSecondhandCarInvoiceUrl(String secondhandCarInvoiceUrl) {
        this.secondhandCarInvoiceUrl = secondhandCarInvoiceUrl == null ? null : secondhandCarInvoiceUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.file_url")
    public String getFileUrl() {
        return fileUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.file_url")
    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl == null ? null : fileUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_file_url")
    public String getActualSaleFileUrl() {
        return actualSaleFileUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_disposal_detail.actual_sale_file_url")
    public void setActualSaleFileUrl(String actualSaleFileUrl) {
        this.actualSaleFileUrl = actualSaleFileUrl == null ? null : actualSaleFileUrl.trim();
    }
}