package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   简道云表单表信息
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_jdy_data_info
 */
public class JdyDataInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   数据ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.data_id")
    private String dataId;

    /**
     * Database Column Remarks:
     *   表单ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.entry_id")
    private String entryId;

    /**
     * Database Column Remarks:
     *   发起月份
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602495")
    private Date widget1732281602495;

    /**
     * Database Column Remarks:
     *   发起日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235702")
    private Date widget1732271235702;

    /**
     * Database Column Remarks:
     *   条线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235713")
    private String widget1732271235713;

    /**
     * Database Column Remarks:
     *   出售车辆单位
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235722")
    private String widget1732271235722;

    /**
     * Database Column Remarks:
     *   公司区域
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051627")
    private String widget1736926051627;

    /**
     * Database Column Remarks:
     *   当地财务审批人员
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051635")
    private String widget1736926051635;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235709")
    private String widget1732271235709;

    /**
     * Database Column Remarks:
     *   所属分公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235742")
    private String widget1732271235742;

    /**
     * Database Column Remarks:
     *   所属车队
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235741")
    private String widget1732271235741;

    /**
     * Database Column Remarks:
     *   品牌车型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235750")
    private String widget1732271235750;

    /**
     * Database Column Remarks:
     *   车型配置
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743895605231")
    private String widget1743895605231;

    /**
     * Database Column Remarks:
     *   车型代码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235734")
    private String widget1732271235734;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744687012604")
    private String widget1744687012604;

    /**
     * Database Column Remarks:
     *   颜色
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544071")
    private String widget1737338544071;

    /**
     * Database Column Remarks:
     *   牌照性质
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602514")
    private String widget1732281602514;

    /**
     * Database Column Remarks:
     *   出厂日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544074")
    private Date widget1737338544074;

    /**
     * Database Column Remarks:
     *   上牌日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235755")
    private Date widget1732271235755;

    /**
     * Database Column Remarks:
     *   上牌年份
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1745227149952")
    private String widget1745227149952;

    /**
     * Database Column Remarks:
     *   里程数（万公里）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747774937598")
    private BigDecimal widget1747774937598;

    /**
     * Database Column Remarks:
     *   车况评级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743581186765")
    private String widget1743581186765;

    /**
     * Database Column Remarks:
     *   评估报告
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579518411")
    private String widget1743579518411;

    /**
     * Database Column Remarks:
     *   牌照性质（简易）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1750208093989")
    private String widget1750208093989;

    /**
     * Database Column Remarks:
     *   官方指导价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605137")
    private BigDecimal widget1747775605137;

    /**
     * Database Column Remarks:
     *   采购裸车价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760832")
    private BigDecimal widget1732583760832;

    /**
     * Database Column Remarks:
     *   购置费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760833")
    private BigDecimal widget1732583760833;

    /**
     * Database Column Remarks:
     *   上牌杂费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760834")
    private BigDecimal widget1732583760834;

    /**
     * Database Column Remarks:
     *   装潢费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760838")
    private BigDecimal widget1732583760838;

    /**
     * Database Column Remarks:
     *   购置总价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760842")
    private BigDecimal widget1732583760842;

    /**
     * Database Column Remarks:
     *   最低出售价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605138")
    private BigDecimal widget1747775605138;

    /**
     * Database Column Remarks:
     *   预估净售价1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732280180068")
    private BigDecimal widget1732280180068;

    /**
     * Database Column Remarks:
     *   预估残值率1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199512")
    private BigDecimal widget1732285199512;

    /**
     * Database Column Remarks:
     *   拍卖保留价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605139")
    private BigDecimal widget1747775605139;

    /**
     * Database Column Remarks:
     *   预估净售价2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642987")
    private BigDecimal widget1744027642987;

    /**
     * Database Column Remarks:
     *   预估残值率2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642988")
    private BigDecimal widget1744027642988;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579339407")
    private String widget1743579339407;

    /**
     * Database Column Remarks:
     *   使用年限（月）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235785")
    private Integer widget1732271235785;

    /**
     * Database Column Remarks:
     *   已用月份数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235772")
    private Integer widget1732271235772;

    /**
     * Database Column Remarks:
     *   车龄（年）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602529")
    private String widget1732281602529;

    /**
     * Database Column Remarks:
     *   原值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235786")
    private BigDecimal widget1732271235786;

    /**
     * Database Column Remarks:
     *   已提折旧
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235787")
    private BigDecimal widget1732271235787;

    /**
     * Database Column Remarks:
     *   净值/残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235788")
    private BigDecimal widget1732271235788;

    /**
     * Database Column Remarks:
     *   财务评估损益
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235789")
    private BigDecimal widget1732271235789;

    /**
     * Database Column Remarks:
     *   拍卖日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684293")
    private Date widget1743641684293;

    /**
     * Database Column Remarks:
     *   拍卖次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799865")
    private Integer widget1743602799865;

    /**
     * Database Column Remarks:
     *   出售底价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445365")
    private BigDecimal widget1743562445365;

    /**
     * Database Column Remarks:
     *   拍卖保留价-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862109")
    private BigDecimal widget1743387862109;

    /**
     * Database Column Remarks:
     *   拍卖最高价-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445367")
    private BigDecimal widget1743562445367;

    /**
     * Database Column Remarks:
     *   实际成交价-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445368")
    private BigDecimal widget1743562445368;

    /**
     * Database Column Remarks:
     *   溢价率（底价）-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862100")
    private BigDecimal widget1743387862100;

    /**
     * Database Column Remarks:
     *   溢价率（保留价）-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799863")
    private BigDecimal widget1743602799863;

    /**
     * Database Column Remarks:
     *   实际残值率-1
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199519")
    private BigDecimal widget1732285199519;

    /**
     * Database Column Remarks:
     *   成交方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365624")
    private String widget1743983365624;

    /**
     * Database Column Remarks:
     *   成交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365636")
    private Date widget1743983365636;

    /**
     * Database Column Remarks:
     *   拍卖日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684294")
    private Date widget1743641684294;

    /**
     * Database Column Remarks:
     *   拍卖次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235810")
    private Integer widget1732271235810;

    /**
     * Database Column Remarks:
     *   出售底价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799866")
    private BigDecimal widget1743602799866;

    /**
     * Database Column Remarks:
     *   拍卖保留价-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445378")
    private BigDecimal widget1743562445378;

    /**
     * Database Column Remarks:
     *   拍卖最高价-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445379")
    private BigDecimal widget1743562445379;

    /**
     * Database Column Remarks:
     *   实际成交价-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445380")
    private BigDecimal widget1743562445380;

    /**
     * Database Column Remarks:
     *   溢价率（底价）-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799867")
    private BigDecimal widget1743602799867;

    /**
     * Database Column Remarks:
     *   溢价率（保留价）-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445383")
    private BigDecimal widget1743562445383;

    /**
     * Database Column Remarks:
     *   实际残值率-2
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445384")
    private BigDecimal widget1743562445384;

    /**
     * Database Column Remarks:
     *   成交方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365631")
    private String widget1743983365631;

    /**
     * Database Column Remarks:
     *   成交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365635")
    private Date widget1743983365635;

    /**
     * Database Column Remarks:
     *   拍卖日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684295")
    private Date widget1743641684295;

    /**
     * Database Column Remarks:
     *   拍卖次数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445375")
    private Integer widget1743562445375;

    /**
     * Database Column Remarks:
     *   出售底价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862103")
    private BigDecimal widget1743387862103;

    /**
     * Database Column Remarks:
     *   拍卖保留价-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445366")
    private BigDecimal widget1743562445366;

    /**
     * Database Column Remarks:
     *   拍卖最高价-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862104")
    private BigDecimal widget1743387862104;

    /**
     * Database Column Remarks:
     *   实际成交价-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862105")
    private BigDecimal widget1743387862105;

    /**
     * Database Column Remarks:
     *   溢价率（底价）-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445369")
    private BigDecimal widget1743562445369;

    /**
     * Database Column Remarks:
     *   溢价率（保留价）-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235811")
    private BigDecimal widget1732271235811;

    /**
     * Database Column Remarks:
     *   实际残值率-3
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445372")
    private BigDecimal widget1743562445372;

    /**
     * Database Column Remarks:
     *   成交方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365633")
    private String widget1743983365633;

    /**
     * Database Column Remarks:
     *   成交日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365637")
    private Date widget1743983365637;

    /**
     * Database Column Remarks:
     *   流程状态 0-进行中 1 完成
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.flow_state")
    private Integer flowState;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_jdy_data_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.data_id")
    public String getDataId() {
        return dataId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.data_id")
    public void setDataId(String dataId) {
        this.dataId = dataId == null ? null : dataId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.entry_id")
    public String getEntryId() {
        return entryId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.entry_id")
    public void setEntryId(String entryId) {
        this.entryId = entryId == null ? null : entryId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602495")
    public Date getWidget1732281602495() {
        return widget1732281602495;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602495")
    public void setWidget1732281602495(Date widget1732281602495) {
        this.widget1732281602495 = widget1732281602495;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235702")
    public Date getWidget1732271235702() {
        return widget1732271235702;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235702")
    public void setWidget1732271235702(Date widget1732271235702) {
        this.widget1732271235702 = widget1732271235702;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235713")
    public String getWidget1732271235713() {
        return widget1732271235713;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235713")
    public void setWidget1732271235713(String widget1732271235713) {
        this.widget1732271235713 = widget1732271235713 == null ? null : widget1732271235713.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235722")
    public String getWidget1732271235722() {
        return widget1732271235722;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235722")
    public void setWidget1732271235722(String widget1732271235722) {
        this.widget1732271235722 = widget1732271235722 == null ? null : widget1732271235722.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051627")
    public String getWidget1736926051627() {
        return widget1736926051627;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051627")
    public void setWidget1736926051627(String widget1736926051627) {
        this.widget1736926051627 = widget1736926051627 == null ? null : widget1736926051627.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051635")
    public String getWidget1736926051635() {
        return widget1736926051635;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1736926051635")
    public void setWidget1736926051635(String widget1736926051635) {
        this.widget1736926051635 = widget1736926051635 == null ? null : widget1736926051635.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235709")
    public String getWidget1732271235709() {
        return widget1732271235709;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235709")
    public void setWidget1732271235709(String widget1732271235709) {
        this.widget1732271235709 = widget1732271235709 == null ? null : widget1732271235709.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235742")
    public String getWidget1732271235742() {
        return widget1732271235742;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235742")
    public void setWidget1732271235742(String widget1732271235742) {
        this.widget1732271235742 = widget1732271235742 == null ? null : widget1732271235742.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235741")
    public String getWidget1732271235741() {
        return widget1732271235741;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235741")
    public void setWidget1732271235741(String widget1732271235741) {
        this.widget1732271235741 = widget1732271235741 == null ? null : widget1732271235741.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235750")
    public String getWidget1732271235750() {
        return widget1732271235750;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235750")
    public void setWidget1732271235750(String widget1732271235750) {
        this.widget1732271235750 = widget1732271235750 == null ? null : widget1732271235750.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743895605231")
    public String getWidget1743895605231() {
        return widget1743895605231;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743895605231")
    public void setWidget1743895605231(String widget1743895605231) {
        this.widget1743895605231 = widget1743895605231 == null ? null : widget1743895605231.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235734")
    public String getWidget1732271235734() {
        return widget1732271235734;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235734")
    public void setWidget1732271235734(String widget1732271235734) {
        this.widget1732271235734 = widget1732271235734 == null ? null : widget1732271235734.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744687012604")
    public String getWidget1744687012604() {
        return widget1744687012604;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744687012604")
    public void setWidget1744687012604(String widget1744687012604) {
        this.widget1744687012604 = widget1744687012604 == null ? null : widget1744687012604.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544071")
    public String getWidget1737338544071() {
        return widget1737338544071;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544071")
    public void setWidget1737338544071(String widget1737338544071) {
        this.widget1737338544071 = widget1737338544071 == null ? null : widget1737338544071.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602514")
    public String getWidget1732281602514() {
        return widget1732281602514;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602514")
    public void setWidget1732281602514(String widget1732281602514) {
        this.widget1732281602514 = widget1732281602514 == null ? null : widget1732281602514.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544074")
    public Date getWidget1737338544074() {
        return widget1737338544074;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1737338544074")
    public void setWidget1737338544074(Date widget1737338544074) {
        this.widget1737338544074 = widget1737338544074;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235755")
    public Date getWidget1732271235755() {
        return widget1732271235755;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235755")
    public void setWidget1732271235755(Date widget1732271235755) {
        this.widget1732271235755 = widget1732271235755;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1745227149952")
    public String getWidget1745227149952() {
        return widget1745227149952;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1745227149952")
    public void setWidget1745227149952(String widget1745227149952) {
        this.widget1745227149952 = widget1745227149952 == null ? null : widget1745227149952.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747774937598")
    public BigDecimal getWidget1747774937598() {
        return widget1747774937598;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747774937598")
    public void setWidget1747774937598(BigDecimal widget1747774937598) {
        this.widget1747774937598 = widget1747774937598;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743581186765")
    public String getWidget1743581186765() {
        return widget1743581186765;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743581186765")
    public void setWidget1743581186765(String widget1743581186765) {
        this.widget1743581186765 = widget1743581186765 == null ? null : widget1743581186765.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579518411")
    public String getWidget1743579518411() {
        return widget1743579518411;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579518411")
    public void setWidget1743579518411(String widget1743579518411) {
        this.widget1743579518411 = widget1743579518411 == null ? null : widget1743579518411.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1750208093989")
    public String getWidget1750208093989() {
        return widget1750208093989;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1750208093989")
    public void setWidget1750208093989(String widget1750208093989) {
        this.widget1750208093989 = widget1750208093989 == null ? null : widget1750208093989.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605137")
    public BigDecimal getWidget1747775605137() {
        return widget1747775605137;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605137")
    public void setWidget1747775605137(BigDecimal widget1747775605137) {
        this.widget1747775605137 = widget1747775605137;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760832")
    public BigDecimal getWidget1732583760832() {
        return widget1732583760832;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760832")
    public void setWidget1732583760832(BigDecimal widget1732583760832) {
        this.widget1732583760832 = widget1732583760832;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760833")
    public BigDecimal getWidget1732583760833() {
        return widget1732583760833;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760833")
    public void setWidget1732583760833(BigDecimal widget1732583760833) {
        this.widget1732583760833 = widget1732583760833;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760834")
    public BigDecimal getWidget1732583760834() {
        return widget1732583760834;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760834")
    public void setWidget1732583760834(BigDecimal widget1732583760834) {
        this.widget1732583760834 = widget1732583760834;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760838")
    public BigDecimal getWidget1732583760838() {
        return widget1732583760838;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760838")
    public void setWidget1732583760838(BigDecimal widget1732583760838) {
        this.widget1732583760838 = widget1732583760838;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760842")
    public BigDecimal getWidget1732583760842() {
        return widget1732583760842;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732583760842")
    public void setWidget1732583760842(BigDecimal widget1732583760842) {
        this.widget1732583760842 = widget1732583760842;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605138")
    public BigDecimal getWidget1747775605138() {
        return widget1747775605138;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605138")
    public void setWidget1747775605138(BigDecimal widget1747775605138) {
        this.widget1747775605138 = widget1747775605138;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732280180068")
    public BigDecimal getWidget1732280180068() {
        return widget1732280180068;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732280180068")
    public void setWidget1732280180068(BigDecimal widget1732280180068) {
        this.widget1732280180068 = widget1732280180068;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199512")
    public BigDecimal getWidget1732285199512() {
        return widget1732285199512;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199512")
    public void setWidget1732285199512(BigDecimal widget1732285199512) {
        this.widget1732285199512 = widget1732285199512;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605139")
    public BigDecimal getWidget1747775605139() {
        return widget1747775605139;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1747775605139")
    public void setWidget1747775605139(BigDecimal widget1747775605139) {
        this.widget1747775605139 = widget1747775605139;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642987")
    public BigDecimal getWidget1744027642987() {
        return widget1744027642987;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642987")
    public void setWidget1744027642987(BigDecimal widget1744027642987) {
        this.widget1744027642987 = widget1744027642987;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642988")
    public BigDecimal getWidget1744027642988() {
        return widget1744027642988;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1744027642988")
    public void setWidget1744027642988(BigDecimal widget1744027642988) {
        this.widget1744027642988 = widget1744027642988;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579339407")
    public String getWidget1743579339407() {
        return widget1743579339407;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743579339407")
    public void setWidget1743579339407(String widget1743579339407) {
        this.widget1743579339407 = widget1743579339407 == null ? null : widget1743579339407.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235785")
    public Integer getWidget1732271235785() {
        return widget1732271235785;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235785")
    public void setWidget1732271235785(Integer widget1732271235785) {
        this.widget1732271235785 = widget1732271235785;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235772")
    public Integer getWidget1732271235772() {
        return widget1732271235772;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235772")
    public void setWidget1732271235772(Integer widget1732271235772) {
        this.widget1732271235772 = widget1732271235772;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602529")
    public String getWidget1732281602529() {
        return widget1732281602529;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732281602529")
    public void setWidget1732281602529(String widget1732281602529) {
        this.widget1732281602529 = widget1732281602529 == null ? null : widget1732281602529.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235786")
    public BigDecimal getWidget1732271235786() {
        return widget1732271235786;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235786")
    public void setWidget1732271235786(BigDecimal widget1732271235786) {
        this.widget1732271235786 = widget1732271235786;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235787")
    public BigDecimal getWidget1732271235787() {
        return widget1732271235787;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235787")
    public void setWidget1732271235787(BigDecimal widget1732271235787) {
        this.widget1732271235787 = widget1732271235787;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235788")
    public BigDecimal getWidget1732271235788() {
        return widget1732271235788;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235788")
    public void setWidget1732271235788(BigDecimal widget1732271235788) {
        this.widget1732271235788 = widget1732271235788;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235789")
    public BigDecimal getWidget1732271235789() {
        return widget1732271235789;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235789")
    public void setWidget1732271235789(BigDecimal widget1732271235789) {
        this.widget1732271235789 = widget1732271235789;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684293")
    public Date getWidget1743641684293() {
        return widget1743641684293;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684293")
    public void setWidget1743641684293(Date widget1743641684293) {
        this.widget1743641684293 = widget1743641684293;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799865")
    public Integer getWidget1743602799865() {
        return widget1743602799865;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799865")
    public void setWidget1743602799865(Integer widget1743602799865) {
        this.widget1743602799865 = widget1743602799865;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445365")
    public BigDecimal getWidget1743562445365() {
        return widget1743562445365;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445365")
    public void setWidget1743562445365(BigDecimal widget1743562445365) {
        this.widget1743562445365 = widget1743562445365;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862109")
    public BigDecimal getWidget1743387862109() {
        return widget1743387862109;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862109")
    public void setWidget1743387862109(BigDecimal widget1743387862109) {
        this.widget1743387862109 = widget1743387862109;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445367")
    public BigDecimal getWidget1743562445367() {
        return widget1743562445367;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445367")
    public void setWidget1743562445367(BigDecimal widget1743562445367) {
        this.widget1743562445367 = widget1743562445367;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445368")
    public BigDecimal getWidget1743562445368() {
        return widget1743562445368;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445368")
    public void setWidget1743562445368(BigDecimal widget1743562445368) {
        this.widget1743562445368 = widget1743562445368;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862100")
    public BigDecimal getWidget1743387862100() {
        return widget1743387862100;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862100")
    public void setWidget1743387862100(BigDecimal widget1743387862100) {
        this.widget1743387862100 = widget1743387862100;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799863")
    public BigDecimal getWidget1743602799863() {
        return widget1743602799863;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799863")
    public void setWidget1743602799863(BigDecimal widget1743602799863) {
        this.widget1743602799863 = widget1743602799863;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199519")
    public BigDecimal getWidget1732285199519() {
        return widget1732285199519;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732285199519")
    public void setWidget1732285199519(BigDecimal widget1732285199519) {
        this.widget1732285199519 = widget1732285199519;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365624")
    public String getWidget1743983365624() {
        return widget1743983365624;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365624")
    public void setWidget1743983365624(String widget1743983365624) {
        this.widget1743983365624 = widget1743983365624 == null ? null : widget1743983365624.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365636")
    public Date getWidget1743983365636() {
        return widget1743983365636;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365636")
    public void setWidget1743983365636(Date widget1743983365636) {
        this.widget1743983365636 = widget1743983365636;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684294")
    public Date getWidget1743641684294() {
        return widget1743641684294;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684294")
    public void setWidget1743641684294(Date widget1743641684294) {
        this.widget1743641684294 = widget1743641684294;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235810")
    public Integer getWidget1732271235810() {
        return widget1732271235810;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235810")
    public void setWidget1732271235810(Integer widget1732271235810) {
        this.widget1732271235810 = widget1732271235810;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799866")
    public BigDecimal getWidget1743602799866() {
        return widget1743602799866;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799866")
    public void setWidget1743602799866(BigDecimal widget1743602799866) {
        this.widget1743602799866 = widget1743602799866;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445378")
    public BigDecimal getWidget1743562445378() {
        return widget1743562445378;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445378")
    public void setWidget1743562445378(BigDecimal widget1743562445378) {
        this.widget1743562445378 = widget1743562445378;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445379")
    public BigDecimal getWidget1743562445379() {
        return widget1743562445379;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445379")
    public void setWidget1743562445379(BigDecimal widget1743562445379) {
        this.widget1743562445379 = widget1743562445379;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445380")
    public BigDecimal getWidget1743562445380() {
        return widget1743562445380;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445380")
    public void setWidget1743562445380(BigDecimal widget1743562445380) {
        this.widget1743562445380 = widget1743562445380;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799867")
    public BigDecimal getWidget1743602799867() {
        return widget1743602799867;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743602799867")
    public void setWidget1743602799867(BigDecimal widget1743602799867) {
        this.widget1743602799867 = widget1743602799867;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445383")
    public BigDecimal getWidget1743562445383() {
        return widget1743562445383;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445383")
    public void setWidget1743562445383(BigDecimal widget1743562445383) {
        this.widget1743562445383 = widget1743562445383;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445384")
    public BigDecimal getWidget1743562445384() {
        return widget1743562445384;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445384")
    public void setWidget1743562445384(BigDecimal widget1743562445384) {
        this.widget1743562445384 = widget1743562445384;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365631")
    public String getWidget1743983365631() {
        return widget1743983365631;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365631")
    public void setWidget1743983365631(String widget1743983365631) {
        this.widget1743983365631 = widget1743983365631 == null ? null : widget1743983365631.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365635")
    public Date getWidget1743983365635() {
        return widget1743983365635;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365635")
    public void setWidget1743983365635(Date widget1743983365635) {
        this.widget1743983365635 = widget1743983365635;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684295")
    public Date getWidget1743641684295() {
        return widget1743641684295;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743641684295")
    public void setWidget1743641684295(Date widget1743641684295) {
        this.widget1743641684295 = widget1743641684295;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445375")
    public Integer getWidget1743562445375() {
        return widget1743562445375;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445375")
    public void setWidget1743562445375(Integer widget1743562445375) {
        this.widget1743562445375 = widget1743562445375;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862103")
    public BigDecimal getWidget1743387862103() {
        return widget1743387862103;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862103")
    public void setWidget1743387862103(BigDecimal widget1743387862103) {
        this.widget1743387862103 = widget1743387862103;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445366")
    public BigDecimal getWidget1743562445366() {
        return widget1743562445366;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445366")
    public void setWidget1743562445366(BigDecimal widget1743562445366) {
        this.widget1743562445366 = widget1743562445366;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862104")
    public BigDecimal getWidget1743387862104() {
        return widget1743387862104;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862104")
    public void setWidget1743387862104(BigDecimal widget1743387862104) {
        this.widget1743387862104 = widget1743387862104;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862105")
    public BigDecimal getWidget1743387862105() {
        return widget1743387862105;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743387862105")
    public void setWidget1743387862105(BigDecimal widget1743387862105) {
        this.widget1743387862105 = widget1743387862105;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445369")
    public BigDecimal getWidget1743562445369() {
        return widget1743562445369;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445369")
    public void setWidget1743562445369(BigDecimal widget1743562445369) {
        this.widget1743562445369 = widget1743562445369;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235811")
    public BigDecimal getWidget1732271235811() {
        return widget1732271235811;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1732271235811")
    public void setWidget1732271235811(BigDecimal widget1732271235811) {
        this.widget1732271235811 = widget1732271235811;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445372")
    public BigDecimal getWidget1743562445372() {
        return widget1743562445372;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743562445372")
    public void setWidget1743562445372(BigDecimal widget1743562445372) {
        this.widget1743562445372 = widget1743562445372;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365633")
    public String getWidget1743983365633() {
        return widget1743983365633;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365633")
    public void setWidget1743983365633(String widget1743983365633) {
        this.widget1743983365633 = widget1743983365633 == null ? null : widget1743983365633.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365637")
    public Date getWidget1743983365637() {
        return widget1743983365637;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info._widget_1743983365637")
    public void setWidget1743983365637(Date widget1743983365637) {
        this.widget1743983365637 = widget1743983365637;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.flow_state")
    public Integer getFlowState() {
        return flowState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.flow_state")
    public void setFlowState(Integer flowState) {
        this.flowState = flowState;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_jdy_data_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}