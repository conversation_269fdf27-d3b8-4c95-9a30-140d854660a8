package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   额度信息表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_quota
 */
public class VehicleQuota implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   额度资产归属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.asset_org_code")
    private String assetOrgCode;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-运营
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.quota_type")
    private Integer quotaType;

    /**
     * Database Column Remarks:
     *   额度总数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.quota")
    private Integer quota;

    /**
     * Database Column Remarks:
     *   预占用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.pre_occupied")
    private Integer preOccupied;

    /**
     * Database Column Remarks:
     *   占用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.occupied")
    private Integer occupied;

    /**
     * Database Column Remarks:
     *   剩余可用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.remaining")
    private Integer remaining;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_quota")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.asset_org_code")
    public String getAssetOrgCode() {
        return assetOrgCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.asset_org_code")
    public void setAssetOrgCode(String assetOrgCode) {
        this.assetOrgCode = assetOrgCode == null ? null : assetOrgCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.quota_type")
    public Integer getQuotaType() {
        return quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.quota_type")
    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.quota")
    public Integer getQuota() {
        return quota;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.quota")
    public void setQuota(Integer quota) {
        this.quota = quota;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.pre_occupied")
    public Integer getPreOccupied() {
        return preOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.pre_occupied")
    public void setPreOccupied(Integer preOccupied) {
        this.preOccupied = preOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.occupied")
    public Integer getOccupied() {
        return occupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.occupied")
    public void setOccupied(Integer occupied) {
        this.occupied = occupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.remaining")
    public Integer getRemaining() {
        return remaining;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.remaining")
    public void setRemaining(Integer remaining) {
        this.remaining = remaining;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_quota.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}