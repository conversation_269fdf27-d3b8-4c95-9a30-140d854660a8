package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vin_model_info
 */
public class VinModelInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   车型名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_name")
    private String modelName;

    /**
     * Database Column Remarks:
     *   车型信息json
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_json")
    private String modelJson;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vin_model_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_name")
    public String getModelName() {
        return modelName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_name")
    public void setModelName(String modelName) {
        this.modelName = modelName == null ? null : modelName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_json")
    public String getModelJson() {
        return modelJson;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vin_model_info.model_json")
    public void setModelJson(String modelJson) {
        this.modelJson = modelJson == null ? null : modelJson.trim();
    }
}