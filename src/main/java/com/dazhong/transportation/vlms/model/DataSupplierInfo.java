package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   供应商信息表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_data_supplier_info
 */
public class DataSupplierInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   供应商名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.name")
    private String name;

    /**
     * Database Column Remarks:
     *   供应商地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.address")
    private String address;

    /**
     * Database Column Remarks:
     *   手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.phone")
    private String phone;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_data_supplier_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.name")
    public String getName() {
        return name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.name")
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.address")
    public String getAddress() {
        return address;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.address")
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.phone")
    public String getPhone() {
        return phone;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.phone")
    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_data_supplier_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}