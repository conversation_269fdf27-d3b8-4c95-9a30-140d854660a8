package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车牌任务信息表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_license_plate_task_info
 */
public class LicensePlateTaskInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_number")
    private String taskNumber;

    /**
     * Database Column Remarks:
     *   任务类型 1-上牌任务 2-退牌任务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_type")
    private Integer taskType;

    /**
     * Database Column Remarks:
     *   是否使用额度（上牌） 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.use_quota_type")
    private Integer useQuotaType;

    /**
     * Database Column Remarks:
     *   是否使用额度（退牌） 1-退还 2-不退还 3-不涉及
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.return_quota_type")
    private Integer returnQuotaType;

    /**
     * Database Column Remarks:
     *   额度资产归属公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_id")
    private Integer assetCompanyId;

    /**
     * Database Column Remarks:
     *   额度资产归属公司名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_name")
    private String assetCompanyName;

    /**
     * Database Column Remarks:
     *   额度类型 1-社会牌照 2-纳管 3-网约 4-新能源网约 5-营运
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.quota_type")
    private Integer quotaType;

    /**
     * Database Column Remarks:
     *   匹配类型 1-明确匹配额度编号 2-未明确匹配额度编号 3-不知道额度编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.match_type")
    private Integer matchType;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_license_plate_task_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_number")
    public String getTaskNumber() {
        return taskNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_number")
    public void setTaskNumber(String taskNumber) {
        this.taskNumber = taskNumber == null ? null : taskNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_type")
    public Integer getTaskType() {
        return taskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.task_type")
    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.use_quota_type")
    public Integer getUseQuotaType() {
        return useQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.use_quota_type")
    public void setUseQuotaType(Integer useQuotaType) {
        this.useQuotaType = useQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.return_quota_type")
    public Integer getReturnQuotaType() {
        return returnQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.return_quota_type")
    public void setReturnQuotaType(Integer returnQuotaType) {
        this.returnQuotaType = returnQuotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_id")
    public Integer getAssetCompanyId() {
        return assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_id")
    public void setAssetCompanyId(Integer assetCompanyId) {
        this.assetCompanyId = assetCompanyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_name")
    public String getAssetCompanyName() {
        return assetCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.asset_company_name")
    public void setAssetCompanyName(String assetCompanyName) {
        this.assetCompanyName = assetCompanyName == null ? null : assetCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.quota_type")
    public Integer getQuotaType() {
        return quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.quota_type")
    public void setQuotaType(Integer quotaType) {
        this.quotaType = quotaType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.match_type")
    public Integer getMatchType() {
        return matchType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.match_type")
    public void setMatchType(Integer matchType) {
        this.matchType = matchType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_license_plate_task_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}