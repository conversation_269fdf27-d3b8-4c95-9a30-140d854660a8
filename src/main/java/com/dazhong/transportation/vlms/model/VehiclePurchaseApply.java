package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   车辆采购申请
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_purchase_apply
 */
public class VehiclePurchaseApply implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   采购申请编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_no")
    private String purchaseApplyNo;

    /**
     * Database Column Remarks:
     *   钉钉审批编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.approval_number")
    private String approvalNumber;

    /**
     * Database Column Remarks:
     *   采购申请状态 1-未提交 2-审批中 3-审批通过 4-审批拒绝 5-已作废 6-已完成(已关闭)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_status")
    private Integer purchaseApplyStatus;

    /**
     * Database Column Remarks:
     *   采购产品线 1-寻网业务 2-商务业务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_product_line")
    private Integer applyProductLine;

    /**
     * Database Column Remarks:
     *   采购名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_name")
    private String applyName;

    /**
     * Database Column Remarks:
     *   采购备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_remark")
    private String applyRemark;

    /**
     * Database Column Remarks:
     *   采购车辆数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_quantity")
    private Integer purchaseQuantity;

    /**
     * Database Column Remarks:
     *   收货车辆数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.take_delivery_quantity")
    private Integer takeDeliveryQuantity;

    /**
     * Database Column Remarks:
     *   预占用数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.pre_occupied")
    private Integer preOccupied;

    /**
     * Database Column Remarks:
     *   车辆上牌占用数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.vehicle_occupied")
    private Integer vehicleOccupied;

    /**
     * Database Column Remarks:
     *   单据所属资产公司ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.owner_id")
    private Integer ownerId;

    /**
     * Database Column Remarks:
     *   单据所属机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_id")
    private Long applyOrgId;

    /**
     * Database Column Remarks:
     *   申请公司code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_code")
    private String applyOrgCode;

    /**
     * Database Column Remarks:
     *   申请人工号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user_no")
    private String applyUserNo;

    /**
     * Database Column Remarks:
     *   申请人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user")
    private String applyUser;

    /**
     * Database Column Remarks:
     *   申购公司code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_code")
    private String subscriptionCompanyCode;

    /**
     * Database Column Remarks:
     *   申购公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_name")
    private String subscriptionCompanyName;

    /**
     * Database Column Remarks:
     *   供应商大类
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.supplier_type")
    private Integer supplierType;

    /**
     * Database Column Remarks:
     *   采购意向单号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.intention_no")
    private String intentionNo;

    /**
     * Database Column Remarks:
     *   提交时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.submit_date")
    private Date submitDate;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_code")
    private String applicantDepartmentCode;

    /**
     * Database Column Remarks:
     *   申请人钉钉部门名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_name")
    private String applicantDepartmentName;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_apply")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_no")
    public String getPurchaseApplyNo() {
        return purchaseApplyNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_no")
    public void setPurchaseApplyNo(String purchaseApplyNo) {
        this.purchaseApplyNo = purchaseApplyNo == null ? null : purchaseApplyNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.approval_number")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.approval_number")
    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber == null ? null : approvalNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_status")
    public Integer getPurchaseApplyStatus() {
        return purchaseApplyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_apply_status")
    public void setPurchaseApplyStatus(Integer purchaseApplyStatus) {
        this.purchaseApplyStatus = purchaseApplyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_product_line")
    public Integer getApplyProductLine() {
        return applyProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_product_line")
    public void setApplyProductLine(Integer applyProductLine) {
        this.applyProductLine = applyProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_name")
    public String getApplyName() {
        return applyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_name")
    public void setApplyName(String applyName) {
        this.applyName = applyName == null ? null : applyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_remark")
    public String getApplyRemark() {
        return applyRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_remark")
    public void setApplyRemark(String applyRemark) {
        this.applyRemark = applyRemark == null ? null : applyRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_quantity")
    public Integer getPurchaseQuantity() {
        return purchaseQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.purchase_quantity")
    public void setPurchaseQuantity(Integer purchaseQuantity) {
        this.purchaseQuantity = purchaseQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.take_delivery_quantity")
    public Integer getTakeDeliveryQuantity() {
        return takeDeliveryQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.take_delivery_quantity")
    public void setTakeDeliveryQuantity(Integer takeDeliveryQuantity) {
        this.takeDeliveryQuantity = takeDeliveryQuantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.pre_occupied")
    public Integer getPreOccupied() {
        return preOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.pre_occupied")
    public void setPreOccupied(Integer preOccupied) {
        this.preOccupied = preOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.vehicle_occupied")
    public Integer getVehicleOccupied() {
        return vehicleOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.vehicle_occupied")
    public void setVehicleOccupied(Integer vehicleOccupied) {
        this.vehicleOccupied = vehicleOccupied;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.owner_id")
    public Integer getOwnerId() {
        return ownerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.owner_id")
    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_id")
    public Long getApplyOrgId() {
        return applyOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_id")
    public void setApplyOrgId(Long applyOrgId) {
        this.applyOrgId = applyOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_code")
    public String getApplyOrgCode() {
        return applyOrgCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_org_code")
    public void setApplyOrgCode(String applyOrgCode) {
        this.applyOrgCode = applyOrgCode == null ? null : applyOrgCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user_no")
    public String getApplyUserNo() {
        return applyUserNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user_no")
    public void setApplyUserNo(String applyUserNo) {
        this.applyUserNo = applyUserNo == null ? null : applyUserNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user")
    public String getApplyUser() {
        return applyUser;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.apply_user")
    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser == null ? null : applyUser.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_code")
    public String getSubscriptionCompanyCode() {
        return subscriptionCompanyCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_code")
    public void setSubscriptionCompanyCode(String subscriptionCompanyCode) {
        this.subscriptionCompanyCode = subscriptionCompanyCode == null ? null : subscriptionCompanyCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_name")
    public String getSubscriptionCompanyName() {
        return subscriptionCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.subscription_company_name")
    public void setSubscriptionCompanyName(String subscriptionCompanyName) {
        this.subscriptionCompanyName = subscriptionCompanyName == null ? null : subscriptionCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.supplier_type")
    public Integer getSupplierType() {
        return supplierType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.supplier_type")
    public void setSupplierType(Integer supplierType) {
        this.supplierType = supplierType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.intention_no")
    public String getIntentionNo() {
        return intentionNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.intention_no")
    public void setIntentionNo(String intentionNo) {
        this.intentionNo = intentionNo == null ? null : intentionNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.submit_date")
    public Date getSubmitDate() {
        return submitDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.submit_date")
    public void setSubmitDate(Date submitDate) {
        this.submitDate = submitDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_code")
    public String getApplicantDepartmentCode() {
        return applicantDepartmentCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_code")
    public void setApplicantDepartmentCode(String applicantDepartmentCode) {
        this.applicantDepartmentCode = applicantDepartmentCode == null ? null : applicantDepartmentCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_name")
    public String getApplicantDepartmentName() {
        return applicantDepartmentName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.applicant_department_name")
    public void setApplicantDepartmentName(String applicantDepartmentName) {
        this.applicantDepartmentName = applicantDepartmentName == null ? null : applicantDepartmentName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_apply.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}