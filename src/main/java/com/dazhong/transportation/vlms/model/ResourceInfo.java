package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   资源表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_resource_info
 */
public class ResourceInfo implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   资源key(推荐uuid)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_key")
    private String resourceKey;

    /**
     * Database Column Remarks:
     *   资源名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_name")
    private String resourceName;

    /**
     * Database Column Remarks:
     *   资源code
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_code")
    private String resourceCode;

    /**
     * Database Column Remarks:
     *   资源url
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_url")
    private String resourceUrl;

    /**
     * Database Column Remarks:
     *   资源类型 0-系统 1-静态资源 2-功能点
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_type")
    private Integer resourceType;

    /**
     * Database Column Remarks:
     *   资源图标url
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_icon_url")
    private String resourceIconUrl;

    /**
     * Database Column Remarks:
     *   父资源id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.parent_resource_id")
    private Long parentResourceId;

    /**
     * Database Column Remarks:
     *   系统编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.system_code")
    private String systemCode;

    /**
     * Database Column Remarks:
     *   排序标记
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.sort")
    private Integer sort;

    /**
     * Database Column Remarks:
     *   菜单是否隐藏 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide")
    private Integer hide;

    /**
     * Database Column Remarks:
     *   标题是否隐藏 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide_title")
    private Integer hideTitle;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.misc_desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_resource_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_key")
    public String getResourceKey() {
        return resourceKey;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_key")
    public void setResourceKey(String resourceKey) {
        this.resourceKey = resourceKey == null ? null : resourceKey.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_name")
    public String getResourceName() {
        return resourceName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_name")
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName == null ? null : resourceName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_code")
    public String getResourceCode() {
        return resourceCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_code")
    public void setResourceCode(String resourceCode) {
        this.resourceCode = resourceCode == null ? null : resourceCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_url")
    public String getResourceUrl() {
        return resourceUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_url")
    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl == null ? null : resourceUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_type")
    public Integer getResourceType() {
        return resourceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_type")
    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_icon_url")
    public String getResourceIconUrl() {
        return resourceIconUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.resource_icon_url")
    public void setResourceIconUrl(String resourceIconUrl) {
        this.resourceIconUrl = resourceIconUrl == null ? null : resourceIconUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.parent_resource_id")
    public Long getParentResourceId() {
        return parentResourceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.parent_resource_id")
    public void setParentResourceId(Long parentResourceId) {
        this.parentResourceId = parentResourceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.system_code")
    public String getSystemCode() {
        return systemCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.system_code")
    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode == null ? null : systemCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.sort")
    public Integer getSort() {
        return sort;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.sort")
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide")
    public Integer getHide() {
        return hide;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide")
    public void setHide(Integer hide) {
        this.hide = hide;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide_title")
    public Integer getHideTitle() {
        return hideTitle;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.hide_title")
    public void setHideTitle(Integer hideTitle) {
        this.hideTitle = hideTitle;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.misc_desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.misc_desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_resource_info.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}