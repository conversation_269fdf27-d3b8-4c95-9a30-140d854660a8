package com.dazhong.transportation.vlms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   采购车载设备表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table t_vehicle_purchase_equipment
 */
public class VehiclePurchaseEquipment implements Serializable {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.id")
    private Integer id;

    /**
     * Database Column Remarks:
     *   采购申id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.purchase_apply_id")
    private Long purchaseApplyId;

    /**
     * Database Column Remarks:
     *   设备名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.equipment_name")
    private String equipmentName;

    /**
     * Database Column Remarks:
     *   制造商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.manufacturer_id")
    private Integer manufacturerId;

    /**
     * Database Column Remarks:
     *   供应商
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.supplier_id")
    private Integer supplierId;

    /**
     * Database Column Remarks:
     *   单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.unit_price")
    private BigDecimal unitPrice;

    /**
     * Database Column Remarks:
     *   数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.quantity")
    private Integer quantity;

    /**
     * Database Column Remarks:
     *   其他费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.other_costs")
    private BigDecimal otherCosts;

    /**
     * Database Column Remarks:
     *   设备总价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.total_price")
    private BigDecimal totalPrice;

    /**
     * Database Column Remarks:
     *   状态（0=正常   1=已删除）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.is_deleted")
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_id")
    private Long createOperId;

    /**
     * Database Column Remarks:
     *   创建人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_name")
    private String createOperName;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   更新人id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_id")
    private Long updateOperId;

    /**
     * Database Column Remarks:
     *   更新人名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_name")
    private String updateOperName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: t_vehicle_purchase_equipment")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.id")
    public Integer getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.id")
    public void setId(Integer id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.purchase_apply_id")
    public Long getPurchaseApplyId() {
        return purchaseApplyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.purchase_apply_id")
    public void setPurchaseApplyId(Long purchaseApplyId) {
        this.purchaseApplyId = purchaseApplyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.equipment_name")
    public String getEquipmentName() {
        return equipmentName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.equipment_name")
    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName == null ? null : equipmentName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.manufacturer_id")
    public Integer getManufacturerId() {
        return manufacturerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.manufacturer_id")
    public void setManufacturerId(Integer manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.supplier_id")
    public Integer getSupplierId() {
        return supplierId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.supplier_id")
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.unit_price")
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.unit_price")
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.other_costs")
    public BigDecimal getOtherCosts() {
        return otherCosts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.other_costs")
    public void setOtherCosts(BigDecimal otherCosts) {
        this.otherCosts = otherCosts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.total_price")
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.total_price")
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.is_deleted")
    public Integer getIsDeleted() {
        return isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.is_deleted")
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_id")
    public Long getCreateOperId() {
        return createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_id")
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_name")
    public String getCreateOperName() {
        return createOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.create_oper_name")
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName == null ? null : createOperName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_id")
    public Long getUpdateOperId() {
        return updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_id")
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_name")
    public String getUpdateOperName() {
        return updateOperName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: t_vehicle_purchase_equipment.update_oper_name")
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName == null ? null : updateOperName.trim();
    }
}