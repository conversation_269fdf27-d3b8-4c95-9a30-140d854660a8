package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExportFileTypeEnum {
    EXPORT_ASSET_VEHICLE_INFO("导出车辆数据", 1, false),
    EXPORT_TRANSACTION_RECORD("导出额度流水记录", 2, false),
    EXPORT_LICENSE_PLATE_TASK_RECORD("导出上牌退牌任务记录", 3, false);

    /**
     * 标题
     */
    private final String title;

    /**
     * 来源
     */
    private final int fileSource;

    /**
     * 是否是临时导出
     */
    private final boolean isTemp;
}