package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum PublicBooleanStatusEnum {
    TRUE(1, "是",true),
    FALSE(2, "否",false);
    private final Integer code;

    private final String desc;

    private final Boolean isBoolean;

    public static String getDesc(Integer code) {
        if(code == null){
            return StringUtils.EMPTY;
        }
        for (PublicBooleanStatusEnum optEnum : PublicBooleanStatusEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getCode(String desc) {
        if (StringUtils.isBlank(desc)) {
            return null;
        }
        for (PublicBooleanStatusEnum optEnum : PublicBooleanStatusEnum.values()) {
            if (optEnum.desc.equals(desc)) {
                return optEnum.code;
            }
        }
        return null;
    }

    public static PublicBooleanStatusEnum getEnum(String desc) {
        for (PublicBooleanStatusEnum optEnum : PublicBooleanStatusEnum.values()) {
            if (optEnum.desc.equals(desc)) {
                return optEnum;
            }
        }
        return null;
    }
}
