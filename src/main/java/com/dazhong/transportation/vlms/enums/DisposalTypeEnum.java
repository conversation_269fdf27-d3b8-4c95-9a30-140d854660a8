package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum DisposalTypeEnum {
    NORMAL_SCRAP(1, "正常报废"),
    ACCIDENT_SCRAP(2, "事故报废");

    private final Integer code;
    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (DisposalTypeEnum disposalType : DisposalTypeEnum.values()) {
            if (disposalType.code.equals(code)) {
                return disposalType.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}