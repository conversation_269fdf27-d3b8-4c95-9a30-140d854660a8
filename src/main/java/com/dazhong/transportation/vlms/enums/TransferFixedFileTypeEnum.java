package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum TransferFixedFileTypeEnum {
    PurchaseFileType_1(1, "加装明细"),
    PurchaseFileType_3(2, "上牌费明细"),
    PurchaseFileType_4(3, "其他");
    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if(code == null){
            return StringUtils.EMPTY;
        }
        for (TransferFixedFileTypeEnum optEnum : TransferFixedFileTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }

}
