package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum FileBusinessTypeEnum {
    PURCHASE_APPLICATION(1, "采购申请"),
    FIXED_ASSET_APPLICATION(2, "转固申请"),
    FIXED_ASSET_APPLICATION_2(3, "转固申请（无效）"),
    VEHICLE_MASTER_DATA_APPLICATION(5, "车辆主数据申请单"),
    DISPOSAL_APPLICATION(6, "车辆处置申请单"),
    REVERSE_DISPOSAL_APPLICATION(7, "车辆逆处置申请单");

    private final Integer code;
    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (FileBusinessTypeEnum optEnum : FileBusinessTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}
