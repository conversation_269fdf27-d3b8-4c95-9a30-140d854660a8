package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum ProductLineEnum {
    PATROL_BUSINESS(1, "出租车业务线",1),
    COMMERCIAL_BUSINESS(2, "商务长包业务线",2),
    ProductLine_3(3, "SVIP业务线",2),
    ProductLine_4(4, "网约车业务线",1),
    ProductLine_5(5, "新快车业务线",1),
    ;
    private final Integer code;

    private final String desc;

    private final Integer line;

    public static String getDesc(Integer code) {
        if(code == null){
            return StringUtils.EMPTY;
        }
        for (ProductLineEnum optEnum : ProductLineEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getCode(String desc) {
        if(StringUtils.isBlank(desc)){
            return null;
        }
        for (ProductLineEnum optEnum : ProductLineEnum.values()) {
            if (optEnum.desc.equals(desc)) {
                return optEnum.code;
            }
        }
        return null;
    }

    public static Integer getLine(Integer code) {
        if(code == null){
            return -1;
        }
        for (ProductLineEnum optEnum : ProductLineEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.line;
            }
        }
        return -1;
    }

}
