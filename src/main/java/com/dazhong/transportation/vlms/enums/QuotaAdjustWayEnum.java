package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum QuotaAdjustWayEnum {
    PRE_OCCUPY(1, "预占用"),
    OCCUPY(2, "占用");

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (QuotaAdjustWayEnum adjustWayEnum : QuotaAdjustWayEnum.values()) {
            if (adjustWayEnum.code.equals(code)) {
                return adjustWayEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}