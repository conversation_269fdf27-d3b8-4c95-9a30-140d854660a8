package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum ReturnQuotaTypeEnum {
    RETURN(1, "退还"),
    NOT_RETURN(2, "不退还"),
    NOT_INVOLVED(3, "不涉及");

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if(code == null){
            return StringUtils.EMPTY;
        }
        for (ReturnQuotaTypeEnum optEnum : ReturnQuotaTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }

    // 根据value获取枚举类型
    public static ReturnQuotaTypeEnum fromValue(int value) {
        for (ReturnQuotaTypeEnum type : ReturnQuotaTypeEnum.values()) {
            if (type.getCode() == value) {
                return type;
            }
        }
        return null;
    }

    // 根据name获取枚举类型
    public static ReturnQuotaTypeEnum fromName(String name) {
        for (ReturnQuotaTypeEnum type : ReturnQuotaTypeEnum.values()) {
            if (type.getDesc().equals(name)) {
                return type;
            }
        }
        return null;
    }
    
    // 根据中文描述获取数字代码
    public static Integer getCodeByDesc(String desc) {
        if(StringUtils.isBlank(desc)){
            return null;
        }
        for (ReturnQuotaTypeEnum type : ReturnQuotaTypeEnum.values()) {
            if (type.getDesc().equals(desc)) {
                return type.getCode();
            }
        }
        return null;
    }
}