package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ImportExcelTypeEnum {
    vehicleDecoration(1, "批量导入装潢信息"),
    vehicleOtherInfo(2, "批量导入其他信息"),
    vehicleDepreciationInfo(9, "批量导入折旧信息"),
    vehicleInspectionExpiryDate(10, "批量登记年检到期日"),
    vehicleMasterDate(11, "批量导入车辆主数据"),
    VehicleDataInfo(12, "上传管理员批量修改数据")
    ;
    private final Integer code;

    private final String desc;

    public static ImportExcelTypeEnum getImportExcel(Integer code) {
        for (ImportExcelTypeEnum optEnum : ImportExcelTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum;
            }
        }
        return null;
    }

}
