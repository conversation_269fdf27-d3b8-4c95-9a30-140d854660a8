package com.dazhong.transportation.vlms.enums;

import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetail;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetailBusinessSell;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetailDisposal;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 处置文档类型枚举
 * 定义不同文档类型对应的Excel读取类，提供类型安全的映射关系
 * 
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum DisposalDocumentTypeEnum {
    
    /**
     * 出售申请（巡网业务）
     */
    DISPOSAL_APPLICATION(1, "出售申请（巡网业务）", ImportDisposalVehicleDetailDisposal.class),
    
    /**
     * 报废申请
     */
    SCRAP_APPLICATION(2, "报废申请", ImportDisposalVehicleDetailDisposal.class),
    
    /**
     * 出售申请单（商务业务）
     */
    BUSINESS_SELL_APPLICATION(3, "出售申请单（商务业务）", ImportDisposalVehicleDetailBusinessSell.class);
    
    /**
     * 文档类型代码
     */
    private final Integer code;
    
    /**
     * 文档类型描述
     */
    private final String description;
    
    /**
     * 对应的Excel读取类
     */
    private final Class<? extends ImportDisposalVehicleDetail> excelClass;
    
    /**
     * 根据文档类型代码获取对应的枚举
     * 
     * @param code 文档类型代码
     * @return 对应的枚举，如果未找到则返回null
     */
    public static DisposalDocumentTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (DisposalDocumentTypeEnum typeEnum : DisposalDocumentTypeEnum.values()) {
            if (typeEnum.code.equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    /**
     * 根据文档类型代码获取对应的Excel读取类
     * 
     * @param code 文档类型代码
     * @return 对应的Excel读取类，如果未找到则返回null
     */
    public static Class<? extends ImportDisposalVehicleDetail> getExcelClassByCode(Integer code) {
        DisposalDocumentTypeEnum typeEnum = getByCode(code);
        return typeEnum != null ? typeEnum.getExcelClass() : null;
    }
    
    /**
     * 验证文档类型代码是否有效
     * 
     * @param code 文档类型代码
     * @return 如果有效返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
