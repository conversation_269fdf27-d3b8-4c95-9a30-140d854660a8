package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum QuotaTypeEnum {
    CIVILIAN_LICENSE_PLATE(1, "社会牌照"),
    ONBOARD_OPERATING(2, "纳管营运牌"),
    CAR_HAILING_OPERATING(3, "网约营运牌"),
    NEW_ENERGY_CAR_HAILING_OPERATING(4, "新能源网约牌"),
    OPERATING_LICENSE(5, "营运牌照"),
    HIGHWAY_INTERCITY_CHARTER(6, "公路客运-省际包车"),
    HIGHWAY_INTERCITY_PASSENGER(7, "公路客运-省际客运"),
    HIGHWAY_CITY_CHARTER(8, "公路客运-市内包车"),
    TAXI_PASSENGER(9, "出租客运"),
    ONBOARD_CAR_HAILING_OPERATING(10, "纳管网约营运牌"),
    CONTROLLED_ONBOARD_OPERATING(11, "专控纳管营运牌"),
    CONTROLLED_CIVILIAN(12, "专控社牌"),
    UNKNOWN_VIRTUAL(13, "不详（虚拟额度）"),
    RESERVATION_TAXI_OPERATING(14, "预约出租客运");

    private final Integer code;

    private final String desc;


    public static String getDesc(Integer code) {
        if(code == null){
            return StringUtils.EMPTY;
        }
        for (QuotaTypeEnum optEnum : QuotaTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }

    // 根据value获取枚举类型
    public static QuotaTypeEnum fromValue(int value) {
        for (QuotaTypeEnum type : QuotaTypeEnum.values()) {
            if (type.getCode() == value) {
                return type;
            }
        }
        return null;
    }

    // 根据name获取枚举类型
    public static QuotaTypeEnum fromName(String name) {
        for (QuotaTypeEnum type : QuotaTypeEnum.values()) {
            if (type.getDesc().equals(name)) {
                return type;
            }
        }
        return null;
    }
    
    // 根据中文描述获取数字代码
    public static Integer getCodeByDesc(String desc) {
        if(StringUtils.isBlank(desc)){
            return null;
        }
        for (QuotaTypeEnum type : QuotaTypeEnum.values()) {
            if (type.getDesc().equals(desc)) {
                return type.getCode();
            }
        }
        return null;
    }
}
