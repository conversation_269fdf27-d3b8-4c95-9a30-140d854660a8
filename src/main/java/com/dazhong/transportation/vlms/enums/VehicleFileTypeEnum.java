package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum VehicleFileTypeEnum {
    FIXED_ASSET_APPROVAL(1, "固定资产审批"),
    VEHICLE_DECORATION(2, "车辆装潢"),
    VEHICLE_ACCESSORIES_DETAIL(3, "车辆附属设备明细"),
    OTHER(4, "其他"),
    VEHICLE_DISPOSAL(5, "车辆处置"),
    VEHICLE_MASTER_DATA_APPLICATION(6, "车辆主数据申请单");

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (VehicleFileTypeEnum fileTypeEnum : VehicleFileTypeEnum.values()) {
            if (fileTypeEnum.code.equals(code)) {
                return fileTypeEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}