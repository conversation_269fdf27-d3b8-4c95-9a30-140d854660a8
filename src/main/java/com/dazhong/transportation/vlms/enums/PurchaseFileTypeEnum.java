package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum PurchaseFileTypeEnum {
    PurchaseFileType_1(1, "固定资产审批表"),
    PurchaseFileType_2(2, "车辆装潢表"),
    PurchaseFileType_3(3, "车辆附属设备明细表"),
    PurchaseFileType_4(4, "其他");
    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if(code == null){
            return StringUtils.EMPTY;
        }
        for (PurchaseFileTypeEnum optEnum : PurchaseFileTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }

}
