package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum DocumentStatusEnum {
    NOT_SUBMITTED(1, "未提交"),
    UNDER_REVIEW(2, "审批中"),
    REVIEW_REJECTED(3, "审批拒绝"),
    REVIEW_APPROVED(4, "审批通过"),
    CANCELLED(5, "已作废");

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (DocumentStatusEnum statusEnum : DocumentStatusEnum.values()) {
            if (statusEnum.code.equals(code)) {
                return statusEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}