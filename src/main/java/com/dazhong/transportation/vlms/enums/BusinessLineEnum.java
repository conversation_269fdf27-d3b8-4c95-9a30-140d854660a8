package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum BusinessLineEnum {
    PATROL(1, "巡网"),
    LONG_TERM_LEASE(2, "长包"),
    TEMPORARY_RENTAL(3, "零租"),
    BUS(4, "大巴"),
    OFFICIAL_BUSINESS(5, "公务用车"),
    LONG_TERM_TEMPORARY_RENTAL(6, "长包+临租");

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if(code == null){
            return StringUtils.EMPTY;
        }
        for (BusinessLineEnum optEnum : BusinessLineEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getCode(String desc) {
        if(StringUtils.isBlank(desc)){
            return null;
        }
        for (BusinessLineEnum optEnum : BusinessLineEnum.values()) {
            if (optEnum.desc.equals(desc)) {
                return optEnum.code;
            }
        }
        return null;
    }
}
