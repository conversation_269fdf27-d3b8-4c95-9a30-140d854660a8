package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum VehicleAttachmentTypeEnum {

    vehicleLicense(3, "行驶证文件","vehicleLicenseUrl"),
    certificateOwnership(4, "产证文件","certificateOwnershipUrl"),
    certificateConformity(5, "合格证文件","certificateConformityUrl"),
    vehicleInvoice(6, "发票文件","vehicleInvoiceUrl"),
    purchaseTax(7, "购置税文件","purchaseTaxUrl"),
    operatingPermit(8, "营运证文件","operatingPermitUrl"),
    secondhandCarInvoice(9, "二手车交易发票文件","secondhandCarInvoiceUrl");

    private final Integer code;

    private final String desc;

    private final String filePath;


    public static VehicleAttachmentTypeEnum getVehicleAttachmentType(Integer code) {
        for (VehicleAttachmentTypeEnum optEnum : VehicleAttachmentTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum;
            }
        }
        return null;
    }

}
