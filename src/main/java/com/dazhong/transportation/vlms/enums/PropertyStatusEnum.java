package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum PropertyStatusEnum {
    UNDER_CONSTRUCTION(0, "在建工程"),
    FIXED_ASSET(1, "固定资产"),
    DISPOSAL_APPROVAL(2, "处置审批中"),
    PENDING_DISPOSAL(3, "待处置（未交付）"),
    PENDING_SCRAP(4, "待报废（未交付）"),
    DISPOSED(5, "已处置"),
    SCRAPPED(6, "已报废");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取描述
     *
     * @param code 枚举代码
     * @return 枚举描述
     */
    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (PropertyStatusEnum statusEnum : PropertyStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据code获取描述
     *
     * @return 枚举描述
     */
    public static Integer getCode(String desc) {
        if (desc == null) {
            return null;
        }
        for (PropertyStatusEnum statusEnum : PropertyStatusEnum.values()) {
            if (statusEnum.getDesc().equals(desc)) {
                return statusEnum.getCode();
            }
        }
        return null;
    }
}