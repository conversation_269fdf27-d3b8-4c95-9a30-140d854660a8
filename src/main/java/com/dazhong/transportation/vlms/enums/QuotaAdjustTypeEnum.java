package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum QuotaAdjustTypeEnum {
    INCREASE(1, "总数增加"),
    DECREASE(2, "总数减少");

    private final Integer code;

    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (QuotaAdjustTypeEnum adjustEnum : QuotaAdjustTypeEnum.values()) {
            if (adjustEnum.code.equals(code)) {
                return adjustEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}