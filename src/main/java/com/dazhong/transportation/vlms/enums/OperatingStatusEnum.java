package com.dazhong.transportation.vlms.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum OperatingStatusEnum {
    PENDING(1, "待运"),
    RENTED(2, "租出"),
    OperatingStatus_3(3,"公务"),
    OperatingStatus_4(4,"个人经租"),
    OperatingStatus_5(5,"长包自驾"),
    OperatingStatus_6(6,"长包带驾"),
    OperatingStatus_7(7,"零租带驾"),
    OperatingStatus_8(8,"待租"),
    OperatingStatus_9(9,"新车待发"),
    OperatingStatus_10(10,"待售"),
    OperatingStatus_11(11,"网约车队（租用）"),
    OperatingStatus_12(12,"出售成交"),
    OperatingStatus_13(13,"租证车"),
    OperatingStatus_14(14,"以租代购"),
    OperatingStatus_15(15,"富利卡车队（租用）"),
    ;

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取描述
     *
     * @param code 枚举代码
     * @return 枚举描述
     */
    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (OperatingStatusEnum statusEnum : OperatingStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}