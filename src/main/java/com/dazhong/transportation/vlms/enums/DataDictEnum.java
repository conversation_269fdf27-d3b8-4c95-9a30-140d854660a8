package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum DataDictEnum {
    GAS_TYPE("gasType", "能源类型", "GasType"),
    MANUFACTURER("manufacturer", "制造商", "Manufacturer"),
    VEHICLE_BRAND("vehicleBrand", "车辆品牌", "VehicleBrand"),
    VEHICLE_TYPE("vehicleType", "车辆类型", "VehicleType"),
    WHEEL_DRIVE("wheelDrive", "驱动类型", "WheelDrive"),
    BREAK_MODE("breakMode", "制动形式", "BreakMode"),
    TURN_MODE("turnMode", "转向方式", "TurnMode"),
    DRIVE_POSITION("drivePosition", "驾驶位置", "DrivePosition"),
    ENGINE_POSITION("enginePosition", "发动机位置", "EnginePosition"),
    GEAR_BOX_TYPE("gearBoxType", "变速箱形式", "GearBoxType"),
    VEHICLE_ABBREVIATION("vehicleAbbreviation", "商品车型", "VehicleAbbreviation"),
    EXHAUST("exhaust", "环保标准", "Exhaust"),
    VEHICLE_COLOR("vehicleColor", "车辆颜色", "VehicleColor"),
    OPERATION_CATEGORY("operationCategory", "运营类别", "OperationCategory"),
    CONTRACT_TYPE("contractType", "合同形式", "ContractType"),
    VEHICLE_CATEGORY("vehicleCategory", "号牌种类", "VehicleCategory"),
    OPERATE_TYPE("operateType", "运营性质", "OperateType"),
    USAGE("usage","使用性质", "Usage"),
    OBTAIN_WAY("obtainWay","获取方式", "ObtainWay"),

    //特殊字典
    OWNER("owner", "车辆拥有公司", "Owner"),
    SUPPLIER("supplier", "供应商", "Supplier"),
    ORGANIZATION("organization", "组织机构", "Organization"),
    CHECK_ORGANIZATION("checkOrganization", "选中的组织机构", "Organization"),
    AREA("area", "区域", "Area"),
    SUPPLIER_TYPE("supplierType", "供应商大类", "supplierType");

    


    private final String value;
    private final String name;
    private final String tableName;

    //通过表名获取对应枚举类
    public static DataDictEnum getByTableName(String tableName) {
        if(StringUtils.isBlank(tableName)){
            return null;
        }
        for (DataDictEnum dictEnum : DataDictEnum.values()) {
            if (dictEnum.getTableName().equals(tableName)) {
                return dictEnum;
            }
        }
        return null;
    }

}
