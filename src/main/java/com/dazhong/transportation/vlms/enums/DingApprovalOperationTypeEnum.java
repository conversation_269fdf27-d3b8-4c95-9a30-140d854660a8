package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DingApprovalOperationTypeEnum {

    EXECUTE_TASK_NORMAL("EXECUTE_TASK_NORMAL","正常执行任务"),

    EXECUTE_TASK_AGENT("EXECUTE_TASK_AGENT","代理人执行任务"),

    APPEND_TASK_BEFORE("APPEND_TASK_BEFORE","前加签任务"),

    APPEND_TASK_AFTER("APPEND_TASK_AFTER","后加签任务"),

    REDIRECT_TASK("REDIRECT_TASK","转交任务"),

    START_PROCESS_INSTANCE("START_PROCESS_INSTANCE","发起流程实例"),

    TERMINATE_PROCESS_INSTANCE("TERMINATE_PROCESS_INSTANCE","终止(撤销)"),

    FINISH_PROCESS_INSTANCE("FINISH_PROCESS_INSTANCE","结束流程实例"),

    ADD_REMARK("ADD_REMARK","添加评论"),

    REDIRECT_PROCESS("REDIRECT_PROCESS","审批退回"),

    PROCESS_CC("PROCESS_CC","抄送");

    private final String code;

    private final String desc;

}
