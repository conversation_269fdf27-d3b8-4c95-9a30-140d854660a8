package com.dazhong.transportation.vlms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum OperateLogBusinessTypeEnum {
    SYSTEM_MANAGEMENT(1, "系统管理"),
    ORGANIZATION_ARCHITECTURE(2, "组织架构"),
    VEHICLE_PURCHASE(3, "车辆采购"),
    QUOTA_TRANSACTION(4, "额度流水单"),
    VEHICLE_MASTER_DATA(5, "车辆主数据"),
    DISPOSAL_APPLICATION(6, "车辆处置申请单"),
    REVERSE_DISPOSAL_APPLICATION(7, "车辆逆处置申请单");

    private final Integer code;
    private final String desc;

    public static String getDesc(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (OperateLogBusinessTypeEnum optEnum : OperateLogBusinessTypeEnum.values()) {
            if (optEnum.code.equals(code)) {
                return optEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}