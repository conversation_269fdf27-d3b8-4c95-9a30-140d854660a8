package com.dazhong.transportation.vlms.dto.response;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试GetVehicleBaseInfoResponse的JSON反序列化
 */
public class GetVehicleBaseInfoResponseTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @DisplayName("测试包含'-'字符串的JSON反序列化")
    public void testDeserializeWithDashValues() throws Exception {
        // 模拟包含"-"字符串的JSON响应
        String jsonWithDashValues = "{\n" +
                "    \"code\": 0,\n" +
                "    \"message\": \"success\",\n" +
                "    \"data\": {\n" +
                "        \"info\": {\n" +
                "            \"id\": 1,\n" +
                "            \"fullModelName\": \"测试车型\",\n" +
                "            \"seatCount\": \"-\",\n" +
                "            \"engineType\": \"V6\",\n" +
                "            \"ttmMonth\": \"2023-01\",\n" +
                "            \"bodyLength\": \"-\",\n" +
                "            \"bodyWidth\": \"1850\",\n" +
                "            \"bodyHeight\": \"-\",\n" +
                "            \"wheelBase\": \"2850\",\n" +
                "            \"curbWeight\": \"-\",\n" +
                "            \"tankage\": \"-\",\n" +
                "            \"fuelLabelName\": \"95号汽油\",\n" +
                "            \"engineDisplacementMl\": \"-\",\n" +
                "            \"batteryPower\": \"50\",\n" +
                "            \"actualTotal\": \"-\",\n" +
                "            \"cltcTotal\": \"500\",\n" +
                "            \"wltcCost\": 8.5,\n" +
                "            \"wheelFront\": \"-\",\n" +
                "            \"wheelBack\": \"-\",\n" +
                "            \"doorCount\": \"-\",\n" +
                "            \"vehicleSeriesName\": \"测试车系\",\n" +
                "            \"serialTypeName\": \"测试品牌\",\n" +
                "            \"hundredKilometerAcceleration\": \"8.5\",\n" +
                "            \"topSpeed\": \"200\",\n" +
                "            \"torque\": \"350\",\n" +
                "            \"fuelTypeName\": \"汽油\",\n" +
                "            \"wheelSize\": \"225/50R17\",\n" +
                "            \"mrsp\": \"300000\"\n" +
                "        }\n" +
                "    }\n" +
                "}";

        // 执行反序列化
        GetVehicleBaseInfoResponse response = objectMapper.readValue(jsonWithDashValues, GetVehicleBaseInfoResponse.class);

        // 验证反序列化结果
        assertNotNull(response);
        assertEquals(0, response.getCode());
        assertEquals("success", response.getMessage());
        
        assertNotNull(response.getData());
        assertNotNull(response.getData().getInfo());
        
        GetVehicleBaseInfoResponse.VehicleBaseInfo info = response.getData().getInfo();
        
        // 验证包含"-"的字段被正确转换为null
        assertNull(info.getSeatCount(), "seatCount应该为null");
        assertNull(info.getBodyLength(), "bodyLength应该为null");
        assertNull(info.getBodyHeight(), "bodyHeight应该为null");
        assertNull(info.getCurbWeight(), "curbWeight应该为null");
        assertNull(info.getTankage(), "tankage应该为null");
        assertNull(info.getEngineDisplacementMl(), "engineDisplacementMl应该为null");
        assertNull(info.getActualTotal(), "actualTotal应该为null");
        assertNull(info.getWheelFront(), "wheelFront应该为null");
        assertNull(info.getWheelBack(), "wheelBack应该为null");
        assertNull(info.getDoorCount(), "doorCount应该为null");
        
        // 验证正常数值字段正确解析
        assertEquals(Integer.valueOf(1850), info.getBodyWidth());
        assertEquals(Integer.valueOf(2850), info.getWheelBase());
        assertEquals(Integer.valueOf(500), info.getCltcTotal());
        
        // 验证字符串字段正常
        assertEquals("测试车型", info.getFullModelName());
        assertEquals("V6", info.getEngineType());
        assertEquals("95号汽油", info.getFuelLabelName());
    }

    @Test
    @DisplayName("测试正常数值的JSON反序列化")
    public void testDeserializeWithNormalValues() throws Exception {
        String jsonWithNormalValues = "{\n" +
                "    \"code\": 0,\n" +
                "    \"message\": \"success\",\n" +
                "    \"data\": {\n" +
                "        \"info\": {\n" +
                "            \"id\": 1,\n" +
                "            \"fullModelName\": \"正常车型\",\n" +
                "            \"seatCount\": \"5\",\n" +
                "            \"bodyLength\": \"4800\",\n" +
                "            \"bodyWidth\": \"1850\",\n" +
                "            \"bodyHeight\": \"1450\",\n" +
                "            \"wheelBase\": \"2850\",\n" +
                "            \"curbWeight\": \"1500\",\n" +
                "            \"tankage\": \"60\",\n" +
                "            \"engineDisplacementMl\": \"2000\",\n" +
                "            \"actualTotal\": \"600\",\n" +
                "            \"cltcTotal\": \"500\",\n" +
                "            \"wheelFront\": \"1550\",\n" +
                "            \"wheelBack\": \"1560\",\n" +
                "            \"doorCount\": \"4\"\n" +
                "        }\n" +
                "    }\n" +
                "}";

        GetVehicleBaseInfoResponse response = objectMapper.readValue(jsonWithNormalValues, GetVehicleBaseInfoResponse.class);
        
        assertNotNull(response);
        GetVehicleBaseInfoResponse.VehicleBaseInfo info = response.getData().getInfo();
        
        // 验证所有数值字段正确解析
        assertEquals(Integer.valueOf(5), info.getSeatCount());
        assertEquals(Integer.valueOf(4800), info.getBodyLength());
        assertEquals(Integer.valueOf(1850), info.getBodyWidth());
        assertEquals(Integer.valueOf(1450), info.getBodyHeight());
        assertEquals(Integer.valueOf(2850), info.getWheelBase());
        assertEquals(Integer.valueOf(1500), info.getCurbWeight());
        assertEquals(Integer.valueOf(60), info.getTankage());
        assertEquals(Integer.valueOf(2000), info.getEngineDisplacementMl());
        assertEquals(Integer.valueOf(600), info.getActualTotal());
        assertEquals(Integer.valueOf(500), info.getCltcTotal());
        assertEquals(Integer.valueOf(1550), info.getWheelFront());
        assertEquals(Integer.valueOf(1560), info.getWheelBack());
        assertEquals(Integer.valueOf(4), info.getDoorCount());
    }

    @Test
    @DisplayName("测试空字符串和null值的JSON反序列化")
    public void testDeserializeWithEmptyAndNullValues() throws Exception {
        String jsonWithEmptyValues = "{\n" +
                "    \"code\": 0,\n" +
                "    \"message\": \"success\",\n" +
                "    \"data\": {\n" +
                "        \"info\": {\n" +
                "            \"id\": 1,\n" +
                "            \"fullModelName\": \"测试车型\",\n" +
                "            \"seatCount\": \"\",\n" +
                "            \"bodyLength\": null,\n" +
                "            \"bodyWidth\": \"   \",\n" +
                "            \"curbWeight\": \"  -  \"\n" +
                "        }\n" +
                "    }\n" +
                "}";

        GetVehicleBaseInfoResponse response = objectMapper.readValue(jsonWithEmptyValues, GetVehicleBaseInfoResponse.class);
        
        assertNotNull(response);
        GetVehicleBaseInfoResponse.VehicleBaseInfo info = response.getData().getInfo();
        
        // 验证空字符串、null和包含空格的"-"都被正确处理为null
        assertNull(info.getSeatCount());
        assertNull(info.getBodyLength());
        assertNull(info.getBodyWidth());
        assertNull(info.getCurbWeight());
    }
}
