package com.dazhong.transportation.vlms;

import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;

import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleSyncDataRequest;
import com.dazhong.transportation.vlms.utils.SignUtil;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("Api Sign Test")
public class ApiSignTest {

    @Test
    public void test(){

        String secretKey = "CKKW4MIGyhlLtTnHNcKWk1jtq66n3qGY";

        SearchAssetVehicleSyncDataRequest request = new SearchAssetVehicleSyncDataRequest();
        request.setCarNo("沪BH7560");

        Long timestamp = System.currentTimeMillis();
        try {
            String sign = SignUtil.sign(request, secretKey, timestamp);
            System.out.println(sign);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
