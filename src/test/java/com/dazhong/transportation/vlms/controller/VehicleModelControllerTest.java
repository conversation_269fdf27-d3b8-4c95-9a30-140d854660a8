package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.dto.request.SearchVehicleModelListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleModelInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.IVehicleModelService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 车型控制器测试类
 * 主要测试车型编号下拉列表接口功能
 * 
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
@SpringBootTest
@ActiveProfiles("test")
public class VehicleModelControllerTest {

    @Resource
    private VehicleModelController vehicleModelController;

    @Resource
    private IVehicleModelService vehicleModelService;

    /**
     * 测试获取车型编号下拉列表接口
     * 验证接口能够正常返回数据
     */
    @Test
    public void testComboVehicleModelNo() {
        // 执行测试
        ResultResponse<ComboResponse<Long, String>> response = vehicleModelController.comboVehicleModelNo();

        // 验证结果
        assertNotNull(response, "响应结果不能为空");
        assertEquals(Integer.valueOf(0), response.getCode(), "响应状态码应该为0（成功）");
        assertNotNull(response.getData(), "响应数据不能为空");
        assertNotNull(response.getData().getList(), "下拉列表数据不能为空");

        // 验证数据格式
        if (!response.getData().getList().isEmpty()) {
            ComboResponse.ComboData<Long, String> firstItem = response.getData().getList().get(0);
            assertNotNull(firstItem.getKey(), "下拉列表项的key不能为空");
            assertNotNull(firstItem.getValue(), "下拉列表项的value不能为空");
            assertTrue(firstItem.getKey() instanceof Long, "key应该是Long类型");
            assertTrue(firstItem.getValue() instanceof String, "value应该是String类型");
            assertFalse(firstItem.getValue().trim().isEmpty(), "车型编号不能为空字符串");
        }
    }

    /**
     * 测试服务层的车型编号下拉列表方法
     * 验证服务层逻辑正确性
     */
    @Test
    public void testVehicleModelServiceComboVehicleModelNo() {
        // 执行测试
        ComboResponse<Long, String> result = vehicleModelService.comboVehicleModelNo();

        // 验证结果
        assertNotNull(result, "服务层返回结果不能为空");
        assertNotNull(result.getList(), "下拉列表数据不能为空");

        // 验证数据质量：所有返回的车型编号都不能为空
        for (ComboResponse.ComboData<Long, String> item : result.getList()) {
            assertNotNull(item.getKey(), "车型ID不能为空");
            assertNotNull(item.getValue(), "车型编号不能为空");
            assertFalse(item.getValue().trim().isEmpty(), "车型编号不能为空字符串");
            assertTrue(item.getKey() > 0, "车型ID应该大于0");
        }
    }

    /**
     * 测试车型编号下拉列表与车型名称下拉列表的区别
     * 验证两个接口返回的数据字段不同
     */
    @Test
    public void testComboVehicleModelNoVsComboVehicleModel() {
        // 获取车型编号下拉列表
        ComboResponse<Long, String> modelNoResult = vehicleModelService.comboVehicleModelNo();
        
        // 获取车型名称下拉列表
        ComboResponse<Long, String> modelNameResult = vehicleModelService.comboVehicleModel();

        // 验证两个列表都不为空
        assertNotNull(modelNoResult, "车型编号下拉列表不能为空");
        assertNotNull(modelNameResult, "车型名称下拉列表不能为空");

        // 如果两个列表都有数据，验证它们的value字段不同
        if (!modelNoResult.getList().isEmpty() && !modelNameResult.getList().isEmpty()) {
            // 找到相同ID的记录进行比较
            for (ComboResponse.ComboData<Long, String> modelNoItem : modelNoResult.getList()) {
                for (ComboResponse.ComboData<Long, String> modelNameItem : modelNameResult.getList()) {
                    if (modelNoItem.getKey().equals(modelNameItem.getKey())) {
                        // 相同ID的记录，value字段应该不同（一个是车型编号，一个是车型名称）
                        // 注意：这里不强制要求不同，因为可能存在车型编号和车型名称相同的情况
                        // 但至少要确保两个字段都不为空
                        assertNotNull(modelNoItem.getValue(), "车型编号不能为空");
                        assertNotNull(modelNameItem.getValue(), "车型名称不能为空");
                        assertFalse(modelNoItem.getValue().trim().isEmpty(), "车型编号不能为空字符串");
                        assertFalse(modelNameItem.getValue().trim().isEmpty(), "车型名称不能为空字符串");
                        break;
                    }
                }
            }
        }
    }

    /**
     * 测试接口的异常处理
     * 模拟异常情况验证错误处理机制
     */
    @Test
    public void testComboVehicleModelNoExceptionHandling() {
        // 这个测试主要验证接口的异常处理逻辑
        // 在正常情况下，接口应该能够正常处理并返回结果
        // 即使没有数据，也应该返回空列表而不是抛出异常

        assertDoesNotThrow(() -> {
            ResultResponse<ComboResponse<Long, String>> response = vehicleModelController.comboVehicleModelNo();
            assertNotNull(response, "即使在异常情况下，响应也不应该为null");
        }, "接口调用不应该抛出异常");
    }

    /**
     * 测试车型列表查询 - 根据车型编号查询
     * 验证新增的vehicleModelNo查询条件功能
     */
    @Test
    public void testSearchVehicleModelListByVehicleModelNo() {
        // 准备测试数据
        SearchVehicleModelListRequest request = new SearchVehicleModelListRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setVehicleModelNo("BMW"); // 设置车型编号查询条件

        // 执行测试
        ResultResponse<PageResponse<VehicleModelInfoResponse>> response = vehicleModelController.searchVehicleModelList(request);

        // 验证结果
        assertNotNull(response, "响应结果不能为空");
        assertEquals(Integer.valueOf(0), response.getCode(), "响应状态码应该为0（成功）");
        assertNotNull(response.getData(), "响应数据不能为空");
        assertNotNull(response.getData().getList(), "车型列表不能为空");

        // 验证查询结果中包含车型编号相关的数据
        if (!response.getData().getList().isEmpty()) {
            for (VehicleModelInfoResponse vehicleModel : response.getData().getList()) {
                // 验证返回的车型编号包含查询关键字（模糊查询）
                if (vehicleModel.getVehicleModelNo() != null) {
                    assertTrue(vehicleModel.getVehicleModelNo().toUpperCase().contains("BMW"),
                        "返回的车型编号应该包含查询关键字");
                }
            }
        }
    }

    /**
     * 测试车型列表查询 - 同时使用车型名称和车型编号查询
     * 验证多条件组合查询功能
     */
    @Test
    public void testSearchVehicleModelListWithMultipleConditions() {
        // 准备测试数据
        SearchVehicleModelListRequest request = new SearchVehicleModelListRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setVehicleModelName("宝马"); // 设置车型名称查询条件
        request.setVehicleModelNo("320"); // 设置车型编号查询条件

        // 执行测试
        ResultResponse<PageResponse<VehicleModelInfoResponse>> response = vehicleModelController.searchVehicleModelList(request);

        // 验证结果
        assertNotNull(response, "响应结果不能为空");
        assertEquals(Integer.valueOf(0), response.getCode(), "响应状态码应该为0（成功）");
        assertNotNull(response.getData(), "响应数据不能为空");

        // 验证查询结果同时满足两个条件
        if (!response.getData().getList().isEmpty()) {
            for (VehicleModelInfoResponse vehicleModel : response.getData().getList()) {
                // 验证车型名称包含查询关键字
                if (vehicleModel.getVehicleModelName() != null) {
                    assertTrue(vehicleModel.getVehicleModelName().contains("宝马"),
                        "返回的车型名称应该包含查询关键字");
                }
                // 验证车型编号包含查询关键字
                if (vehicleModel.getVehicleModelNo() != null) {
                    assertTrue(vehicleModel.getVehicleModelNo().contains("320"),
                        "返回的车型编号应该包含查询关键字");
                }
            }
        }
    }

    /**
     * 测试车型列表查询 - 空查询条件
     * 验证当vehicleModelNo为空时不影响其他查询条件
     */
    @Test
    public void testSearchVehicleModelListWithEmptyVehicleModelNo() {
        // 准备测试数据
        SearchVehicleModelListRequest request = new SearchVehicleModelListRequest();
        request.setPageNum(1);
        request.setPageSize(5);
        request.setVehicleModelName("丰田"); // 只设置车型名称
        request.setVehicleModelNo(null); // 车型编号为空

        // 执行测试
        ResultResponse<PageResponse<VehicleModelInfoResponse>> response = vehicleModelController.searchVehicleModelList(request);

        // 验证结果
        assertNotNull(response, "响应结果不能为空");
        assertEquals(Integer.valueOf(0), response.getCode(), "响应状态码应该为0（成功）");

        // 再次测试空字符串的情况
        request.setVehicleModelNo(""); // 车型编号为空字符串
        ResultResponse<PageResponse<VehicleModelInfoResponse>> response2 = vehicleModelController.searchVehicleModelList(request);

        assertNotNull(response2, "响应结果不能为空");
        assertEquals(Integer.valueOf(0), response2.getCode(), "响应状态码应该为0（成功）");
    }

    /**
     * 测试车型列表查询 - 边界情况处理
     * 验证特殊字符和边界值的处理
     */
    @Test
    public void testSearchVehicleModelListBoundaryConditions() {
        // 测试特殊字符
        SearchVehicleModelListRequest request = new SearchVehicleModelListRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setVehicleModelNo("%"); // SQL通配符

        // 执行测试，应该不会抛出异常
        assertDoesNotThrow(() -> {
            ResultResponse<PageResponse<VehicleModelInfoResponse>> response = vehicleModelController.searchVehicleModelList(request);
            assertNotNull(response, "响应结果不能为空");
        }, "特殊字符查询不应该抛出异常");

        // 测试很长的字符串
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longString.append("a");
        }
        request.setVehicleModelNo(longString.toString()); // 100个字符的字符串
        assertDoesNotThrow(() -> {
            ResultResponse<PageResponse<VehicleModelInfoResponse>> response = vehicleModelController.searchVehicleModelList(request);
            assertNotNull(response, "响应结果不能为空");
        }, "长字符串查询不应该抛出异常");
    }
}
