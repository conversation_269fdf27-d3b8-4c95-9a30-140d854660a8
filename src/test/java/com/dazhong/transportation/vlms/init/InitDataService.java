package com.dazhong.transportation.vlms.init;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.dazhong.transportation.vlms.enums.BusinessLineEnum;
import com.dazhong.transportation.vlms.enums.ProductLineEnum;
import com.dazhong.transportation.vlms.enums.QuotaTypeEnum;
import com.dazhong.transportation.vlms.mapper.*;
import com.dazhong.transportation.vlms.mapper.extend.*;
import com.dazhong.transportation.vlms.model.*;
import com.dazhong.transportation.vlms.service.IDataDictService;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.dazhong.transportation.vlms.mapper.DataMaintainDictInfoDynamicSqlSupport.dataMaintainDictInfo;
import static com.dazhong.transportation.vlms.mapper.DataOwnerInfoDynamicSqlSupport.dataOwnerInfo;
import static com.dazhong.transportation.vlms.mapper.DataSupplierInfoDynamicSqlSupport.dataSupplierInfo;
import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaDynamicSqlSupport.licensePlateQuota;
import static com.dazhong.transportation.vlms.mapper.LicensePlateQuotaTransactionRecordDynamicSqlSupport.licensePlateQuotaTransactionRecord;
import static com.dazhong.transportation.vlms.mapper.OrgInfoDynamicSqlSupport.orgInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleInfoDynamicSqlSupport.vehicleInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleManagementLegacyInfoDynamicSqlSupport.vehicleManagementLegacyInfo;
import static com.dazhong.transportation.vlms.mapper.VehicleModelInfoDynamicSqlSupport.vehicleModelInfo;
import static com.dazhong.transportation.vlms.mapper.VinModelInfoDynamicSqlSupport.vinModelInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 车辆管理系统(VLMS)数据初始化服务
 *
 * 该服务类主要用于初始化系统所需的基础数据，包括:
 * - 车型数据：包含车辆型号、品牌、系列等基本信息
 * - 供应商数据：车辆供应商的基本信息
 * - 车辆拥有公司数据：拥有车辆的公司信息
 * - 车辆基本信息：包括车牌号、VIN码、发动机号等
 * - 车辆资产信息：包括购置价格、折旧信息等财务数据
 * - 各类数据字典：如获取方式、使用性质、燃料类型等
 *
 * 所有数据通过读取Excel文件进行初始化，使用EasyExcel进行解析。
 * 初始化过程中使用多线程处理以提高性能，特别是对于大量数据的导入。
 *
 * 注意：
 * 1. 初始化操作会清空原有数据，请谨慎使用
 * 2. 建议在测试环境中使用，生产环境请做好数据备份
 * 3. 初始化过程可能耗时较长，请耐心等待
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("init data")
public class InitDataService {

    /** 线程池执行器 */
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    /** 车辆信息扩展Mapper */
    @Autowired
    private VehicleInfoExtendMapper vehicleInfoMapper;

    /** 车辆管理遗留信息扩展Mapper */
    @Autowired
    private VehicleManagementLegacyInfoExtendMapper vehicleManagementLegacyInfoMapper;

    /** 车型信息扩展Mapper */
    @Autowired
    private VehicleModelInfoExtendMapper vehicleModelInfoMapper;

    /** 数据维护字典信息Mapper */
    @Autowired
    private DataMaintainDictInfoMapper dataMaintainDictInfoMapper;

    /** 供应商信息扩展Mapper */
    @Autowired
    private DataSupplierInfoExtendMapper dataSupplierInfoMapper;

    /** 车辆拥有公司信息扩展Mapper */
    @Autowired
    private DataOwnerInfoExtendMapper dataOwnerInfoMapper;

    @Autowired
    private VinModelInfoMapper vinModelInfoMapper;

    @Autowired
    private LicensePlateQuotaTransactionRecordMapper licensePlateQuotaTransactionRecordMapper;

    @Autowired
    private LicensePlateQuotaMapper licensePlateQuotaMapper;

    @Autowired
    private OrgInfoMapper orgInfoMapper;

    @Autowired
    private IDataDictService dataDictService;

    /**
     * 一键初始化所有系统数据
     *
     * 该方法会依次执行所有初始化方法，包括:
     * - 初始化字典数据
     * - 初始化车型数据
     * - 初始化自定义车型
     * - 初始化供应商数据
     * - 初始化车辆拥有公司数据
     * - 初始化车辆基本信息
     * - 初始化车辆资产信息
     *
     * 注意: 由于数据量较大，该方法执行时间可能较长
     */
    @Test
    @DisplayName("一键初始化所有数据")
    public void initAllData() {
        System.out.println("开始一键初始化所有系统数据...");

        // 初始化字典数据
        System.out.println("1. 开始初始化字典数据...");
        initDict();
        System.out.println("字典数据初始化完成");

        // 初始化车型数据
        System.out.println("2. 开始初始化车型数据...");
        initVehicleModelData();
        System.out.println("车型数据初始化完成");

        // 初始化自定义车型
         System.out.println("3. 开始初始化自定义车型...");
         initCustomVehicleModelData();
         System.out.println("自定义车型初始化完成");

        // 初始化供应商数据
        System.out.println("4. 开始初始化供应商数据...");
        initSupplierData();
        System.out.println("供应商数据初始化完成");

        // 初始化车辆拥有公司数据
        System.out.println("5. 开始初始化车辆拥有公司数据...");
        initOwnerData();
        System.out.println("车辆拥有公司数据初始化完成");

        // 初始化车辆基本信息
        System.out.println("6. 开始初始化车辆基本信息...");
        initVehicleInfoData();
        initVehicleAssetInfoData();
        System.out.println("车辆基本信息初始化完成");

        System.out.println("所有系统数据初始化完成！");
    }


    /**
     * 初始化自定义车型数据
     *
     * 从Excel文件中读取自定义车型数据并导入系统。
     * 处理流程:
     * 1. 读取Excel中的VIN码、车型ID、车型名称、品牌、系列信息
     * 2. 根据车型ID查找已有车型
     * 3. 基于已有车型创建新的自定义车型记录
     * 4. ID从10001开始自增
     */
    @Test
    @DisplayName("初始化自定义车型")
    public void initCustomVehicleModelData() {


        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(0, "vin");
        tableMap.put(1, "modelId");
        tableMap.put(2, "modelName");
        tableMap.put(3, "brand");
        tableMap.put(4, "series");

        // 设置起始ID
        AtomicLong id = new AtomicLong(10885L);

        // 读取初始化Excel
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250530/VehicleModelNew.xlsx");
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 转换Excel数据到Map key: 列名称, value: 列值
                Map<String, String> dataMap = new HashMap<>();
                data.forEach((key, value) -> {
                    String columnName = tableMap.get(key);
                    dataMap.put(columnName, value);
                });

                // 验证必填字段
                if(StringUtils.isBlank(dataMap.get("modelId")) || StringUtils.isBlank(dataMap.get("modelName"))) {
                    return;
                }

                // 查询原车型信息
                VehicleModelInfo vehicleModelInfo = vehicleModelInfoMapper.selectByPrimaryKey(parseLong(dataMap.get("modelId"))).orElse(null);
                if(vehicleModelInfo == null) {
                    System.out.println("车型编号不存在：" + dataMap.get("modelId"));
                    return;
                }

                // 创建新的自定义车型
                VehicleModelInfo createVehicleModelInfo = new VehicleModelInfo();
                BeanUtils.copyProperties(vehicleModelInfo, createVehicleModelInfo);
                createVehicleModelInfo.setId(id.get());
                createVehicleModelInfo.setVehicleModelName(parseString(dataMap.get("modelName")));
                createVehicleModelInfo.setVehicleBrandName(parseString(dataMap.get("brand")));
                createVehicleModelInfo.setVehicleSeriesName(parseString(dataMap.get("series")));

                // 保存到数据库
                vehicleModelInfoMapper.insertSelectiveWithId(createVehicleModelInfo);
                System.out.println("新车型ID:" + id.get() + " 对应原车型ID:" + dataMap.get("modelId"));


                id.incrementAndGet();
                System.out.println("初始化车型信息：" + createVehicleModelInfo.getId());

            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
            }
        }).sheet(0).headRowNumber(1).doRead();
    }


    @Test
    @DisplayName("初始化供应商")
    public void initSupplierData() {
        String sheetName = "Supplier";

        // 清空供应商表原有信息
        DeleteStatementProvider deleteStatement = deleteFrom(dataSupplierInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        dataSupplierInfoMapper.delete(deleteStatement);

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(0, "id");
        tableMap.put(3, "address");
        tableMap.put(4, "phone");
        tableMap.put(5, "name");

        // 读取初始化Excel
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/Supplier.xlsx");
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 转换Excel数据到Map key: 字段名称, value: 字段值
                Map<String, String> dataMap = new HashMap<>();
                data.forEach((key, value) -> {
                    String columnName = tableMap.get(key);
                    dataMap.put(columnName, value);
                });

                // 构建并保存供应商信息
                DataSupplierInfo saveSupplierInfo = new DataSupplierInfo();
                saveSupplierInfo.setId(parseLong(dataMap.get("id")));
                saveSupplierInfo.setAddress(parseString(dataMap.get("address")));
                saveSupplierInfo.setPhone(parseString(dataMap.get("phone")));
                saveSupplierInfo.setName(parseString(dataMap.get("name")));

                // 设置审计字段
                Date now = new Date();
                saveSupplierInfo.setCreateTime(now);
                saveSupplierInfo.setCreateOperId(-1L);
                saveSupplierInfo.setCreateOperName("system");
                saveSupplierInfo.setUpdateTime(now);
                saveSupplierInfo.setUpdateOperId(-1L);
                saveSupplierInfo.setUpdateOperName("system");

                dataSupplierInfoMapper.insertSelectiveWithId(saveSupplierInfo);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
            }
        }).sheet(sheetName).headRowNumber(1).doRead();
    }


    @Test
    @DisplayName("初始化车辆拥有公司表数据")
    public void initOwnerData() {
        String sheetName = "Owner";

        // 清空车辆拥有公司表原有信息
        DeleteStatementProvider deleteStatement = deleteFrom(dataOwnerInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        dataOwnerInfoMapper.delete(deleteStatement);

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(0, "id");
        tableMap.put(3, "address");
        tableMap.put(4, "phone");
        tableMap.put(5, "name");

        // 读取初始化Excel
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/Owner.xlsx");
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 转换Excel数据到Map key: 字段名称, value: 字段值
                Map<String, String> dataMap = new HashMap<>();
                data.forEach((key, value) -> {
                    String columnName = tableMap.get(key);
                    dataMap.put(columnName, value);
                });

                // 构建并保存车辆拥有公司信息
                DataOwnerInfo saveOwnerInfo = new DataOwnerInfo();
                saveOwnerInfo.setId(parseLong(dataMap.get("id")));
                saveOwnerInfo.setAddress(parseString(dataMap.get("address")));
                saveOwnerInfo.setPhone(parseString(dataMap.get("phone")));
                saveOwnerInfo.setName(parseString(dataMap.get("name")));

                // 设置审计字段
                Date now = new Date();
                saveOwnerInfo.setCreateTime(now);
                saveOwnerInfo.setCreateOperId(-1L);
                saveOwnerInfo.setCreateOperName("system");
                saveOwnerInfo.setUpdateTime(now);
                saveOwnerInfo.setUpdateOperId(-1L);
                saveOwnerInfo.setUpdateOperName("system");

                dataOwnerInfoMapper.insertSelectiveWithId(saveOwnerInfo);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
            }
        }).sheet(sheetName).headRowNumber(1).doRead();
    }



    @Test
    @DisplayName("初始化车型数据")
    public void initVehicleModelData() {
        String sheetName = "VehicleModel";
        Integer headLine = 1;

        // // 清空车型历史数据
        // DeleteStatementProvider deleteStatement = deleteFrom(vehicleModelInfo)
        //     .build()
        //     .render(RenderingStrategies.MYBATIS3);
        // vehicleModelInfoMapper.delete(deleteStatement);
        // System.out.println("已清空车型历史数据");

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();

        // 读取初始化Excel
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250530/VehicleModel.xlsx");
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 第一行为表头，记录字段映射关系
                if(context.readRowHolder().getRowIndex() == headLine) {
                    tableMap.putAll(data);
                    return;
                }

                // 转换Excel数据到Map key: 字段名称, value: 字段值
                Map<String, String> dataMap = new HashMap<>();
                data.forEach((key, value) -> {
                    String columnName = tableMap.get(key);
                    dataMap.put(columnName, value);
                });

                // 构建并保存车型信息
                VehicleModelInfo vehicleModelInfo = parseVehicleModelInfo(dataMap);
                Date now = new Date();
                vehicleModelInfo.setCreateTime(now);
                vehicleModelInfo.setCreateOperId(-1L);
                vehicleModelInfo.setCreateOperName("system");
                vehicleModelInfo.setUpdateTime(now);
                vehicleModelInfo.setUpdateOperId(-1L);
                vehicleModelInfo.setUpdateOperName("system");
                vehicleModelInfoMapper.insertSelectiveWithId(vehicleModelInfo);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
            }
        }).sheet(0).headRowNumber(headLine).doRead();
    }

    /**
     * 解析车型信息
     *
     * @param data 数据Map
     * @return 车型信息对象
     */
    private VehicleModelInfo parseVehicleModelInfo(Map<String, String> data){
        VehicleModelInfo saveVehicleModelInfo = new VehicleModelInfo();
        // ID
        saveVehicleModelInfo.setId(parseLong(data.get("id")));
        // 车型编号
        saveVehicleModelInfo.setVehicleModelNo(parseString(data.get("vehicle_model_no")));
        saveVehicleModelInfo.setVehicleModelName(parseString(data.get("vehicle_model_no")));

        // 发动机型号
        saveVehicleModelInfo.setEngineModelNo(parseString(data.get("engine_model_no")));
        // 排量
        saveVehicleModelInfo.setCapacity(parseDecimal(data.get("capacity")));
        // 功率
        saveVehicleModelInfo.setPower(parseInt(data.get("power")));
        // 前轮距
        saveVehicleModelInfo.setTreadFront(parseInt(data.get("tread_front")));
        // 后轮距
        saveVehicleModelInfo.setTreadRear(parseInt(data.get("tread_rear")));
        // 轮胎数
        saveVehicleModelInfo.setWheelQuantity(parseInt(data.get("wheel_quantity")));
        // 轮胎规格
        saveVehicleModelInfo.setWheelParam(parseString(data.get("wheel_param")));
        // 后轴钢板弹簧数
        saveVehicleModelInfo.setSpringLamination(parseInt(data.get("spring_lamination")));
        // 轴距1
        saveVehicleModelInfo.setWheelBase1(parseInt(data.get("wheel_base1")));
        // 轴距2
        saveVehicleModelInfo.setWheelBase2(parseInt(data.get("wheel_base2")));
        // 轴距3
        saveVehicleModelInfo.setWheelBase3(parseInt(data.get("wheel_base3")));
        // 轴数
        saveVehicleModelInfo.setAxleQuantity(parseInt(data.get("axle_quantity")));
        // 车身长度
        saveVehicleModelInfo.setOutLength(parseInt(data.get("out_length")));
        // 车身宽度
        saveVehicleModelInfo.setOutWidth(parseInt(data.get("out_width")));
        // 车身高度
        saveVehicleModelInfo.setOutHeight(parseInt(data.get("out_height")));
        // 货箱内部长度
        saveVehicleModelInfo.setContainerLength(parseInt(data.get("container_length")));
        // 货箱内部宽度
        saveVehicleModelInfo.setContainerWidth(parseInt(data.get("container_width")));
        // 货箱内部高度
        saveVehicleModelInfo.setContainerHeight(parseInt(data.get("container_height")));
        // 整备质量
        saveVehicleModelInfo.setTotalMass(parseInt(data.get("total_mass")));
        // 核定载质量
        saveVehicleModelInfo.setAssessMass(parseInt(data.get("assess_mass")));
        // 座位数
        saveVehicleModelInfo.setAssessPassenger(parseInt(data.get("assess_passenger")));
        // 准牵引总质量
        saveVehicleModelInfo.setTractionMass(parseInt(data.get("traction_mass")));
        // 驾驶室载客
        saveVehicleModelInfo.setCabPassenger(parseInt(data.get("cab_passenger")));
        // 国产/进口
        saveVehicleModelInfo.setManufactureLocation(parseInt(data.get("manufacture_location")));
        // 车门数
        saveVehicleModelInfo.setDoorQuantity(parseInt(data.get("door_quantity")));
        // 档位数
        saveVehicleModelInfo.setGearQuantity(parseInt(data.get("gear_quantity")));
        // 0-100加速
        saveVehicleModelInfo.setAcceleration(parseDecimal(data.get("acceleration")));
        // 最高车速
        saveVehicleModelInfo.setSpeed(parseInt(data.get("speed")));
        // 最小转弯半径
        saveVehicleModelInfo.setTurningRadius(parseDecimal(data.get("turning_radius")));
        // 最小离地间隙
        saveVehicleModelInfo.setRoadClearance(parseInt(data.get("road_clearance")));
        // 最大爬坡度
        saveVehicleModelInfo.setGradient(parseInt(data.get("gradient")));
        // 等速油耗
        saveVehicleModelInfo.setFuelEconomy(parseDecimal(data.get("fuel_economy")));
        // 百公里油耗
        saveVehicleModelInfo.setFuelEconomyMiit(parseDecimal(data.get("fuel_economy_miit")));
        // 扭矩
        saveVehicleModelInfo.setTorque(parseInt(data.get("torque")));
        // 压缩比
        saveVehicleModelInfo.setCompressionRatio(parseString(data.get("compression_ratio")));
        // 制造商
        saveVehicleModelInfo.setManufacturerId(parseInt(data.get("manufacturer_id")));
        // 能源类型
        saveVehicleModelInfo.setGasTypeId(parseInt(data.get("gas_type_id")));
        // 车辆品牌
        saveVehicleModelInfo.setVehicleBrandId(parseInt(data.get("vehicle_brand_id")));
        // 车辆类型
        saveVehicleModelInfo.setVehicleTypeId(parseInt(data.get("vehicle_type_id")));
        // 驱动类型
        saveVehicleModelInfo.setWheelDriveId(parseInt(data.get("wheel_drive_id")));
        // 制动形式
        saveVehicleModelInfo.setBreakModeId(parseInt(data.get("break_mode_id")));
        // 转向方式
        saveVehicleModelInfo.setTurnModeId(parseInt(data.get("turn_mode_id")));
        // 驾驶位置
        saveVehicleModelInfo.setDrivePositionId(parseInt(data.get("drive_position_id")));
        // 发动机位置
        saveVehicleModelInfo.setEnginePositionId(parseInt(data.get("engine_position_id")));
        // 商品车型
        saveVehicleModelInfo.setVehicleAbbreviationId(parseInt(data.get("vehicle_abbreviation_id")));
        // 环保标准
        saveVehicleModelInfo.setExhaustId(parseInt(data.get("exhaust_id")));
        // 变速箱形式
        saveVehicleModelInfo.setGearBoxTypeId(parseInt(data.get("gear_box_type_id")));
        return saveVehicleModelInfo;

    }

    /**
     * 初始化车辆基本信息数据
     *
     * 从Excel文件中读取车辆数据并导入系统。使用多线程处理以提高性能。
     *
     * 处理流程:
     * 1. 读取车辆基本信息(车牌号、VIN、发动机号等)
     * 2. 过滤无效数据(非正常状态、特定车牌前缀)
     * 3. 使用线程池异步处理每条数据，提高处理效率
     * 4. 同步保存车辆基本信息到t_vehicle_info表
     * 5. 同步保存车辆管理遗留信息到t_vehicle_management_legacy_info表
     * 6. 使用CountDownLatch等待所有异步任务完成
     * 7. 关闭线程池释放资源
     *
     * 注意事项:
     * - 该方法会清空车辆历史数据和车辆管理遗留信息历史数据
     * - 处理过程中会过滤掉非正常状态的车辆数据
     * - 会过滤掉特定前缀的车牌号(如"沪CW"、"沪CU")
     * - 使用线程池异步处理，提高大批量数据导入效率
     */
    @Test
    @DisplayName("初始化车辆数据")
    public void initVehicleInfoData() {
        // 初始化计数器，用于等待所有任务完成
        CountDownLatch latch = new CountDownLatch(1);
        String sheetName = "Vehicle";
        Integer headLine = 1;

        // 清空车辆历史数据
        DeleteStatementProvider deleteStatement = deleteFrom(vehicleInfo)
            .build()
            .render(RenderingStrategies.MYBATIS3);
        vehicleInfoMapper.delete(deleteStatement);
        System.out.println("已清空车辆历史数据");

        // 清空车辆管理遗留信息历史数据
        DeleteStatementProvider deleteStatement2 = deleteFrom(vehicleManagementLegacyInfo)
            .build()
            .render(RenderingStrategies.MYBATIS3);
        vehicleManagementLegacyInfoMapper.delete(deleteStatement2);
        System.out.println("已清空车辆管理遗留信息历史数据");

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();

        // 读取初始化Excel
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250515/Vehicle.xlsx");
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 第一行为表头，记录字段映射关系
                if(context.readRowHolder().getRowIndex() == headLine) {
                    tableMap.putAll(data);
                    return;
                }

                // 异步处理每行数据
                taskExecutor.execute(() -> {
                    // 转换Excel数据到Map key: 字段名称, value: 字段值
                    Map<String, String> dataMap = new HashMap<>();
                    data.forEach((key, value) -> {
                        String columnName = tableMap.get(key);
                        dataMap.put(columnName, value);
                    });
                    Date now = new Date();


                    // 构建并保存车辆基本信息
                    VehicleInfo vehicleInfo = buildVehicleInfo(dataMap, now);
                    vehicleInfoMapper.insertSelectiveWithId(vehicleInfo);

                    // 构建并保存车辆管理遗留信息
                    VehicleManagementLegacyInfo legacyInfo = buildLegacyInfo(dataMap, now);
                    vehicleManagementLegacyInfoMapper.insertSelectiveWithId(legacyInfo);
                });
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
                // 等待半分钟，确保异步数据处理完成
                try {
                    Thread.sleep(30000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                latch.countDown();
            }
        }).sheet(0).headRowNumber(headLine).doRead();

        try {
            // 等待所有任务完成
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 关闭线程池
        taskExecutor.shutdown();
    }


    /**
     * 初始化车辆基本信息数据
     *
     * 从Excel文件中读取车辆数据并导入系统。使用多线程处理以提高性能。
     *
     * 处理流程:
     * 1. 读取车辆基本信息(车牌号、VIN、发动机号等)
     * 2. 过滤无效数据(非正常状态、特定车牌前缀)
     * 3. 使用线程池异步处理每条数据，提高处理效率
     * 4. 同步保存车辆基本信息到t_vehicle_info表
     * 5. 同步保存车辆管理遗留信息到t_vehicle_management_legacy_info表
     * 6. 使用CountDownLatch等待所有异步任务完成
     * 7. 关闭线程池释放资源
     *
     * 注意事项:
     * - 该方法会清空车辆历史数据和车辆管理遗留信息历史数据
     * - 处理过程中会过滤掉非正常状态的车辆数据
     * - 会过滤掉特定前缀的车牌号(如"沪CW"、"沪CU")
     * - 使用线程池异步处理，提高大批量数据导入效率
     */
    @Test
    @DisplayName("新增车辆初始化数据")
    public void insertVehicleInfoData() {
        // 初始化计数器，用于等待所有任务完成
        CountDownLatch latch = new CountDownLatch(1);
        Integer headLine = 1;

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();

        // 读取初始化Excel
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250530/Vehicle.xlsx");
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 第一行为表头，记录字段映射关系
                if(context.readRowHolder().getRowIndex() == headLine) {
                    tableMap.putAll(data);
                    return;
                }

                // 异步处理每行数据
                taskExecutor.execute(() -> {
                    // 转换Excel数据到Map key: 字段名称, value: 字段值
                    Map<String, String> dataMap = new HashMap<>();
                    data.forEach((key, value) -> {
                        String columnName = tableMap.get(key);
                        dataMap.put(columnName, value);
                    });
                    Date now = new Date();


                    // 构建并保存车辆基本信息
                    VehicleInfo vehicleInfo = buildVehicleInfo(dataMap, now);
                    vehicleInfoMapper.insertSelectiveWithId(vehicleInfo);

                    // 构建并保存车辆管理遗留信息
                    VehicleManagementLegacyInfo legacyInfo = buildLegacyInfo(dataMap, now);
                    vehicleManagementLegacyInfoMapper.insertSelectiveWithId(legacyInfo);
                });
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
                // 等待半分钟，确保异步数据处理完成
                try {
                    Thread.sleep(30000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                latch.countDown();
            }
        }).sheet(0).headRowNumber(headLine).doRead();

        try {
            // 等待所有任务完成
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 关闭线程池
        taskExecutor.shutdown();
    }

    /**
     * 构建车辆基本信息对象
     *
     * @param dataMap 数据Map
     * @param now 当前时间
     * @return 车辆基本信息对象
     */
    private VehicleInfo buildVehicleInfo(Map<String, String> dataMap, Date now) {
        VehicleInfo vehicleInfo = new VehicleInfo();

        // 设置基本信息
        vehicleInfo.setId(parseLong(dataMap.get("id")));
        vehicleInfo.setLicensePlate(parseString(dataMap.get("license_plate")));
        vehicleInfo.setVin(parseString(dataMap.get("vin")));
        vehicleInfo.setEngineNo(parseString(dataMap.get("engine_no")));

        // 设置日期相关信息
        vehicleInfo.setProductDate(parseDate(dataMap.get("product_date")));
        vehicleInfo.setRealRetirementDate(parseDate(dataMap.get("real_retirement_date")));
        vehicleInfo.setRetirementDateRegistrationCard(parseDate(dataMap.get("retirement_date_registration_card")));

        // 设置使用相关信息
        vehicleInfo.setUsageAgeLimit(parseInt(dataMap.get("usage_age_limit")));
        vehicleInfo.setDepreciationAgeLimit(parseInt(dataMap.get("depreciation_age_limit")));
        vehicleInfo.setObtainWayId(parseInt(dataMap.get("obtain_way_id")));
        vehicleInfo.setUsageIdRegistrationCard(parseInt(dataMap.get("usage_id_registration_card")));

        // 设置其他属性
        vehicleInfo.setVehicleColorId(parseInt(dataMap.get("vehicle_color_id")));
        vehicleInfo.setAreaId(parseInt(dataMap.get("area_id")));
        vehicleInfo.setVehicleModelId(parseLong(dataMap.get("vehicle_model_id")));
        vehicleInfo.setSupplierId(parseInt(dataMap.get("supplier_id")));
        vehicleInfo.setCertificateNumber(parseString(dataMap.get("certificate_number")));
        vehicleInfo.setOwnOrganizationId(parseLong(dataMap.get("own_organization_id")));
        vehicleInfo.setUsageOrganizationId(parseLong(dataMap.get("usage_organization_id")));
        vehicleInfo.setAssetCompanyId(parseInt(dataMap.get("owner_id")));
        vehicleInfo.setVehicleAssetId(parseString(dataMap.get("vehicle_asset_id")));

        // 资产状态 0 在建工程 1-固定资产 2-处置审批中3待处置（未交付）  4-待报废（未交付） 5-已处置 6-已报废")
        String propertyStatus = dataMap.get("vhielce_status");
        if(StringUtils.equals("0", propertyStatus)) {
            vehicleInfo.setPropertyStatus(5);  // 已处置   
        } else {
            vehicleInfo.setPropertyStatus(1);  // 固定资产
        }

        // 设置审计字段
        vehicleInfo.setCreateTime(now);
        vehicleInfo.setCreateOperId(-1L);
        vehicleInfo.setCreateOperName("system");
        vehicleInfo.setUpdateTime(now);
        vehicleInfo.setUpdateOperId(-1L);
        vehicleInfo.setUpdateOperName("system");

        return vehicleInfo;
    }

    /**
     * 构建车辆管理遗留信息对象
     *
     * @param dataMap 数据Map
     * @param now 当前时间
     * @return 车辆管理遗留信息对象
     */
    private VehicleManagementLegacyInfo buildLegacyInfo(Map<String, String> dataMap, Date now) {
        VehicleManagementLegacyInfo legacyInfo = new VehicleManagementLegacyInfo();

        // 设置基本信息
        legacyInfo.setId(parseLong(dataMap.get("id")));
        legacyInfo.setVin(parseString(dataMap.get("vin")));  // 用于字段关联

        // 设置日期相关信息
        legacyInfo.setPurchaseDate(parseDate(dataMap.get("purchase_date")));
        legacyInfo.setStartDate(parseDate(dataMap.get("start_date")));
        legacyInfo.setLicenseDate(parseDate(dataMap.get("license_date")));
        legacyInfo.setOperationStartDate(parseDate(dataMap.get("operation_start_date")));

        // 设置其他属性
        legacyInfo.setOperatingNo(parseString(dataMap.get("operating_no")));
        legacyInfo.setHasRight(parseInt(dataMap.get("has_right")));
        legacyInfo.setOwnerId(parseInt(dataMap.get("owner_id")));
        legacyInfo.setAssetOwnerId(parseInt(dataMap.get("asset_owner_id")));
        legacyInfo.setVehicleCategoryId(parseInt(dataMap.get("vehicle_category_id")));
        legacyInfo.setOperateTypeId(parseInt(dataMap.get("operate_type_id")));
        legacyInfo.setContractTypeId(parseInt(dataMap.get("contract_type_id")));
        legacyInfo.setOperationCategoryId(parseInt(dataMap.get("operation_category_id")));
        legacyInfo.setCompanyOwnerId(parseInt(dataMap.get("company_owner_id")));
        legacyInfo.setFromCompany(parseString(dataMap.get("from_company")));

        // 设置审计字段
        legacyInfo.setCreateTime(now);
        legacyInfo.setCreateOperId(-1L);
        legacyInfo.setCreateOperName("system");
        legacyInfo.setUpdateTime(now);
        legacyInfo.setUpdateOperId(-1L);
        legacyInfo.setUpdateOperName("system");

        return legacyInfo;
    }

    /**
     * 初始化车辆资产
     *
     * 从Excel文件中读取车辆资产数据并导入系统。使用多线程处理以提高性能。
     * 处理流程:
     * 1. 读取车辆资产数据
     * 2. 过滤无效数据(非正常状态、特定车牌前缀)
     * 3. 同步保存车辆资产信息
     * 4. 等待所有异步任务完成后关闭线程池
     */
    @Test
    @DisplayName("初始化车辆资产")
    public void initVehicleAssetInfoData() {
        // 初始化计数器，用于等待所有任务完成
        CountDownLatch latch = new CountDownLatch(1);
        Integer headLine = 1;

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();

        // 读取初始化Excel
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250530/VehicleAsset.xlsx");
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 第一行为表头，记录字段映射关系
                if(context.readRowHolder().getRowIndex() == headLine) {
                    tableMap.putAll(data);
                    return;
                }

                taskExecutor.execute(() -> {
                    // 转换Excel数据到Map key: 字段名称, value: 字段值
                    Map<String, String> dataMap = new HashMap<>();
                    data.forEach((key, value) -> {
                        String columnName = tableMap.get(key);
                        dataMap.put(columnName, value);
                    });

                    // 获取关联车辆ID并验证
                    Long vehicleId = parseLong(dataMap.get("vehicle_id"));
                    if(vehicleId == null) {
                        return;
                    }

                    VehicleInfo vehicleInfo = vehicleInfoMapper.selectByPrimaryKey(vehicleId).orElse(null);
                    if(vehicleInfo == null) {
                        return;
                    }

                    // 更新车辆资产信息
                    VehicleInfo updateVehicleInfo = new VehicleInfo();
                    updateVehicleInfo.setId(vehicleInfo.getId());
                    updateVehicleInfo.setRelateAssetId(parseLong(dataMap.get("id")));
                    // 设置价格相关信息
                    updateVehicleInfo.setPurchasePrice(parseDecimal(dataMap.get("purchase_price")));          // 裸车价
                    updateVehicleInfo.setPurchaseTax(parseDecimal(dataMap.get("purchase_tax")));             // 购置税
                    updateVehicleInfo.setLicensePlatePrice(parseDecimal(dataMap.get("license_plate_price"))); // 牌照费
                    updateVehicleInfo.setLicensePlateOtherPrice(parseDecimal(dataMap.get("license_plate_other_price"))); // 上牌杂费
                    updateVehicleInfo.setUpholsterPrice(parseDecimal(dataMap.get("upholster_price")));       // 装潢费
                    updateVehicleInfo.setTotalPrice(parseDecimal(dataMap.get("total_price")));               // 购置总价
                    updateVehicleInfo.setRemainPrice(parseDecimal(dataMap.get("remain_price")));             // 账面净值
                    updateVehicleInfo.setSecondHandPrice(parseDecimal(dataMap.get("second_hand_price")));    // 旧车销售价

                    // 设置审计字段
                    vehicleInfo.setUpdateTime(new Date());
                    vehicleInfo.setUpdateOperId(-1L);
                    vehicleInfo.setUpdateOperName("system");

                    vehicleInfoMapper.updateByPrimaryKeySelective(updateVehicleInfo);
                });
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
                // 等待半分钟，确保异步数据处理完成
                try {
                    Thread.sleep(30000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                latch.countDown();
            }
        }).sheet(0).headRowNumber(headLine).doRead();

        try {
            // 等待所有任务完成
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 关闭线程池
        taskExecutor.shutdown();
    }

    /**
     * 初始化所有字典数据
     *
     * 该方法会依次初始化系统中所有的字典数据，包括:
     * - 获取方式
     * - 使用性质
     * - 制造商
     * - 燃料类型
     * - 车辆品牌
     * - 车辆类型
     * - 驱动类型
     * - 制动形式
     * - 转向方式
     * - 驾驶位置
     * - 发动机位置
     * - 车辆简称
     * - 排放标准
     * - 变速箱形式
     * - 颜色
     * - 号牌种类
     * - 运营性质
     * - 合同形式
     * - 运营类别
     */
    @Test
    @DisplayName("初始化字典表")
    public void initDict() {
        // 获取方式
        initObtainWay();
        // 使用性质
        initUsage();
        // 制造商
        initManufacturer();
        // 燃料类型
        initGasType();
        // 车辆品牌
        initVehicleBrand();
        // 车辆类型
        initVehicleType();
        // 驱动类型
        initWheelDrive();
        // 制动形式
        initBreakMode();
        // 转向方式
        initTurnMode();
        // 驾驶位置
        initDrivePosition();
        // 发动机位置
        initEnginePosition();
        // 车辆简称
        initVehicleAbbreviation();
        // 排放标准
        initExhaust();
        // 变速箱形式
        initGearBoxType();

        // 颜色
        initVehicleColor();
        // 号牌种类
        initVehicleCategory();
        // 运营性质
        initOperateType();
        // 合同形式
        initContractType();
        // 运营类别
        initOperationCategory();
    }

    /**
     * 从Excel读取并初始化字典数据
     *
     * @param inputStream Excel文件输入流
     * @param sheetName Excel工作表名称
     * @param dataName 字典数据名称
     * @param systemCode 字典系统编码
     */
    public void readDictExcel(InputStream inputStream, String sheetName, String dataName, String systemCode) {
        if(StringUtils.isBlank(systemCode)) {
            return;
        }

        // 清空字典表原有信息
        DeleteStatementProvider deleteStatement = deleteFrom(dataMaintainDictInfo)
                .where(dataMaintainDictInfo.systemCode, isEqualTo(systemCode))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        dataMaintainDictInfoMapper.delete(deleteStatement);

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(0, "data_code");
        tableMap.put(3, "data_value");

        // 读取并解析Excel数据
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 转换Excel数据到Map key: 字段名称, value: 字段值
                Map<String, String> dataMap = new HashMap<>();
                data.forEach((key, value) -> {
                    String columnName = tableMap.get(key);
                    dataMap.put(columnName, value);
                });

                // 构建并保存字典数据
                DataMaintainDictInfo saveData = new DataMaintainDictInfo();
                saveData.setDataName(dataName);
                saveData.setSystemCode(systemCode);
                saveData.setDataCode(parseString(dataMap.get("data_code")));
                saveData.setDataValue(parseString(dataMap.get("data_value")));

                // 设置审计字段
                Date now = new Date();
                saveData.setCreateTime(now);
                saveData.setCreateOperId(-1L);
                saveData.setCreateOperName("system");
                saveData.setUpdateTime(now);
                saveData.setUpdateOperId(-1L);
                saveData.setUpdateOperName("system");

                dataMaintainDictInfoMapper.insertSelective(saveData);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
            }
        }).sheet(sheetName).headRowNumber(1).doRead();
    }

    /**
     * 初始化获取方式字典数据
     * 从Excel文件的ObtainWay工作表读取获取方式相关数据
     */
    @Test
    @DisplayName("初始化获取方式")
    public void initObtainWay(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/ObtainWay.xlsx");
        readDictExcel(inputStream,"ObtainWay", "获取方式", "obtainWay");
    }

    /**
     * 初始化使用性质字典数据
     * 从Excel文件的Usage工作表读取使用性质相关数据
     */
    @Test
    @DisplayName("初始化使用性质")
    public void initUsage(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/Usage.xlsx");
        readDictExcel(inputStream,"Usage", "使用性质", "usage");
    }

    /**
     * 初始化制造商字典数据
     * 从Excel文件的Manufacturer工作表读取制造商相关数据
     */
    @Test
    @DisplayName("初始化制造商")
    public void initManufacturer(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/Manufacturer.xlsx");
        readDictExcel(inputStream,"Manufacturer", "制造商", "manufacturer");
    }

    /**
     * 初始化燃料类型字典数据
     * 从Excel文件的GasType工作表读取燃料类型相关数据
     */
    @Test
    @DisplayName("初始化燃料类型")
    public void initGasType(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/GasType.xlsx");
        readDictExcel(inputStream,"GasType", "燃料类型", "gasType");
    }

    /**
     * 初始化车辆品牌字典数据
     * 从Excel文件的VehicleBrand工作表读取车辆品牌相关数据
     */
    @Test
    @DisplayName("初始化车辆品牌")
    public void initVehicleBrand(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/VehicleBrand.xlsx");
        readDictExcel(inputStream,"VehicleBrand", "车辆品牌", "vehicleBrand");
    }

    /**
     * 初始化车辆类型字典数据
     * 从Excel文件的VehicleType工作表读取车辆类型相关数据
     */
    @Test
    @DisplayName("初始化车辆类型")
    public void initVehicleType(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/VehicleType.xlsx");
        readDictExcel(inputStream,"VehicleType", "车辆类型", "vehicleType");
    }

    /**
     * 初始化驱动类型字典数据
     * 从Excel文件的WheelDrive工作表读取驱动类型相关数据
     */
    @Test
    @DisplayName("初始化驱动类型")
    public void initWheelDrive(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/WheelDrive.xlsx");
        readDictExcel(inputStream,"WheelDrive", "驱动类型", "wheelDrive");
    }

    /**
     * 初始化制动形式字典数据
     * 从Excel文件的BreakMode工作表读取制动形式相关数据
     */
    @Test
    @DisplayName("初始化制动形式")
    public void initBreakMode(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/BreakMode.xlsx");
        readDictExcel(inputStream,"BreakMode", "制动形式", "breakMode");
    }

    /**
     * 初始化转向方式字典数据
     * 从Excel文件的TurnMode工作表读取转向方式相关数据
     */
    @Test
    @DisplayName("初始化转向方式")
    public void initTurnMode(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/TurnMode.xlsx");
        readDictExcel(inputStream,"TurnMode", "转向方式", "turnMode");
    }

    /**
     * 初始化驾驶位置字典数据
     * 从Excel文件的DrivePosition工作表读取驾驶位置相关数据
     */
    @Test
    @DisplayName("初始化驾驶位置")
    public void initDrivePosition(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/DrivePosition.xlsx");
        readDictExcel(inputStream,"DrivePosition", "驾驶位置", "drivePosition");
    }

    /**
     * 初始化发动机位置字典数据
     * 从Excel文件的EnginePosition工作表读取发动机位置相关数据
     */
    @Test
    @DisplayName("初始化发动机位置")
    public void initEnginePosition(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/EnginePosition.xlsx");
        readDictExcel(inputStream,"EnginePosition", "发动机位置", "enginePosition");
    }

    /**
     * 初始化车辆简称字典数据
     * 从Excel文件的VehicleAbbreviation工作表读取车辆简称相关数据
     * 注意：该方法使用单独的Excel文件(/init/VehicleAbbreviation.xlsx)
     */
    @Test
    @DisplayName("初始化车辆简称")
    public void initVehicleAbbreviation(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/VehicleAbbreviation.xlsx");
        readDictExcel(inputStream,"VehicleAbbreviation", "车辆简称", "vehicleAbbreviation");
    }

    /**
     * 初始化排放标准字典数据
     * 从Excel文件的Exhaust工作表读取排放标准相关数据
     */
    @Test
    @DisplayName("初始化排放标准")
    public void initExhaust(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/Exhaust.xlsx");
        readDictExcel(inputStream,"Exhaust", "排放标准", "exhaust");
    }

    /**
     * 初始化变速箱形式字典数据
     * 从Excel文件的GearBoxType工作表读取变速箱形式相关数据
     */
    @Test
    @DisplayName("初始化变速箱形式")
    public void initGearBoxType(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/GearBoxType.xlsx");
        readDictExcel(inputStream,"GearBoxType", "变速箱形式", "gearBoxType");
    }

    /**
     * 初始化车身颜色字典数据
     * 从Excel文件的VehicleColor工作表读取车身颜色相关数据
     */
    @Test
    @DisplayName("初始化颜色")
    public void initVehicleColor(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/VehicleColor.xlsx");
        readDictExcel(inputStream,"VehicleColor", "车身颜色", "vehicleColor");
    }

    /**
     * 初始化号牌种类字典数据
     * 从Excel文件的VehicleCategory工作表读取号牌种类相关数据
     */
    @Test
    @DisplayName("初始化号牌种类")
    public void initVehicleCategory(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/VehicleCategory.xlsx");
        readDictExcel(inputStream,"VehicleCategory", "号牌种类", "vehicleCategory");
    }

    /**
     * 初始化运营性质字典数据
     * 从Excel文件的OperateType工作表读取运营性质相关数据
     */
    @Test
    @DisplayName("初始化运营性质")
    public void initOperateType(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/OperateType.xlsx");
        readDictExcel(inputStream,"OperateType", "运营性质", "operateType");
    }

    /**
     * 初始化合同形式字典数据
     * 从Excel文件的ContractType工作表读取合同形式相关数据
     */
    @Test
    @DisplayName("初始化合同形式")
    public void initContractType(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/ContractType.xlsx");
        readDictExcel(inputStream,"ContractType", "合同形式", "contractType");
    }


    /**
     * 初始化运营类别字典数据
     * 从Excel文件的OperationCategory工作表读取运营类别相关数据
     */
    @Test
    @DisplayName("初始化运营类别")
    public void initOperationCategory(){
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/OperationCategory.xlsx");
        readDictExcel(inputStream,"OperationCategory", "运营类别", "operationCategory");
    }

    /**
     * 初始化新车型至现存车辆
     *
     * 该方法用于更新现有车辆的车型信息，将新的车型数据应用到已存在的车辆记录中。
     *
     * 处理流程:；；
     * 1. 从t_vehicle_model_info表获取车型名称与ID的映射关系
     * 2. 从t_vin_model_info表获取VIN码与车型名称的映射关系
     * 3. 查询所有车辆信息
     * 4. 使用线程池并行处理车辆信息更新
     * 5. 根据VIN码找到对应的车型名称
     * 6. 根据车型名称找到对应的车型ID
     * 7. 如果车辆当前车型ID与新车型ID不同，则更新车型ID
     * 8. 使用CountDownLatch等待所有更新任务完成
     *
     * 注意事项:
     * - 该方法不会清空原有数据，只会更新车型ID
     * - 只有当VIN码能找到对应车型名称，且车型名称能找到对应车型ID时才会更新
     * - 使用线程池并行处理，提高大批量数据更新效率
     * - 设置了30分钟的超时时间，超时后会记录日志但不会中断其他任务
     */
    @Test
    @DisplayName("初始化新车型至现存车辆")
    public void initNewModelToExistingVehicle(){
        System.out.println("开始执行车辆车型信息更新...");

        try {
            // 1. 从t_vehicle_model_info获取车型名称与ID的映射
            Map<String, Long> modelNameToIdMap = getModelNameToIdMap();
            System.out.println("获取到车型名称与ID的映射，共 " + modelNameToIdMap.size() + " 条记录");

            // 2. 从t_vin_model_info获取VIN与车型名称的映射
            Map<String, String> vinToModelNameMap = getVinToModelNameMap();
            System.out.println("获取到VIN与车型名称的映射，共 " + vinToModelNameMap.size() + " 条记录");

            // 3. 更新t_vehicle_info中的车型ID
            updateVehicleModelIds(vinToModelNameMap, modelNameToIdMap);
        } catch (Exception e) {
            // 记录异常信息，便于排查问题
            System.err.println("车辆车型信息更新过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    @DisplayName("初始化额度单流水记录")
    public void initLicensePlateQuotaTransactionRecord() {
        // 读取Excel文件
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/大众在役车期初数据.xlsx");
        readLicensePlateQuotaTransactionRecordExcel(inputStream, "额度单", "额度单", "licensePlateQuotaTransactionRecord");
    }

    @Test
    @DisplayName("初始化额度单记录")
    public void initLicensePlateQuota() {
        // 读取Excel文件
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/大众在役车期初数据.xlsx");
        readLicensePlateQuotaExcel(inputStream, "额度总数", "额度总数", "licensePlateQuota");
    }

    /**
     * 查询并构建组织层级结构
     *
     * @param orgIds 组织ID列表
     * @return 包含完整层级结构的组织信息列表
     */
    @Test
    @DisplayName("查询组织层级结构")
    public List<Map<String, Object>> queryOrgHierarchy(List<Long> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            System.out.println("输入的组织ID列表为空");
            return Collections.emptyList();
        }

        System.out.println("\n=== 开始构建组织层级结构 ===");

        // 存储所有查询到的组织信息，key为组织ID
        Map<Long, OrgInfo> allOrgsMap = new HashMap<>();

        // 第一步：查询指定的组织信息
        List<OrgInfo> targetOrgs = queryOrgsByIds(orgIds);
        if (targetOrgs.isEmpty()) {
            System.out.println("未找到指定ID的组织信息");
            return Collections.emptyList();
        }

        System.out.println("\n1. 初始组织信息查询完成：");
        for (OrgInfo org : targetOrgs) {
            System.out.println(String.format("   组织ID: %d, 名称: %s, 父组织ID: %s",
                org.getId(), org.getCompanyName(), org.getParentId()));
            allOrgsMap.put(org.getId(), org);
        }

        // 第二步：递归查询所有父组织
        System.out.println("\n2. 开始递归查询父组织");
        Set<Long> processedOrgIds = new HashSet<>();
        for (OrgInfo org : targetOrgs) {
            findAllParentOrgs(org, allOrgsMap, processedOrgIds);
        }

        System.out.println("\n3. 所有查询到的组织信息：");
        allOrgsMap.forEach((id, org) -> {
            System.out.println(String.format("   ID: %d, 名称: %s, 父组织ID: %s",
                id, org.getCompanyName(), org.getParentId()));
        });

        // 第三步：构建组织层级关系
        System.out.println("\n4. 开始构建组织层级关系");
        Map<Long, List<Long>> childrenMap = new HashMap<>();
        Set<Long> rootOrgIds = new HashSet<>();

        // 遍历所有组织，建立父子关系
        for (OrgInfo org : allOrgsMap.values()) {
            Long orgId = org.getId();
            Long parentId = org.getParentId();

            if (parentId == null || parentId <= 0 || !allOrgsMap.containsKey(parentId)) {
                // 如果没有父组织或父组织不在查询结果中，则视为根组织
                rootOrgIds.add(orgId);
                System.out.println("   找到根组织：" + org.getCompanyName() + " (ID: " + orgId + ")");
            } else {
                // 将当前组织添加到父组织的子列表中
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(orgId);
                System.out.println("   添加父子关系：父组织 " + parentId + " -> 子组织 " + orgId);
            }
        }

        // 第四步：构建组织层级树
        System.out.println("\n5. 开始构建组织层级树");
        List<Map<String, Object>> result = new ArrayList<>();

        System.out.println("   根组织数量：" + rootOrgIds.size());
        for (Long rootId : rootOrgIds) {
            OrgInfo rootOrg = allOrgsMap.get(rootId);
            if (rootOrg != null) {
                Map<String, Object> rootNode = buildOrgTreeNode(rootOrg, childrenMap, allOrgsMap);
                result.add(rootNode);
                System.out.println("   构建根组织树：" + rootOrg.getCompanyName() + " (ID: " + rootId + ")");
            }
        }

        System.out.println("\n6. 组织层级树构建完成");
        System.out.println("   总组织数：" + allOrgsMap.size());
        System.out.println("   根组织数：" + result.size());

        return result;
    }

    /**
     * 构建单个组织节点及其子树
     */
    private Map<String, Object> buildOrgTreeNode(OrgInfo org, Map<Long, List<Long>> childrenMap, Map<Long, OrgInfo> allOrgsMap) {
        Map<String, Object> node = new HashMap<>();
        node.put("id", org.getId());
        node.put("name", org.getCompanyName());
        node.put("code", org.getCompanyCode());
        node.put("parentId", org.getParentId());
        node.put("type", org.getOrgType());

        // 处理子组织
        List<Long> childIds = childrenMap.get(org.getId());
        if (childIds != null && !childIds.isEmpty()) {
            List<Map<String, Object>> children = new ArrayList<>();
            for (Long childId : childIds) {
                OrgInfo childOrg = allOrgsMap.get(childId);
                if (childOrg != null) {
                    children.add(buildOrgTreeNode(childOrg, childrenMap, allOrgsMap));
                }
            }
            if (!children.isEmpty()) {
                node.put("children", children);
            }
        }

        return node;
    }

    /**
     * 根据组织ID列表查询组织信息
     *
     * @param orgIds 组织ID列表
     * @return 组织信息列表
     */
    private List<OrgInfo> queryOrgsByIds(List<Long> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            System.out.println("警告：输入的组织ID列表为空");
            return Collections.emptyList();
        }

        try {
            // 打印SQL查询条件
            System.out.println("执行组织查询，组织ID列表：" + orgIds);

            // 使用MyBatis动态SQL构建查询
            SelectStatementProvider provider = select(orgInfo.allColumns())
                    .from(orgInfo)
                    .where(orgInfo.id, isIn(orgIds))
                    // .and(orgInfo.isDeleted, isEqualTo(0)) // 只查询未删除的组织（已注释）
                    .build()
                    .render(RenderingStrategies.MYBATIS3);

            // 打印生成的SQL语句
            System.out.println("生成的SQL查询语句：" + provider.getSelectStatement());
            System.out.println("SQL参数：" + provider.getParameters());

            // 执行查询
            List<OrgInfo> results = orgInfoMapper.selectMany(provider);

            // 打印查询结果
            System.out.println("查询结果数量：" + (results != null ? results.size() : 0));
            if (results == null || results.isEmpty()) {
                System.out.println("警告：未找到任何匹配的组织信息");
            } else {
                System.out.println("查询到的组织信息：");
                for (OrgInfo org : results) {
                    System.out.println(String.format("ID: %d, 名称: %s, 代码: %s, 父组织ID: %d",
                        org.getId(),
                        org.getCompanyName(),
                        org.getCompanyCode(),
                        org.getParentId()));
                }
            }

            return results;
        } catch (Exception e) {
            System.err.println("查询组织信息时发生错误：" + e.getMessage());
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    /**
     * 递归查询所有父组织
     */
    private void findAllParentOrgs(OrgInfo org, Map<Long, OrgInfo> allOrgsMap, Set<Long> processedOrgIds) {
        if (org == null) {
            System.out.println("警告：传入的组织对象为null");
            return;
        }

        // 如果已经处理过该组织，则直接返回
        if (processedOrgIds.contains(org.getId())) {
            System.out.println("组织 " + org.getId() + " 已经处理过，跳过");
            return;
        }

        // 标记该组织已处理
        processedOrgIds.add(org.getId());
        System.out.println("处理组织：" + org.getId() + " (" + org.getCompanyName() + ")");

        // 如果没有父组织，则直接返回
        Long parentId = org.getParentId();
        if (parentId == null || parentId <= 0) {
            System.out.println("组织 " + org.getId() + " 没有父组织");
            return;
        }

        // 如果父组织已经在映射中，则直接返回
        if (allOrgsMap.containsKey(parentId)) {
            System.out.println("组织 " + org.getId() + " 的父组织 " + parentId + " 已在缓存中");
            return;
        }

        try {
            // 查询父组织信息
            System.out.println("查询组织 " + org.getId() + " 的父组织（ID: " + parentId + "）");
            Optional<OrgInfo> parentOrg = orgInfoMapper.selectByPrimaryKey(parentId);

            if (parentOrg.isPresent()) {
                OrgInfo parent = parentOrg.get();
                System.out.println("找到父组织：" + parent.getId() + " (" + parent.getCompanyName() + ")");
                allOrgsMap.put(parentId, parent);

                // 递归查询父组织的父组织
                findAllParentOrgs(parent, allOrgsMap, processedOrgIds);
            } else {
                System.out.println("警告：未找到组织 " + org.getId() + " 的父组织 " + parentId);
            }
        } catch (Exception e) {
            System.err.println("查询父组织时发生错误，当前组织ID：" + org.getId() + "，父组织ID：" + parentId);
            System.err.println("错误信息：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试查询组织层级结构
     *
     * 该方法用于测试queryOrgHierarchy函数的功能，传入一组组织ID，
     * 查询并打印完整的组织层级结构。
     */
    @Test
    @DisplayName("测试查询组织层级结构")
    public void testQueryOrgHierarchy() {
        try {
            // 创建测试用的组织ID列表
            List<Long> testOrgIds = Arrays.asList(
                366L, 455L, 457L, 80L, 47L, 56L, 55L, 54L, 461L, 465L,
                460L, 81L, 462L, 459L, 464L, 12385L, 35L, 11106L, 11032L,
                12955L, 12956L, 12897L, 12957L, 12830L, 12899L, 12420L,
                12898L, 13993L, 515L, 11799L, 573L, 485L, 516L, 451L,
                487L, 13799L, 12954L, 422L, 456L, 12887L, 197L, 517L,
                482L, 13828L, 569L, 13829L, 84L, 73L, 419L
            );

            System.out.println("\n=== 开始组织层级结构测试 ===");
            System.out.println("输入组织ID数量：" + testOrgIds.size());

            // 验证数据库连接
            try {
                SelectStatementProvider testProvider = select(count())
                    .from(orgInfo)
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
                long totalCount = orgInfoMapper.count(testProvider);
                System.out.println("数据库连接正常，组织表总记录数：" + totalCount);
            } catch (Exception e) {
                System.err.println("数据库连接测试失败：" + e.getMessage());
                e.printStackTrace();
                return;
            }

            // 调用查询方法
            List<Map<String, Object>> result = queryOrgHierarchy(testOrgIds);

            // 输出结果统计
            System.out.println("\n=== 查询结果统计 ===");
            System.out.println("根组织数量：" + result.size());

            // 收集所有组织信息（包括父组织）
            Set<Map<String, Object>> allOrgs = new TreeSet<>((o1, o2) -> {
                Long id1 = (Long) o1.get("id");
                Long id2 = (Long) o2.get("id");
                return id1.compareTo(id2);
            });
            collectAllOrgs(result, allOrgs);

            // 打印所有组织列表
            System.out.println("\n=== 所有相关组织列表 ===");
            System.out.println("总计：" + allOrgs.size() + " 个组织");
            if (allOrgs.isEmpty()) {
                System.out.println("警告：未找到任何组织信息，请检查：");
                System.out.println("1. 输入的组织ID是否正确");
                System.out.println("2. 组织表中是否存在这些记录");
                System.out.println("3. 组织记录的is_deleted字段是否为0");
            } else {
                System.out.println("---------------------------------------------------------------------------------------------");
                System.out.printf("%-10s | %-30s | %-20s | %-10s | %-30s%n", "组织ID", "组织名称", "组织代码", "父组织ID", "父组织名称");
                System.out.println("---------------------------------------------------------------------------------------------");
                for (Map<String, Object> org : allOrgs) {
                    Long parentId = (Long) org.get("parentId");
                    String parentName = "";
                    if (parentId != null && parentId > 0) {
                        // 在allOrgs中查找父组织名称
                        for (Map<String, Object> parent : allOrgs) {
                            if (parentId.equals(parent.get("id"))) {
                                parentName = (String) parent.get("name");
                                break;
                            }
                        }
                    }
                    System.out.printf("%-10d | %-30s | %-20s | %-10s | %-30s%n",
                        org.get("id"),
                        org.get("name"),
                        org.get("code"),
                        parentId == null ? "" : parentId,
                        parentName);
                }
                System.out.println("---------------------------------------------------------------------------------------------");
            }
        } catch (Exception e) {
            System.err.println("\n测试执行过程中发生错误：");
            System.err.println("错误信息：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 递归收集所有组织信息
     *
     * @param orgs 组织层级结构
     * @param allOrgs 用于存储所有组织的集合
     */
    private void collectAllOrgs(List<Map<String, Object>> orgs, Set<Map<String, Object>> allOrgs) {
        if (orgs == null || orgs.isEmpty()) {
            return;
        }

        for (Map<String, Object> org : orgs) {
            // 添加当前组织
            allOrgs.add(org);

            // 递归处理子组织
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> children = (List<Map<String, Object>>) org.getOrDefault("children", Collections.emptyList());
            collectAllOrgs(children, allOrgs);
        }
    }

    /**
     * 读取车牌配额交易记录Excel文件
     *
     * @param inputStream Excel文件输入流
     * @param sheetName   sheet名称
     * @param dictName    字典名称
     * @param dictCode    字典代码
     */
    private void readLicensePlateQuotaTransactionRecordExcel(InputStream inputStream, String sheetName, String dictName, String dictCode) {
        // 清空表数据
        DeleteStatementProvider deleteStatement = deleteFrom(licensePlateQuotaTransactionRecord)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        licensePlateQuotaTransactionRecordMapper.delete(deleteStatement);

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(5, "quota_number");
        tableMap.put(2, "license_plate");
        tableMap.put(6, "asset_company_name");
        tableMap.put(4, "quota_type");
        tableMap.put(3, "quota_print_date");

        // 预先加载所有公司信息，用于后续查询
        SelectStatementProvider selectStatement = select(dataOwnerInfo.allColumns())
                .from(dataOwnerInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<DataOwnerInfo> allCompanies = dataOwnerInfoMapper.selectMany(selectStatement);
        Map<String, Integer> companyNameToIdMap = new HashMap<>();
        for (DataOwnerInfo company : allCompanies) {
            companyNameToIdMap.put(company.getName(), company.getId().intValue());
        }

        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 转换Excel数据到Map key: 字段名称, value: 字段值
                Map<String, String> dataMap = new HashMap<>();
                data.forEach((key, value) -> {
                    String columnName = tableMap.get(key);
                    dataMap.put(columnName, value);
                });

                // 根据公司名称查询公司ID
                String companyName = parseString(dataMap.get("asset_company_name"));
                Integer companyId = null;
                if (StringUtils.isNotBlank(companyName)) {
                    companyId = companyNameToIdMap.get(companyName);
                    if (companyId == null) {
                        System.out.println("警告：找不到公司名称对应的ID：" + companyName);
                    }
                }

                LicensePlateQuotaTransactionRecord record = new LicensePlateQuotaTransactionRecord();
                record.setQuotaPrintDate(parseDate(dataMap.get("quota_print_date")));
                record.setQuotaNumber(parseString(dataMap.get("quota_number")));
                record.setQuotaType(QuotaTypeEnum.getCodeByDesc(dataMap.get("quota_type")));
                record.setAssetCompanyId(companyId); // 设置查询到的公司ID
                record.setAssetCompanyName(companyName);

                // 设置默认值
                record.setIsDeleted(0);
                Date now = new Date();
                record.setCreateTime(now);
                record.setCreateOperId(-1L);
                record.setCreateOperName("system");
                record.setUpdateTime(now);
                record.setUpdateOperId(-1L);
                record.setUpdateOperName("system");

                licensePlateQuotaTransactionRecordMapper.insertSelective(record);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("额度单流水记录数据初始化完成！");
            }
        }).sheet(sheetName).headRowNumber(1).doRead();
    }

    /**
     * 读取车牌配额交易记录Excel文件
     *
     * @param inputStream Excel文件输入流
     * @param sheetName   sheet名称
     * @param dictName    字典名称
     * @param dictCode    字典代码
     */
    private void readLicensePlateQuotaExcel(InputStream inputStream, String sheetName, String dictName, String dictCode) {
        // 清空表数据
        DeleteStatementProvider deleteStatement = deleteFrom(licensePlateQuota)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        licensePlateQuotaMapper.delete(deleteStatement);

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(1, "asset_company_name");
        tableMap.put(2, "quota_type");
        tableMap.put(3, "quota");
        tableMap.put(4, "occupied");
        tableMap.put(5, "remaining");

        // 预先加载所有公司信息，用于后续查询
        SelectStatementProvider selectStatement = select(dataOwnerInfo.allColumns())
                .from(dataOwnerInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<DataOwnerInfo> allCompanies = dataOwnerInfoMapper.selectMany(selectStatement);
        Map<String, Integer> companyNameToIdMap = new HashMap<>();
        for (DataOwnerInfo company : allCompanies) {
            companyNameToIdMap.put(company.getName(), company.getId().intValue());
        }

        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 转换Excel数据到Map key: 字段名称, value: 字段值
                Map<String, String> dataMap = new HashMap<>();
                data.forEach((key, value) -> {
                    String columnName = tableMap.get(key);
                    dataMap.put(columnName, value);
                });

                // 根据公司名称查询公司ID
                String companyName = parseString(dataMap.get("asset_company_name"));
                Integer companyId = null;
                if (StringUtils.isNotBlank(companyName)) {
                    companyId = companyNameToIdMap.get(companyName);
                    if (companyId == null) {
                        System.out.println("警告：找不到公司名称对应的ID：" + companyName);
                    }
                }

                LicensePlateQuota record = new LicensePlateQuota();
                record.setQuotaType(QuotaTypeEnum.getCodeByDesc(dataMap.get("quota_type")));
                record.setAssetCompanyId(companyId); // 设置查询到的公司ID
                record.setQuota(Integer.parseInt(dataMap.get("quota")));
                record.setOccupied(Integer.parseInt(dataMap.get("occupied")));
                record.setRemaining(Integer.parseInt(dataMap.get("remaining")));

                // 设置默认值
                record.setIsDeleted(0);
                Date now = new Date();
                record.setCreateTime(now);
                record.setCreateOperId(-1L);
                record.setCreateOperName("system");
                record.setUpdateTime(now);
                record.setUpdateOperId(-1L);
                record.setUpdateOperName("system");

                licensePlateQuotaMapper.insertSelective(record);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("额度单流水记录数据初始化完成！");
            }
        }).sheet(sheetName).headRowNumber(1).doRead();
    }

    /**
     * 初始化车辆额度信息批量更新
     *
     * 从Excel文件读取数据并批量更新t_vehicle_info表中的指定字段
     */
    @Test
    @DisplayName("批量更新车辆额度信息")
    public void initVehicleQuotaInfoBatchUpdate() {
        // 读取Excel文件
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/大众在役车期初数据.xlsx");
        readVehicleInfoBatchUpdateExcel(inputStream, "车辆", "车辆额度信息", "vehicleInfoBatchUpdate");
    }

    /**
     * 读取车辆信息并执行批量更新
     *
     * @param inputStream Excel文件输入流
     * @param sheetName   sheet名称
     * @param dictName    字典名称
     * @param dictCode    字典代码
     */
    private void readVehicleInfoBatchUpdateExcel(InputStream inputStream, String sheetName, String dictName, String dictCode) {
        // 初始化计数器，用于等待所有任务完成
        CountDownLatch latch = new CountDownLatch(1);

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(3, "license_plate");
        tableMap.put(4, "vin");
        tableMap.put(6, "product_line");
        tableMap.put(7, "business_line");
        tableMap.put(8, "quota_type");
        tableMap.put(9, "quota_asset_company_id");

        // 预先加载所有公司信息，用于后续查询
        SelectStatementProvider selectOwnerStatement = select(dataOwnerInfo.allColumns())
                .from(dataOwnerInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<DataOwnerInfo> allCompanies = dataOwnerInfoMapper.selectMany(selectOwnerStatement);
        Map<String, Integer> companyNameToIdMap = new HashMap<>();
        for (DataOwnerInfo company : allCompanies) {
            companyNameToIdMap.put(company.getName(), company.getId().intValue());
        }

        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 第一行为表头，记录字段映射关系
                if (context.readRowHolder().getRowIndex() == 1) {
                    return;
                }

                taskExecutor.execute(() -> {
                    // 转换Excel数据到Map key: 字段名称, value: 字段值
                    Map<String, String> dataMap = new HashMap<>();
                    data.forEach((key, value) -> {
                        String columnName = tableMap.get(key);
                        dataMap.put(columnName, value);
                    });

                    // 获取车牌号和VIN码
                    String licensePlate = parseString(dataMap.get("license_plate"));
                    String vin = parseString(dataMap.get("vin"));

                    if (StringUtils.isBlank(licensePlate) && StringUtils.isBlank(vin)) {
                        return;
                    }

                    // 查询车辆信息（优先使用VIN码查找）
                    VehicleInfo vehicleInfoData = null;
                    if (StringUtils.isNotBlank(vin)) {
                        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                                .from(vehicleInfo)
                                .where()
                                .and(vehicleInfo.vin, isEqualTo(vin))
                                .limit(1)
                                .build()
                                .render(RenderingStrategies.MYBATIS3);
                        vehicleInfoData = vehicleInfoMapper.selectOne(selectStatement).orElse(null);
                    }
                    if (vehicleInfoData == null && StringUtils.isNotBlank(licensePlate)) {
                        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                                .from(vehicleInfo)
                                .where()
                                .and(vehicleInfo.licensePlate, isEqualTo(licensePlate))
                                .limit(1)
                                .build()
                                .render(RenderingStrategies.MYBATIS3);
                        vehicleInfoData = vehicleInfoMapper.selectOne(selectStatement).orElse(null);
                    }
                    if (vehicleInfoData == null) {
                        System.out.println("未找到匹配的车辆信息，车牌：" + licensePlate + "，VIN：" + vin);
                        return;
                    }

                    // 构建更新内容
                    VehicleInfo updateVehicleInfo = new VehicleInfo();
                    updateVehicleInfo.setId(vehicleInfoData.getId());

                    // 示例：更新额度类型（根据描述获取code）
                    String quotaTypeDesc = dataMap.get("quota_type");
                    Integer quotaTypeCode = QuotaTypeEnum.getCodeByDesc(quotaTypeDesc);
                    if (quotaTypeCode != null) {
                        updateVehicleInfo.setQuotaType(quotaTypeCode);
                    }
                    updateVehicleInfo.setQuotaAssetCompanyId(companyNameToIdMap.get(dataMap.get("quota_asset_company_id")));
                    updateVehicleInfo.setProductLine(ProductLineEnum.getCode(dataMap.get("product_line")));
                    updateVehicleInfo.setBusinessLine(BusinessLineEnum.getCode(dataMap.get("business_line")));

                    // 设置审计字段
                    Date now = new Date();
                    updateVehicleInfo.setUpdateTime(now);
                    updateVehicleInfo.setUpdateOperId(-1L);
                    updateVehicleInfo.setUpdateOperName("system");

                    // 执行更新
                    vehicleInfoMapper.updateByPrimaryKeySelective(updateVehicleInfo);
                });
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
                try {
                    Thread.sleep(30000); // 延迟确保异步任务完成
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                latch.countDown();
            }
        }).sheet(sheetName).headRowNumber(1).doRead();

        try {
            latch.await(); // 等待所有任务完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("批量更新车辆额度信息过程中被中断", e);
        } finally {
            taskExecutor.shutdown(); // 关闭线程池
        }

        System.out.println("车辆额度信息批量更新完成！");
    }

    /**
     * 初始化车辆额度信息批量更新
     *
     * 从Excel文件读取数据并批量更新t_vehicle_info表中的指定字段
     */
    @Test
    @DisplayName("批量更新车辆额度信息")
    public void initVehicleUsageIdBatchUpdate() {
        // 读取Excel文件
        InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/租赁车刷使用性质.xlsx");
        readVehicleUsageIdBatchUpdateExcel(inputStream, "Sheet1", "车辆额度信息", "vehicleInfoBatchUpdate");
    }

    /**
     * 读取车辆信息并执行批量更新
     *
     * @param inputStream Excel文件输入流
     * @param sheetName   sheet名称
     * @param dictName    字典名称
     * @param dictCode    字典代码
     */
    private void readVehicleUsageIdBatchUpdateExcel(InputStream inputStream, String sheetName, String dictName, String dictCode) {
        // 初始化计数器，用于等待所有任务完成
        CountDownLatch latch = new CountDownLatch(1);

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(1, "vin");
        tableMap.put(2, "usage_id_registration_card");

        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                // 第一行为表头，记录字段映射关系
                if (context.readRowHolder().getRowIndex() == 1) {
                    return;
                }

                taskExecutor.execute(() -> {
                    // 转换Excel数据到Map key: 字段名称, value: 字段值
                    Map<String, String> dataMap = new HashMap<>();
                    data.forEach((key, value) -> {
                        String columnName = tableMap.get(key);
                        dataMap.put(columnName, value);
                    });

                    // 获取车牌号和VIN码
                    String vin = parseString(dataMap.get("vin"));

                    if (StringUtils.isBlank(vin)) {
                        return;
                    }

                    // 查询车辆信息（优先使用VIN码查找）
                    VehicleInfo vehicleInfoData = null;
                    if (StringUtils.isNotBlank(vin)) {
                        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                                .from(vehicleInfo)
                                .where()
                                .and(vehicleInfo.vin, isEqualTo(vin))
                                .limit(1)
                                .build()
                                .render(RenderingStrategies.MYBATIS3);
                        vehicleInfoData = vehicleInfoMapper.selectOne(selectStatement).orElse(null);
                    }
                    if (vehicleInfoData == null) {
                        System.out.println("未找到匹配的车辆信息，VIN：" + vin);
                        return;
                    }

                    // 获取字典表
                    List<String> systemCodeList = new ArrayList<>();
                    systemCodeList.add("usage");
                    Map<String, Map<Integer, String>> dictInfoMap = dataDictService.getDataMaintainDictMap(systemCodeList);
                    Map<Integer, String> usageMap = dictInfoMap.get("usage");
                    Map<String, Integer> usageMapNew = usageMap.entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getValue,  // 新键是原值
                                    Map.Entry::getKey,    // 新值是原键
                                    (existing, replacement) -> existing, // 处理冲突：保留先出现的值
                                    LinkedHashMap::new    // 使用LinkedHashMap保持插入顺序
                            ));

                    // 构建更新内容
                    VehicleInfo updateVehicleInfo = new VehicleInfo();
                    updateVehicleInfo.setId(vehicleInfoData.getId());
                    Integer usageIdRegistrationCard = usageMapNew.get(dataMap.get("usage_id_registration_card"));
                    if (null != usageIdRegistrationCard) {
                        updateVehicleInfo.setUsageIdRegistrationCard(usageMapNew.get(dataMap.get("usage_id_registration_card")));

                        // 设置审计字段
                        Date now = new Date();
                        updateVehicleInfo.setUpdateTime(now);
                        updateVehicleInfo.setUpdateOperId(-1L);
                        updateVehicleInfo.setUpdateOperName("system");

                        // 执行更新
                        vehicleInfoMapper.updateByPrimaryKeySelective(updateVehicleInfo);
                    } else {
                        System.out.println("未找到匹配的使用性质枚举，：usage_id_registration_card" + dataMap.get("usage_id_registration_card"));
                    }
                });
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                System.out.println("所有数据解析完成！");
                try {
                    Thread.sleep(30000); // 延迟确保异步任务完成
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                latch.countDown();
            }
        }).sheet(sheetName).headRowNumber(1).doRead();

        try {
            latch.await(); // 等待所有任务完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("批量更新车辆额度信息过程中被中断", e);
        } finally {
            taskExecutor.shutdown(); // 关闭线程池
        }

        System.out.println("车辆额度信息批量更新完成！");
    }

    /**
     * 解析日期字符串为Date对象
     * 支持多种常见日期格式，增强健壮性
     *
     * @param value 日期字符串，支持两种格式:
     *              - yyyy-MM-dd
     *              - yyyy-MM-dd HH:mm:ss
     * @return 解析后的Date对象，如果输入为空或"NULL"则返回null
     */
    private Date parseDate(String value) {
        if(StringUtils.isBlank(value)) {
            return null;
        }
        if(StringUtils.equals("NULL", value)) {
            return null;
        }

        // 尝试多种日期格式
        String[] possiblePatterns = {
            "yyyy-MM-dd",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd",
            "yyyy/M/d",
            "yyyy-M-d",
            "yyyy/MM/dd HH:mm:ss"
        };

        // 移除可能的时间戳后缀和空白字符
        value = value.trim();
        if (value.contains(".")) {
            value = value.split("\\.")[0];
        }

        for (String pattern : possiblePatterns) {
            Date stringToDate = DateTimeUtils.stringToDate(value, pattern);
            if(stringToDate != null) {
                return stringToDate;
            }
        }

        // 尝试解析数字格式的Excel日期（自1900年1月1日起的天数）
        try {
            if (StringUtils.isNumeric(value)) {
                double excelDate = Double.parseDouble(value);
                return org.apache.poi.ss.usermodel.DateUtil.getJavaDate(excelDate);
            }
        } catch (Exception e) {
            System.out.println("无法解析数字格式的Excel日期: " + value);
        }

        System.out.println("警告: 无法解析日期 '" + value + "', 将使用null值");
        return null;
    }

    /**
     * 解析字符串为BigDecimal对象
     *
     * @param value 数字字符串
     * @return BigDecimal对象，如果输入为空、"NULL"或解析失败则返回null
     */
    private BigDecimal parseDecimal(String value) {
        if(StringUtils.isBlank(value)) {
            return null;
        }
        if(StringUtils.equals("NULL", value)) {
            return null;
        }
        try {
            return new BigDecimal(value);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析字符串为Integer对象
     *
     * @param value 数字字符串
     * @return Integer对象，如果输入为空或"NULL"则返回null
     */
    private Integer parseInt(String value) {
        if(StringUtils.isBlank(value)) {
            return null;
        }
        if(StringUtils.equals("NULL", value)) {
            return null;
        }
        return new BigDecimal(value).intValue();
    }

    /**
     * 处理字符串值
     *
     * @param value 输入字符串
     * @return 处理后的字符串，如果输入为空或"NULL"则返回null
     */
    private String parseString(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        if(StringUtils.equals("NULL", value)) {
            return null;
        }
        return value;
    }

    /**
     * 解析字符串为Long对象
     *
     * @param value 数字字符串
     * @return Long对象，如果输入为空或"NULL"则返回null
     */
    private Long parseLong(String value) {
        if(StringUtils.isBlank(value)) {
            return null;
        }
        if(StringUtils.equals("NULL", value)) {
            return null;
        }
        return Long.valueOf(value);
    }

    /**
     * 获取车型名称与ID的映射
     *
     * 该方法从数据库中查询所有自定义车型信息(ID > 10000)，
     * 并创建车型名称到车型ID的映射关系，用于后续车辆车型更新。
     *
     * @return 车型名称到车型ID的映射Map，key为车型名称，value为车型ID
     */
    private Map<String, Long> getModelNameToIdMap() {
        // 创建结果映射
        Map<String, Long> modelNameToIdMap = new HashMap<>();

        // 构建查询语句，只查询ID大于10000的自定义车型
        SelectStatementProvider selectStatement = select(vehicleModelInfo.allColumns())
                .from(vehicleModelInfo)
                .where(vehicleModelInfo.id, isGreaterThan(10000L))  // 自定义车型ID从10001开始
                .orderBy(vehicleModelInfo.vehicleModelName.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        // 执行查询
        List<VehicleModelInfo> vehicleModelInfoList = vehicleModelInfoMapper.selectMany(selectStatement);

        // 遍历结果集，构建映射关系
        for (VehicleModelInfo modelInfo : vehicleModelInfoList) {
            Long id = modelInfo.getId();
            String modelName = modelInfo.getVehicleModelName();

            // 验证车型名称有效性
            if (modelName != null && !modelName.trim().isEmpty()) {
                // 规范化车型名称（去除前后空格）并建立映射
                modelNameToIdMap.put(modelName.trim(), id);
            }
        }

        return modelNameToIdMap;
    }

    /**
     * 获取VIN与车型名称的映射
     *
     * 该方法从数据库中查询所有VIN码与车型名称的关联信息，
     * 并创建VIN码到车型名称的映射关系，用于后续车辆车型更新。
     *
     * @return VIN码到车型名称的映射Map，key为VIN码，value为车型名称
     */
    private Map<String, String> getVinToModelNameMap() {
        // 创建结果映射
        Map<String, String> vinToModelNameMap = new HashMap<>();

        // 构建查询语句，只查询车型名称不为空的记录
        SelectStatementProvider selectStatement = select(vinModelInfo.allColumns())
                .from(vinModelInfo)
                .where(vinModelInfo.modelName, isNotNull())  // 确保车型名称不为空
                .build()
                .render(RenderingStrategies.MYBATIS3);

        // 执行查询
        List<VinModelInfo> vinModelInfoList = vinModelInfoMapper.selectMany(selectStatement);

        // 遍历结果集，构建映射关系
        for (VinModelInfo vinInfo : vinModelInfoList) {
            String vin = vinInfo.getVin();
            String modelName = vinInfo.getModelName();

            // 验证VIN码和车型名称的有效性
            if (vin != null && !vin.trim().isEmpty() &&
                modelName != null && !modelName.trim().isEmpty()) {
                // 规范化VIN码和车型名称（去除前后空格）并建立映射
                vinToModelNameMap.put(vin.trim(), modelName.trim());
            }
        }

        return vinToModelNameMap;
    }

    /**
     * 更新车辆信息表中的车型ID
     *
     * 该方法根据VIN码与车型名称的映射以及车型名称与ID的映射，
     * 更新车辆信息表中的车型ID字段。使用线程池并行处理，提高更新效率。
     *
     * @param vinToModelNameMap VIN码到车型名称的映射
     * @param modelNameToIdMap 车型名称到车型ID的映射
     */
    private void updateVehicleModelIds(Map<String, String> vinToModelNameMap, Map<String, Long> modelNameToIdMap) {
        // 初始化计数器，用于等待所有任务完成
        CountDownLatch latch = new CountDownLatch(1);

        // 获取所有需要更新的车辆信息
        SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                .from(vehicleInfo)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<VehicleInfo> vehicleInfoList = vehicleInfoMapper.selectMany(selectStatement);

        // 使用线程池处理更新任务
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(10);
        taskExecutor.setMaxPoolSize(20);
        taskExecutor.setQueueCapacity(200);
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy()); // 拒绝策略
        taskExecutor.setThreadNamePrefix("vehicle-model-update-");
        taskExecutor.initialize();

        AtomicInteger processedCount = new AtomicInteger(0);
        int totalCount = vehicleInfoList.size();

        // 遍历所有车辆信息，使用线程池并行处理更新
        for (VehicleInfo vehicle : vehicleInfoList) {
            taskExecutor.execute(() -> {
                try {
                    // 获取车辆VIN码
                    String vin = vehicle.getVin();

                    // 验证VIN码有效性
                    if (vin == null || vin.trim().isEmpty()) {
                        return; // 跳过无效VIN码的车辆
                    }

                    // 规范化VIN码（去除前后空格）
                    vin = vin.trim();

                    // 根据VIN获取车型名称
                    String modelName = vinToModelNameMap.get(vin);
                    if (modelName == null) {
                        // 如果找不到对应的车型名称，跳过处理
                        return;
                    }

                    // 根据车型名称获取车型ID
                    Long newModelId = modelNameToIdMap.get(modelName);
                    if (newModelId == null) {
                        // 记录未找到车型ID的情况，便于后续排查
                        System.out.println("未找到车型名称[" + modelName + "]对应的车型ID，车架号: " + vin);
                        return;
                    }

                    // 只有当车型ID发生变化时才进行更新，避免不必要的数据库操作
                    Long currentModelId = vehicle.getVehicleModelId();
                    if (currentModelId == null || !currentModelId.equals(newModelId)) {
                        // 创建更新对象，只更新必要字段
                        VehicleInfo updateVehicleInfo = new VehicleInfo();
                        updateVehicleInfo.setId(vehicle.getId());
                        updateVehicleInfo.setVehicleModelId(newModelId);

                        // 执行更新操作
                        vehicleInfoMapper.updateByPrimaryKeySelective(updateVehicleInfo);
                    }
                } finally {
                    // 更新处理计数，用于统计和控制任务完成
                    int current = processedCount.incrementAndGet();
                    if (current >= totalCount) {
                        // 当所有任务处理完成时，释放CountDownLatch
                        latch.countDown();
                    }
                }
            });
        }

        try {
            // 等待所有任务完成或超时
            if (!latch.await(30, TimeUnit.MINUTES)) {
                System.out.println("更新车型任务执行超时");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("更新车型任务被中断", e);
        } finally {
            // 关闭线程池
            taskExecutor.shutdown();
        }

        System.out.println("车辆车型信息更新完成，共处理 " + processedCount.get() + " 条记录");
    }

    /**
     * 更新车辆财务车型信息
     *
     * 该方法从Excel文件中读取车架号与财务车型的对应关系，并更新数据库中的相关信息。
     * 处理流程：
     * 1. 读取FinanceModel.xlsx文件中的VIN码和财务车型名称
     * 2. 使用线程池并行处理数据更新
     * 3. 建立车型ID与财务车型名称的映射关系
     * 4. 批量更新vehicle_model表中的财务车型名称
     *
     * 注意事项：
     * - Excel文件第一行为标题行，需要跳过
     * - 使用线程池提高处理效率
     * - 建立缓存减少数据库查询
     * - 异常情况需要记录日志
     */
    @Test
    @DisplayName("更新财务车型信息")
    public void updateFinanceModelInfo() {
        System.out.println("开始更新财务车型信息...");

        // 初始化计数器，用于等待所有任务完成
        CountDownLatch latch = new CountDownLatch(1);

        // 存储车型ID与财务车型名称的映射关系
        Map<Long, String> modelIdToFinanceNameMap = new HashMap<>();

        // 映射字段信息 key: 列索引, value: 字段名称
        Map<Integer, String> tableMap = new HashMap<>();
        tableMap.put(0, "vin");
        tableMap.put(1, "finance_model_name");

        try {
            // 读取初始化Excel
            InputStream inputStream = ExcelReader.class.getResourceAsStream("/init/20250512/FinanceModel.xlsx");
            if (inputStream == null) {
                System.err.println("错误：找不到FinanceModel.xlsx文件");
                return;
            }

            // 使用AtomicInteger来跟踪处理进度
            AtomicInteger processedCount = new AtomicInteger(0);
            AtomicInteger totalCount = new AtomicInteger(0);

            // 读取Excel数据
            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    // 跳过第一行（标题行）
                    if (context.readRowHolder().getRowIndex() == 0) {
                        return;
                    }

                    totalCount.incrementAndGet();

                    // 异步处理每行数据
                    taskExecutor.execute(() -> {
                        try {
                            // 转换Excel数据到Map
                            Map<String, String> dataMap = new HashMap<>();
                            data.forEach((key, value) -> {
                                String columnName = tableMap.get(key);
                                dataMap.put(columnName, value);
                            });

                            // 获取VIN码和财务车型名称
                            String vin = parseString(dataMap.get("vin"));
                            String financeModelName = parseString(dataMap.get("finance_model_name"));

                            if (StringUtils.isBlank(vin) || StringUtils.isBlank(financeModelName)) {
                                System.out.println("警告：VIN码或财务车型名称为空，跳过处理");
                                return;
                            }

                            // 根据VIN码查询车辆信息
                            SelectStatementProvider selectStatement = select(vehicleInfo.allColumns())
                                    .from(vehicleInfo)
                                    .where(vehicleInfo.vin, isEqualTo(vin))
                                    .limit(1)
                                    .build()
                                    .render(RenderingStrategies.MYBATIS3);

                            VehicleInfo vehicleInfo = vehicleInfoMapper.selectOne(selectStatement).orElse(null);

                            if (vehicleInfo != null && vehicleInfo.getVehicleModelId() != null) {
                                // 将车型ID和财务车型名称的映射关系存入Map
                                synchronized (modelIdToFinanceNameMap) {
                                    modelIdToFinanceNameMap.put(vehicleInfo.getVehicleModelId(), financeModelName);
                                }
                            }
                        } finally {
                            // 更新处理计数
                            if (processedCount.incrementAndGet() >= totalCount.get()) {
                                latch.countDown();
                            }
                        }
                    });
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    System.out.println("Excel数据解析完成！");
                }
            }).sheet(0).doRead();

            // 等待所有任务完成或超时
            if (!latch.await(30, TimeUnit.MINUTES)) {
                System.out.println("更新财务车型信息任务执行超时");
                return;
            }

            // 打印映射关系
            System.out.println("车型ID与财务车型名称的映射关系：");
            modelIdToFinanceNameMap.forEach((modelId, financeName) ->
                System.out.println("车型ID: " + modelId + ", 财务车型名称: " + financeName));

             // 批量更新vehicle_model表
             modelIdToFinanceNameMap.forEach((modelId, financeName) -> {
                 VehicleModelInfo updateModel = new VehicleModelInfo();
                 updateModel.setId(modelId);
                 updateModel.setFinancialModelName(financeName);

                 // 设置审计字段
                 Date now = new Date();
                 updateModel.setUpdateTime(now);
                 updateModel.setUpdateOperId(-1L);
                 updateModel.setUpdateOperName("system");

                 vehicleModelInfoMapper.updateByPrimaryKeySelective(updateModel);
             });

            System.out.println("财务车型信息更新完成，共处理 " + processedCount.get() + " 条记录");

        } catch (Exception e) {
            System.err.println("更新财务车型信息时发生错误：" + e.getMessage());
            e.printStackTrace();
        }
    }

}
