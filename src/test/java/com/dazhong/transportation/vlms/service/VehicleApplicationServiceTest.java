package com.dazhong.transportation.vlms.service;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.dazhong.transportation.vlms.database.TableLicensePlateTaskInfoService;
import com.dazhong.transportation.vlms.dto.LicensePlateTaskVehicleDetailDto;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("Vehicle Device Service Test")
public class VehicleApplicationServiceTest {

    @Autowired
    private IVehicleApplicationService vehicleApplicationService;

    @Autowired
    private IVehicleDisposalService vehicleDisposalService;

    @Autowired
    private TableLicensePlateTaskInfoService tableLicensePlateTaskInfoService;

    @Test
    @DisplayName("Test search vehicle device list")
    public void testDingTalkResultProcess() {
        vehicleDisposalService.dingTalkResultProcess("ITJ91m2pQ4-v6SBbqrqr9A04961741922196", "agree");
    }

    @Test
    @DisplayName("Test search vehicle device list")
    public void selectLatestVehicleDetailByVin() {
        LicensePlateTaskVehicleDetailDto vehicleDetailDto = tableLicensePlateTaskInfoService.selectLatestVehicleDetailByVin("WD202503030000001");
        System.out.println(vehicleDetailDto.getTaskNumber());
    }

}
