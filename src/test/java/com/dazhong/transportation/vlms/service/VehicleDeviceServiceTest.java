package com.dazhong.transportation.vlms.service;

import com.alibaba.fastjson.JSON;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDeviceListRequest;
import com.dazhong.transportation.vlms.dto.response.VehicleDeviceInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("Vehicle Device Service Test")
public class VehicleDeviceServiceTest {

    @Autowired
    private IVehicleDeviceService vehicleDeviceService;


    @Test
    @DisplayName("Test search vehicle device list")
    public void testSearchVehicleDeviceList(){
        SearchVehicleDeviceListRequest request = new SearchVehicleDeviceListRequest();
        request.setPageSize(2);
        request.setPageNum(1);

        PageResponse<VehicleDeviceInfoResponse> pageResponse = vehicleDeviceService.searchVehicleDeviceList(request);
        System.out.println(JSON.toJSONString(pageResponse));
    }
}
