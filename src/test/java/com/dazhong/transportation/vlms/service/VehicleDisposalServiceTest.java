package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.database.impl.TableVehicleDisposalServiceImpl;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.mapper.VehicleDisposalMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("Vehicle Disposal Service Test - 测试isBusinessSale字段的条件判断逻辑")
public class VehicleDisposalServiceTest {

    @Mock
    private VehicleDisposalMapper vehicleDisposalMapper;

    @InjectMocks
    private TableVehicleDisposalServiceImpl tableVehicleDisposalService;

    private TokenUserInfo tokenUserInfo;

    @BeforeEach
    public void setUp() {
        // 创建测试用的TokenUserInfo
        tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(1L);
        tokenUserInfo.setName("Test User");
        // 设置组织ID列表和所有者ID列表用于权限过滤测试
        tokenUserInfo.setOrgIdList(Arrays.asList(1L, 2L, 3L));
        tokenUserInfo.setOwnerIdList(Arrays.asList(1, 2, 3));
    }

    @Test
    @DisplayName("测试isBusinessSale=1时应用权限过滤")
    public void testQueryVehicleDisposalListWithBusinessSaleFilter() {
        // 创建查询请求，设置isBusinessSale=1
        SearchVehicleDisposalListRequest request = new SearchVehicleDisposalListRequest();
        request.setIsBusinessSale(1); // 设置为商务出售，应该应用权限过滤

        // Mock返回结果
        List<VehicleDisposalListDto> mockResult = Collections.emptyList();
        when(vehicleDisposalMapper.selectVehicleDisposalList(any(SelectStatementProvider.class)))
                .thenReturn(mockResult);

        // 执行查询
        List<VehicleDisposalListDto> result = tableVehicleDisposalService.queryVehicleDisposalList(request, tokenUserInfo);

        // 验证结果不为空
        assertNotNull(result, "查询结果不应为空");

        // 验证mapper被调用
        verify(vehicleDisposalMapper).selectVehicleDisposalList(any(SelectStatementProvider.class));

        System.out.println("isBusinessSale=1时的测试通过：应用权限过滤");
    }

    @Test
    @DisplayName("测试isBusinessSale=0时不应用权限过滤")
    public void testQueryVehicleDisposalListWithoutBusinessSaleFilter() {
        // 创建查询请求，设置isBusinessSale=0
        SearchVehicleDisposalListRequest request = new SearchVehicleDisposalListRequest();
        request.setIsBusinessSale(0); // 设置为非商务出售，不应该应用权限过滤

        // Mock返回结果
        List<VehicleDisposalListDto> mockResult = Collections.emptyList();
        when(vehicleDisposalMapper.selectVehicleDisposalList(any(SelectStatementProvider.class)))
                .thenReturn(mockResult);

        // 执行查询
        List<VehicleDisposalListDto> result = tableVehicleDisposalService.queryVehicleDisposalList(request, tokenUserInfo);

        // 验证结果不为空
        assertNotNull(result, "查询结果不应为空");

        // 验证mapper被调用
        verify(vehicleDisposalMapper).selectVehicleDisposalList(any(SelectStatementProvider.class));

        System.out.println("isBusinessSale=0时的测试通过：不应用权限过滤");
    }

    @Test
    @DisplayName("测试isBusinessSale为null时不应用权限过滤")
    public void testQueryVehicleDisposalListWithNullBusinessSale() {
        // 创建查询请求，不设置isBusinessSale（为null）
        SearchVehicleDisposalListRequest request = new SearchVehicleDisposalListRequest();
        // request.setIsBusinessSale(null); // 默认为null，不应该应用权限过滤

        // Mock返回结果
        List<VehicleDisposalListDto> mockResult = Collections.emptyList();
        when(vehicleDisposalMapper.selectVehicleDisposalList(any(SelectStatementProvider.class)))
                .thenReturn(mockResult);

        // 执行查询
        List<VehicleDisposalListDto> result = tableVehicleDisposalService.queryVehicleDisposalList(request, tokenUserInfo);

        // 验证结果不为空
        assertNotNull(result, "查询结果不应为空");

        // 验证mapper被调用
        verify(vehicleDisposalMapper).selectVehicleDisposalList(any(SelectStatementProvider.class));

        System.out.println("isBusinessSale=null时的测试通过：不应用权限过滤");
    }

    @Test
    @DisplayName("测试逻辑正确性：验证不同isBusinessSale值的行为")
    public void testBusinessSaleLogic() {
        // 测试各种情况下的逻辑
        SearchVehicleDisposalListRequest request1 = new SearchVehicleDisposalListRequest();
        request1.setIsBusinessSale(1);

        SearchVehicleDisposalListRequest request2 = new SearchVehicleDisposalListRequest();
        request2.setIsBusinessSale(0);

        SearchVehicleDisposalListRequest request3 = new SearchVehicleDisposalListRequest();
        // isBusinessSale为null

        // Mock返回结果
        List<VehicleDisposalListDto> mockResult = Collections.emptyList();
        when(vehicleDisposalMapper.selectVehicleDisposalList(any(SelectStatementProvider.class)))
                .thenReturn(mockResult);

        // 执行查询
        tableVehicleDisposalService.queryVehicleDisposalList(request1, tokenUserInfo);
        tableVehicleDisposalService.queryVehicleDisposalList(request2, tokenUserInfo);
        tableVehicleDisposalService.queryVehicleDisposalList(request3, tokenUserInfo);

        // 验证所有查询都成功执行
        verify(vehicleDisposalMapper, org.mockito.Mockito.times(3))
                .selectVehicleDisposalList(any(SelectStatementProvider.class));

        System.out.println("所有isBusinessSale逻辑测试通过");
    }
}
