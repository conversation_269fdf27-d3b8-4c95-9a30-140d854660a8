package com.dazhong.transportation.vlms.service;

import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.response.QueryVinModelInfoJingYouResponse;
import com.dazhong.transportation.vlms.dto.response.QueryVinModelInfoResponse;
import com.dazhong.transportation.vlms.excel.ImportTransfer;
import com.dazhong.transportation.vlms.excel.util.ExcelUtil;
import com.dazhong.transportation.vlms.mapper.VinModelInfoMapper;
import com.dazhong.transportation.vlms.model.VinModelInfo;
import com.dazhong.transportation.vlms.utils.RedisUtils;
import com.google.gson.Gson;

import cn.hutool.core.collection.CollectionUtil;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("Vehicle Service Test")
public class VinModelInfoServiceTest {

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private VinModelInfoMapper vinModelInfoMapper;

    @Autowired
    private RestTemplate restTemplate;

    @MockBean
    private TokenUserInfo tokenUserInfo;  // 使用 MockBean 来模拟 TokenUserInfo

    @Autowired
    public RedisUtils redisUtils;

    @Before
    public void setUp() {
        // 模拟 TokenUserInfo 返回的用户信息
        when(tokenUserInfo.getUserId()).thenReturn(1L);
        when(tokenUserInfo.getName()).thenReturn("John Doe");
    }


    @Test
    @DisplayName("探数查车型")
    public void testTanShuVehicleModelInfo() {
        List<ImportTransfer> readList = ExcelUtil.readLocal("C:\\Users\\<USER>\\Desktop\\查询车型信息.xlsx", 0, ImportTransfer.class);

        // 获取车架号列表
        List<String> vinList = readList.stream().map(ImportTransfer::getVin).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(vinList)) {
            for (String vin : vinList) {
                //调用HTTP接口查询
                String url = Global.instance.getVehicleModelInfo;
                Map<String, Object> params = new HashMap<>();
                params.put("vin", vin);
                params.put("key", "fbc8e82670fefa34ede05000c1554305");
                ResponseEntity<QueryVinModelInfoResponse> response = restTemplate.postForEntity(url, params, QueryVinModelInfoResponse.class);

                if (params.get("key").equals("fbc8e82670fefa34ede05000c1554305")) {
                    // 优化：使用 incr 方法累加值
                    redisUtils.incr("tanshuapi-getModel", 1);
                }

                VinModelInfo updateVinModelInfo = new VinModelInfo();
                updateVinModelInfo.setVin(vin);

                if (null != response.getBody()) {
                    Gson gson = new Gson();
                    String jsonString = gson.toJson(response.getBody().getData());
                    if (StringUtils.isEmpty(jsonString) || "[]".equals(jsonString)) {
                        updateVinModelInfo.setModelJson(response.toString());
                    } else {
                        JSONObject jsonObject = JSONObject.parseObject(jsonString);
                        updateVinModelInfo.setModelName(jsonObject.getString("name"));
                        if (jsonObject.getString("model_list").length() > 8000) {
                            jsonObject.remove("model_list");
                            updateVinModelInfo.setModelJson(jsonObject.toJSONString());
                        } else {
                            updateVinModelInfo.setModelJson(jsonString);
                        }
                    }
                    vinModelInfoMapper.insertSelective(updateVinModelInfo);
                }
            }
        }

    }

    @Test
    @DisplayName("精友查车型")
    public void testJingYouVehicleInfo() {
        List<ImportTransfer> readList = ExcelUtil.readLocal("C:\\Users\\<USER>\\Desktop\\工作簿3.xlsx", 0, ImportTransfer.class);

        // 获取车架号列表
        List<String> vinList = readList.stream().map(ImportTransfer::getVin).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(vinList)) {
            for (String vin : vinList) {
                //调用HTTP接口查询
                String url = "https://jingyou.evcard.vip/ClaimCloudProd-app/vehicleIdentify/getVehicleIdentifyByVin/vintihuan/27864239";
                url = url.replace("vintihuan", vin);

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.set("Host", "jingyou.evcard.vip");
                headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0");
                headers.set("Accept", "*/*;type=ajax");
                headers.set("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
                headers.set("Accept-Encoding", "gzip, deflate, br, zstd");
                headers.set("Content-Type", "application/x-www-form-urlencoded");
                headers.set("access_token", "69c93036f6a74f399db81892619f01bf");
                headers.set("Snk-Location", "https://jingyou.evcard.vip/#/sipevaluate/basicInfo?lossNo=BY250324092800018&fileName=69c93036f6a74f399db81892619f01bf");
                headers.set("Content-Length", "0");
                headers.set("Origin", "https://jingyou.evcard.vip");
                headers.set("Connection", "keep-alive");
                headers.set("Referer", "https://jingyou.evcard.vip/");
                headers.set("Cookie", "ssomac=JI-Zpbd5QKuVsOgbdzVeOQ; token=gXctX7BkTFas3_BuCccqMg; acw_tc=0aef39a217427801116977389e00462929d3c4b295a8e02f9d831c169c131f; SERVERID=8763c153aa8a72ec9128d4b4edbc5d4e|1742780115|1742780111");
                headers.set("Sec-Fetch-Dest", "empty");
                headers.set("Sec-Fetch-Mode", "cors");
                headers.set("Sec-Fetch-Site", "same-origin");
                HttpEntity<String> httpEntity = new HttpEntity(null, headers);

                ResponseEntity<QueryVinModelInfoJingYouResponse> response = restTemplate.postForEntity(url, httpEntity, QueryVinModelInfoJingYouResponse.class);

                VinModelInfo updateVinModelInfo = new VinModelInfo();
                updateVinModelInfo.setVin(vin);

                if (null != response.getBody()) {
                    Gson gson = new Gson();
                    String jsonString = gson.toJson(response.getBody().getResult());
                    if (StringUtils.isBlank(jsonString) || !jsonString.startsWith("{") && !jsonString.startsWith("[")) {
                        updateVinModelInfo.setModelJson("Invalid JSON data");
                    } else {
                        try {
                            JSONObject jsonObject = JSONObject.parseObject(jsonString);
                            JSONArray jsonArray = jsonObject.getJSONArray("vehicleList");
                            if (CollectionUtil.isNotEmpty(jsonArray)) {
                                JSONObject vehicle = jsonArray.getJSONObject(0);
                                updateVinModelInfo.setModelName(vehicle.getString("vehicleName"));
                            }
                            updateVinModelInfo.setModelJson(jsonObject.toJSONString());
                        } catch (com.alibaba.fastjson.JSONException e) {
                            // 捕获并处理 JSON 解析异常
                            updateVinModelInfo.setModelJson("JSON parsing error: " + e.getMessage());
                        }
                    }
                    vinModelInfoMapper.insertSelective(updateVinModelInfo);
                }
            }
        }

    }
}
