package com.dazhong.transportation.vlms.service;

import com.dazhong.transportation.vlms.dto.DataOwnerDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.response.DataMaintainDictResponse;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.impl.DataDictServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据字典服务测试类
 * 主要测试createOwnerInfo方法的自定义ID功能
 * 
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional // 确保测试后回滚数据
public class DataDictServiceTest {

    @Resource
    private DataDictServiceImpl dataDictService;

    /**
     * 测试新增车辆拥有公司信息 - 不指定ID（系统自动生成）
     */
    @Test
    public void testCreateOwnerInfo_WithoutCustomId() {
        // 准备测试数据
        DataOwnerDto ownerDto = new DataOwnerDto();
        ownerDto.setName("测试公司_自动ID");
        ownerDto.setAddress("测试地址");
        ownerDto.setPhone("13800138000");
        // 不设置ID，让系统自动生成

        List<DataOwnerDto> ownerList = new ArrayList<>();
        ownerList.add(ownerDto);

        DataMaintainDictResponse<DataOwnerDto> request = new DataMaintainDictResponse<>(ownerList);

        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(1L);
        tokenUserInfo.setName("测试用户");

        // 执行测试
        assertDoesNotThrow(() -> {
            dataDictService.createOwnerInfo(request, tokenUserInfo);
        });
    }

    /**
     * 测试新增车辆拥有公司信息 - 指定自定义ID（ID不存在）
     * 验证自定义ID能够正确保存到数据库
     */
    @Test
    public void testCreateOwnerInfo_WithCustomId_NotExists() {
        // 准备测试数据
        DataOwnerDto ownerDto = new DataOwnerDto();
        ownerDto.setId(99999L); // 使用一个不太可能存在的ID
        ownerDto.setName("测试公司_自定义ID_验证保存");
        ownerDto.setAddress("测试地址");
        ownerDto.setPhone("13800138001");

        List<DataOwnerDto> ownerList = new ArrayList<>();
        ownerList.add(ownerDto);

        DataMaintainDictResponse<DataOwnerDto> request = new DataMaintainDictResponse<>(ownerList);

        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(1L);
        tokenUserInfo.setName("测试用户");

        // 执行测试
        assertDoesNotThrow(() -> {
            dataDictService.createOwnerInfo(request, tokenUserInfo);
        });

        // 验证ID是否正确保存 - 通过查询验证
        // 注意：这里需要确保测试环境支持数据库操作
        // 如果测试环境不支持，可以通过日志或其他方式验证
    }

    /**
     * 测试新增车辆拥有公司信息 - 指定自定义ID（ID已存在）
     * 应该抛出ServiceException
     */
    @Test
    public void testCreateOwnerInfo_WithCustomId_AlreadyExists() {
        // 首先创建一个记录
        DataOwnerDto firstOwnerDto = new DataOwnerDto();
        firstOwnerDto.setId(88888L);
        firstOwnerDto.setName("第一个测试公司");
        firstOwnerDto.setAddress("测试地址1");
        firstOwnerDto.setPhone("13800138002");

        List<DataOwnerDto> firstOwnerList = new ArrayList<>();
        firstOwnerList.add(firstOwnerDto);

        DataMaintainDictResponse<DataOwnerDto> firstRequest = new DataMaintainDictResponse<>(firstOwnerList);

        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(1L);
        tokenUserInfo.setName("测试用户");

        // 创建第一个记录
        dataDictService.createOwnerInfo(firstRequest, tokenUserInfo);

        // 尝试创建第二个记录，使用相同的ID
        DataOwnerDto secondOwnerDto = new DataOwnerDto();
        secondOwnerDto.setId(88888L); // 使用相同的ID
        secondOwnerDto.setName("第二个测试公司");
        secondOwnerDto.setAddress("测试地址2");
        secondOwnerDto.setPhone("13800138003");

        List<DataOwnerDto> secondOwnerList = new ArrayList<>();
        secondOwnerList.add(secondOwnerDto);

        DataMaintainDictResponse<DataOwnerDto> secondRequest = new DataMaintainDictResponse<>(secondOwnerList);

        // 执行测试，应该抛出异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataDictService.createOwnerInfo(secondRequest, tokenUserInfo);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("已被占用"));
        assertTrue(exception.getMessage().contains("88888"));
    }

    /**
     * 测试新增车辆拥有公司信息 - 公司名称重复
     * 应该抛出ServiceException
     */
    @Test
    public void testCreateOwnerInfo_DuplicateName() {
        // 首先创建一个记录
        DataOwnerDto firstOwnerDto = new DataOwnerDto();
        firstOwnerDto.setName("重复名称测试公司");
        firstOwnerDto.setAddress("测试地址1");
        firstOwnerDto.setPhone("13800138004");

        List<DataOwnerDto> firstOwnerList = new ArrayList<>();
        firstOwnerList.add(firstOwnerDto);

        DataMaintainDictResponse<DataOwnerDto> firstRequest = new DataMaintainDictResponse<>(firstOwnerList);

        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(1L);
        tokenUserInfo.setName("测试用户");

        // 创建第一个记录
        dataDictService.createOwnerInfo(firstRequest, tokenUserInfo);

        // 尝试创建第二个记录，使用相同的名称
        DataOwnerDto secondOwnerDto = new DataOwnerDto();
        secondOwnerDto.setName("重复名称测试公司"); // 使用相同的名称
        secondOwnerDto.setAddress("测试地址2");
        secondOwnerDto.setPhone("13800138005");

        List<DataOwnerDto> secondOwnerList = new ArrayList<>();
        secondOwnerList.add(secondOwnerDto);

        DataMaintainDictResponse<DataOwnerDto> secondRequest = new DataMaintainDictResponse<>(secondOwnerList);

        // 执行测试，应该抛出异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataDictService.createOwnerInfo(secondRequest, tokenUserInfo);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("名称"));
        assertTrue(exception.getMessage().contains("已存在"));
    }

    /**
     * 测试新增车辆拥有公司信息 - 空数据验证
     * 应该抛出ServiceException
     */
    @Test
    public void testCreateOwnerInfo_EmptyData() {
        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(1L);
        tokenUserInfo.setName("测试用户");

        // 测试空请求
        ServiceException exception1 = assertThrows(ServiceException.class, () -> {
            dataDictService.createOwnerInfo(null, tokenUserInfo);
        });
        assertTrue(exception1.getMessage().contains("请求数据不能为空"));

        // 测试空列表
        DataMaintainDictResponse<DataOwnerDto> emptyRequest = new DataMaintainDictResponse<>(new ArrayList<>());
        ServiceException exception2 = assertThrows(ServiceException.class, () -> {
            dataDictService.createOwnerInfo(emptyRequest, tokenUserInfo);
        });
        assertTrue(exception2.getMessage().contains("请求数据不能为空"));

        // 测试空名称
        DataOwnerDto ownerDto = new DataOwnerDto();
        ownerDto.setName(""); // 空名称
        ownerDto.setAddress("测试地址");
        ownerDto.setPhone("13800138006");

        List<DataOwnerDto> ownerList = new ArrayList<>();
        ownerList.add(ownerDto);

        DataMaintainDictResponse<DataOwnerDto> request = new DataMaintainDictResponse<>(ownerList);

        ServiceException exception3 = assertThrows(ServiceException.class, () -> {
            dataDictService.createOwnerInfo(request, tokenUserInfo);
        });
        assertTrue(exception3.getMessage().contains("公司名称不能为空"));
    }

    /**
     * 测试insertById方法 - 验证自定义ID能够正确保存
     * 这是一个单元测试，专门测试数据库层的ID保存功能
     */
    @Test
    public void testInsertById_CustomIdSaved() {
        // 准备测试数据
        DataOwnerDto ownerDto = new DataOwnerDto();
        ownerDto.setId(88888L); // 指定自定义ID
        ownerDto.setName("测试ID保存功能");
        ownerDto.setAddress("测试地址");
        ownerDto.setPhone("13800138999");

        List<DataOwnerDto> ownerList = new ArrayList<>();
        ownerList.add(ownerDto);

        DataMaintainDictResponse<DataOwnerDto> request = new DataMaintainDictResponse<>(ownerList);

        TokenUserInfo tokenUserInfo = new TokenUserInfo();
        tokenUserInfo.setUserId(1L);
        tokenUserInfo.setName("测试用户");

        // 执行新增操作
        assertDoesNotThrow(() -> {
            dataDictService.createOwnerInfo(request, tokenUserInfo);
        });

        // 验证：尝试再次使用相同ID应该失败（证明ID已被保存）
        DataOwnerDto duplicateOwnerDto = new DataOwnerDto();
        duplicateOwnerDto.setId(88888L); // 使用相同的ID
        duplicateOwnerDto.setName("重复ID测试");
        duplicateOwnerDto.setAddress("测试地址2");
        duplicateOwnerDto.setPhone("13800138998");

        List<DataOwnerDto> duplicateOwnerList = new ArrayList<>();
        duplicateOwnerList.add(duplicateOwnerDto);

        DataMaintainDictResponse<DataOwnerDto> duplicateRequest = new DataMaintainDictResponse<>(duplicateOwnerList);

        // 应该抛出ID已被占用的异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            dataDictService.createOwnerInfo(duplicateRequest, tokenUserInfo);
        });

        // 验证异常信息包含ID冲突提示
        assertTrue(exception.getMessage().contains("已被占用"));
        assertTrue(exception.getMessage().contains("88888"));
    }
}
