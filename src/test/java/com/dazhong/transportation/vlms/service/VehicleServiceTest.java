package com.dazhong.transportation.vlms.service;

import static org.mockito.Mockito.when;

import java.util.concurrent.CountDownLatch;

import com.dazhong.transportation.vlms.dto.response.GetCarInfoResponse;
import org.junit.Before;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchAssetVehicleRequest;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("Vehicle Service Test")
public class VehicleServiceTest {

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private IVehicleService vehicleService;

    @MockBean
    private TokenUserInfo tokenUserInfo;  // 使用 MockBean 来模拟 TokenUserInfo

    @Before
    public void setUp() {
        // 模拟 TokenUserInfo 返回的用户信息
        when(tokenUserInfo.getUserId()).thenReturn(1L);
        when(tokenUserInfo.getName()).thenReturn("John Doe");
    }


    @Test
    @DisplayName("导出车辆数据测试")
    public void testExportAssetVehicleInfo(){
        //查询条件
        SearchAssetVehicleRequest request = new SearchAssetVehicleRequest();
        CountDownLatch latch = new CountDownLatch(1); // 初始化计数器
        taskExecutor.execute(() ->
        {
            vehicleService.exportAssetVehicleInfo(request, tokenUserInfo);
            latch.countDown();
        });

        try {
            latch.await(); // 等待任务完成
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        taskExecutor.shutdown();
    }

    @Test
    @DisplayName("查询车辆数据同步信息测试")
    public void testSearchAssetVehicleSyncInfo() {
        vehicleService.syncAllVehicleInfoFromDaZhong();
        System.out.println("111");
    }
}
