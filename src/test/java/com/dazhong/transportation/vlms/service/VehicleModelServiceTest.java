package com.dazhong.transportation.vlms.service;

import com.alibaba.fastjson.JSON;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleModelListRequest;
import com.dazhong.transportation.vlms.dto.request.UpsertVehicleModelRequest;
import com.dazhong.transportation.vlms.dto.response.GetVehicleBaseInfoResponse;
import com.dazhong.transportation.vlms.dto.response.QueryVehicleBaseListResponse;
import com.dazhong.transportation.vlms.dto.response.VehicleModelInfoResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import org.junit.Before;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DisplayName("Vehicle Model Service Test")
public class VehicleModelServiceTest {

    @Autowired
    private IVehicleModelService vehicleModelService;

    @Autowired
    private RestTemplate restTemplate;

    @MockBean
    private TokenUserInfo tokenUserInfo;  // 使用 MockBean 来模拟 TokenUserInfo


    @Before
    public void setUp() {
        // 模拟 TokenUserInfo 返回的用户信息
        when(tokenUserInfo.getUserId()).thenReturn(1L);
        when(tokenUserInfo.getName()).thenReturn("John Doe");
    }

    @Test
    @DisplayName("查询汽车之家车型列表第三方接口")
    public void testQueryVehicleBaseList(){
        //调用HTTP接口查询
        String url = "https://md-st.evcard.vip/mdpartner/model/queryVehicleBaseList";
        Map<String, Object> params = new HashMap<>();
        params.put("field", null);
        ResponseEntity<QueryVehicleBaseListResponse> response = restTemplate.postForEntity(url, params, QueryVehicleBaseListResponse.class);
        System.out.println(JSON.toJSONString(response.getBody()));
    }

    @Test
    @DisplayName("查询汽车之家车型信息")
    public void testGetVehicleBaseInfo(){
        //调用HTTP接口查询
        String url = "https://md-st.evcard.vip/mdpartner/model/getVehicleBaseInfo";
        Map<String, Object> params = new HashMap<>();
        params.put("id", 1L);
        ResponseEntity<GetVehicleBaseInfoResponse> response = restTemplate.postForEntity(url, params, GetVehicleBaseInfoResponse.class);
        System.out.println(JSON.toJSONString(response.getBody()));
    }

    @Test
    @DisplayName("获取汽车之家车型下拉框")
    public void testComboAutohomeVehicleModel(){
        String autohomeModelName = "奥迪";
        ComboResponse<Long, String> comboResponse = vehicleModelService.comboAutohomeVehicleModel(autohomeModelName);
        System.out.println(JSON.toJSONString(comboResponse));
    }

    @Test
    @DisplayName("创建车型信息")
    public void testCreateVehicleModel() {
        // 创建 UpsertVehicleModelRequest 请求对象并设置值
        UpsertVehicleModelRequest request = new UpsertVehicleModelRequest();
        request.setVehicleModelName("Toyota Camry 2023");
        request.setVehicleBrandName("Toyota");
        request.setVehicleLevel(4);

        // 调用创建车型的方法
        vehicleModelService.createVehicleModel(request, tokenUserInfo);
    }

    @Test
    @DisplayName("获取车型详情")
    public void testGetVehicleModelDetail() {
        // 假设的车型ID
        Long modelId = 10L;

        // 假设返回的车型详情对象
        // 这里我们直接调用了 vehicleModelService 的方法，但可以根据需要进一步完善验证
        VehicleModelInfoResponse vehicleModelDetail = vehicleModelService.getVehicleModelDetail(modelId);

        // 验证返回的车型详情对象不为空
        assertNotNull(vehicleModelDetail, "Vehicle model details should not be null");
    }

    @Test
    @DisplayName("获取车型列表")
    public void testSearchVehicleModelList(){
        SearchVehicleModelListRequest request = new SearchVehicleModelListRequest();
        request.setPageSize(2);
        request.setPageNum(1);

        PageResponse<VehicleModelInfoResponse> pageResponse = vehicleModelService.searchVehicleModelList(request);
        System.out.println(JSON.toJSONString(pageResponse));
    }

    @Test
    @DisplayName("获取车型下拉框")
    public void testComboVehicleModel(){
        ComboResponse<Long, String> comboResponse = vehicleModelService.comboVehicleModel();
        System.out.println(JSON.toJSONString(comboResponse));
    }

    @Test
    @DisplayName("获取车型编号下拉框（已去重）")
    public void testComboVehicleModelNo(){
        ComboResponse<Long, String> comboResponse = vehicleModelService.comboVehicleModelNo();
        System.out.println("车型编号下拉框数据（已去重）：" + JSON.toJSONString(comboResponse));

        // 验证返回结果不为空
        assertNotNull(comboResponse, "车型编号下拉框数据不应为空");
        assertNotNull(comboResponse.getList(), "车型编号下拉框列表不应为空");

        // 打印去重后的数据数量
        System.out.println("去重后的车型编号数量：" + comboResponse.getList().size());
    }

    @Test
    @DisplayName("测试车型编号精确查询")
    public void testSearchVehicleModelListWithExactVehicleModelNo(){
        SearchVehicleModelListRequest request = new SearchVehicleModelListRequest();
        request.setPageSize(10);
        request.setPageNum(1);
        // 设置一个具体的车型编号进行精确查询测试
        request.setVehicleModelNo("BMW320i");

        PageResponse<VehicleModelInfoResponse> pageResponse = vehicleModelService.searchVehicleModelList(request);
        System.out.println("车型编号精确查询结果：" + JSON.toJSONString(pageResponse));

        // 验证查询结果
        assertNotNull(pageResponse, "查询结果不应为空");
        if (pageResponse.getList() != null && !pageResponse.getList().isEmpty()) {
            // 如果有结果，验证所有返回的车型编号都应该完全匹配查询条件
            for (VehicleModelInfoResponse response : pageResponse.getList()) {
                if (response.getVehicleModelNo() != null) {
                    System.out.println("找到匹配的车型编号：" + response.getVehicleModelNo());
                }
            }
        }
    }
}
