package com.dazhong.transportation.vlms.utils;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * UrlUtils工具类测试
 * 
 * <AUTHOR>
 * @date 2025-01-15
 */
public class UrlUtilsTest {

    @Test
    public void testDecode() {
        // 测试中文字符解码
        String encoded = "%E4%B8%AD%E6%96%87%E8%BD%A6%E6%9E%B6%E5%8F%B7";
        String decoded = UrlUtils.decode(encoded);
        assertEquals("中文车架号", decoded);
        
        // 测试英文字符
        String englishVin = "WVWZZZ1JZ3W386752";
        String decodedEnglish = UrlUtils.decode(englishVin);
        assertEquals(englishVin, decodedEnglish);
        
        // 测试空字符串
        assertNull(UrlUtils.decode(null));
        assertEquals("", UrlUtils.decode(""));
    }

    @Test
    public void testEncode() {
        // 测试中文字符编码
        String chinese = "中文车架号";
        String encoded = UrlUtils.encode(chinese);
        assertNotNull(encoded);
        assertTrue(encoded.contains("%"));
        
        // 测试英文字符
        String englishVin = "WVWZZZ1JZ3W386752";
        String encodedEnglish = UrlUtils.encode(englishVin);
        assertEquals(englishVin, encodedEnglish);
        
        // 测试空字符串
        assertNull(UrlUtils.encode(null));
        assertEquals("", UrlUtils.encode(""));
    }

    @Test
    public void testSafeDecodeMultiple() {
        // 测试单次编码
        String singleEncoded = "%E4%B8%AD%E6%96%87";
        String decoded = UrlUtils.safeDecodeMultiple(singleEncoded);
        assertEquals("中文", decoded);
        
        // 测试多次编码
        String doubleEncoded = "%25E4%25B8%25AD%25E6%2596%2587";
        String decodedMultiple = UrlUtils.safeDecodeMultiple(doubleEncoded);
        assertEquals("中文", decodedMultiple);
        
        // 测试普通字符串
        String normal = "WVWZZZ1JZ3W386752";
        String decodedNormal = UrlUtils.safeDecodeMultiple(normal);
        assertEquals(normal, decodedNormal);
    }

    @Test
    public void testRoundTrip() {
        // 测试编码解码往返
        String original = "中文车架号ABC123";
        String encoded = UrlUtils.encode(original);
        String decoded = UrlUtils.decode(encoded);
        assertEquals(original, decoded);
    }
} 