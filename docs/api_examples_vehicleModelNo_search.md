# 车型编号查询功能 API 使用示例

## 接口信息

**接口名称**: 查询车型列表  
**接口路径**: `/api/vehicleModel/searchVehicleModelList`  
**请求方式**: POST  
**认证要求**: 需要登录认证  

## 新增查询参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| vehicleModelNo | String | 否 | 车型编号，支持模糊查询 | "BMW320i" |

## 请求示例

### 1. 根据车型编号查询

**请求**:
```json
POST /api/vehicleModel/searchVehicleModelList
Content-Type: application/json
Authorization: Bearer {token}

{
  "pageNum": 1,
  "pageSize": 10,
  "vehicleModelNo": "BMW320"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "total": 5,
    "list": [
      {
        "id": 1001,
        "vehicleModelName": "宝马320i",
        "vehicleModelNo": "BMW320i",
        "vehicleBrandName": "宝马",
        "vehicleSeriesName": "宝马3系",
        "assessPassenger": 5,
        "gasTypeId": 1,
        "vehicleLevel": 2
      },
      {
        "id": 1002,
        "vehicleModelName": "宝马320Li",
        "vehicleModelNo": "BMW320Li",
        "vehicleBrandName": "宝马",
        "vehicleSeriesName": "宝马3系",
        "assessPassenger": 5,
        "gasTypeId": 1,
        "vehicleLevel": 2
      }
    ]
  }
}
```

### 2. 组合查询（车型名称 + 车型编号）

**请求**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "vehicleModelName": "宝马",
  "vehicleModelNo": "320i"
}
```

**说明**: 同时使用车型名称和车型编号进行查询，返回同时满足两个条件的结果。

### 3. 只使用车型名称查询（保持向后兼容）

**请求**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "vehicleModelName": "丰田"
}
```

**说明**: 不传递 `vehicleModelNo` 参数，功能与之前完全一致。

### 4. 空值处理

**请求**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "vehicleModelName": "奥迪",
  "vehicleModelNo": null
}
```

**说明**: `vehicleModelNo` 为 null 或空字符串时，该查询条件被忽略。

## 查询逻辑说明

### 模糊查询规则
- 车型编号查询使用 SQL `LIKE` 操作符
- 自动在查询关键字前后添加通配符 `%`
- 例如：输入 "BMW320" 实际查询 `vehicle_model_no LIKE '%BMW320%'`

### 多条件组合
- 多个查询条件之间使用 `AND` 逻辑连接
- 只有非空的查询条件才会生效
- 支持任意组合使用

### 查询优先级
1. 精确匹配优先（如果有的话）
2. 按车型ID降序排列
3. 分页返回结果

## 前端集成示例

### JavaScript/Ajax 调用
```javascript
function searchVehicleModels(params) {
    return fetch('/api/vehicleModel/searchVehicleModelList', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getToken()
        },
        body: JSON.stringify(params)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            return data.data;
        } else {
            throw new Error(data.message);
        }
    });
}

// 使用示例
searchVehicleModels({
    pageNum: 1,
    pageSize: 10,
    vehicleModelNo: 'BMW320'
}).then(result => {
    console.log('查询结果:', result.list);
    console.log('总数:', result.total);
});
```

### Vue.js 组件示例
```vue
<template>
  <div>
    <el-form :model="searchForm" inline>
      <el-form-item label="车型名称">
        <el-input v-model="searchForm.vehicleModelName" placeholder="请输入车型名称"></el-input>
      </el-form-item>
      <el-form-item label="车型编号">
        <el-input v-model="searchForm.vehicleModelNo" placeholder="请输入车型编号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    
    <el-table :data="vehicleList" v-loading="loading">
      <el-table-column prop="vehicleModelName" label="车型名称"></el-table-column>
      <el-table-column prop="vehicleModelNo" label="车型编号"></el-table-column>
      <el-table-column prop="vehicleBrandName" label="品牌"></el-table-column>
    </el-table>
    
    <el-pagination
      @current-change="handlePageChange"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total">
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        vehicleModelName: '',
        vehicleModelNo: ''
      },
      vehicleList: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  methods: {
    async handleSearch() {
      this.currentPage = 1;
      await this.loadVehicleList();
    },
    
    handleReset() {
      this.searchForm = {
        vehicleModelName: '',
        vehicleModelNo: ''
      };
      this.handleSearch();
    },
    
    handlePageChange(page) {
      this.currentPage = page;
      this.loadVehicleList();
    },
    
    async loadVehicleList() {
      this.loading = true;
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          ...this.searchForm
        };
        
        const result = await searchVehicleModels(params);
        this.vehicleList = result.list;
        this.total = result.total;
      } catch (error) {
        this.$message.error('查询失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    }
  },
  
  mounted() {
    this.loadVehicleList();
  }
}
</script>
```

## 错误处理

### 常见错误码
- `0`: 成功
- `-1001`: 参数错误
- `-1002`: 记录不存在
- `-1003`: 业务异常
- `-1004`: 系统异常

### 错误示例
```json
{
  "code": -1001,
  "message": "分页参数错误",
  "data": null
}
```

## 性能优化建议

### 1. 数据库索引
建议创建以下索引以提高查询性能：
```sql
-- 车型编号索引
CREATE INDEX idx_vehicle_model_no ON t_vehicle_model_info(vehicle_model_no);

-- 组合索引（如果经常组合查询）
CREATE INDEX idx_model_name_no ON t_vehicle_model_info(vehicle_model_name, vehicle_model_no);
```

### 2. 前端优化
- 使用防抖（debounce）技术减少频繁查询
- 实现查询结果缓存
- 添加加载状态提示

### 3. 查询建议
- 避免使用过短的查询关键字（建议至少2个字符）
- 合理设置分页大小（建议10-50条）
- 考虑添加查询历史和热门搜索功能
